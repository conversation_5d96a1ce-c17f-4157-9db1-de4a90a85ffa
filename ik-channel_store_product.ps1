$esUrl = "http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200"
$pair = "elastic:"
$encodedCredentials = [System.Convert]::ToBase64String([System.Text.Encoding]::ASCII.GetBytes($pair))
$auth = @{ Authorization = "Basic $encodedCredentials" }

$indexName = "channel_store_product_ik"
# $indexAlias = "channel_store_product"

$body = "{
    `"mappings`": {
        `"properties`": {
            `"product`": {
                `"properties`": {
                    `"name`": {
                        `"type`": `"text`",
                        `"analyzer`": `"ik_max_word`",
                        `"search_analyzer`":`"ik_smart`"
                    },
                    `"channel_category_name`": {
                        `"type`": `"text`",
                        `"analyzer`": `"ik_max_word`",
                        `"search_analyzer`":`"ik_smart`"
                    },
                    `"selling_point`": {
                        `"type`": `"text`",
                        `"analyzer`": `"ik_max_word`",
                        `"search_analyzer`":`"ik_smart`"
                    }
                }
            },
            `"sku_info`": {
                `"properties`": {
                    `"skuv`": {
                        `"properties`": {
                            `"spec_value_value`": {
                                `"type`": `"text`",
                                `"analyzer`": `"ik_max_word`",
                                `"search_analyzer`":`"ik_smart`"
                            }
                        }
                    }
                }
            },
            `"tags`": {
                `"type`": `"text`",
                `"analyzer`": `"ik_max_word`",
                `"search_analyzer`":`"ik_smart`"
            }
        }
    }
}"

# 删除索引
# Invoke-WebRequest -Uri "$esUrl/$indexName" -Method DELETE -Headers $auth | Select-Object Content -ExpandProperty Content

# 清除索引数据
# Invoke-WebRequest -Uri "$esUrl/channel_store_product_20210105/_delete_by_query" -Method POST -ContentType "application/json;charset=UTF-8" -Headers $auth -Body "{`"query`":{`"match_all`":{}}}" | Select-Object Content -ExpandProperty Content

# 创建索引
Invoke-WebRequest -Uri "$esUrl/$indexName" `
    -Method "PUT" `
    -ContentType "application/json;charset=UTF-8" `
    -Headers $auth `
    -Body $body | Select-Object Content -ExpandProperty Content

# 重建索引复制数据
Invoke-WebRequest -Uri "$esUrl/_reindex?wait_for_completion=false" `
    -Method "POST" `
    -ContentType "application/json" `
    -Headers $auth `
    -Body "{`"source`":{`"index`":`"channel_store_product_20210107`"},`"dest`":{`"index`":`"$indexName`"}}" | Select-Object Content -ExpandProperty Content

# 设置别名
# Invoke-WebRequest -Uri "$esUrl/_aliases" `
#     -Headers $auth `
#     -Method "POST" `
#     -ContentType "application/json" `
#     -Body "{`"actions`":[{`"add`":{`"index`":`"$indexName`",`"alias`":`"$indexAlias`"}}]}" | Select-Object Content -ExpandProperty Content

# 移除别名
# Invoke-WebRequest -Uri "$esUrl/_aliases" `
#     -Headers $auth `
#     -Method "POST" `
#     -ContentType "application/json" `
#     -Body "{`"actions`":[{`"remove`":{`"index`":`"channel_store_product_20210105`",`"alias`":`"channel_store_product`"}}]}" | Select-Object Content -ExpandProperty Content

# 设置别名并移除索引（原子操作）
# Invoke-WebRequest -Uri "$esUrl/_aliases" `
#     -Method "POST" `
#     -ContentType "application/json" `
#     -Body "{`"actions`":[{`"add`":{`"index`":`"$indexName`",`"alias`":`"$indexAlias`"}},{`"remove_index`":{`"index`":`"channel_store_product_20210105102843`"}}]}" | Select-Object Content -ExpandProperty Content
