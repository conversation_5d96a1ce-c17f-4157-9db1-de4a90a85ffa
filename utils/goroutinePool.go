package utils

import (
	"fmt"
)

type Task struct {
	f func() error
}

func NewTask(f func() error) *Task {
	t := &Task{
		f: f,
	}
	return t
}

func (t *Task) Execute() {
	t.f()
}

type Pool struct {
	EntryChannel chan *Task
	worker_num   int
	JobsChannel  chan *Task
}

func (p *Pool) NewPool(cap int) *Pool {
	poo := &Pool{
		EntryChannel: make(chan *Task),
		worker_num:   cap,
		JobsChannel:  make(chan *Task),
	}
	return poo
}

func (p *Pool) worker(work_ID int) {
	for task := range p.JobsChannel {
		task.Execute()
		fmt.Printf("Worker ID：%v 执行完毕", work_ID)
	}
}

func (p *Pool) Run() {
	for i := 0; i < p.worker_num; i++ {
		go p.worker(i)
	}
	for task := range p.EntryChannel {
		p.JobsChannel <- task
	}
	close(p.JobsChannel)
	close(p.EntryChannel)
}
