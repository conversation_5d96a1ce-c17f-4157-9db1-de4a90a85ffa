package utils

import (
	"fmt"
	"reflect"
	"testing"
)

func TestSliceUnique(t *testing.T) {
	tests := []struct {
		name string
		args []string
		want []string
	}{
		{
			name: "t1",
			args: []string{"1", "2", "3"},
			want: []string{"1", "2", "3"},
		},
		{
			name: "t2",
			args: []string{"1", "1", "2", "3"},
			want: []string{"1", "2", "3"},
		},
		{
			name: "t3",
			args: []string{"1", "1", "2", "2", "3"},
			want: []string{"1", "2", "3"},
		},
		{
			name: "t4",
			args: []string{},
			want: []string{},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := SliceUnique(tt.args)
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("fail, want %v, got %v", tt.want, got)
			}
		})
	}
}

func TestSliceFilter(t *testing.T) {
	tests := []struct {
		name  string
		args1 []string
		args2 string
		want  []string
	}{
		{
			name:  "t1",
			args1: []string{"", "1", "2", "3"},
			args2: "",
			want:  []string{"1", "2", "3"},
		},
		{
			name:  "t2",
			args1: []string{"1", "1", "2", "3"},
			args2: "1",
			want:  []string{"2", "3"},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := SliceFilter(tt.args1, tt.args2)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fail, want %v, got %v", tt.want, got)
			}
		})
	}
}

func BenchmarkSliceUnique(b *testing.B) {
	tests := []struct {
		name string
		args []string
		want []string
	}{
		{
			name: "t1 ",
			args: []string{"1", "2", "3"},
			want: []string{"1", "2", "3"},
		},
		{
			name: "t2 ",
			args: []string{"1", "1", "2", "3"},
			want: []string{"1", "2", "3"},
		},
		{
			name: "t3 ",
			args: []string{"1", "1", "2", "2", "3"},
			want: []string{"1", "2", "3"},
		},
	}

	for _, tt := range tests {
		tt := tt
		b.Run(tt.name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				SliceUnique(tt.args)
			}
		})
	}
}

func BenchmarkSliceFilter(b *testing.B) {
	tests := []struct {
		name  string
		args1 []string
		args2 string
	}{
		{
			name:  "t1 ",
			args1: []string{"", "1", "2", "3"},
			args2: "",
		},
		{
			name:  "t2 ",
			args1: []string{"1", "1", "2", "3"},
			args2: "1",
		},
	}

	for _, tt := range tests {
		tt := tt
		b.Run(tt.name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				SliceFilter(tt.args1, tt.args2)
			}
		})
	}
}

func TestArrayInGroupsOf(t *testing.T) {
	tests := []struct {
		name  string
		args1 []int
		args2 int64
	}{
		{
			name:  "t1 ",
			args1: []int{1, 2, 3, 4, 5, 6, 6, 7},
			args2: 3,
		},
		{
			name:  "t2 ",
			args1: []int{5, 6, 7, 1, 2, 3, 4, 54, 5, 67, 8, 9, 4},
			args2: 5,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(b *testing.T) {
			of := ArrayInGroupsOf(tt.args1, tt.args2)
			fmt.Println(of)
		})
	}
}
