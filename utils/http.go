package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

var HttpDefaultClient *http.Client
var Client60Second *http.Client
var Client30Second *http.Client

func init() {
	HttpDefaultClient = http.DefaultClient
	Client60Second = &http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	Client30Second = &http.Client{Timeout: time.Second * 30, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}

}

func HttpGet(url, source, ua string, param map[string]interface{}) (int, string) {
	return action(url, source, ua, http.MethodGet, "", param)
}

func HttpPostForm(url, source, ua string, param map[string]interface{}) (int, string) {
	return action(url, source, ua, http.MethodPost, "application/x-www-form-urlencoded", param)
}

func action(uri, source, ua string, httpMethod string, contentType string, param map[string]interface{}) (int, string) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("http.action", err)
		}
	}()
	var req *http.Request
	switch httpMethod {
	case http.MethodGet:
		if param != nil {
			uri += "?" + mapToValues(param).Encode()
		}
		req, _ = http.NewRequest(httpMethod, uri, nil)
	case http.MethodPost:
		httpMethod = http.MethodPost
		var reader io.Reader

		if contentType == "application/x-www-form-urlencoded" {
			reader = strings.NewReader(mapToValues(param).Encode())
		} else if contentType == "application/json;charset=UTF-8" {
			byteData, _ := json.Marshal(param)
			reader = bytes.NewReader(byteData)
		}
		req, _ = http.NewRequest(httpMethod, uri, reader)
		req.Header.Add("Content-Type", contentType)
	default:
		return 0, "不支持的请求类型"
	}

	// for k, v := range httpHeader {
	// 	req.Header.Add(k, v)
	// }
	//ul := uuid.NewV4()
	//sn := strings.ReplaceAll(ul.String(), "-", "")
	//req.Header.Add("sn", sn)
	//req.Header.Add("source", source)
	//req.Header.Add("ua", ua)
	//req.Header.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))

	//client := http.Client{Timeout: time.Second * 30, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}

	res, err := Client30Second.Do(req)
	if err != nil {
		glog.Error(err)
		return 0, err.Error()
	}

	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)
	return res.StatusCode, string(body)
}

func mapToValues(mp map[string]interface{}) url.Values {
	v := url.Values{}
	for key, val := range mp {
		switch val.(type) {
		case int:
			v.Add(key, strconv.Itoa(val.(int)))
		case int32:
			v.Add(key, strconv.Itoa(int(val.(int32))))
		case int64:
			v.Add(key, strconv.Itoa(int(val.(int64))))
		case float64:
			v.Add(key, strconv.FormatFloat(val.(float64), 'E', -1, 64))
		case float32:
			v.Add(key, strconv.FormatFloat(float64(val.(float32)), 'E', -1, 32))
		default:
			v.Add(key, val.(string))
		}
	}
	//glog.Info(v.Encode())
	return v
}

//dataJson : 数据对象转化成json字符串
func BJHttpPost(url string, dataJson []byte, Headers string) ([]byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	//client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	req.Header.Set("Content-Type", "application/json")

	if len(Headers) > 0 {
		strlist := strings.Split(Headers, "&")
		for i := 0; i < len(strlist); i++ {
			v := strlist[i]
			valuelist := strings.Split(v, "|")
			req.Header.Set(valuelist[0], valuelist[1])
		}
	}

	for k, v := range BjSignMap() {
		req.Header.Set(k, v)
	}

	res, err := Client60Second.Do(req)
	if err != nil {
		log.Println(err)
		return []byte(""), err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	return body, err
}

func BjSignMap() map[string]string {
	Timestamp := strconv.Itoa(int(time.Now().Unix()))
	apiStr := GenSonyflake()
	//apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%s&apiSecret=%s
	sign := fmt.Sprintf("apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%s&apiSecret=%s",
		config.GetString("Product_price_sync_app_secret"),
		apiStr,
		config.GetString("Product_price_sync_app_id"),
		Timestamp,
		config.GetString("Product_price_sync_app_secret"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	arr := make(map[string]string)
	arr["apiId"] = config.GetString("Product_price_sync_app_id")
	arr["apiSecre"] = config.GetString("Product_price_sync_app_secret")
	arr["apiStr"] = apiStr
	arr["timestamp"] = Timestamp
	arr["sign"] = md5sign
	return arr
}
