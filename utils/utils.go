package utils

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/streadway/amqp"
	"io"
	"io/ioutil"
	"math/rand"
	"mime/multipart"
	"net/http"
	"runtime"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/ppkg/kit"
	"github.com/xuri/excelize/v2"

	"github.com/maybgit/glog"
)

var (
	DATE_TIME_LAYOUT = "2006-01-02 15:04:05"
)

func CatchPanic() {
	if err := recover(); err != nil {
		if pc, file, line, ok := runtime.Caller(2); ok {
			pcName := runtime.FuncForPC(pc).Name()

			glog.Errorf(fmt.Sprintf("catch panic error: %v\nFILE: %s\nLINE: %d\nFUNCNAME: %s", err, file, line, pcName))
		}
	}
}

// 获取正在运行的函数名
func RunFuncName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])

	split := strings.Split(f.Name(), ".")
	if len(split) > 1 {
		return split[1]
	}

	return f.Name()
}

// 将结构体转换为字符串
func ObjectToJsonString(obj interface{}) string {
	var str string
	res, err := json.Marshal(obj)
	if err != nil {
		glog.Error(err)
	} else {
		str = string(res)
	}

	return str
}

type PaginateReq struct {
	Count    *xorm.Session
	List     *xorm.Session
	Page     int32
	PageSize int32
}

// Paginate 分页查询
func (in *PaginateReq) Paginate(total *int32, data interface{}) error {
	if count, err := in.Count.Count(); err != nil {
		return errors.New("查询Count出错 " + err.Error())
	} else {
		*total = int32(count)
	}

	if *total < 1 {
		return nil
	}
	if in.Page < 1 {
		in.Page = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}
	in.List.Limit(int(in.PageSize), int(in.PageSize*(in.Page-1)))
	if err := in.List.Find(data); err != nil {
		return errors.New("查询List出错 " + err.Error())
	}

	return nil
}

// 数组分组
func ArrayInGroupsOf(arr []int, num int64) [][]int {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]int{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]int, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

// string分组
func ArrayStrGroupsOf(arr []string, num int64) [][]string {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]string{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]string, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

// UploadFile 上传文件需要的信息
// 文件路径方法 reader, err := os.Open(fileName)
// excel方式 b,err := file.WriteToBuffer();reader = bytes.NewReader(b.Bytes())
type UploadFile struct {
	Name   string
	Reader io.Reader
}

// UploadQiNiuResponse 上传到七牛响应
type UploadQiNiuResponse struct {
	FileName string
	Size     int
	Url      string
	Error    string
}

// ToQiNiu 上传文件到七牛云
func (uf *UploadFile) ToQiNiu() (url string, err error) {
	if len(uf.Name) < 1 {
		return "", errors.New("文件名称不能为空")
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", uf.Name)
	if _, err = io.Copy(part, uf.Reader); err != nil {
		return
	}
	if err = writer.Close(); err != nil {
		return
	}

	host := config.GetString("file-upload-url")
	if len(host) == 0 {
		host = "https://api.petrvet.com"
	}
	//host = "http://awen.uat.rvet.cn"
	httpResp, err := http.Post(host+"/fss/newup", writer.FormDataContentType(), body)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()

	resBody, err := ioutil.ReadAll(httpResp.Body)
	if err != nil {
		return
	}

	res := new(UploadQiNiuResponse)
	if err = json.Unmarshal(resBody, res); err != nil {
		return "", errors.New("解析响应body出错 " + err.Error())
	}
	if httpResp.StatusCode >= 400 {
		if len(res.Error) == 0 {
			res.Error = httpResp.Status
		}
		return "", errors.New("请求出错 " + res.Error)
	}

	res.Url = strings.Replace(res.Url, "http://", "https://", 1)

	return res.Url, nil
}

// UploadExcelToQiNiu 上传excel文件到七牛云
func UploadExcelToQiNiu(file *excelize.File, name string) (url string, err error) {
	b, err := file.WriteToBuffer()
	if err != nil {
		return
	}
	if len(name) < 1 {
		name = kit.GetGuid36() + ".xlsx"
	}
	uf := &UploadFile{
		Name:   name,
		Reader: bytes.NewReader(b.Bytes()),
	}

	return uf.ToQiNiu()
}

// 随机字符串
func GenerateRandomStr(size int) string {
	iKind, kinds, result := 3, [][]int{{10, 48}, {26, 97}, {26, 65}}, make([]byte, size)
	rand.Seed(time.Now().UnixNano())
	for i := 0; i < size; i++ {
		iKind = rand.Intn(3)
		scope, base := kinds[iKind][0], kinds[iKind][1]
		result[i] = uint8(base + rand.Intn(scope))
	}
	return string(result)
}

//开启一个mq链接
func NewMqConn() *amqp.Connection {
	mqEndPoint := config.GetString("mq.EndPoint")
	mqUserName := config.GetString("mq.UserName")
	mqPassWord := config.GetString("mq.PassWord")
	mqHostName := config.GetString("mq.HostName")

	var buf bytes.Buffer
	buf.WriteString("amqp://")
	buf.WriteString(mqUserName)
	buf.WriteString(":")
	buf.WriteString(mqPassWord)

	// <Your End Point> 请从控制台获取。如果你使用的是杭州Region，那么Endpoint会形如 137000000010111.mq-amqp.cn-hangzhou-a.aliyuncs.com
	buf.WriteString("@")
	buf.WriteString(mqHostName)
	buf.WriteString("/")
	buf.WriteString(mqEndPoint)

	url := buf.String()
	url = config.GetString("mq.oneself")
	//env:= strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	//if env == "uat"{
	//	url ="*********************************************/"
	//}
	//url = "**************************************/" //add by csf@先写死用新的mq

	// 本地批量处理错误mq
	//url = "***********************************************/"
	//glog.Info("积分中心RabbitMQ地址：", url)
	conn, err := amqp.Dial(url)
	if err != nil {
		glog.Error("RabbitMQ dial fail. err : ", err)
		panic(err)
	}
	return conn
}

func NewMqChannel(conn *amqp.Connection) *amqp.Channel {
	channel, err := conn.Channel()
	if err != nil {
		glog.Error("RabbitMQ Get Channel fail. err : ", err)
		panic(err)
	}
	return channel
}

func ConsumeNew(queue, key, exchange string, fun func(request amqp.Delivery, channelGAll <-chan bool) (response string, err error)) {
	conn := NewMqConn()
	if conn == nil {
		glog.Error("conn is nil")

		return
	}
	defer conn.Close()

	ch := NewMqChannel(conn)
	if ch == nil {
		glog.Error("ch is nil")
		return
	}
	//一次拿一个数据
	if err := ch.Qos(2, 0, false); err != nil {
		glog.Error("Rabbitmq，", queue, "，", key, "，", exchange, "，", err.Error())
	}
	defer ch.Close()

	err := ch.QueueBind(queue, key, exchange, false, nil)
	if err != nil {
		glog.Error(queue, "，", key, "，", exchange, "，", err.Error())
		return
	}

	delivery, err := ch.Consume(queue, queue, false, false, false, false, nil)
	if err != nil {
		glog.Error(err)
	}
	channelGAll := make(chan bool, 2)
	for {
		for d := range delivery {
			channelGAll <- true
			func() {
				defer func() {

					if err := recover(); err != nil {
						glog.Error(err)
					}
				}()

			}()
			go fun(d, channelGAll) // 业务处理
		}
	}
	close(channelGAll)
}
