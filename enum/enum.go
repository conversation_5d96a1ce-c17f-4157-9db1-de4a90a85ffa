package enum

//任务内容:1:批量新建;2:批量更新;3：渠道-批量新建 4：渠道-批量上架 5:批量认领 6:认领全部商品 7:批量认领或全部认领 8:药品全部下架 9:药品恢复上架 10:药品相关任务
//11:渠道--商品导出 12:渠道--批量更新 13批量同步 15批量脚本 16 商品分类同步 17 全量同步分类到指定门店 18 商品从渠道下架处理 19 批量导入前置仓价格信息 20 平台商品导出 151 平台--商品库页面 152 管家--商品库）

const (
	TaskContentBatchCreate = iota + 1
	TaskContentBatchUpdate
	TaskContentChannelBatchCreate
	TaskContentChannelBatchUp
	TaskContentBatchClaim
	TaskContentAllClaim
	TaskContentBatchOrAllClaim
	TaskContentDrugAllDown
	TaskContentDrugUp
	TaskContentDrugRelevant
	TaskContentChannelGoodsExport
	TaskContentChannelBatchUpdate
	TaskContentBatchSynchronization
	TaskContentBatchNull
	TaskContentBatchScript
	TaskContentGoodsCategorySynchronization
	TaskContentALLSynchronizationToShop
	TaskContentChannelBatchDown
	TaskContentBatchPrice
	TaskContentGoodsExport
	TaskContentVirtualGoodsExport
)

var TaskContentMapText = map[int32]string{
	TaskContentBatchCreate:                  "批量新建",
	TaskContentBatchUpdate:                  "批量编辑",
	TaskContentChannelBatchCreate:           "渠道-批量新建",
	TaskContentChannelBatchUp:               "渠道-批量上架",
	TaskContentBatchClaim:                   "批量认领",
	TaskContentAllClaim:                     "认领全部商品",
	TaskContentBatchOrAllClaim:              "批量认领或全部认领",
	TaskContentDrugAllDown:                  "药品全部下架",
	TaskContentDrugUp:                       "药品恢复上架",
	TaskContentDrugRelevant:                 "药品相关任务",
	TaskContentChannelGoodsExport:           "渠道-商品导出",
	TaskContentChannelBatchUpdate:           "渠道-批量编辑",
	TaskContentBatchSynchronization:         "批量同步",
	TaskContentBatchNull:                    "批量任务",
	TaskContentBatchScript:                  "批量脚本",
	TaskContentGoodsCategorySynchronization: "商品分类同步",
	TaskContentALLSynchronizationToShop:     "全量同步分类到指定门店",
	TaskContentChannelBatchDown:             "渠道-批量下架",
	TaskContentBatchPrice:                   "批量导入前置仓价格信息",
	TaskContentGoodsExport:                  "实物-商品导出",
	TaskContentVirtualGoodsExport:           "虚拟-商品导出",
}

const (
	ChannelAwenId        int = iota + 1 //阿闻
	ChannelMtId                         //美团
	ChannelElmId                        //饿了么
	ChannelJddjId                       //京东到家
	ChannelMallId                       //阿闻电商
	ChannelStore                        // 门店
	ChannelBaiDu                        //百度
	ChannelH5                           //h5
	ChannelDigitalHealth                // 医疗互联网渠道
	ChannelAwenPickUpId                 // 阿闻竖屏自提
)

var ChannelMap = map[int]string{
	ChannelAwenId:        "阿闻外卖",
	ChannelMtId:          "美团",
	ChannelElmId:         "饿了么",
	ChannelJddjId:        "京东到家",
	ChannelMallId:        "阿闻电商",
	ChannelStore:         "门店",
	ChannelBaiDu:         "百度",
	ChannelH5:            "H5",
	ChannelDigitalHealth: "医疗互联网渠道",
	ChannelAwenPickUpId:  "阿闻竖屏自提",
}

var WarehouseCategoryMap = map[int]string{
	1: "电商仓",
	3: "门店仓",
	4: "前置仓",
	5: "前置虚拟仓",
}

// 渠道商品记录类型
const (
	//'记录类型(1删除商品2上架3批量上架4下架 5批量下架6自动上架（包括了7天无库存下架的）7自动下架)'
	RecordTypeDelete = iota + 1
	RecordTypeUpOne
	RecordTypeUpBatch
	RecordTypeDownOne
	RecordTypeDownBatch
	RecordTypeAutoUp
	RecordTypeAutoDown
)

// 下架类型 -1：单个的手动下架 0：默认 1: 7天无库存自动下架 2：批量下架  3: 过期商品自动下架 4：oms停用商品自动下架 5：子龙不可销下架 6：非处方药变处方药下架
const (
	//单个的手动下架
	DownRecordTypeOne = -1
	//7天无库存自动下架
	DownRecordType7DayNoStock = iota
	// 批量
	DownRecordTypeBatch
	// 过期
	DownRecordTypeExpire
	// oms停用商品自动下架
	DownRecordTypeOmsDisable
	// 子龙不可销下架
	DownRecordTypeNoSell
	// 非处方药变处方药下架
	DownRecordTypePrescribe
	//子龙变可销
	DownRecordTypeSell
)

// 更新第三方商品id的位置来源（记录调用方法UpdateProductThirdIdBySpu 和 MtProductThirdId 和 ElmProductThirdId的地方）
const (
	UpdateProductThirdIdFrom1  = "1"
	UpdateProductThirdIdFrom2  = "2"
	UpdateProductThirdIdFrom3  = "3"
	UpdateProductThirdIdFrom4  = "4"
	UpdateProductThirdIdFrom5  = "5"
	UpdateProductThirdIdFrom6  = "6"
	UpdateProductThirdIdFrom7  = "7"
	UpdateProductThirdIdFrom8  = "8"
	UpdateProductThirdIdFrom9  = "9"
	UpdateProductThirdIdFrom10 = "10"
	UpdateProductThirdIdFrom11 = "11"
	UpdateProductThirdIdFrom12 = "12"
	UpdateProductThirdIdFrom13 = "13"
	UpdateProductThirdIdFrom14 = "14"
	UpdateProductThirdIdFrom15 = "15"
	UpdateProductThirdIdFrom16 = "16"
	UpdateProductThirdIdFrom17 = "17"
	UpdateProductThirdIdFrom18 = "18"
	UpdateProductThirdIdFrom19 = "19"
	UpdateProductThirdIdFrom20 = "20"
	UpdateProductThirdIdFrom21 = "21"
)
