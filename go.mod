module _

go 1.13

require (
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/BurntSushi/toml v0.3.1
	github.com/go-errors/errors v1.0.1
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.7.0
	github.com/go-xorm/xorm v0.7.9
	github.com/golang/protobuf v1.5.2
	github.com/google/go-cmp v0.5.9 // indirect
	github.com/google/uuid v1.3.0
	github.com/jinzhu/copier v0.4.0
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/labstack/echo/v4 v4.1.16
	github.com/legofun/elasticsearch v0.0.0-20200908032909-fc2d9a812853
	github.com/lib/pq v1.10.7 // indirect
	github.com/limitedlee/microservice v0.1.7
	github.com/mailru/easyjson v0.7.6 // indirect
	github.com/mattn/go-colorable v0.1.7 // indirect
	github.com/mattn/go-isatty v0.0.16 // indirect
	github.com/mattn/go-sqlite3 v2.0.3+incompatible // indirect
	github.com/maybgit/glog v0.1.22
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/niemeyer/pretty v0.0.0-20200227124842-a10e7caefd8e // indirect
	github.com/olivere/elastic/v7 v7.0.19
	github.com/onsi/ginkgo v1.15.0 // indirect
	github.com/onsi/gomega v1.10.5 // indirect
	github.com/panjf2000/ants v1.2.1
	github.com/ppkg/distributed-worker v1.0.10
	github.com/ppkg/kit v0.0.0-20210928070906-2e2b70f489af
	github.com/robfig/cron/v3 v3.0.1
	github.com/shopspring/decimal v1.3.1
	github.com/sony/sonyflake v1.0.0
	github.com/spf13/cast v1.3.1
	github.com/streadway/amqp v1.0.0
	github.com/stretchr/testify v1.8.4 // indirect
	github.com/techoner/gophp v0.2.0
	github.com/tricobbler/mqgo v0.3.24
	github.com/tricobbler/rp-kit v0.0.0-20210319100400-bb22d9ecb4aa
	github.com/xuri/excelize/v2 v2.6.0
	golang.org/x/net v0.14.0 // indirect
	golang.org/x/sync v0.1.0
	golang.org/x/time v0.0.0-20201208040808-7e3f01d25324
	google.golang.org/genproto v0.0.0-20210323160006-e668133fea6a
	google.golang.org/grpc v1.43.0
	google.golang.org/protobuf v1.27.1
	gopkg.in/check.v1 v1.0.0-20200227125254-8fa46927fb4f // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	xorm.io/builder v0.3.11-0.20220531020008-1bd24a7dc978 // indirect
	xorm.io/core v0.7.3 // indirect
)

replace (
	_ => ./
	github.com/maybgit/glog => github.com/maybgit/glog v0.0.0-20220118085313-5fbe11e2f392
)
