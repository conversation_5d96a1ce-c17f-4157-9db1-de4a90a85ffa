package main

import (
	"_/proto/pc"
	"_/services"
	"_/tasks"
	"flag"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"os"
	"strings"
	"time"

	kit "github.com/tricobbler/rp-kit"

	"github.com/limitedlee/microservice/micro"
	"github.com/maybgit/glog"
	"google.golang.org/grpc/reflection"
)

var env string

func init() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh

	env = strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	if env == "staging" || env == "uat" || env == "" {
		kit.IsDebug = true
	}
}

func main() {
	go func() {
		err := http.ListenAndServe(":11113", nil)
		if err != nil {
			return
		}
	}() //pprof性能分析
	//日志命令行参数化处理，可以启用禁用控制台日志等，defer确认在程序退出时将所有缓冲日志写入es
	defer glog.Flush()
	flag.Parse()

	//初始化mysql和redis连接
	services.SetupDB()
	//关闭mysql和redis连接
	defer services.CloseDB()

	//初始化定时任务
	tasks.InitTask()

	microService := micro.MicService{}
	microService.NewServer()

	//商品服务注册
	pc.RegisterDcProductServer(microService.GrpcServer, &services.Product{})
	//渠道商品服务注册
	pc.RegisterDcChannelProductServer(microService.GrpcServer, &services.ChannelProduct{})
	// 引入task
	pc.RegisterDcTaskProductServer(microService.GrpcServer, &tasks.TaskProduct{})

	//服务反射，便于查看grpc的状态
	reflection.Register(microService.GrpcServer)
	fmt.Println("Running in a '\x1b[31m" + strings.ToUpper(env) + "\x1b[0m' environment")

	glog.Info("product-center 服务启动了...")
	//tasks.AutoDown7DaysNoStock(2)
	//tasks.SyncZlProductnew()
	microService.Start()
}
