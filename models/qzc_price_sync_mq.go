package models

import (
	"time"
)

type QzcPriceSyncMq struct {
	Id         int       `xorm:"not null pk autoincr comment('主键') INT(11)"`
	ChannelId  int       `xorm:"not null comment('渠道id') INT(11)"`
	Content    string    `xorm:"not null default '''' comment('推送内容') VARCHAR(100)"`
	IsPush     int       `xorm:"not null default 0 comment('是否推送 0：未推送 1：已推送') INT(11)"`
	CreateTime time.Time
	UpdateTime time.Time
}


type QzcMqContent struct {
	FinanceCode string
	StoreId     string
	ChannelId   int
	ProductId   int
	SkuId       int
	Price       int
}
