package models

type UpetSpecValue struct {
	SpValueId    int    `xorm:"not null pk autoincr comment('规格值id') INT(10)"`
	SpValueName  string `xorm:"not null comment('规格值名称') VARCHAR(100)"`
	SpId         int    `xorm:"not null comment('所属规格id') INT(10)"`
	GcId         int    `xorm:"not null comment('分类id') INT(10)"`
	StoreId      int    `xorm:"not null comment('店铺id') index INT(10)"`
	SpValueColor string `xorm:"default 'NULL' comment('规格颜色') VARCHAR(10)"`
	SpValueSort  int    `xorm:"not null comment('排序') TINYINT(1)"`
}
