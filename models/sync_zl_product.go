package models

type SyncZlProduct struct {
	Id         uint   `json:"id" xorm:"pk autoincr not null INT(10) 'id'"`
	ItemCode   string `json:"item_code" xorm:"not null default '' comment('子龙货号') VARCHAR(50) 'item_code'"`
	ZilongId   string `json:"zilong_id" xorm:"not null default '' comment('子龙门店id') VARCHAR(50) 'zilong_id'"`
	CanSell    int8   `json:"can_sell" xorm:"default 0 comment('1-变为可销 2-变为不可销') TINYINT(4) 'can_sell'"`
	DealStatus int8   `json:"deal_status" xorm:"not null default 0 comment('是否处理') TINYINT(4) 'deal_status'"`
}

type SyncZlProductExtend struct {
	SyncZlProduct `xorm:"extends"`
	FinanceCode   string `json:"finance_code"`
}
