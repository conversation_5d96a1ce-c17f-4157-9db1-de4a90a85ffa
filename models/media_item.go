package models

type MediaItem struct {
	ApicId     int32  `xorm:"not null pk autoincr comment('id') INT(10)"`
	ApicType   int32  `xorm:"not null default 1 comment('1图片 2视频') TINYINT(1)"`
	ApicName   string `xorm:"not null comment('图片/视频名称') VARCHAR(100)"`
	ApicTag    string `xorm:"default '''' comment('图片/视频标签') VARCHAR(255)"`
	AclassId   int32  `xorm:"not null comment('相册/视频库id') INT(10)"`
	ApicPath   string `xorm:"not null comment('图片/视频路径') VARCHAR(255)"`
	ApicSize   int32  `xorm:"not null comment('图片/视频大小') INT(10)"`
	ApicSpec   string `xorm:"not null comment('图片/视频规格') VARCHAR(100)"`
	StoreId    int32  `xorm:"not null comment('所属店铺id') INT(10)"`
	StoreName  string `xorm:"not null default '''' comment('店铺名称') VARCHAR(50)"`
	UploadTime int32  `xorm:"not null comment('上传时间') INT(10)"`
	ApicDomain string `xorm:"not null default '''' comment('域名') VARCHAR(100)"`
}
