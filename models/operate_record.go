package models

import "time"

type OperateRecord struct {
	Id            int32     `xorm:"not null pk autoincr INT(11)"`
	OperateType   int32     `xorm:"not null default 0 comment('操作类型：1移除商品  2编辑商品') tinyint(4)"`
	OperateDetail string    `xorm:"not null default '' comment('操作详情') varchar(256)"`
	CreateId      string    `xorm:"not null default '' comment('创建人id') VARCHAR(32)"`
	CreateName    string    `xorm:"not null default '' comment('创建人姓名') VARCHAR(64)"`
	CreateIp      string    `xorm:"not null default '' comment('创建人IP') VARCHAR(64)"`
	IpLocation    string    `xorm:"not null default '' comment('ip所属位置') VARCHAR(50)"`
	CreateTime    time.Time `xorm:"default 'NULL' comment('创建时间') DATETIME"`
}
