package models

import "encoding/json"

type (
	EsProduct struct {
		ProductName   string `json:"product_name"`   //商品名称
		ProductId     int32  `json:"product_id"`     //商品id
		FinanceCode   string `json:"finance_code"`   //门店id
		FinanceName   string `json:"finance_name"`   //门店名称
		UpTime        int32  `json:"up_time"`        //上架时间
		LocationPoint string `json:"location_point"` //坐标，纬度在前
	}

	EsStore struct {
		LocationPoint string        `json:"location_point"` //坐标，纬度在前
		LocationShape LocationShape `json:"location_shape"` //配送范围
		FinanceCode   string        `json:"financial_code"` //门店id
		Name          string        `json:"name"`           //门店名称
	}

	LocationShape struct {
		Type        string        `json:"type"`
		Coordinates [][][]float32 `json:"coordinates"` //配送范围坐标集合
	}

	EsJddjBrand struct {
		Id          int64  `json:"id"`          //品牌编号
		BrandName   string `json:"brandName"`   //品牌名称
		BrandStatus int8   `json:"brandStatus"` //品牌状态：-1:删除状态1：待审核,2：审核通过，3：驳回
	}

	GJSku struct {
		Id              string  `json:"id"`
		ChannelId       string  `json:"channel_id"`
		CategoryId      int64   `json:"category_id"`       //店内分类,对应京东的shopCategories
		ThirdCategoryId int64   `json:"third_category_id"` //第三方分类id
		BrandId         int64   `json:"brand_id"`
		Name            string  `json:"name"`
		BarCode         string  `json:"bar_code"`
		Pic             string  `json:"pic"`
		ContentPc       string  `json:"content_pc"`
		SkuId           string  `json:"sku_id"`
		WeightForUnit   float32 `json:"weight_for_unit"`
		MarketPrice     int64   `json:"market_price"` //取store_price
		SellingPoint    string  `json:"selling_point"`
		StoreMasterId   string  `json:"store_master_id"` //取channel_store_id
		ProductType     int64   `json:"product_type"`
	}

	ProductSnapshot struct {
		FinanceCode       string `json:"finance_code"`
		UpDownState       int    `json:"up_down_state"`
		ChannelCategoryId string `json:"channel_category_id"`
		ProductId         int32  `json:"product_id"`
		SkuId             int32  `json:"sku_id"`
		JsonData          string `json:"json_data"`
		UpdateDate        string `json:"update_date"`
	}

	SimplePromotion struct {
		Id         int32  `json:"Id"`
		Types      int32  `json:"Types"`
		ShopId     string `json:"shop_id"`
		UpdateDate string `json:"update_date"`
	}

	NearbyStoreInfo struct {
		Store     ProductShopDetail  `json:"store"`     //门店信息
		Promotion []*PromotionDetail `json:"promotion"` //优惠信息
		Product   []*ProductDetail   `json:"product"`   //商品信息
	}
	ProductShopDetail struct {
		FinanceCode       string             `json:"finance_code"`        //门店财务编码
		ShopName          string             `json:"shop_name"`           //门店名称
		ShopImage         string             `json:"shop_image"`          //门店图片
		ShopMobile        string             `json:"shop_mobile"`         //门店电话
		Distance          string             `json:"distance"`            //距离
		ShippingFee       string             `json:"shipping_fee"`        //运费
		MinCharge         string             `json:"min_charge"`          //起送价
		ServiceStatus     int32              `json:"service_status"`      //服务状态，1正常，2闭店，3打烊
		ServiceStatusText string             `json:"service_status_text"` //服务状态描述
		AdvanceOrder      int32              `json:"advance_order"`       //是否可预定，0否1是
		AdvanceDate       []int32            `json:"advance_date"`        //接受预定日期
		ServiceDate       []int32            `json:"service_date"`        //营业日期
		ServiceOpenTime   int32              `json:"service_open_time"`   //营业开始时间
		ServiceCloseTime  int32              `json:"service_close_time"`  //营业结束时间
		Product           []*ProductDetail   `json:"product"`             //商品信息
		Promotion         []*PromotionDetail `json:"promotion"`           //优惠活动信息
		IsSelfLifting     bool               `json:"is_self_lifting"`     //是否支持自提(true:支持 false:不支持)
		StockUpTime       int32              `json:"stock_up_time"`       //备货时长 单位分钟 不能小于0
		ShopAddress       string             `json:"shop_address"`        //门店地址
		Notice            string             `json:"notice"`              //店铺公告
		PointX            string             `json:"pointx"`              //经度
		PointY            string             `json:"pointy"`              //维度
	}
	ProductDetail struct {
		Id            int32              `json:"id"`             //商品id
		CategoryId    int32              `json:"category_id"`    //商品分类id
		Name          string             `json:"name"`           //商品名称
		Pic           string             `json:"pic"`            //商品图片
		Price         string             `json:"price"`          //实际价格
		RetailPrice   string             `json:"retail_price"`   //建议零售价格
		SkuId         int32              `json:"sku_id"`         //商品skuid
		SellingPoint  string             `json:"selling_point"`  //商品卖点
		ContentMobile string             `json:"content_mobile"` //商品手机端详情内容
		Weight        int32              `json:"weight"`         //商品重量，单位g
		Spec          []Spec             `json:"spec"`           //商品规格
		ShopDetail    *ProductShopDetail `json:"shop_detail"`    //商品所属门店信息
		IsPromotion   int8               `json:"is_promotion"`   //是否参与活动，0否1是
		Promotion     []*PromotionDetail `json:"promotion"`      //优惠活动信息
	}
	//新建商品库商品请求内容
	ChannelProductRequest struct {
		//商品主表
		Product *ChannelProduct `protobuf:"bytes,1,opt,name=product,proto3" json:"product"`
		//SKU信息
		SkuInfo []*SkuInfo `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo,proto3" json:"sku_info"`
		//财务编码
		FinanceCode string `protobuf:"bytes,4,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
		//商品标签
		Tags string `protobuf:"bytes,5,opt,name=tags,proto3" json:"tags"`
	}
	//商品库新增商品的SKU信息
	SkuInfo struct {
		//建议价格
		RetailPrice int32 `protobuf:"varint,3,opt,name=retail_price,json=retailPrice,proto3" json:"retail_price"`
		//SKU ID
		SkuId int32 `protobuf:"varint,4,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
		//商品ID
		ProductId int32 `protobuf:"varint,5,opt,name=product_id,json=productId,proto3" json:"product_id"`
		//市场价
		MarketPrice int32 `protobuf:"varint,6,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
		//商品条码
		BarCode string `protobuf:"bytes,8,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
		//渠道ID
		ChannelId int32 `protobuf:"varint,9,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
		//是否可用
		IsUse int32 `protobuf:"varint,10,opt,name=is_use,json=isUse,proto3" json:"is_use"`
		//重量
		WeightForUnit float64 `protobuf:"fixed64,11,opt,name=weight_for_unit,json=weightForUnit,proto3" json:"weight_for_unit"`
		//重量单位
		WeightUnit string `protobuf:"bytes,12,opt,name=weight_unit,json=weightUnit,proto3" json:"weight_unit"`
		//最小购买数量
		MinOrderCount int32 `protobuf:"varint,13,opt,name=min_order_count,json=minOrderCount,proto3" json:"min_order_count"`
		//价格单位
		PriceUnit string `protobuf:"bytes,14,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit"`
		//前置仓价格
		PreposePrice int32 `protobuf:"varint,15,opt,name=prepose_price,json=preposePrice,proto3" json:"prepose_price"`
		//门店仓价格
		StorePrice int32 `protobuf:"varint,16,opt,name=store_price,json=storePrice,proto3" json:"store_price"`
	}

	// 同步价格异常
	SyncPriceCheck struct {
		// 商品信息
		ProductId int32 `json:"product_id"`
		// 信息
		Message string `json:"message"`
	}

	//{
	//"msg":"[{\"msg\":\"对应商品不存在\",\"app_food_code\":\"10471762\"}]",
	//"data":"ok"
	//}

	MsgMT struct {
		Msg         string `json:"msg"`
		AppFoodCode string `json:"app_food_code"`
	}

	FailSkuProduct struct {
		ProductId string
		SkuId     string
	}

	TaskDetail struct {
		ChannelId  int
		Error      string // 有Error的直接取error没有的取数量
		AllNum     int    // 总数数量 这里是总共需要执行的门店或者说是appid （jd 的数量加起来并部相等，因为跟新的时候可能有些渠道不执行// ）
		FailNum    int    // 失败数量
		SuccessNum int    // 成功的数量
	}

	ListData struct {
		CustomSkuId string `json:"custom_sku_id"`
		ErrorMsg    string `json:"error_msg"`
	}

	MsgELE struct {
		FailedList  []ListData `json:"failed_list"`
		SuccessList []ListData `json:"success_list"`
	}

	StoreRelation struct {
		FinanceCode    string `json:"finance_code"`
		ChannelId      int64  `json:"channel_id"`
		ChannelStoreId string `json:"channel_store_id"`
	}
)

func (s *NearbyStoreInfo) MarshalBinary() ([]byte, error) {
	return json.Marshal(s)
}

func (s *NearbyStoreInfo) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, s)
}

type RecordData struct {
	Name      string `json:"name"`
	ProductId int    `json:"product_id"`
	SkuId     int    `json:"sku_id"`
	A8        string `json:"a8"`
	ZiLong    string `json:"zi_long"`
}
