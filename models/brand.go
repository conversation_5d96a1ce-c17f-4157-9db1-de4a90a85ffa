package models

import (
	"time"
)

type Brand struct {
	Id              int32     `xorm:"not null pk autoincr INT(11)"`
	Name            string    `xorm:"default 'NULL' comment('品牌名称') VARCHAR(100)"`
	Sort            int32     `xorm:"default NULL comment('排序') INT(11)"`
	Logo            string    `xorm:"default 'NULL' comment('logo图片') VARCHAR(1000)"`
	Description     string    `xorm:"default 'NULL' comment('品牌描述') VARCHAR(1000)"`
	CreateDate      time.Time `xorm:"default 'current_timestamp()' DATETIME"`
	IsRecommend     int32     `xorm:"default NULL comment('是否推荐') TINYINT(1)"`
	ShowType        int32     `xorm:"default NULL comment('展现形式 1-图片，2-文字') INT(11)"`
	Initial         string    `xorm:"default 'NULL' comment('首字母') VARCHAR(1)"`
	CompanyId       int32     `xorm:"default NULL INT(36)"`
	BrandCategoryId int32     `xorm:"default NULL comment('所属分类id') INT(11)"`
}
