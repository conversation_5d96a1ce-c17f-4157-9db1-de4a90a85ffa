package models

type WxVideoCategory struct {
	Id                       int    `xorm:"not null pk autoincr comment('主键') INT(11)"`
	ThirdCatId               int    `xorm:"not null default 0 comment('三级类目id') INT(11)"`
	ThirdCatName             string `xorm:"not null default '''' comment('三级类目名称') VARCHAR(32)"`
	Qualification            string `xorm:"not null default '''' comment('类目资质') VARCHAR(1000)"`
	QualificationType        int    `xorm:"not null default 0 comment('类目资质类型,0:不需要,1:必填,2:选填') INT(11)"`
	ProductQualification     string `xorm:"not null default '''' comment('商品资质') VARCHAR(100)"`
	ProductQualificationType int    `xorm:"not null default 0 comment('商品资质类型,0:不需要,1:必填,2:选填') INT(11)"`
	SecondCatId              int    `xorm:"not null default 0 comment('二级类目id') INT(11)"`
	SecondCatName            string `xorm:"not null default '''' comment('二级类目名称') VARCHAR(32)"`
	FirstCatId               int    `xorm:"not null default 0 comment('一级类目id') INT(11)"`
	FirstCatName             string `xorm:"not null default '''' comment('一级类目名称') VARCHAR(32)"`
	IsQualification          int    `xorm:"not null default 0 comment('是否申请资质; 0否,1是') INT(11)"`
	QualificationUrl         string `xorm:"not null default '''' comment('资质图片url(多个以逗号分隔)') VARCHAR(1000)"`
}
