package models

import (
	"time"
)

type QzcPriceSync struct {
	Id             int       `xorm:"not null pk autoincr comment('主键') INT(11)"`
	WarehouseId    int       `xorm:"not null default 0 comment('仓库ID') index INT(11)"`
	WarehouseName  string    `xorm:"default 'NULL' comment('仓库名称') VARCHAR(50)"`
	ThirdSkuId     string    `xorm:"not null default '''' comment('货号') index VARCHAR(36)"`
	SkuId          int       `xorm:"default NULL comment('SKUID') index INT(11)"`
	Price          int       `xorm:"default NULL comment('价格') INT(11)"`
	CreateTime     time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME created"`
	CreateUserName string    `xorm:"default 'NULL' comment('创建人') VARCHAR(128)"`
	UpdateUserName string    `xorm:"default 'NULL' comment('更新人') VARCHAR(128)"`
	LastTime       time.Time `xorm:"default 'current_timestamp()' comment('最后修改时间') DATETIME updated"`
	ProductId      int       `xorm:"default NULL comment('商品ID') index INT(11)"`
}
