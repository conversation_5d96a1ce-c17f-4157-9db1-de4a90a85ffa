package models

import (
	"_/proto/pc"
	"fmt"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/spf13/cast"
)

type ChannelProduct struct {
	Id                   int32     `xorm:"not null pk comment('商品id') INT(11)"`
	ChannelId            int32     `xorm:"not null pk comment('渠道id') INT(11)"`
	CategoryId           int32     `xorm:"default NULL comment('分类id') INT(11)"`
	BrandId              int32     `xorm:"default NULL comment('品牌id') INT(11)"`
	Name                 string    `xorm:"default 'NULL' comment('商品名称') VARCHAR(255)"`
	ShortName            string    `xorm:"default 'NULL' comment('商品短标题') VARCHAR(255)"`
	Code                 string    `xorm:"default 'NULL' comment('商品编号') VARCHAR(36)"`
	BarCode              string    `xorm:"default 'NULL' comment('商品条码') VARCHAR(36)"`
	CreateDate           time.Time `xorm:"default comment('商品添加日期') DATETIME created"`
	UpdateDate           time.Time `xorm:"default 'NULL' comment('商品最后更新日期') DATETIME updated"`
	IsDel                int32     `xorm:"default 0 comment('是否删除') INT(11)"`
	IsGroup              int32     `xorm:"default 0 comment('是否为组合商品') INT(11)"`
	Pic                  string    `xorm:"default 'NULL' comment('商品图片（多图）') VARCHAR(1000)"`
	SellingPoint         string    `xorm:"default 'NULL' comment('商品卖点') VARCHAR(200)"`
	Video                string    `xorm:"default 'NULL' comment('商品视频地址') VARCHAR(500)"`
	ContentPc            string    `xorm:"default 'NULL' comment('电脑端详情内容') TEXT"`
	ContentMobile        string    `xorm:"default 'NULL' comment('手机端详情内容') TEXT"`
	IsDiscount           int32     `xorm:"default NULL comment('是否参与优惠折扣') INT(11)"`
	ProductType          int32     `xorm:"default NULL comment('商品类别（1-实物商品，2-虚拟商品）') INT(11)"`
	IsUse                int32     `xorm:"default 0 comment('商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）') INT(11)"`
	DelDate              time.Time `xorm:"default 'NULL' comment('删除时间') DATETIME"`
	ChannelCategoryId    int32     `xorm:"not null default 0 comment('渠道的分类id') INT(11)"`
	ChannelCategoryName  string    `xorm:"default 'NULL' comment('渠道的分类名称') VARCHAR(100)"`
	CategoryName         string    `xorm:"default 'NULL' comment('分类名称') VARCHAR(100)"`
	ChannelName          string    `xorm:"default 'NULL' comment('渠道名称（美团，饿了么，阿闻，京东）') VARCHAR(50)"`
	LastEditUser         string    `xorm:"default 'NULL' comment('最后编辑用户') VARCHAR(50)"`
	ChannelTagId         int32     `xorm:"default 'NULL' comment('渠道商品类目id') INT(11)"`
	IsRecommend          int32     `xorm:"not null default 0 comment('是否是推荐商品,1是0否') TINYINT(1)"`
	UseRange             string    `xorm:"default 'NULL' comment('商品应用范围（1电商，2前置仓，3门店仓）,字符串拼接') VARCHAR(100)"`
	GroupType            int32     `xorm:"default NULL comment('组合类型(1:实实组合,2:虚虚组合,3.虚实组合)') INT(11)"`
	TermType             int32     `xorm:"default NULL comment('只有虚拟商品才有值(1.有效期至多少  2.有效期天数)') INT(11)"`
	TermValue            int32     `xorm:"default NULL comment('如果term_type=1 存：时间戳  如果term_type=2 存多少天') INT(11)"`
	VirtualInvalidRefund int32     `xorm:"default NULL comment('是否支持过期退款 1：是 0：否') INT(11)"`
}

var channelProductTypeMap = map[int32]string{
	1: "实物商品", 2: "虚拟商品", 3: "组合商品",
}

var channelProductGroupTypeMap = map[int32]string{
	1: "实实组合", 2: "虚虚组合", 3: "虚实组合",
}

// GetProductTypeText 商品类型文本描述
func (c *ChannelProduct) GetProductTypeText() string {
	if c.ProductType == 3 {
		return channelProductGroupTypeMap[c.GroupType]
	}
	return channelProductTypeMap[c.ProductType]
}
func (c *ChannelProductException) GetProductTypeText() string {
	if c.ProductType == 3 {
		return channelProductGroupTypeMap[c.GroupType]
	}
	return channelProductTypeMap[c.ProductType]
}

// QueryChannelProductListByReq 列表查询
func QueryChannelProductListByReq(db *xorm.Engine, in *pc.ChannelProductListReq) (query *xorm.Session, err error) {
	query = db.Table("channel_product").Alias("a").Where("a.channel_id=?", in.ChannelId)

	if in.UpDownState == 1 || in.UpDownState == 0 || in.UpDownState == 3 {
		andQuery := "a.id "
		if in.UpDownState == 0 || in.UpDownState == 3 {
			andQuery += "not "
		}
		andArgs := []interface{}{in.ChannelId}
		andQuery += "in (SELECT distinct product_id FROM channel_store_product WHERE channel_id=? and up_down_state = 1 "
		if len(in.FinanceCode) > 0 {
			andQuery += "and finance_code = ?"
			andArgs = append(andArgs, in.FinanceCode)

		}
		andQuery += ")"
		query.And(andQuery, andArgs...)
	}
	//查询药品的数据
	if in.IsDrugs == 1 {
		query.And("a.id in (select id from  product where is_drugs=1)")
	}
	//美团可能要查询力荐
	if in.ChannelId == 2 && len(in.FinanceCode) > 0 {
		if in.IsRecommend == 1 {
			query.And("a.id in (select distinct product_id FROM channel_product_snapshot where channel_id=2 and finance_code=? and JSON_EXTRACT(json_data,'$.product.is_recommend')=1)", in.FinanceCode)
		}
		//if in.MinOrderCount > 0 && in.MinOrderCount > 0 {
		//	if in.MinOrderCount == in.MaxOrderCount {
		//		query.And("a.id in (select distinct product_id FROM channel_product_snapshot where channel_id=2 and finance_code=? and  JSON_EXTRACT(json_data,'$.sku_info[0].min_order_count') =?)", in.FinanceCode, in.MinOrderCount)
		//	} else {
		//		query.And("a.id in (select distinct product_id FROM channel_product_snapshot where channel_id=2 and finance_code=? and  JSON_EXTRACT(json_data,'$.sku_info[0].min_order_count') >=? and JSON_EXTRACT(json_data,'$.sku_info[0].min_order_count')<=?)", in.FinanceCode, in.MinOrderCount, in.MaxOrderCount)
		//	}
		//}
	}

	if (in.UpDownState == 2 || in.UpDownState == 3) && len(in.FinanceCode) > 0 { // 有库存筛选
		// 阿闻渠道 自提有库存也算有库存
		stockChannelId := cast.ToString(in.ChannelId)
		if in.ChannelId == 1 {
			stockChannelId += ",10"
		}
		query.And(fmt.Sprintf(`a.id in (select distinct s.product_id from dc_dispatch.warehouse_relation_shop r
inner join dc_order.warehouse_goods wg on wg.warehouse_id = r.warehouse_id
inner join dc_product.sku s on s.id = wg.goodsid
where r.channel_id in(%s) and r.shop_id = ? and (wg.stock - ifnull(
(select sum(stock) as freeze_stock from dc_order.order_freeze_stock f where f.sku_id = wg.goodsid and f.warehouse_id = wg.warehouse_id),0))>0
)`, stockChannelId), in.FinanceCode)
	}

	// 侧边栏分类过滤
	if in.CategoryId > 0 && len(in.FinanceCode) > 0 {
		var categoryIds []string
		if err = db.Table("channel_category").Select("id").Where("channel_id = 1 and (id = ? or parent_id = ?)",
			in.CategoryId, in.CategoryId).Find(&categoryIds); err != nil {
			return
		}
		if len(categoryIds) == 0 {
			query.And("0")
			return
		}

		query.And(fmt.Sprintf(`ifnull(
		(select ps.channel_category_id from channel_product_snapshot ps where ps.product_id = a.id and ps.channel_id = a.channel_id
		and ps.finance_code = '%s' and ps.channel_category_id > 0 limit 1),a.category_id) in (%s)`, in.FinanceCode, strings.Join(categoryIds, ",")))
	}

	//美团要查询起购数
	if in.ChannelId == 2 && len(in.FinanceCode) > 0 {
		if in.MinOrderCount > 0 && in.MinOrderCount > 0 {
			//if in.MinOrderCount == in.MaxOrderCount {
			//	query.And("a.id in (select distinct product_id FROM channel_product_snapshot where channel_id=2 and finance_code=? and  JSON_EXTRACT(json_data,'$.sku_info[0].min_order_count') =?) OR EXISTS (SELECT 1  FROM channel_sku csku WHERE csku.min_order_count>0 AND csku.min_order_count=? and csku.product_id =a.id)", in.FinanceCode, in.MinOrderCount, in.MinOrderCount)
			//} else {
			query.And("a.id in (SELECT s.product_id FROM channel_product  cp1   LEFT JOIN (SELECT * FROM channel_sku WHERE channel_id=2 GROUP BY product_id ) s  ON cp1.channel_id=s.channel_id AND cp1.id=s.product_id LEFT JOIN channel_product_snapshot cps ON s.product_id = cps.product_id AND s.channel_id = cps.channel_id and cps.finance_code = ?  WHERE  cp1.channel_id=2 AND IF(cps.id IS NULL, s.min_order_count >= ? AND s.min_order_count <= ?, JSON_EXTRACT(json_data, '$.sku_info[0].min_order_count') >= ? AND  JSON_EXTRACT(json_data, '$.sku_info[0].min_order_count') <= ? ))", in.FinanceCode, in.MinOrderCount, in.MaxOrderCount, in.MinOrderCount, in.MaxOrderCount)
			//}
		}
	}

	if in.BrandId != 0 {
		query.And("a.brand_id=?", in.BrandId)
	}
	if in.ProductType != 0 {
		switch in.ProductType {
		case 31: // 实实组合
			query.And("a.product_type = 3 and a.group_type = 1")
		case 33: // 实虚组合
			query.And("a.product_type = 3 and a.group_type = 3")
		default:
			query.And("a.product_type=?", in.ProductType)
		}
	}

	in.Where = strings.TrimSpace(in.Where)

	if len(in.Where) > 0 {
		switch in.WhereType {
		case "":
			query.And("a.name like ? OR a.id = ? OR a.code = ? OR a.bar_code = ?", "%"+in.Where+"%", in.Where, in.Where, in.Where)
		case "name":
			query.And("a."+in.WhereType+" like ?", "%"+in.Where+"%")
		case "code", "id":
			query.And("a."+in.WhereType+"=?", in.Where)
		case "bar_code":
			query.And("a.id in (select product_id from channel_sku where channel_id = ? and bar_code = ?)", in.ChannelId, in.Where)
		case "third_spu_sku_id":
			query.And("a.id in (select product_id from channel_sku_third where channel_id= ? and third_spu_sku_id LIKE ?)", in.ChannelId, "%"+in.Where+"%")
		case "sku_id":
			query.And("a.id in (select product_id from sku where id = ?)", in.Where)
		}
	}

	query.OrderBy("a.id desc")

	return
}

// 异常的第三方货号和ERPID
type ExceptionThird struct {
	ThirdSkuId string `xorm:"default 'NULL' comment('第三方货号') VARCHAR(255)"`
	ErpId      int32  `xorm:"default NULL comment('ERP仓库ID') INT(11)"`
	SkuId      int32  `xorm:"default NULL comment('ERP仓库ID') INT(11)"`
}

type ChannelProductException struct {
	Id          int32     `xorm:"not null pk comment('商品id') INT(11)"`
	ChannelId   int32     `xorm:"not null pk comment('渠道id') INT(11)"`
	Name        string    `xorm:"default 'NULL' comment('商品名称') VARCHAR(255)"`
	ShortName   string    `xorm:"default 'NULL' comment('商品短标题') VARCHAR(255)"`
	UpdateDate  time.Time `xorm:"default 'NULL' comment('商品最后更新日期') DATETIME updated"`
	Pic         string    `xorm:"default 'NULL' comment('商品图片（多图）') VARCHAR(1000)"`
	ProductType int32     `xorm:"default NULL comment('商品类别（1-实物商品，2-虚拟商品）') INT(11)"`
	GroupType   int32     `xorm:"default NULL comment('组合类型(1:实实组合,2:虚虚组合,3.虚实组合)') INT(11)"`
	ThirdSkuId  string    `xorm:"default 'NULL' comment('第三方货号') VARCHAR(255)"`
	ErpId       int32     `xorm:"default NULL comment('ERP仓库ID') INT(11)"`
	SkuId       int32     `xorm:"default NULL comment('SKUID') INT(11)"`
}

type ChannelProductSpecialtyModel struct {
	// 商品Id列表-必须
	ProductIds []string `json:"product_ids"`
	// 财务编码
	FinanceCodes []string `json:"finance_codes"`

	//是否为“力荐”商品，字段取值范围：0-否， 1-是。单个门店最多支持设置100个力荐商品。
	IsSpecialty int `json:"is_specialty"`

	// 商品sku的最小购买量
	MinOrderCount int `json:"min_order_count"`

	ChannelId int `json:"channel_id"`
}
