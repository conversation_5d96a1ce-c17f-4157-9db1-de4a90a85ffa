package models

type MediaClass struct {
	AclassId    int32  `xorm:"not null pk autoincr comment('相册/视频库id') INT(10)"`
	ApicType    int32  `xorm:"not null default 0 comment('1图片 2视频') TINYINT(4)"`
	AclassName  string `xorm:"not null comment('相册/视频库名称') VARCHAR(100)"`
	StoreId     int32  `xorm:"not null comment('所属店铺id') INT(10)"`
	StoreName   string `xorm:"not null default '''' comment('店铺名称') VARCHAR(50)"`
	AclassSort  int32  `xorm:"not null comment('排序') TINYINT(3)"`
	AclassCover string `xorm:"not null comment('相册/视频库封面') VARCHAR(255)"`
	UploadTime  int32  `xorm:"not null comment('上传时间') INT(10)"`
	IsDefault   int32  `xorm:"not null default 0 comment('是否为默认相册,1代表默认') TINYINT(1)"`
}
