package models

import "time"

type ImportLog struct {
	Id            int32     `xorm:"not null pk autoincr INT(11)"`
	Type          int32     `xorm:"not null default 0 comment('类型 0自提点 1搜索词库') TINYINT(3)"`
	Status        int32     `xorm:"not null default 0 comment('状态:0-执行中 1-已完成') TINYINT(3)"`
	Num           int32     `xorm:"not null default 0 comment('导入数量') TINYINT(3)"`
	UserNo        string    `xorm:"comment('操作人') VARCHAR(255)"`
	Result        string    `xorm:"comment('结果') VARCHAR(512)"`
	ResultUrl     string    `xorm:"comment('结果excel链接') VARCHAR(512)"`
	ImportUrl     string    `xorm:"comment('导入列表先储存') VARCHAR(512)"`
	StoreId       int32     `xorm:"not null default 1 comment('店铺id') INT(11)"` //电商主体id
	CreatedAt     time.Time `xorm:"not null comment('导入时间') DATETIME"`
	EffectiveTime time.Time `json:"effective_time" xorm:"default 'null' comment('有效时间') DATETIME 'effective_time'"`
}
