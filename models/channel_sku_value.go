package models

type ChannelSkuValue struct {
	Id          int32  `xorm:"not null pk autoincr INT(11)"`
	SpecId      int32  `xorm:"default NULL comment('规格ID') INT(11)"`
	SpecValueId int32  `xorm:"default NULL comment('规格值ID') INT(11)"`
	SkuId       int32  `xorm:"default NULL comment('SKU ID') INT(11)"`
	ProductId   int32  `xorm:"default NULL comment('商品ID') INT(11)"`
	Pic         string `xorm:"default 'NULL' comment('规格属性图片') VARCHAR(500)"`
	Sort        int32  `xorm:"default NULL comment('排序') INT(11)"`
	ChannelId   int32  `xorm:"default NULL comment('渠道id') INT(11)"`
}
