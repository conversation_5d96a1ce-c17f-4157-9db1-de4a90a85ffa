package models

import (
	"time"
)

type TaskList struct {
	Id               int32     `xorm:"not null pk autoincr comment('任务id') INT(11)"`
	ChannelId        int32     `xorm:"not null default 0 comment('渠道id(0-所有平台,1-阿闻，2-美团，3-饿了么)') INT(11)"`
	TaskContent      int32     `xorm:"not null default 0 comment('任务内容:1:批量新建;2:批量更新') TINYINT(1)"`
	TaskStatus       int32     `xorm:"not null default 1 comment('任务状态:1:调度中;2:进行中;3:已完成;4:已取消') TINYINT(1)"`
	TaskDetail       string    `xorm:"not null default '''' comment('任务详情') VARCHAR(1000)"`
	OperationFileUrl string    `xorm:"not null default '''' comment('操作文件路径') TEXT"`
	RequestHeader    string    `xorm:"not null default '''' comment('操作请求的token值，类似userinfo') VARCHAR(255)"`
	ResulteFileUrl   string    `xorm:"not null default '''' comment('操作结果文件路径') VARCHAR(255)"`
	Status           int32     `xorm:"not null default 1 comment('状态:1:正常;2:冻结;') TINYINT(1)"`
	ModifyId         string    `xorm:"not null default ''0'' comment('修改人id') VARCHAR(100)"`
	ModifyTime       time.Time `xorm:"default 'NULL' comment('修改时间') DATETIME"`
	CreateId         string    `xorm:"not null default ''0'' comment('创建人id') VARCHAR(100)"`
	CreateTime       time.Time `xorm:"default 'NULL' comment('创建时间') DATETIME"`
	CreateName       string    `xorm:"not null default '''' comment('创建人姓名') VARCHAR(100)"`
	CreateMobile     string    `xorm:"not null default '''' comment('创建人手机号') VARCHAR(100)"`
	CreateIp         string    `xorm:"not null default '''' comment('创建人id') VARCHAR(255)"`
	IpLocation       string    `xorm:"not null default '''' comment('ip所属位置') VARCHAR(100)"`
	SuccessNum       int32     `xorm:"not null default 0 comment('成功数量)') INT(11)"`
	FailNum          int32     `xorm:"not null default 0 comment('失败数量)') INT(11)"`
	ExtendedData     string    `xorm:"not null default '''' comment('任务名称扩展字段') TEXT"`
	ShopNum          int32     `xorm:"not null default 0 comment('操作门店数量') INT(11)"`
	Category         int32     `xorm:"not null default 0 comment('仓库类型 4-前置仓 5-虚拟前置仓') INT(11)"`
	ContextData      string    `xorm:"default 'NULL' comment('上下文数据') TEXT"`
}
