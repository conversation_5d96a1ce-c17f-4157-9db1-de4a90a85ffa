package models

type R1Sku struct {
	/**
	 * 物料编码
	 */
	SkuNo string `json:"sku_no"`
	/**
	 * 物料名称
	 */
	SkuName string `json:"sku_name"`
	/**
	 * 品牌id
	 */
	BrandId int `json:"brand_id"`
	/**
	 * 品牌名称
	 */
	BrandName string `json:"brand_name"`
	/*
	 * 规格型号
	 */
	SpecValue string `json:"spec_value"`
	/**
	 * 条码
	 */
	Barcode string `json:"barcode"`
	/**
	 * 单位名称
	 */
	UnitName string `json:"unit_name"`
	/**
	 * 净重
	 */
	NetWeight float64 `json:"net_weight"`
	/**
	 * 重量单位d
	 */
	WeightUnitId int `json:"weight_unit_id"`
	/**
	 * 重量单位名称
	 */
	WeightUnitName string `json:"weight_unit_name"`
	/**
	 * 体积
	 */
	SkuVolume float64 `json:"sku_volume"`
	/**
	 * 体积单位id
	 */
	VolumeUnitId int `json:"volume_unit_id"`
	/**
	 * 体积单位名称
	 */
	VolumeUnitName string `json:"volume_unit_name"`
	/**
	 * 分类名称
	 */
	CategoryName string `json:"category_name"`
	/**
	 * 分类编码
	 */
	CategoryCode string `json:"category_code"`
	/**
	 * 分类id
	 */
	CategoryId int `json:"category_id"`
	// 启用停用字段
	EnabledSku int `json:"enabled_sku"`
	// 价格price 单位默认是元
	Price string `json:"price"`
	// 图片 英文逗号分割
	Pic string `json:"pic"`
	// 组织code
	OrgCode string `json:"org_code"`
	// 组织名称
	OrgName string `json:"org_name"`
	//组织id、
	OrgID int `json:"org_id"`
}

type LogData struct {
	Data *R1Sku `json:"data"`
	Msg  string `json:"msg"`
}

type AddUpOrDownTaskDTO struct {
	// 门店信息单个门店
	FinanceCode string `json:"finance_code"`
	// 勾选的商品信息，多个商品用逗号分割
	ProductIds []int32 `json:"product_ids"`
	ChannelId  int32   `json:"channel_id"`
}

type UpProduct struct {
	Id          int    `json:"id"`
	ChannelId   int    `json:"channel_id"`
	FinanceCode string `json:"finance_code"`
	ProductId   int    `json:"product_id"`
	SkuId       int    `json:"sku_id"`
}

type GoodsStock struct {
	Goodsid     int `json:"goodsid"`
	Stock       int `json:"stock"`
	WarehouseId int `json:"warehouse_id"`
}

type MoveProductData struct {
	//1：阿闻渠道（本地） 2.美团渠道（线上）3.饿了么渠道（线上） ）4.京东渠道（线上）
	// 5：阿闻管家（本地） 6.美团渠道（本地） 7.饿了么渠道（本地）  8.京东渠道（本地 9：互联网医疗本地
	ChannelId    int
	CategoryId   int
	CategoryName string
	// 需要移动的商品数
	AllNum int
	// 失败的商品数
	FailNum int
	// 成功的商品数
	SuccessNum int
	// 对应的id(skuid或者productId)
	Ids []string
	//类型名称
	MoveName string
}

type SanpCategory struct {
	Id          int    `json:"id"`
	FinanceCode string `json:"finance_code"`
	ProductId   int    `json:"product_id"`
	CategoryId  int    `json:"category_id"`
}

type InitResErrListMsg struct {
	AppFoodCode string `json:"app_food_code"`
	ErrorMsg    string `json:"error_msg"`
}
