package models

import "time"

type RecordChannelProduct struct {
	Id          int       `xorm:"not null pk autoincr comment('id') INT(11)"`
	ProductId   int       `xorm:"not null comment('商品id') INT(11)"`
	ChannelId   int64     `xorm:"not null comment('渠道id') BIGINT(20)"`
	SkuId       int       `xorm:"not null comment('sku_id') INT(11)"`
	ProductName string    `xorm:"default '''' comment('商品名称') VARCHAR(100)"`
	Zilong      string    `xorm:"default '''' comment('子龙货号') VARCHAR(30)"`
	A8          string    `xorm:"default '''' comment('a8货号') VARCHAR(30)"`
	RecordType  int64     `xorm:"not null comment('记录类型(1删除商品2上架(包括了切仓上架)3批量上架4下架（包括了切仓下架） 5批量下架6自动上架7自动下架)') BIGINT(20)"`
	RecordTime  time.Time `xorm:"not null comment('操作时间') DATETIME"`
	FinanceCode string    `xorm:"not null comment('门店财务编码') VARCHAR(30)"`
	UserNo      string    `xorm:"default 'NULL' comment('用户no') VARCHAR(100)"`
	UserName    string    `xorm:"default 'NULL' comment('用户name') VARCHAR(30)"`
	CreateDate  time.Time `xorm:"default 'current_timestamp()' comment('创建日期') DATETIME"`
	UpdateDate  time.Time `xorm:"default 'current_timestamp()' comment('更新日期') DATETIME"`
}
