package models

type GjSkuThird struct {
	Id            int    `xorm:"not null pk INT(11)"`
	ProductId     int    `xorm:"default NULL comment('商品ID') index INT(11)"`
	SkuId         int    `xorm:"not null comment('商品库SKU ID') INT(11)"`
	ThirdSpuId    string `xorm:"default 'NULL' comment('第三方SPUID') VARCHAR(36)"`
	ThirdSkuId    string `xorm:"default 'NULL' comment('第三方SKUID') index VARCHAR(36)"`
	ThirdSpuSkuId string `xorm:"default 'NULL' comment('拼接的货号，作唯一键') unique VARCHAR(73)"`
	ErpId         int    `xorm:"default NULL comment('ERP仓库ID') INT(11)"`
}
