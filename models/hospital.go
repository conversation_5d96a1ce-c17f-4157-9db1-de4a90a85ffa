package models

type Hospital struct {
	Id                  int    //系统id
	ClinicId            string //对应的组织机构ID
	ParentId            int    //对应的父级组织机构id
	ClinicName          string //医院名称
	ClinicShortname     string //医院简称
	Brand               string //品牌名称
	BrandId             int    //品牌名称id
	SubBrand            string //子品牌
	BrandCode           string //品牌编码（财务编码）
	ClinicStatus        int    //医院状态基础信息_医院状态：0未知，1营业，2暂停，3闭店，4交接，5筹备，99删除
	ClinicType          string //医院类型
	CreateTime          string //创院时间
	Province            string //地址：省份
	City                string //地址：城市
	Address             string //地址：地址
	Dean                string //院长
	DeanNumber          string //院长电话
	InnerContactor      string //第二联系人|运营经理
	InnerContactorPhone string //第二联系人|运营经理电话
	HospitalPhone       string //前台电话
	HasMedcine          int    //是否有医疗业务：1是
	HasMeirong          int    //是否有美容业务：1是
	HasLingshou         int    //是否有零售业务：1是
	System              string //关联ID_HIS系统    单选：小暖/林特/迅德/汉思/其他
	SystemId            int    //小暖系统内部ID
	BusinessLicenseName string //营业执照名称
	Longitude           string //经度
	Latitude            string //纬度
	MeituanId           string //美团id
	StructOuterCode     string //组织机构编码（财务编码）
	AddTime             string //添加时间
	LastModify          string //最后修改时间
}

type HospitalResponse struct {
	Code    int                  `json:"code"`
	Status  int                  `json:"status"`
	Message string               `json:"message"`
	Error   string               `json:"error"`
	Data    HospitalDataResponse `json:"data"`
}

type HospitalDataResponse struct {
	List []Hospital
}
