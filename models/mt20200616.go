package models

type Mt20200616 struct {
	SkuId       string `xorm:"default 'NULL' VARCHAR(255)"`
	SpuId       string `xorm:"default 'NULL' VARCHAR(255)"`
	ProductName string `xorm:"default 'NULL' VARCHAR(255)"`
	SpecName    string `xorm:"default 'NULL' VARCHAR(255)"`
	SpecValue   string `xorm:"default 'NULL' VARCHAR(255)"`
	A8Third     string `xorm:"default 'NULL' VARCHAR(255)"`
	MarketPrice string `xorm:"default 'NULL' VARCHAR(255)"`
	RetailPrice string `xorm:"default 'NULL' VARCHAR(255)"`
	ErpThird    string `xorm:"default 'NULL' VARCHAR(255)"`
	A8Third1    string `xorm:"default 'NULL' VARCHAR(255)"`
	ZilongThird string `xorm:"default 'NULL' VARCHAR(255)"`
	GyThird     string `xorm:"default 'NULL' VARCHAR(255)"`
	Remark      string `xorm:"default 'NULL' VARCHAR(255)"`
}
