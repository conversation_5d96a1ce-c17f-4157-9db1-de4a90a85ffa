package models

import "time"

type ChannelCategoryThirdid struct {
	Id             int32     `xorm:"not null pk autoincr comment('平台分类id') INT(11)"`
	ChannelId      int32     `xorm:"not null default 0 comment('渠道id(1-阿闻，2-美团，3-饿了么)') INT(11)"`
	ChannelStoreId string    `xorm:"not null default 0 comment('渠道门店id') varchar(50)"`
	CategoryId     string    `xorm:"not null default 0 comment('第三方分类ID') varchar(30)"`
	CreateTime     time.Time `xorm:"DATETIME created"`
}
