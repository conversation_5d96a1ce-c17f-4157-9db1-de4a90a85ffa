package models

type CategoryNav struct {
	CategoryId  int32  `xorm:"not null pk comment('商品分类id') INT(11)"`
	<PERSON><PERSON>       string `xorm:"default 'NULL' comment('商品分类别名') VARCHAR(100)"`
	Pic         string `xorm:"default 'NULL' comment('分类图片') VARCHAR(100)"`
	Categoryids string `xorm:"default 'NULL' comment('推荐子级分类') VARCHAR(800)"`
	Brandids    string `xorm:"default 'NULL' comment('推荐的品牌') VARCHAR(800)"`
	Adv1        string `xorm:"default 'NULL' comment('广告图1') VARCHAR(100)"`
	Adv2        string `xorm:"default 'NULL' comment('广告图2') VARCHAR(100)"`
	Adv1Aim     string `xorm:"default 'NULL' comment('广告图1目标地址') VARCHAR(255)"`
	Adv2Aim     string `xorm:"default 'NULL' comment('广告图2目标地址') VARCHAR(255)"`
}
