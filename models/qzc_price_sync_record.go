package models

import (
	"time"
)

type QzcPriceSyncRecord struct {
	Id          int       `xorm:"not null pk autoincr comment('主键') INT(11)"`
	Type        int       `xorm:"not null default 0 comment('操作类型 1导入 2导出') TINYINT(4)"`
	WarehouseId int       `xorm:"not null default 0 comment('仓库id') index INT(10)"`
	ThirdSkuId  string    `xorm:"not null default ''0'' VARCHAR(50)"`
	Operation   string    `xorm:"not null default '''' comment('操作内容') VARCHAR(100)"`
	UserNo      string    `xorm:"not null default '''' comment('操作人') VARCHAR(11)"`
	UserName    string    `xorm:"not null default '''' comment('操作人名称') VARCHAR(128)"`
	UserIp      string    `xorm:"not null default '''' comment('操作人id') VARCHAR(128)"`
	CreateTime  time.Time `xorm:"default 'current_timestamp()' comment('操作时间') DATETIME created"`
}
