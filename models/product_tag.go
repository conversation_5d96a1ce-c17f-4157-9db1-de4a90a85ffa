package models

import "strings"

type ProductTag struct {
	Id              int    `xorm:"not null pk autoincr INT(11)"`
	ProductId       int    `xorm:"default NULL comment('商品ID') INT(11)"`
	SkuId           int    `xorm:"not null comment('商品skuid') index INT(11)"`
	ProductType     int    `xorm:"default NULL comment('商品类别（1-实物商品，2-虚拟商品）') INT(11)"`
	Species         string `xorm:"default 'NULL' comment('物种') VARCHAR(100)"`
	Varieties       string `xorm:"default 'NULL' comment('品种') VARCHAR(100)"`
	Sex             string `xorm:"default 'NULL' comment('性别') VARCHAR(100)"`
	Shape           string `xorm:"default 'NULL' comment('体型') VARCHAR(100)"`
	Age             string `xorm:"default 'NULL' comment('年龄') VARCHAR(100)"`
	SpecialStage    string `xorm:"default 'NULL' comment('特殊阶段') VARCHAR(100)"`
	IsSterilization string `xorm:"default 'NULL' comment('是否绝育') VARCHAR(100)"`
	ContentType     string `xorm:"default 'NULL' comment('内容类型') VARCHAR(100)"`
	Status          string `xorm:"default 'NULL' comment('状态') VARCHAR(100)"`
}

// 将商品标签信息合成为逗号分割的字符串
func (product *ProductTag) ToTagsString() string {
	var tagsStr strings.Builder
	var ignoreTagValue = "不限"
	if len(product.Species) > 0 && strings.Compare(product.Species, ignoreTagValue) != 0 {
		tagsStr.WriteString(product.Species + ",")
	}
	if len(product.Varieties) > 0 && strings.Compare(product.Varieties, ignoreTagValue) != 0 {
		tagsStr.WriteString(product.Varieties + ",")
	}
	if len(product.Sex) > 0 && strings.Compare(product.Sex, ignoreTagValue) != 0 {
		tagsStr.WriteString(product.Sex + ",")
	}
	if len(product.Shape) > 0 && strings.Compare(product.Shape, ignoreTagValue) != 0 {
		tagsStr.WriteString(product.Shape + ",")
	}
	if len(product.Age) > 0 && strings.Compare(product.Age, ignoreTagValue) != 0 {
		tagsStr.WriteString(product.Age + ",")
	}
	if len(product.SpecialStage) > 0 && strings.Compare(product.SpecialStage, ignoreTagValue) != 0 {
		tagsStr.WriteString(product.SpecialStage + ",")
	}
	if len(product.IsSterilization) > 0 && strings.Compare(product.IsSterilization, ignoreTagValue) != 0 {
		tagsStr.WriteString(product.IsSterilization + ",")
	}
	if len(product.ContentType) > 0 && strings.Compare(product.ContentType, ignoreTagValue) != 0 {
		tagsStr.WriteString(product.ContentType + ",")
	}
	if len(product.Status) > 0 && strings.Compare(product.Status, ignoreTagValue) != 0 {
		tagsStr.WriteString(product.Status + ",")
	}
	if tagsStr.Len() == 0 {
		return tagsStr.String()
	} else {
		return tagsStr.String()[:tagsStr.Len()-1]
	}

}
