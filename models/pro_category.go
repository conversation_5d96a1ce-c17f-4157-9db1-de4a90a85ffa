package models

import (
	"time"

	"github.com/go-xorm/xorm"
)

type ProCategory struct {
	// 分类id
	Id int32 `json:"id" xorm:"pk autoincr not null comment('分类id') int32 'id'"`
	// 连锁ID
	ChainId int64 `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	// 分类名称
	Name string `json:"name" xorm:"default 'null' comment('分类名称') VARCHAR(100) 'name'"`
	// 父类id
	ParentId int32 `json:"parent_id" xorm:"default 'null' comment('父类id') int32 'parent_id'"`
	// 排序
	Sort int32 `json:"sort" xorm:"default 'null' comment('排序') int32 'sort'"`
	// 图片(三级分类存)
	Img string `json:"img" xorm:"default '' comment('图片(三级分类存)') VARCHAR(100) 'img'"`
	// 添加时间
	CreateDate time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date' created"`
	// 修改时间
	UpdateDate time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date' updated"`
	// 创建人ID
	CreatedBy int64 `json:"created_by" xorm:"not null default 0 comment('创建人ID') BIGINT 'created_by'"`
	// 创建人名称
	CreatedName string `json:"created_name" xorm:"not null default '' comment('创建人名称') VARCHAR(100) 'created_name'"`
	// 更新人ID
	UpdatedBy int64 `json:"updated_by" xorm:"not null default 0 comment('更新人ID') BIGINT 'updated_by'"`
	// 更新人名称
	UpdatedName string `json:"updated_name" xorm:"not null default '' comment('更新人名称') VARCHAR(100) 'updated_name'"`
	//分类类型：1-门店线上，2-门店线下，3-服务分类，4-活体分类
	Type int32 `json:"type" xorm:"default 1 comment('分类类型：1-门店线上，2-门店线下，3-服务分类，4-活体分类') INT 'type'"`
}

// 获取分类名称
func GetProductCategory(db *xorm.Engine, where map[string]interface{}) (out map[int]string, err error) {
	session := db.NewSession()
	defer session.Close()

	id, ok := where["id"]
	if ok {
		session = session.Where("id=?", id)
	}
	ids, ok := where["ids"]
	if ok {
		session = session.In("id", ids)
	}

	data := make([]ProCategory, 0)
	if err = session.Table("eshop.pro_category").Find(&data); err != nil {
		return
	}
	out = make(map[int]string)
	for _, v := range data {
		out[int(v.Id)] = v.Name
	}

	return
}
