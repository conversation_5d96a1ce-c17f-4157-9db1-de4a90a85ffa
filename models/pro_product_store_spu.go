package models

import (
	"fmt"
	"github.com/go-xorm/xorm"

	"time"
)

type ProProductStoreSpu struct {
	Id                int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
	StoreId           string    `json:"store_id" xorm:"not null default 0 comment('门店的id') VARCHAR(50) 'store_id'"`
	ProductId         int       `json:"product_id" xorm:"not null default 0 comment('商品ID') INT 'product_id'"`
	ChannelId         int       `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)') INT 'channel_id'"`
	ChannelCategoryId int       `json:"channel_category_id" xorm:"default '' comment('分类id') VARCHAR(50) 'channel_category_id'"`
	SyncError         string    `json:"sync_error" xorm:"default 'null' comment('铺品或者上架报错信息') VARCHAR(500) 'sync_error'"`
	ProductThirdId    string    `json:"product_third_id" xorm:"default '' comment('第三方回写的商品ID') VARCHAR(50) 'product_third_id'"`
	ProductType       int       `json:"product_type" xorm:"default 1 comment('商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)') INT 'product_type'"`
	CreateDate        time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品添加日期') DATETIME 'create_date' created"`
	UpdateDate        time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品最后更新日期') DATETIME 'update_date' updated"`
	//商品分类
	NamePath string `json:"namePath" xorm:"default 'null' comment('分类组合名称') VARCHAR(500) 'namePath'"`
}

// 添加查询构建器
type SpuQuery struct {
	ProductId           int // 0 表示未设置
	ProductIds          []int
	ChannelId           int // 0 表示未设置
	ChannelIds          []int
	StoreId             string // 空字符串表示未设置
	StoreIds            []string
	ExcludeProductTypes []int
	ProductTypes        []int
	ChannelCategoryId   string // 根据分类查询
}

func (p *ProProductStoreSpu) QueryMap(db *xorm.Engine, query SpuQuery) (map[string]ProProductStoreSpu, []ProProductStoreSpu, error) {
	session := db.NewSession()
	defer session.Close()

	// 使用 > 0 判断是否设置了查询条件
	if query.ProductId > 0 {
		session = session.Where("product_id = ?", query.ProductId)
	}
	if len(query.ProductIds) > 0 {
		session = session.In("product_id", query.ProductIds)
	}
	if query.ChannelId > 0 {
		session = session.Where("channel_id = ?", query.ChannelId)
	}
	if len(query.ChannelIds) > 0 {
		session = session.In("channel_id", query.ChannelIds)
	}
	if query.StoreId != "" {
		session = session.Where("store_id = ?", query.StoreId)
	}
	if len(query.StoreIds) > 0 {
		session = session.In("store_id", query.StoreIds)
	}
	if len(query.ExcludeProductTypes) > 0 {
		session = session.NotIn("product_type", query.ExcludeProductTypes)
	}
	if len(query.ProductTypes) > 0 {
		session = session.In("product_type", query.ProductTypes)
	}
	if query.ChannelCategoryId != "" {
		session = session.Where("channel_category_id = ?", query.ChannelCategoryId)
	}

	data := make([]ProProductStoreSpu, 0)
	out := make(map[string]ProProductStoreSpu)

	if err := session.Table("eshop.pro_product_store_spu").Find(&data); err != nil {
		return nil, nil, err
	}

	for _, v := range data {
		k := fmt.Sprintf("%d_%s_%d", v.ChannelId, v.StoreId, v.ProductId)
		out[k] = v
	}
	return out, data, nil
}
