package models

import "time"

type AgencyProduct struct {
	Id          int       `xorm:"not null pk autoincr comment('主键') INT(11)"`
	AppChannel  int       `xorm:"not null comment('应用配置的id') unique(agency_product_un) INT(11)"`
	SkuId       int       `xorm:"not null comment('商品的sku_id') unique(agency_product_un) INT(11)"`
	ProductName string    `xorm:"not null comment('导入商品的名称') VARCHAR(100)"`
	CreateBy    string    `xorm:"default '''' comment('创建人') VARCHAR(20)"`
	CreateTime  time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime  time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
}

type FinanceAppChannel struct {
	FinanceCode string `json:"finance_code"`
	AppChannel  int32  `json:"app_channel"`
}

type AgencyProductDto struct {
	AppChannel  int    `json:"app_channel"`
	SkuId       int    `json:"sku_id"`
	ProductName string `json:"product_name"`
	ProductId   int    `json:"product_id"`
}

type GroupProductSkuDto struct {
	SkuId     int `json:"sku_id"`
	ProductId int `json:"product_id"`
}

type StoreAppDto struct {
	FinanceCode string `json:"finance_code"`
	AppChannel  int    `json:"app_channel"`
}
