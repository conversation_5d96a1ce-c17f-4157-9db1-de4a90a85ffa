package models

import (
	"time"
)

type WarehouseRelationShop struct {
	Id            int       `xorm:"not null pk autoincr INT(11)"`
	WarehouseId   int       `xorm:"default 0 comment('本地仓库id') INT(11)"`
	WarehouseName string    `xorm:"default 'NULL' comment('关联门店名称') VARCHAR(200)"`
	ShopId        string    `xorm:"default 'NULL' comment('关联的门店id') VARCHAR(100)"`
	ShopName      string    `xorm:"default 'NULL' comment('关联门店名称') VARCHAR(200)"`
	ChannelId     int       `xorm:"default 0 comment('渠道id') INT(11)"`
	CreateTime    time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
}
type WarehouseRelationShopExtend struct {
	WarehouseRelationShop `xorm:"extends"`
	Category              int32 `json:"category"`
}

func (c WarehouseRelationShop) TableName() string {
	return "dc_dispatch.warehouse_relation_shop"
}
