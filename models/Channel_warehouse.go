package models

import "time"

type ChannelWarehouse struct {
	ShopId        string `json:"shop_id"`
	ShopName      string `json:"shop_name"`
	WarehouseId   int    `json:"warehouse_id"`
	WarehouseCode string `json:"warehouse_code"`
	Category      int    `json:"category"`
	ChannelId     int    `json:"channel_id"`
	//SellDrugs     int    `json:"sell_drugs"` // v6.27.2 todo 删除
}

type ChannelWarehouseResp struct {
	//仓库id
	WarehouseIds []int32 `json:"warehouse_ids"`

	// 渠道id
	ChannelId int `json:"channel_id"`
	// 是否同一个仓库 只针对阿闻和阿闻竖屏自提  true 是 false 否
	IsSame bool `json:"is_same"`
}

type WarehouseRelationShopRedis struct {
	WarehouseId   int32     `json:"warehouse_id"`
	WarehouseName string    `json:"warehouse_name"`
	Code          string    `json:"code"`
	ShopId        string    `json:"shop_id"`
	ShopName      string    `json:"shop_name"`
	ChannelId     int32     `json:"channel_id"`
	Category      int32     `json:"category"`
	CreateTime    time.Time `json:"create_time"`
}

type WarehouseShop struct {
	ShopId    string `json:"shop_id"`
	ChannelId int    `json:"channel_id"`
}
