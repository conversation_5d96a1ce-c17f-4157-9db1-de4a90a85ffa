package models

import "time"

type RecordProduct struct {
	Id          int       `xorm:"not null pk autoincr comment('主键') INT(11)"`
	RecordType  int64     `xorm:"not null comment('记录类型（1修改货号，2删除商品）') BIGINT(20)"`
	RecordTime  time.Time `xorm:"not null comment('记录的操作时间') DATETIME"`
	RecordData  string    `xorm:"not null default '''' comment('记录信息') VARCHAR(1000)"`
	UserNo      string    `xorm:"not null default '''' comment('操作人') VARCHAR(20)"`
	UserName    string    `xorm:"not null default '''' comment('操作人') VARCHAR(30)"`
	Upc         string    `xorm:"not null default '''' comment('upc') VARCHAR(50)"`
	ProductName string    `xorm:"not null default '''' comment('商品名称') VARCHAR(100)"`
	ProductId   int       `xorm:"not null comment('商品id') INT(11)"`
	SkuId       int       `xorm:"not null comment('商品skuid') INT(11)"`
	ZilongNew   string    `xorm:"not null default '''' comment('修改之后子龙货号') VARCHAR(30)"`
	A8New       string    `xorm:"not null default '''' comment('修改之后a8货号') VARCHAR(30)"`
	ZilongOld   string    `xorm:"not null default '''' comment('修改之前的子龙货号') VARCHAR(30)"`
	A8Old       string    `xorm:"not null default '''' comment('修改之前的a8货号') VARCHAR(30)"`
	CreateDate  time.Time `xorm:"default 'current_timestamp()' comment('创建日期') DATETIME"`
	UpdateDate  time.Time `xorm:"default 'current_timestamp()' comment('更新日期') DATETIME"`
}
