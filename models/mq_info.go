package models

import (
	"time"
)

type MqInfo struct {
	Id       int       `xorm:"not null pk autoincr comment('主键') INT(11)"`
	Exchange string    `xorm:"default 'NULL' comment('交换机') VARCHAR(255)"`
	Quene    string    `xorm:"default 'NULL' comment('队列名') VARCHAR(255)"`
	Content  string    `xorm:"default 'NULL' comment('内容') VARCHAR(255)"`
	Ispush   int       `xorm:"default 0 comment('是否推送') INT(255)"`
	Lastdate time.Time `xorm:"default 'current_timestamp()' comment('最后时间') TIMESTAMP"`
}
