package models

import (
	"time"
)

type GjSku struct {
	Id            int32       `xorm:"not null pk comment('SKU ID') INT(11)"`
	ProductId     int32       `xorm:"default NULL comment('商品库中的商品ID') index INT(11)"`
	MarketPrice   int32       `xorm:"default NULL comment('市场价') INT(11)"`
	RetailPrice   int32       `xorm:"default NULL comment('建议价格') INT(11)"`
	BarCode       string      `xorm:"default 'NULL' comment('商品条码') VARCHAR(36)"`
	WeightForUnit float64     `xorm:"default NULL comment('重量') DOUBLE(8,2)"`
	PreposePrice  int32       `xorm:"default NULL comment('前置仓价格') INT(11)"`
	StorePrice    int32       `xorm:"default NULL comment('门店仓价格') INT(11)"`
	UpdateDate    time.Time   `xorm:"default 'current_timestamp()' DATETIME"`
}
