package models

type GjSkuValue struct {
	Id          int    `xorm:"not null pk INT(11)"`
	SpecId      int    `xorm:"default NULL comment('规格ID') INT(11)"`
	SpecValueId int    `xorm:"default NULL comment('规格值ID') INT(11)"`
	SkuId       int    `xorm:"default NULL comment('SKU ID') INT(11)"`
	ProductId   int    `xorm:"default NULL comment('商品ID') INT(11)"`
	Pic         string `xorm:"default 'NULL' comment('规格属性图片') VARCHAR(500)"`
	Sort        int    `xorm:"default NULL comment('排序') INT(11)"`
}
