package models

import (
	"time"
)

type PriceSyncResponse struct {
	Id         int64     `xorm:"pk autoincr BIGINT(11)"`
	Response   string    `xorm:"default 'NULL' comment('请求参数') MEDIUMTEXT"`
	Enable     int       `xorm:"default NULL comment('是否可用 0:不可用 1:可用') INT(11)"`
	CreateTime time.Time `xorm:"created comment('创建时间') DATETIME"` //创建时间
	LastTime   time.Time `xorm:"updated comment('更新时间') DATETIME"` //更新时间
}
