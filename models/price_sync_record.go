package models

import (
	"time"
)

type PriceSyncRecord struct {
	Id          int       `xorm:"not null pk autoincr INT(11)"`
	PriceSyncId int       `xorm:"default NULL comment('price_sync表的id') INT(11)"`
	ChannelId   int       `xorm:"default NULL comment('渠道id') INT(11)"`
	Content     string    `xorm:"default 'NULL' comment('推送内容') TEXT"`
	IsPush      int       `xorm:"default NULL comment('是否推送 0：未推送 1：已推送') INT(11)"`
	CreateTime  time.Time `xorm:"created comment('创建时间') DATETIME"` //创建时间
	LastTime    time.Time `xorm:"updated comment('更新时间') DATETIME"` //更新时间
}
