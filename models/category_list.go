package models

type CategoryList struct {
	Id         int    `xorm:"not null comment('uuid 平台分类id') int(11)"`
	Name       string `xorm:"default 'NULL' comment('分类名称') VARCHAR(100)"`
	ParentId   int    `xorm:"default NULL comment('父类id') int(11)"`
	IsInvented int    `xorm:"default '0' comment('是否允许虚拟商品') int(11)"`
}

type CategoryListTree struct {
	Id       int
	Name     string
	ParentId int
	Children []*CategoryListTree
}
