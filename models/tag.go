package models

type Tag struct {
	Id     int32  `xorm:"not null pk autoincr INT(11)"`
	Name   string `xorm:"default 'NULL' comment('标签名称') VARCHAR(200)"`
	Value  string `xorm:"default '''' comment('分类TAG值') VARCHAR(255)"`
	Sort   int32  `xorm:"default NULL comment('排序') INT(11)"`
	Type   int32  `xorm:"default NULL comment('标签类型（1-默认标签，2-活动标签）') INT(11)"`
	IsShow int32  `xorm:"default NULL comment('是否显示') INT(11)"`
}
