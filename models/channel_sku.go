package models

import (
	"_/proto/pc"
	"fmt"
	"github.com/go-xorm/xorm"
	"github.com/spf13/cast"
	"strings"
)

type ChannelSku struct {
	Id            int32   `xorm:"default NULL comment('SKU ID') INT(11)"`
	ProductId     int32   `xorm:"default NULL comment('商品库中的商品ID') index(product_id) INT(11)"`
	MarketPrice   int32   `xorm:"default NULL comment('市场价') INT(11)"`
	RetailPrice   int32   `xorm:"default NULL comment('建议价格') INT(11)"`
	ChannelId     int32   `xorm:"default NULL comment('渠道id') index(product_id) INT(11)"`
	IsUse         int32   `xorm:"default NULL comment('是否可用，可用的数据 ，会推送到渠道') INT(11)"`
	WeightForUnit float64 `xorm:"default NULL comment('重量') DOUBLE"`
	VipDiscount   float64 `xorm:"default NULL comment('会员折扣') DECIMAL(4,2)"`
	WeightUnit    string  `xorm:"default 'NULL' comment('重量名称') VARCHAR(20)"`
	MinOrderCount int32   `xorm:"default 'NULL' comment('最小购买数量') INT(11)"`
	BarCode       string  `xorm:"default 'NULL' comment('商品条码') VARCHAR(36)"`
	PriceUnit     string  `xorm:"default 'NULL' comment('价格单位') VARCHAR(20)"`
	PreposePrice  int32   `xorm:"default 'NULL' comment('前置仓价格') INT(11)"`
	StorePrice    int32   `xorm:"default 'NULL' comment('门店仓价格') INT(11)"`
}

type ChannelSkuExtend struct {
	*ChannelSku     `xorm:"extends"`
	*ChannelProduct `xorm:"extends"`
	// 组合商品子商品
	Children []*ChannelSkuGroup
	IsDrugs  int32 `xorm:"is_drugs"`
}

type ChannelSkuExtends []*ChannelSkuExtend

// QueryChannelSkuExtendByProductIds 渠道商品转换到sku完整信息
func QueryChannelSkuExtendByProductIds(db *xorm.Engine, channelId int32, pIds []int32) (es ChannelSkuExtends, err error) {
	if err = db.Table("channel_product").Alias("cp").
		Join("inner", "channel_sku cs", fmt.Sprintf("cs.product_id = cp.id and cs.channel_id =%d", channelId)).
		Join("inner", "product gj", "gj.id = cp.id").
		Select("cs.*,cp.id,cp.name,cp.pic,cp.channel_id,cp.is_use,cp.update_date,cp.product_type,cp.group_type,gj.is_drugs").
		Where("cp.channel_id = ?", channelId).In("cp.id", pIds).OrderBy("cs.id asc").Find(&es); err != nil {
		return
	}
	err = es.PreloadChildren(db)
	return
}

// PreloadChildren 组合商品加载子商品
func (es ChannelSkuExtends) PreloadChildren(db *xorm.Engine) error {
	var gIds []int32
	for _, e := range es {
		if e.ProductType == 3 {
			gIds = append(gIds, e.ProductId)
		}
	}

	if len(gIds) == 0 {
		return nil
	}

	var children []*ChannelSkuGroup
	childGroupBySku := make(map[int][]*ChannelSkuGroup)
	if err := db.Where("channel_id = ?", es[0].ChannelProduct.ChannelId).In("product_id", gIds).Find(&children); err != nil {
		return err
	}
	for _, child := range children {
		childGroupBySku[child.SkuId] = append(childGroupBySku[child.SkuId], child)
	}
	for _, e := range es {
		if cg, ok := childGroupBySku[int(e.ChannelSku.Id)]; ok {
			e.Children = cg
		}
	}

	return nil
}

// QueryStock 批量查库存，返回当前库存及锁定库存
func (es ChannelSkuExtends) QueryStock(db *xorm.Engine, warehouseId int) (rs map[int32]*pc.ChannelProductList_Sku_Stock, err error) {
	var sIds []int32

	for _, e := range es {
		if e.ProductType == 1 {
			sIds = append(sIds, e.ChannelSku.Id)
		} else if e.ProductType == 3 {
			for _, child := range e.Children {
				if child.ProductType == 1 {
					sIds = append(sIds, int32(child.GroupSkuId))
				}
			}
		}
	}

	type Stock struct {
		Qty     int32 `json:"qty"`
		LockQty int32 `json:"lock_qty"`
		SkuId   int32 `json:"sku_id"`
	}

	var stocks []*Stock
	stockMap := make(map[int32]*Stock)

	if err = db.Table("dc_order.warehouse_goods").Alias("wg").
		Where("warehouse_id = ?", warehouseId).In("goodsid", sIds).
		Select(`wg.stock as qty,wg.goodsid as sku_id,
(select sum(stock) from dc_order.order_freeze_stock f where f.sku_id = wg.goodsid and f.warehouse_id = wg.warehouse_id) lock_qty`).
		Find(&stocks); err != nil {
		return
	}

	for _, stock := range stocks {
		stockMap[stock.SkuId] = stock
	}

	rs = make(map[int32]*pc.ChannelProductList_Sku_Stock)

	for _, e := range es {
		lss := new(pc.ChannelProductList_Sku_Stock)
		if e.ProductType == 1 {
			if s, ok := stockMap[e.ChannelSku.Id]; ok {
				lss.LockQty = s.LockQty
				lss.Qty = s.Qty - s.LockQty
			}
		} else if e.ProductType == 3 {
			for _, child := range e.Children {
				if child.ProductType == 1 {
					if s, ok := stockMap[int32(child.GroupSkuId)]; ok {
						if (s.Qty-s.LockQty) < 1 || child.Count == 0 {
							lss.Qty = 0
							break
						}
						qty := (s.Qty - s.LockQty) / int32(child.Count)
						if qty < 1 { // 没有库存直接重置0
							lss.Qty = 0
							break
						} else if lss.Qty == 0 || qty < lss.Qty { // 初始化或者当前商品库存更小，则等于当前库存
							lss.Qty = qty
						}
					} else {
						lss.Qty = 0
						break
					}
				}
			}
		} else if e.ProductType == 2 {
			continue
		}

		rs[e.ChannelSku.Id] = lss
	}

	return
}

// MergeSnapshot 附加快照信息
// 价格 取快照，没有则取渠道价格表、分类
func (es ChannelSkuExtends) MergeSnapshot(db *xorm.Engine, financeCode string) (err error) {
	if len(es) == 0 {
		return
	}

	var sIds []int32
	for _, e := range es {
		sIds = append(sIds, e.ChannelSku.Id)
	}

	channelId := es[0].ChannelSku.ChannelId

	type SkuPrice struct {
		SkuId             int32  `json:"sku_id"`
		ChannelStorePrice int32  `json:"channel_store_price"`
		SnapPrices        string `json:"snap_prices"`
		SnapSkuIds        string `json:"snap_sku_ids"`
		SnapId            int32  `json:"snap_id"`
		SnapProductName   string `json:"snap_product_name"`
		SnapProductPic    string `json:"snap_product_pic"`
		ChannelCategoryId int32  `json:"channel_category_id"`
		SnapIsRecommend   int32  `json:"snap_is_recommend"`
		SnapMinOrderCount int32  `json:"snap_min_order_count"`
		IsDrugs           int32  `json:"is_drugs"`
	}

	var skuPrices []*SkuPrice
	query := db.Table("channel_sku").Alias("cs").Where("cs.channel_id = ?", channelId).
		Join("left", "channel_product_snapshot s", fmt.Sprintf("s.product_id = cs.product_id and s.finance_code ='%s' and s.channel_id = %d",
			financeCode, channelId)).
		Join("left", "gj_product gj", "gj.id = cs.product_id ").
		Select(`cs.id as sku_id,cs.store_price as channel_store_price,s.id as snap_id,s.channel_category_id,
json_unquote(JSON_EXTRACT(json_data,'$.product.name')) as snap_product_name,
json_unquote(JSON_EXTRACT(json_data,'$.product.pic')) as snap_product_pic,
JSON_EXTRACT(json_data,'$.sku_info[*].market_price') as snap_prices,
JSON_EXTRACT(json_data,'$.sku_info[*].sku_id') as snap_sku_ids,
IFNULL(json_unquote(JSON_EXTRACT(json_data,'$.product.is_recommend')),0) as snap_is_recommend,
IFNULL(JSON_EXTRACT(s.json_data,'$.sku_info[0].min_order_count'),cs.min_order_count) as snap_min_order_count,
gj.is_drugs 
`,
		).
		In("cs.id", sIds).OrderBy("s.update_date desc") // 快照表有重复数据，以更新时间最新为准
	if err = query.Find(&skuPrices); err != nil {
		return
	}

	skuPriceMap := make(map[int32]*SkuPrice)
	for _, price := range skuPrices {
		skuPriceMap[price.SkuId] = price
	}

	for _, e := range es {
		if sp, ok := skuPriceMap[e.ChannelSku.Id]; ok {
			if sp.ChannelCategoryId > 0 {
				e.ChannelProduct.ChannelCategoryId = sp.ChannelCategoryId
			}
			var hasPrice bool
			// 存在快照
			if sp.SnapId > 0 {
				e.ChannelProduct.Pic = sp.SnapProductPic
				e.ChannelProduct.Name = sp.SnapProductName
				e.ChannelProduct.IsRecommend = sp.SnapIsRecommend
				e.ChannelSku.MinOrderCount = sp.SnapMinOrderCount
				e.IsDrugs = sp.IsDrugs
				// 1.快照价格优先
				if len(sp.SnapPrices) > 2 {
					sps := strings.Split(sp.SnapPrices[1:len(sp.SnapPrices)-1], ", ")
					ids := strings.Split(sp.SnapSkuIds[1:len(sp.SnapSkuIds)-1], ", ")
					for index, id := range ids {
						if cast.ToInt32(id) == sp.SkuId && len(sps) > index {
							e.ChannelSku.MarketPrice = cast.ToInt32(sps[index])
							hasPrice = true
						}
					}
				}
			}
			// 2.渠道价格表
			if !hasPrice {
				e.ChannelSku.MarketPrice = sp.ChannelStorePrice
			}
		}
	}
	return
}
