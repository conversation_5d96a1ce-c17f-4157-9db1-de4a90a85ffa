package models

import (
	"time"
)

type PriceSync struct {
	Id          int64     `xorm:"pk autoincr BIGINT(20)"`
	FinanceCode string    `xorm:"default 'NULL' comment('财务编码') VARCHAR(100)"`
	ProductId   int       `xorm:"default NULL comment('商品id') INT(11)"`
	Sku         int       `xorm:"default NULL comment('sku') INT(11)"`
	Price       int       `xorm:"default NULL comment('价格') INT(11)"`
	ZlProductid string    `xorm:"default 'NULL' comment('子龙货号') VARCHAR(100)"`
	Enable      int       `xorm:"default NULL comment('是否可用 0:不可用 1:可用') INT(11)"`
	CreateTime  time.Time `xorm:"created comment('创建时间') DATETIME"` //创建时间
	LastTime    time.Time `xorm:"updated comment('更新时间') DATETIME"` //更新时间
}
type PriceSyncMedical struct {
	Id            int64     `xorm:"pk autoincr BIGINT(20)"`
	FinanceCode   string    `xorm:"default 'NULL' comment('财务编码') VARCHAR(100)"`
	ProductId     int       `xorm:"default NULL comment('商品id') INT(11)"`
	Sku           int       `xorm:"default NULL comment('sku') INT(11)"`
	Price         int       `xorm:"default NULL comment('价格') INT(11)"`
	HospitalPrice int       `xorm:"default NULL comment('互联网医院价格') INT(11)"`
	ZlProductid   string    `xorm:"default 'NULL' comment('子龙货号') VARCHAR(100)"`
	Enable        int       `xorm:"default NULL comment('是否可用 0:不可用 1:可用') INT(11)"`
	CreateTime    time.Time `xorm:"created comment('创建时间') DATETIME"` //创建时间
	LastTime      time.Time `xorm:"updated comment('更新时间') DATETIME"` //更新时间
}
