package models

import (
	"time"
)

type R1Price struct {
	Id                       int64     `xorm:"pk autoincr BIGINT(20)"`
	SkuNo                    string    `xorm:"not null comment('货号') unique VARCHAR(128)"`
	CentralizedPurchasePrice int       `xorm:"not null default 0 comment('集采价，分') INT(11)"`
	RetailPrice              int       `xorm:"not null default 0 comment('建议零售价格，分') INT(11)"`
	TradePrice               int       `xorm:"not null default 0 comment('批发价，分') INT(11)"`
	CreateTime               time.Time `xorm:"not null default 'current_timestamp()' DATETIME created"`
	UpdateTime               time.Time `xorm:"not null default 'current_timestamp()' DATETIME updated"`
}
