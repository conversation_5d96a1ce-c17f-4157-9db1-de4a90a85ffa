package models

import (
	"time"
)

type Category struct {
	Id              int32     `xorm:"not null pk autoincr comment('平台分类id') INT(11)"`
	Name            string    `xorm:"default 'NULL' comment('分类名称') VARCHAR(100)"`
	ParentId        int32     `xorm:"default NULL comment('父类id') INT(11)"`
	CategoryArrId   int32     `xorm:"default NULL comment('分类归属ID') INT(11)"`
	Sort            int32     `xorm:"default NULL comment('排序') INT(11)"`
	CreateDate      time.Time `xorm:"default 'current_timestamp()' DATETIME"`
	IsInvented      int32     `xorm:"default NULL comment('是否允许发布虚拟商品') INT(11)"`
	IsVerify        int32     `xorm:"default 1 comment('是否可发布需要核销的虚拟商品 1-是 0否') INT(11)"`
	IsNoverify      int32     `xorm:"default 1 comment('是否可发布不需要核销的虚拟商品 1-是 0否') INT(11)"`
	ProductShowType int32     `xorm:"default NULL comment('商品展展示方式（1-颜色，2-SPU）') INT(11)"`
	Img             string    `xorm:"default '''' comment('图片(三级分类存)') VARCHAR(100)"`
	TypeId          int32     `xorm:"default NULL comment('关联的类型ID') INT(11)"`
	Tag             string    `xorm:"default 'NULL' comment('分类的TAG标签，多个逗号分开') VARCHAR(200)"`
}
