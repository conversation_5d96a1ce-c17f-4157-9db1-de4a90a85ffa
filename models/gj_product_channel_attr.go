package models

// 管家商品渠道属性
type GjProductChannelAttr struct {
	Id          int32  `xorm:"not null pk autoincr INT(11)"`
	ProductId   int32  `xorm:"default NULL comment('商品ID') index(product_id) INT(11)"`
	ChannelId   int32  `xorm:"default NULL comment('渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)') index(product_id) INT(11)"`
	AttrId      int32  `xorm:"default NULL comment('属性ID') INT(11)"`
	AttrName    string `xorm:"default 'NULL' comment('属性名') VARCHAR(50)"`
	AttrValueId string `xorm:"not NULL default '' comment('属性值ID') VARCHAR(1000)"`
	AttrValue   string `xorm:" not NULL default '' comment('属性值') VARCHAR(2000)"`
}
