package models

import (
	"time"
)

type HospitalProductPrice struct {
	Id         int       `xorm:"not null pk autoincr INT(11)"`
	SkuId      int       `xorm:"default NULL comment('sku_id') INT(11)"`
	Price      int       `xorm:"default NULL comment('商品价格(分)') INT(11)"`
	CreateDate time.Time `xorm:"default 'current_timestamp()' comment('添加日期') DATETIME"`
	CreateBy   string    `xorm:"default 'NULL' comment('添加人') VARCHAR(100)"`
	UpdateDate time.Time `xorm:"default 'current_timestamp()' comment('最后更新日期') DATETIME"`
	UpdateBy   string    `xorm:"default 'NULL' comment('更新人') VARCHAR(100)"`
	CreateName string    `xorm:"default '' comment('创建人') VARCHAR(64)"`
	UpdateName string    `xorm:"default '' comment('修改人') VARCHAR(64)"`
}

type HospitalProductPriceDto struct {
	Id        int `json:"id"`
	SkuId     int `json:"sku_id"`
	Price     int `json:"price"`
	ProductId int `json:"product_id"`
}
