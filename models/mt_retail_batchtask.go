package models

type MtRetailBatchtask struct {
	Id         int    `xorm:"not null pk autoincr INT(11)"`
	TaskId     int    `xorm:"not null default 0 comment('美团任务ID') index INT(11)"`
	TaskStatus int    `xorm:"not null default 0 comment('任务状态，1已完成，0未完成') INT(10)"`
	TaskData   string `xorm:"not null comment('任务参数') TEXT"`
	UserNo     string `xorm:"not null comment('用户编号') varchar(50)"`
	MtResponse string `xorm:"not null comment('美团返回值') TEXT"`
	CreateDate int64  `xorm:"not null default 0 comment('创建时间') INT(10) created"`
	UpdateDate int64  `xorm:"not null default 0 comment('更新时间') INT(10) updated"`
	AppChannel int32  `xorm:"not null default 1 comment('1.阿闻自有,2.TP代运营') INT(11)"`
}

type TaskData struct {
	InitData struct {
		Type        int    `json:"type"`
		IsAllPois   int    `json:"is_all_pois"`
		AppPoiCodes string `json:"app_poi_codes"`
		RetailInfo  []struct {
			AppFoodCode string `json:"app_food_code"`
			Name        string `json:"name"`
			Description string `json:"description"`
			Skus        []struct {
				SkuId          string `json:"sku_id"`
				Spec           string `json:"spec"`
				Upc            string `json:"upc"`
				Price          string `json:"price"`
				Stock          string `json:"stock"`
				Unit           string `json:"unit"`
				MinOrderCount  int    `json:"min_order_count"`
				AvailableTimes struct {
					Monday    string `json:"monday"`
					Tuesday   string `json:"tuesday"`
					Wednesday string `json:"wednesday"`
					Thursday  string `json:"thursday"`
					Friday    string `json:"friday"`
					Saturday  string `json:"saturday"`
					Sunday    string `json:"sunday"`
				} `json:"available_times"`
				LocationCode          string      `json:"location_code"`
				BoxNum                string      `json:"box_num"`
				BoxPrice              string      `json:"box_price"`
				LadderBoxNum          string      `json:"ladder_box_num"`
				LadderBoxPrice        string      `json:"ladder_box_price"`
				WeightForUnit         string      `json:"weight_for_unit"`
				WeightUnit            string      `json:"weight_unit"`
				OpenSaleAttrValueList interface{} `json:"openSaleAttrValueList"`
			} `json:"skus"`
			MinOrderCount   int    `json:"min_order_count"`
			Unit            string `json:"unit"`
			CategoryName    string `json:"category_name"`
			IsSoldOut       int    `json:"is_sold_out"`
			Picture         string `json:"picture"`
			Sequence        int    `json:"sequence"`
			TagId           int    `json:"tag_id"`
			ZhName          string `json:"zh_name"`
			OriginName      string `json:"origin_name"`
			PictureContents string `json:"picture_contents"`
			IsSpecialty     int    `json:"is_specialty"`
			CommonAttrValue []struct {
				AttrId    int    `json:"attrId"`
				AttrName  string `json:"attrName"`
				ValueList []struct {
					ValueId int    `json:"valueId"`
					Value   string `json:"value"`
				} `json:"valueList"`
			} `json:"common_attr_value"`
			LimitSaleInfo struct {
				LimitSale bool   `json:"limitSale"`
				Type      int    `json:"type"`
				Frequency int    `json:"frequency"`
				Begin     string `json:"begin"`
				Count     int    `json:"count"`
				End       string `json:"end"`
			} `json:"limit_sale_info"`
		} `json:"retail_info"`
	} `json:"init_data"`
}
