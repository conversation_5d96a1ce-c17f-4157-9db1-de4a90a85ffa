package models

import (
	"time"
)

type ChannelStoreProduct struct {
	Id                  int       `xorm:"not null pk autoincr INT(11)"`
	ChannelId           int       `xorm:"comment('渠道id') INT(11)"`
	FinanceCode         string    `xorm:"comment('财务编码') VARCHAR(50)"`
	ProductId           int       `xorm:"comment('商品id') INT(11)"`
	ChannelCategoryId   int       `xorm:"comment('分类id') INT(11)"`
	IsRecommend         int       `xorm:"comment('是否是推荐商品,1是0否') INT(1)"`
	UpDownState         int       `xorm:"comment('上下架状态（1-上架，0-下架）') INT(11)"`
	SnapshotId          int       `xorm:"comment('快照id，最后一次上架更新用的是哪个快照') INT(11)"`
	CreateDate          time.Time `xorm:"comment('创建时间') DATETIME created"`
	UpdateDate          time.Time `xorm:"comment('更新时间') DATETIME updated"`
	ChannelCategoryName string    `xorm:"default 'NULL' comment('渠道分类名称') VARCHAR(100)"`
	Name                string    `xorm:"default 'NULL' comment('商品名称') VARCHAR(255)"`
	SkuId               int       `xorm:"default NULL comment('商品sku') INT(11)"`
	MarketPrice         int       `xorm:"default NULL comment('市场价') INT(11)"`
	HasStock            int       `xorm:"default NULL comment('市场价') INT(11)"`
	DownType            int       `xorm:"default 0 comment('下架类型：0：之前的默认数据 1： 目前只有7天无库存下架') INT(11)"`
}

type ChannelProducts struct {
	Id          string `json:"id"`
	FinanceCode string `json:"finance_code"`
	ChannelId   int    `json:"channel_id"`
	ThirdSkuId  string `json:"third_sku_id"`
}

type ChannelVipProduct struct {
	FinanceCode string `xorm:"comment('财务编码') VARCHAR(50)"`
	ProductId   int32  `xorm:"comment('商品id') INT(11)"`
	Name        string `xorm:"default 'NULL' comment('商品名称') VARCHAR(255)"`
	SkuId       int32  `xorm:"default NULL comment('商品sku') INT(11)"`
	MarketPrice int32  `xorm:"default NULL comment('市场价') INT(11)"`
	JsonData    string `xorm:"default 'NULL' comment('json格式的快照数据 ') TEXT"`
}
