package models

import (
	"github.com/go-xorm/xorm"
)

type ProProductChannelAttr struct {
	Id int32 `json:"id" xorm:"pk autoincr not null int32 'id'"`
	//商品ID
	ProductId int32 `json:"product_id" xorm:"default 'null' comment('商品ID') int32 'product_id'"`
	//渠道id(1-小程序，2-美团，3-饿了么，4-京东到家
	ChannelId int32 `json:"channel_id" xorm:"default 'null' comment('渠道id(1-小程序，2-美团，3-饿了么，4-京东到家)') int32 'channel_id'"`
	//属性ID
	AttrId int32 `json:"attr_id" xorm:"default 'null' comment('属性ID') int32 'attr_id'"`
	//属性名
	AttrName string `json:"attr_name" xorm:"default 'null' comment('属性名') VARCHAR(50) 'attr_name'"`
	//属性值ID
	AttrValueId string `json:"attr_value_id" xorm:"not null default '' comment('属性值ID') VARCHAR(1000) 'attr_value_id'"`
	//属性值
	AttrValue string `json:"attr_value" xorm:"not null default '' comment('属性值') VARCHAR(2000) 'attr_value'"`
	//添加时间
	CreateDate string `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date' created"`
	//修改时间
	UpdateDate string `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date' updated"`
}

// out0 是 map[商品id][]ProProductChannelAttr
// out1 是 map[商品id][渠道id][]ProProductChannelAttr
func GetProductChannelAttrInfo(db *xorm.Engine, where map[string]interface{}) (out0 map[int32][]ProProductChannelAttr, out1 map[int32]map[int32][]ProProductChannelAttr, err error) {
	session := db.NewSession()
	defer session.Close()
	out0 = make(map[int32][]ProProductChannelAttr)
	out1 = make(map[int32]map[int32][]ProProductChannelAttr)
	productIds, ok := where["productIds"]
	if ok {
		session = session.In("product_id", productIds)
	}
	productId, ok := where["productId"]
	if ok {
		session = session.Where("product_id=?", productId)
	}

	channelIds, ok := where["channelIds"]
	if ok {
		session = session.In("channel_id", channelIds)
	}

	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("channel_id=?", channelId)
	}

	data := make([]ProProductChannelAttr, 0)
	if err = session.Table("eshop.pro_product_channel_attr").Find(&data); err != nil {
		return
	}
	outType := 0
	if v, ok := where["outType"]; ok {
		outType = v.(int)
	}

	for _, v := range data {
		switch outType {
		case 1:
			if _, ok := out1[v.ProductId]; !ok {
				out1[v.ProductId] = make(map[int32][]ProProductChannelAttr)
			}
			out1[v.ProductId][v.ChannelId] = append(out1[v.ProductId][v.ChannelId], v)
		default:
			if _, ok := out0[v.ProductId]; !ok {
				out0[v.ProductId] = make([]ProProductChannelAttr, 0)
			}
			out0[v.ProductId] = append(out0[v.ProductId], v)
		}

	}
	return
}
