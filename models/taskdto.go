package models

import "_/proto/pc"

type AutoGenerated struct {
	AppPoiCode string     `json:"app_poi_code"`
	FoodData   []FoodDataStore `json:"food_data"`
}
type Skus struct {
	SkuID string `json:"sku_id"`
	Stock string `json:"stock"`
}
type FoodData struct {
	AppFoodCode string `json:"app_food_code"`
	Skus        []SkusStore `json:"skus"`
}

type EsToStruct struct {
	pc.ChannelProductRequest
	FinanceCode string `json:"finance_code"`
	Tags        string
}

type UPetEsToStruct struct {
	UpetGoods
	Tags string
}
