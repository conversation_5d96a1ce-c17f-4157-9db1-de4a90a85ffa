package models

type UpetTags struct {
	TagId         int    `xorm:"not null pk autoincr comment('标签编号') INT(11)"`
	TagName       string `xorm:"not null comment('标签名称') VARCHAR(50)"`
	TagSort       int    `xorm:"default NULL comment('标签排序') TINYINT(4)"`
	TagCount      int    `xorm:"default NULL comment('标签使用计数') INT(11)"`
	TagTypeId     int    `xorm:"default 0 comment('标签类型：0为默认，1为活动标签') TINYINT(4)"`
	TagNearTagids string `xorm:"default 'NULL' comment('相近标签') VARCHAR(100)"`
	GcId          int    `xorm:"default NULL comment('商品分类id') INT(11)"`
	GcId1         int    `xorm:"default NULL comment('一级分类id') INT(11)"`
	GcId2         int    `xorm:"default NULL comment('二级分类id') INT(11)"`
	GcId3         int    `xorm:"default NULL comment('三级分类id') INT(11)"`
	GcName        string `xorm:"default 'NULL' comment('标签分类') VARCHAR(200)"`
	TagState      int    `xorm:"default 1 comment('0不显示1显示') TINYINT(1)"`
}
