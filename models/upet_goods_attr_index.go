package models

type UpetGoodsAttrIndex struct {
	GoodsId       int32 `xorm:"not null pk comment('商品id') INT(10)"`
	GoodsCommonid int32 `xorm:"not null comment('商品公共表id') index INT(10)"`
	GcId          int32 `xorm:"not null pk comment('商品分类id') INT(10)"`
	TypeId        int32 `xorm:"not null comment('类型id') INT(10)"`
	AttrId        int32 `xorm:"not null comment('属性id') INT(10)"`
	AttrValueId   int32 `xorm:"not null pk comment('属性值id') index INT(10)"`
}
