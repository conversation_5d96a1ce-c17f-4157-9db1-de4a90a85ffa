package models

import (
	"time"
)

type ChannelCategory struct {
	Id           int32     `xorm:"not null pk autoincr comment('平台分类id') INT(11)"`
	ChannelId    int32     `xorm:"not null default 0 comment('渠道id(1-阿闻，2-美团，3-饿了么)') INT(11)"`
	Name         string    `xorm:"default NULL comment('分类名称') VARCHAR(100)"`
	OriginalData string    `xorm:"default NULL comment('原数据') VARCHAR(1000)"`
	ParentId     int32     `xorm:"default NULL comment('父类id') INT(11)"`
	Sort         int32     `xorm:"default NULL comment('排序') INT(11)"`
	CreateDate   time.Time `xorm:"DATETIME created"`
	UpdateDate   time.Time `xorm:"DATETIME updated"`
	CreateName   string    `xorm:"not null default '' comment('创建人') VARCHAR(50)"`
}
