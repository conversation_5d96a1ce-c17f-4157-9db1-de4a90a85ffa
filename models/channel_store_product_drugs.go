package models

type ChannelStoreProductDrugs struct {
	Id          int    `xorm:"not null pk autoincr INT(11)"`
	ChannelId   int    `xorm:"comment('渠道id') INT(11)"`
	FinanceCode string `xorm:"comment('财务编码') VARCHAR(50)"`
	ProductId   int    `xorm:"comment('商品id') INT(11)"`
	UpDownState int    `xorm:"comment('上下架状态（1-上架，0-下架）') INT(11)"`
	SnapshotId  int    `xorm:"comment('快照id，最后一次上架更新用的是哪个快照') INT(11)"`
	SkuId       int    `xorm:"default NULL comment('商品sku') INT(11)"`
}
