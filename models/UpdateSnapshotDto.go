package models

type UpdateSnapshotDto struct {
	//更新字段--商品名称
	UpdateGoodsName int32
	//更新字段--商品卖点
	UpdateSellingPoint int32
	//更新字段--商品图片
	UpdatePic int32
	//更新字段--商品视频
	UpdateVideo int32
	//更新字段--商品品牌
	UpdateBrandId int32
	//更新字段--商品属性
	UpdateAttr int32
	//更新字段--货号
	UpdateCode int32
	//更新字段--市场价
	UpdateMarketPrice int32
	//更新字段--建议零售价
	UpdateRetailPrice int32
	//更新字段--商品条码
	UpdateBarCode int32
	//更新字段--图片详情
	UpdatePicDetails int32
	//更新字段--重量
	UpdateHeight int32
}