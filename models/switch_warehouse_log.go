package models

type SwitchWarehouseLog struct {
	Id                   int64  `json:"id"`                     // 任务id
	ChannelId            int32  `json:"channel_id"`             // 渠道id(0-所有平台,1-阿闻，2-美团，3-饿了么，4-京东到家，10-阿闻竖屏自提)
	Action               int32  `json:"action"`                 // 操作类型：1-导入门店，2-移除门店，3-仓库侧移除门店
	FinanceCode          string `json:"finance_code"`           // 财务编码
	ShopName             string `json:"shop_name"`              // 门店名称
	SrcWarehouseCode     string `json:"src_warehouse_code"`     // 切仓前仓库编码
	SrcWarehouseName     string `json:"src_warehouse_name"`     // 切仓前仓库名称
	SrcWarehouseCategory int32  `json:"src_warehouse_category"` // 切仓前仓库类型1-电商仓3-门店仓4-前置仓5-虚拟前置仓
	DstWarehouseCode     string `json:"dst_warehouse_code"`     // 切仓后仓库编码
	DstWarehouseName     string `json:"dst_warehouse_name"`     // 切仓后仓库名称
	DstWarehouseCategory int32  `json:"dst_warehouse_category"` // 切仓后仓库类型1-电商仓3-门店仓4-前置仓5-虚拟前置仓
	Content              string `json:"content"`                // 操作详情
	CreateId             string `json:"create_id"`              // 创建人id
	CreateTime           string `json:"create_time"`            // 创建时间
	CreateName           string `json:"create_name"`            // 创建人姓名
	CreateMobile         string `json:"create_mobile"`          // 创建人手机号
	CreateIp             string `json:"create_ip"`              // 创建人ip
	IpLocation           string `json:"ip_location"`            // ip所属位置
}
