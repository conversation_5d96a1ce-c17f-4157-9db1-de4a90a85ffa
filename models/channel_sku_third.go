package models

type ChannelSkuThird struct {
	Id            int32  `xorm:"not null pk autoincr INT(11)"`
	ProductId     int32  `xorm:"default NULL comment('商品ID') INT(11)"`
	SkuId         int32  `xorm:"not null comment('商品库SKU ID') INT(11)"`
	ThirdSpuId    string `xorm:"default 'NULL' comment('第三方SPUID') VARCHAR(36)"`
	ThirdSkuId    string `xorm:"default 'NULL' comment('第三方SKUID') VARCHAR(36)"`
	ErpId         int32  `xorm:"default NULL comment('ERP仓库ID') INT(11)"`
	ChannelId     int32  `xorm:"not null comment('渠道id') INT(11)"`
	ThirdSpuSkuId string `xorm:"default 'NULL' comment('拼接的货号') VARCHAR(73)"`
	IsUse         int32  `xorm:"default 0 comment('是否使用') INT(11)"`
}

//平台和渠道的货号
type ChannelSkuThirdAndPt struct {
	Id            int32  `xorm:"not null pk autoincr INT(11)"`
	ProductId     int32  `xorm:"default NULL comment('商品ID') INT(11)"`
	SkuId         int32  `xorm:"not null comment('商品库SKU ID') INT(11)"`
	ThirdSpuId    string `xorm:"default 'NULL' comment('第三方SPUID') VARCHAR(36)"`
	ThirdSkuId    string `xorm:"default 'NULL' comment('第三方SKUID') VARCHAR(36)"`
	ErpId         int32  `xorm:"default NULL comment('ERP仓库ID') INT(11)"`
	ChannelId     int32  `xorm:"not null comment('渠道id') INT(11)"`
	ThirdSpuSkuId string `xorm:"default 'NULL' comment('拼接的货号') VARCHAR(73)"`
	IsUse         int32  `xorm:"default 0 comment('是否使用') INT(11)"`
	ThirdSkuIdPt  string `xorm:"default 'NULL' comment('平台第三方SKUID') VARCHAR(36)"`
}
