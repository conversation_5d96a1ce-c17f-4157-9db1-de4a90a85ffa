package models

import (
	"github.com/go-xorm/xorm"
)

type ProSku struct {
	Id            int32   `json:"id" xorm:"pk autoincr not null comment('SKU ID') INT 'id'"`
	ProductId     int32   `json:"product_id" xorm:"default 'null' comment('商品库中的商品ID') INT 'product_id'"`
	MarketPrice   int32   `json:"market_price" xorm:"default 'null' comment('市场价') INT 'market_price'"`
	RetailPrice   int32   `json:"retail_price" xorm:"default 'null' comment('建议价格/零售价') INT 'retail_price'"`
	BasicPrice    int32   `json:"basic_price" xorm:"default 0 comment('成本价') INT 'basic_price'"`
	BarCode       string  `json:"bar_code" xorm:"default 'null' comment('商品条码') VARCHAR(36) 'bar_code'"`
	WeightForUnit float64 `json:"weight_for_unit" xorm:"default 'null' comment('重量') DOUBLE(8) 'weight_for_unit'"`
	PreposePrice  int32   `json:"prepose_price" xorm:"default 'null' comment('前置仓价格') INT 'prepose_price'"`
	StorePrice    int32   `json:"store_price" xorm:"default 'null' comment('门店仓价格') INT 'store_price'"`
	ElmPrice      int32   `json:"elm_price" xorm:"default 0 comment('饿了么价，单位分') INT 'elm_price'"`
	MtPrice       int32   `json:"mt_price" xorm:"default 0 comment('美团价，单位分') INT 'mt_price'"`
	XcxPrice      int32   `json:"xcx_price" xorm:"default 0 comment('小程序价，单位分') INT 'xcx_price'"`
	StoreUnitKey  string  `json:"store_unit_key" xorm:"default '' comment('库存单位key') VARCHAR(255) 'store_unit_key'"`
	StoreUnit     string  `json:"store_unit" xorm:"default '' comment('库存单位') VARCHAR(255) 'store_unit'"`
	ProductSpecs  string  `json:"product_specs" xorm:"default '' comment('规格信息') VARCHAR(64) 'product_specs'"`
	SkuCode       string  `json:"sku_code" xorm:"not null default '' comment('商品货号') VARCHAR(64) 'sku_code'"`
	CreateDate    string  `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date' created"`
	UpdateDate    string  `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date' updated"`
	TaxRate       float64 `json:"tax_rate" xorm:"default '0.00' comment('税率(示例10%即存入10)') DECIMAL(5) 'tax_rate'"`
	IsDel         int32   `json:"is_del" xorm:"default 0 comment('是否删除') INT 'is_del'"`
}

func GetSkuMapInfo(db *xorm.Engine, where map[string]interface{}) (out map[int32][]ProSku, out1 map[int32]ProSku, err error) {

	session := db.NewSession()
	defer session.Close()
	productIds, ok := where["productIds"]

	if ok && len(productIds.([]int32)) > 0 {
		session = session.In("product_id", productIds)
	}
	productId, ok := where["productId"]
	if ok {
		session = session.Where("product_id=?", productId)
	}

	skuId, ok := where["skuId"]
	if ok {
		session = session.Where("id=?", skuId)
	}

	skuIds, ok := where["skuIds"]
	if ok && len(skuIds.([]int32)) > 0 {
		session = session.In("id", skuIds)
	}

	out = make(map[int32][]ProSku)
	out1 = make(map[int32]ProSku)
	data := make([]ProSku, 0)
	if err = session.Table("eshop.pro_sku").Find(&data); err != nil {
		return
	}

	for _, v := range data {
		if _, ok := out[v.ProductId]; !ok {
			out[v.ProductId] = make([]ProSku, 0)
		}
		out[v.ProductId] = append(out[v.ProductId], v)
		out1[v.Id] = v
	}

	return
}

// 商品条码
func SkuBarcodeIsExist(db *xorm.Engine, where map[string]interface{}) (out bool, err error) {
	session := db.NewSession()
	defer session.Close()
	barCode, ok := where["barCode"]
	if ok {
		session = session.Where("a.bar_code=?", barCode)
	}
	chainId, ok := where["chainId"]
	if ok {
		session = session.Where("b.chain_id=?", chainId)
	}
	notEqualProductId, ok := where["notEqualProductId"]
	if ok {
		session = session.Where("b.id!=?", notEqualProductId)
	}

	skuid := 0
	out, err = session.Table("eshop.pro_sku").Alias("a").
		Join("inner", "eshop.pro_product b", "a.product_id = b.id and b.is_del=0").
		Where("").
		Cols("a.id").Get(&skuid)
	return
}
