package models

import (
	"github.com/go-xorm/xorm"
	"time"
)

type ChannelProductSnapshot struct {
	Id             int       `xorm:"not null pk autoincr INT(11)"`
	ChannelId      int       `xorm:"default NULL comment('渠道id') INT(11)"`
	UserNo         string    `xorm:"default 'NULL' comment('用户编号') VARCHAR(50)"`
	FinanceCode    string    `xorm:"default 'NULL' comment('财务编码') VARCHAR(50)"`
	ProductId      int32     `xorm:"default NULL comment('商品id') INT(11)"`
	JsonData       string    `xorm:"default 'NULL' comment('json格式的快照数据 ') TEXT"`
	ProductThirdId string    `xorm:"default '' comment('第三方的商品ID') VARCHAR(255)"`
	SyncError      string    `xorm:"default '' comment('操作第三方时候的错信息') VARCHAR(255)"`
	CreateDate     time.Time `xorm:"created"`
}

type ChannelProductSnapshotExtend struct {
	*ChannelProductSnapshot `xorm:"extends"`
	ChannelLastCategoryName string `xorm:"channel_last_category_name"`
}

// ListChannelProductSnapshotByProductIds 渠道商品转换到sku完整信息
func ListChannelProductSnapshotByProductIds(db *xorm.Engine, channelId int32, pIds []int32, financeCode string) (shots []ChannelProductSnapshot, err error) {
	if err = db.Table("channel_product_snapshot").Alias("cps").
		Join("LEFT", "channel_sku cs", "cs.product_id=cps.product_id AND cs.channel_id=cps.channel_id AND cps.product_third_id<>''").
		Select("cps.product_id AS product_id,cps.sync_error AS sync_error,cs.id AS product_third_id").
		Where("cps.channel_id = ? AND cps.finance_code=?", channelId, financeCode).In("cps.product_id", pIds).Find(&shots); err != nil {
		return
	}
	return
}
