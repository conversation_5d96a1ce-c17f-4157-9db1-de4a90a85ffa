package models

type (
	RedisSimpleProduct struct {
		Id                int32
		Name              string
		SkuId             int32
		ChannelCategoryId int32
		SellingPoint      string
		ContentMobile     string
		Pic               string
		MarketPrice       int32
		RetailPrice       int32
		WeightForUnit     float64
		Skuv              []SimpleSkuv
		IsPromotion       int8               `json:"is_promotion"` //是否参与活动，0否1是
		Promotion         []*PromotionDetail `json:"promotion"`    //优惠活动信息
	}
	SimpleSkuv struct {
		SpecName       string
		SpecValueValue string
	}
	PromotionDetail struct {
		Id                             int32  `json:"id"`                              //优惠活动id
		Type                           int32  `json:"type"`                            //优惠活动类型，1满减 2限时折扣 3满减运费
		ReachMoney                     string `json:"reach_money"`                     //满足金额
		ReduceMoney                    string `json:"reduce_money"`                    //减免金额
		TimeDiscountEndDate            string `json:"timeDiscount_endDate"`            //限时折扣截止时间
		TimeDiscountType               int    `json:"timeDiscount_type"`               //限时折扣类型 0 折扣 1 固定价格
		TimeDiscountValue              int    `json:"timeDiscount_value"`              //限时折扣类型值 0 折扣率 1 固定价格值,分
		TimeDiscountLimitCountByOrder  int    `json:"timeDiscount_limitCountByOrder"`  //限时折扣每单可以下单数量  0 不限制  非0限制数量
		TimeDiscountLimitCountByStock  int    `json:"timeDiscount_limitCountByStock"`  //限时折扣可以下单数量库存限制 0 不限制  非0限制数量
		TimeDiscountRemainCountByStock int    `json:"timeDiscount_remainCountByStock"` //限时折扣当天可以下单的库存数量
		TimeDiscountMaxSkuCountByOrder int    `json:"timeDiscount_maxSkuCountByOrder"` //限时折扣每单可以下单sku数量
	}
)

//func (s *RedisSimpleProduct) MarshalBinary() ([]byte, error) {
//	return json.Marshal(s)
//}
//
//func (s *RedisSimpleProduct) UnmarshalBinary(data []byte) error {
//	return json.Unmarshal(data, s)
//}
