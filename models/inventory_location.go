package models

import (
	"fmt"
	"github.com/go-xorm/xorm"
	"time"
)

type Location struct {
	Id          int       `json:"id" xorm:"pk autoincr not null comment('主键ID') INT 'id'"`
	ChainId     int64     `json:"chain_id" xorm:"not null comment('连锁ID') BIGINT 'chain_id'"`
	StoreId     string    `json:"store_id" xorm:"default 0 comment('门店的主键') VARCHAR(50) 'store_id'"`
	WarehouseId int       `json:"warehouse_id" xorm:"not null comment('仓库ID') INT 'warehouse_id'"`
	Code        string    `json:"code" xorm:"not null comment('库位编码') VARCHAR(64) 'code'"`
	Name        string    `json:"name" xorm:"not null comment('库位名称') VARCHAR(128) 'name'"`
	ProductId   int       `json:"product_id" xorm:"not null default 0 comment('商品id') INT 'product_id'"`
	SkuId       int       `json:"sku_id" xorm:"not null default 0 comment('sku id') INT 'sku_id'"`
	CreatedTime time.Time `json:"created_time" xorm:"not null comment('创建时间') DATETIME 'created_time' created"`
	UpdatedTime time.Time `json:"updated_time" xorm:"not null comment('修改时间') DATETIME 'updated_time' updated"`
}

// TableName 表名
func (s *Location) TableName() string {
	return "eshop.inventory_location"
}

type LocationQuery struct {
	StoreId string  `json:"store_id"`
	SkuIds  []int32 `json:"sku_ids"`
}

func (s *Location) GetLocationList(db *xorm.Engine, query LocationQuery) (out map[string]Location, err error) {
	session := db.NewSession()
	defer session.Close()
	if len(query.SkuIds) > 0 {
		session = session.In("sku_id", query.SkuIds)
	}
	if len(query.StoreId) > 0 {
		session = session.Where("store_id=?", query.StoreId)
	}

	out = make(map[string]Location)
	data := make([]Location, 0)
	if err = session.Table("eshop.inventory_location").Find(&data); err != nil {
		return
	}

	for _, v := range data {
		k := fmt.Sprintf("%s_%d", v.StoreId, v.SkuId)

		out[k] = v
	}

	return
}
