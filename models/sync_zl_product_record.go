package models

type SyncZlProductRecord struct {
	Id         uint   `json:"id" xorm:"pk autoincr not null INT(10) 'id'"`
	ItemCode   string `json:"item_code" xorm:"not null default '' comment('子龙货号') VARCHAR(50) 'item_code'"`
	ZilongId   string `json:"zilong_id" xorm:"not null default '' comment('子龙门店id') VARCHAR(50) 'zilong_id'"`
	CanSell    int8   `json:"can_sell" xorm:"default 1 comment('0-不可销,1-可销') TINYINT(4) 'can_sell'"`
	DealStatus int8   `json:"deal_status" xorm:"not null default 0 comment('状态：0待处理 1已处理 2失败') TINYINT(4) 'deal_status'"`
}

type SyncZlProductRecordExtend struct {
	SyncZlProductRecord `xorm:"extends"`
	FinanceCode         string `json:"finance_code"`
	ProductId           int    `json:"product_id"`
	IsDrugs             int    `json:"is_drugs"`
	IsPrescribedDrug    int    `json:"is_prescribed_drug"`
}

type ZlCode struct {
	Code     string `json:"code"`
	ZilongId string `json:"zilong_id" xorm:"not null default '' comment('子龙门店id') VARCHAR(50) 'zilong_id'"`
}
