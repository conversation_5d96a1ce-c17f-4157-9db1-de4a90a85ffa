package models

type ChannelSkuGroup struct {
	Id             int `xorm:"not null pk autoincr INT(11)"`
	ProductId      int `xorm:"default NULL comment('组合商品关联的商品ID') INT(11)"`
	SkuId          int `xorm:"default NULL comment('组合商品关联的SKU') INT(11)"`
	GroupProductId int `xorm:"default NULL comment('组合商品product_id') INT(11)"`
	GroupSkuId     int `xorm:"default NULL comment('组合商品SKUID') INT(11)"`
	Count          int `xorm:"default NULL comment('组合的商品数量') INT(11)"`
	DiscountValue  int `xorm:"default NULL comment('折扣值（当折扣类型为1时，存百分比。折扣类型为2时，存具体设置的价格。）') INT(11)"`
	DiscountType   int `xorm:"default NULL comment('折扣类型（1按折扣优惠，2按固定价格优惠）') INT(11)"`
	MarketPrice    int `xorm:"default NULL comment('市场价') INT(11)"`
	ChannelId      int `xorm:"default NULL comment('渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)') INT(11)"`
	ProductType    int `xorm:"default NULL comment('商品类别（1-实物商品，2-虚拟商品，3-组合商品）') INT(11)"`
}
