package models

import (
	"fmt"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
)

type ProProductStoreInfo struct {
	// 主键
	Id int32 `json:"id" xorm:"pk autoincr not null INT 'id'"`
	// skuId
	SkuId int32 `json:"sku_id" xorm:"default 0 INT 'sku_id'"`

	// 商品ID
	ProductId int32 `json:"product_id" xorm:"default 0 comment('商品ID') INT 'product_id'"`

	// 门店的主键
	StoreId string `json:"store_id" xorm:"default 0 comment('门店的主键') VARCHAR(50) 'store_id'"`

	// 渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)
	ChannelId int32 `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)') INT 'channel_id'"`

	// 上下架状态（1-上架，0-下架）
	UpDownState int32 `json:"up_down_state" xorm:"default 0 comment('上下架状态（1-上架，0-下架）') INT 'up_down_state'"`

	// 1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败
	Status int32 `json:"status" xorm:"default 0 comment('1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败 ') INT 'status'"`

	// 建议价格/零售价
	RetailPrice int32 `json:"retail_price" xorm:"default 0 comment('建议价格/零售价') INT 'retail_price'"`
	//市场价
	MarketPrice int32 `json:"market_price" xorm:"default 'null' comment('市场价') INT 'market_price'"`
	// 0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功
	IsDistribution int32 `json:"is_distribution" xorm:"default 0 comment('0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功') INT 'is_distribution'"`

	// 铺品或者上架报错信息
	SyncError string `json:"sync_error" xorm:"not null comment('铺品或者上架报错信息') VARCHAR(500) 'sync_error'"`

	// 第三方回写的skuID
	SkuThirdId string `json:"sku_third_id" xorm:"default '' comment('第三方回写的skuID') VARCHAR(50) 'sku_third_id'"`

	// 商品添加日期
	CreateDate time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品添加日期') DATETIME 'create_date' created"`

	// 商品最后更新日期
	UpdateDate time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品最后更新日期') DATETIME 'update_date' updated"`
	//商品名称
	ProductName string `json:"product_name" xorm:"default '' comment('商品名称') VARCHAR(255) 'product_name'"`
	//商品描述
	ProductDesc string `json:"product_desc" xorm:"default '' comment('商品描述') VARCHAR(255) 'product_desc'"`
	ProductPic  string `json:"product_pic" xorm:"default '' comment('商品图片') VARCHAR(255) 'product_pic'"`

	//商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)
	ProductType int32 `json:"product_type" xorm:"default 1 comment('商品类型：1-商品，2-服务 3-活体') INT 'product_type'"`
	//服务时长
	ServiceDuration int32 `json:"service_duration" xorm:"default 0 comment('服务时长') INT 'service_duration'"`
	//出生日期
	BirthDate string `json:"birth_date" xorm:"default 'null' comment('出生日期') VARCHAR(255) 'birth_date'"`
	//宠物品种
	PetVariety string `json:"pet_variety" xorm:"default '' comment('宠物品种') VARCHAR(255) 'pet_variety'"`
	//宠物品种名称
	PetVarietyName string `json:"pet_variety_name" xorm:"default '' comment('宠物品种名称') VARCHAR(255) 'pet_variety_name'"`
	//条码
	BarCode string `json:"bar_code" xorm:"default 'null' comment('条码') VARCHAR(36) 'bar_code'"`
}

type ProProductStoreInfoExt struct {
	ProProductStoreInfo `xorm:"extends"`
	SkuId               int32 `json:"sku_id" xorm:"default 'null' comment('第三方类目ID') int32 'sku_id'"`
}

type ProProductStoreAppChannel struct {
	ProProductStoreInfo `xorm:"extends"`
	AppChannel          int32  `json:"app_channel" xorm:"default 0 comment('1.阿闻自有,2.TP代运营') int32 'app_channel'"`
	ChannelStoreId      string `json:"channel_store_id" xorm:"default 'null' comment('渠道门店id') VARCHAR(50) 'channel_store_id'"`
	CategoryName        string `json:"category_name" xorm:"default 'null' comment('分类名称') VARCHAR(100) 'category_name'"`
	//商品名称
	Name string `json:"name" xorm:"default 'null' comment('商品名称') VARCHAR(255) 'name'"`
	//商品卖点
	SellingPoint string `json:"selling_point" xorm:"default 'null' comment('商品卖点') VARCHAR(200) 'selling_point'"`
	//商品图片（多图）
	Pic string `json:"pic" xorm:"default 'null' comment('商品图片（多图）') VARCHAR(1000) 'pic'"`
	//电脑端详情内容
	ContentPc string `json:"content_pc" xorm:"comment('电脑端详情内容') MEDIUMTEXT 'content_pc'"`
	// 第三方类目ID
	CategoryThirdId int32 `json:"category_third_id" xorm:"default 'null' comment('第三方类目ID') int32 'category_third_id'"`
	// 第三方类目名称
	CategoryThirdName string   `json:"category_third_name" xorm:"default 'null' comment('第三方类目名称') int32 'category_third_name'"`
	Skus              []ProSku `json:"skus"`
	Attr              []ProProductChannelAttr
}

// data 是 铺品数据列表
// out1 是map[店铺id_商品id]{渠道切片}
func GetProductStoreInfo(db *xorm.Engine, where map[string]interface{}) (data []ProProductStoreInfo, out1 map[string][]ProProductStoreInfo, err error) {
	session := db.NewSession()
	defer session.Close()
	productIds, ok := where["productIds"]
	if ok {
		session = session.In("a.product_id", productIds)
	}
	productId, ok := where["productId"]
	if ok {
		session = session.Where("a.product_id=?", productId)
	}

	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("a.channel_id=?", channelId)
	}
	channelIds, ok := where["channelIds"]
	if ok {
		session = session.In("a.channel_id", channelIds)
	}

	// 未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功
	isDistribution, ok := where["isDistribution"]
	if ok {
		session = session.Where("a.is_distribution=?", isDistribution)
	}

	// 返回数据 结构体类型
	out1 = make(map[string][]ProProductStoreInfo)
	data = make([]ProProductStoreInfo, 0)
	if err = session.Table("eshop.pro_product_store_info").Alias("a").Select("a.*").Find(&data); err != nil {
		return
	}
	outType := 0
	if v, ok := where["outType"]; ok {
		outType = v.(int)
	}
	for _, v := range data {
		switch outType {
		case 1:
			k := fmt.Sprintf("%s_%d", v.StoreId, v.ProductId)
			if _, ok := out1[k]; !ok {
				out1[k] = make([]ProProductStoreInfo, 0)
			}
			out1[k] = append(out1[k], v)
		}
	}

	return
}

func GetProductStoreInfoExist(db *xorm.Engine, where map[string]interface{}) (out map[string]ProProductStoreInfo, err error) {
	session := db.NewSession()
	defer session.Close()

	productId, ok := where["productId"]
	if ok {
		session = session.Where("product_id=?", productId)
	}

	productIds, ok := where["productIds"]
	if ok {
		session = session.In("product_id", productIds)
	}

	skuIds, ok := where["skuIds"]
	if ok {
		session = session.In("sku_id", skuIds)
	}

	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("channel_id=?", channelId)
	}
	channelIds, ok := where["channelIds"]
	if ok {
		session = session.In("channel_id", channelIds)
	}

	storeId, ok := where["storeId"]
	if ok {
		session = session.Where("store_id=?", storeId)
	}

	storeIds, ok := where["storeIds"]
	if ok {
		session = session.In("store_id", storeIds)
	}

	out = make(map[string]ProProductStoreInfo)
	data := make([]ProProductStoreInfo, 0)
	if err = session.Table("eshop.pro_product_store_info").Find(&data); err != nil {
		return
	}

	for _, v := range data {
		k := fmt.Sprintf("%d_%s_%d", v.ChannelId, v.StoreId, v.SkuId)
		out[k] = v
	}
	return

}

func (m ProProductStoreInfo) UpdateProductStoreInfo(db *xorm.Engine, where map[string]interface{}, cols string) (err error) {
	session := db.NewSession()
	defer session.Close()
	id, ok := where["id"]
	if ok {
		session = session.Where("id=?", id)
	}

	productId, ok := where["productId"]
	if ok {
		session = session.Where("product_id=?", productId)
	}

	productIds, ok := where["productIds"]
	if ok {
		session = session.In("product_id", productIds)
	}

	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("channel_id=?", channelId)
	}
	channelIds, ok := where["channelIds"]
	if ok {
		session = session.In("channel_id", channelIds)
	}

	storeId, ok := where["storeId"]
	if ok {
		session = session.Where("store_id=?", storeId)
	}

	storeIds, ok := where["storeIds"]
	if ok {
		session = session.In("store_id", storeIds)
	}
	if cols != "" {
		session = session.Cols(cols)
	}
	if _, err = session.Table("eshop.pro_product_store_info").Update(&m); err != nil {
		glog.Error("更新铺品表失败，err=", err.Error())
		return
	}

	return

}

// 统计已上架、已下架、未铺品
func StatsProductStoreInfo(db *xorm.Engine, where map[string]interface{}) (out int, err error) {
	session := db.NewSession()
	defer session.Close()
	storeId, ok := where["storeId"]
	if ok {
		session = session.Where("store_id=?", storeId)
	}

	upDownState, ok := where["upDownState"]
	if ok {
		session = session.Where("up_down_state=?", upDownState)
	}
	isDistribution, ok := where["isDistribution"]
	if ok {
		session = session.Where("is_distribution=?", isDistribution)
	}
	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("channel_id=?", channelId)
	}

	_, err = session.Table("eshop.pro_product_store_info").Select("id").Get(&out)
	return
}
