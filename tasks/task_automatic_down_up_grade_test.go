package tasks

import (
	"testing"
)

func Test_AutoDown7DaysNoStock(t *testing.T) {
	AutoDown7DaysNoStock(2)
}

func Test_AutoUpHasStock(t *testing.T) {
	//AutoUpHasStock(2)
	AutoUpCansell()
}

func TestAutoUpHasStock(t *testing.T) {
	type args struct {
		tid int
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{name: "七天无库存下架的上架"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			AutoUpHasStock(1)
		})
	}
}
