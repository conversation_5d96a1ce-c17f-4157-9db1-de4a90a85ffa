package tasks

import (
	"_/models"
	"_/proto/dac"
	"_/proto/pc"
	"_/services"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	kit "github.com/tricobbler/rp-kit"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
)

const (
	syncCategoryKey        = "task:productcenter:synccategory"
	syncElmCategoryKey     = "task:productCenter:syncElmCategory"
	syncWxVideoCategoryKey = "task:productcenter:syncWxVideoCategory"

	syncNewCategoryKey          = "task:productcenter:synccategory:new"
	syncNewCategorySchedulerKey = "task:productcenter:synccategory:Scheduler"
)

// 同步新增的分类到各个渠道
func initTaskCategory(c *cron.Cron) {
	// 同步单个分类到指定平台所有门店
	//todo v6.4.0可以废弃这里的任务已经去除 改成调用分布式任务处理所有门店任务
	c.AddFunc("@every 5s", func() {
		taskCron(syncCategoryKey, func() {
			var task = services.GetFirstUnFinishedTaskList(-1, 16)
			if task != nil {
				// 更新任务状态-进行中
				updateModel := models.TaskList{
					Id:         task.Id,
					TaskStatus: 2,
				}
				services.UpdateTaskListInfo(updateModel)
				var fileUrl, taskDetail string
				// 提取参数
				var syncCategoryParams services.CategorySync
				err := json.Unmarshal([]byte(task.OperationFileUrl), &syncCategoryParams)

				if err != nil {
					taskDetail = "失败-序列化参数失败" + err.Error()
				} else {
					// 同步至第三方
					syncCategoryParams.SyncCategoryToThird()
					fileUrl, taskDetail = syncCategoryParams.Export()
				}
				// 更新任务状态-已完成
				updateModel.TaskStatus = 3
				updateModel.TaskDetail = taskDetail
				updateModel.ResulteFileUrl = fileUrl
				services.UpdateTaskListInfo(updateModel)
			}
		}, time.Hour*2)
	})

	// todo   v6.4.0 新增加的同步分类任务到第三方 分布式任务实现
	c.AddFunc("@every 5s", func() {
		taskCron(syncNewCategoryKey, SyncCategoryToThirdNew, time.Hour*4)
	})

	////分布式处理同步分类任务
	//c.AddFunc("@every 10s", pubilchProductTask(syncNewCategorySchedulerKey, services.DealAsyncTaskCategory(int32(services.SyncCategoryTaskContent)), 30))

	// 同步全部分类到指定平台指定分院
	c.AddFunc("@every 5s", func() {
		taskCron(syncCategoryKey, func() {
			// 查询待开始的任务
			var task = services.GetFirstUnFinishedTaskList(-1, 17)
			if task != nil {
				updateModel := models.TaskList{
					Id:         task.Id,
					TaskStatus: 2,
				}
				// 更新任务状态-进行中
				services.UpdateTaskListInfo(updateModel)
				var fileUrl, taskDetail string
				// 序列化参数
				var request pc.SyncChannelCategoryRequest
				err := json.Unmarshal([]byte(task.OperationFileUrl), &request)
				if err != nil {
					taskDetail = "失败:获取所有分类失败"
				} else {
					clientMt := pc.GetDcChannelProductClient()
					defer clientMt.Close()
					//获取所有分类
					out, err := new(services.Product).QueryChannelCategory(clientMt.Ctx, &pc.CategoryRequest{
						Where: &pc.Category{
							ChannelId: services.ChannelAwenId,
						},
					})
					if err != nil {
						taskDetail = "失败:获取所有分类失败"
					} else {
						// 先同步根分类
						var syncCategory services.CategorySync
						for _, v := range out.Details {
							if v.ParentId == 0 {
								syncCategory.BuildSyncCategoryToThirdParams(int(v.Id), int(v.ParentId), int(request.ChannelId), int(v.Sort), 1, v.Name, []string{}, []string{request.ChannelStoreId})
								syncCategory.SyncCategoryToThird()
							}
						}
						// 再同步二级分类
						for _, v := range out.Details {
							if v.ParentId != 0 {
								syncCategory.BuildSyncCategoryToThirdParams(int(v.Id), int(v.ParentId), int(request.ChannelId), int(v.Sort), 1, v.Name, []string{}, []string{request.ChannelStoreId})
								syncCategory.SyncCategoryToThird()
							}
						}
						fileUrl, taskDetail = syncCategory.Export()
					}
					// 更新任务状态-完成
					updateModel.TaskStatus = 3
					updateModel.TaskDetail = taskDetail
					updateModel.ResulteFileUrl = fileUrl
					services.UpdateTaskListInfo(updateModel)
				}
			}
		}, time.Hour*2)
	})

	// 同步饿了么类目列表
	c.AddFunc("0 1 * * *", syncElmCategory)

	// 同步微信视频号类目列表
	c.AddFunc("0 1 * * *", syncWxVideoCategory)

	// 分类商品移动
	c.AddFunc("@every 5s", moveCategoryProduct)
}

// 移动商品分类任务
/*
	1:  修改渠道商品分类
	2： 修改快照分类
	3： 针对已上架的需要修改上架表分类，因为快照里面也存了分类信息
	3： 同步商品到第三方
*/
func moveCategoryProduct() {

	defer func() {
		if err := recover(); err != nil {
			glog.Error("--------------moveCategoryProductErr-------------", err, string(utils.PanicTrace()))
		}
	}()

	prefix := "moveCategoryProduct定时任务执行："
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:moveCategoryProduct"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer func() {
		_, err := redis.Del(taskLock).Result()
		//glog.Info("删除锁记录：", result, " err: ", err)
		if err != nil {
			redis = services.GetRedisConn()
			redis.Del(taskLock)
		}
	}()

	// 获取定时任务
	var task = services.GetFirstUnFinishedTaskList(-1, services.MoveCategoryProduct)
	if task != nil {
		glog.Info("moveCategoryProduct任务开始", task.Id)
		prefix += fmt.Sprintf(" 定时任务id %d", task.Id)
		// 更新任务状态-进行中
		updateModel := models.TaskList{
			Id:         task.Id,
			TaskStatus: 2,
		}
		services.UpdateTaskListInfo(updateModel)
		//var fileUrl string
		// 提取参数
		var operationData pc.MoveProductVo
		err := json.Unmarshal([]byte(task.OperationFileUrl), &operationData)
		if err != nil {
			glog.Error(prefix, " 解析任务json失败：", err.Error())
			return
		}

		var service services.Product

		resData := &services.MoveCategoryResult{
			OldCategoryId: operationData.OldCategoryId,
			NewCategoryId: operationData.NewCategoryId,
			TaskId:        int(task.Id),
		}

		//1: 处理gj商品苦商品
		glog.Info(prefix, " 处理gj任务开始：", task.Id)
		product, err := service.MoveGJProduct(context.Background(), &operationData, task)
		glog.Info(prefix, " 处理gj任务结束", task.Id, " err: ", err)
		resData.ErrorData = append(resData.ErrorData, product.ErrorData...)

		//2: 处理渠道 按照渠道处理
		channelIds := []int{services.ChannelAwenId, services.ChannelMtId, services.ChannelElmId, services.ChannelJddjId, services.ChannelDigitalHealth}

		for i := range channelIds {
			channel_id := channelIds[i]

			if channel_id == services.ChannelAwenId {

				productChannel, err := service.MoveAwenCategoryProduct(context.Background(), &operationData, task)
				resData.ErrorData = append(resData.ErrorData, productChannel.ErrorData...)

				if err != nil {
					glog.Error(prefix, " 处理渠道失败", channel_id, " 任务id:", task.Id, err.Error())
				} else {
					glog.Info(prefix, " 处理渠道成功", channel_id, " 任务id:", task.Id)
				}
			}
			if channel_id == services.ChannelMtId {
				productChannel, err := service.MoveMtCategoryProduct(context.Background(), &operationData, task)
				resData.ErrorData = append(resData.ErrorData, productChannel.ErrorData...)

				if err != nil {
					glog.Error(prefix, " 处理渠道失败", channel_id, " 任务id:", task.Id, " err: ", err.Error())
				} else {
					glog.Info(prefix, " 处理渠道成功", channel_id, " 任务id:", task.Id)
				}
			}
			if channel_id == services.ChannelElmId {
				productChannel, err := service.MoveEleMeCategoryProduct(context.Background(), &operationData, task)
				resData.ErrorData = append(resData.ErrorData, productChannel.ErrorData...)
				if err != nil {
					glog.Error(prefix, " 处理渠道失败", channel_id, " 任务id:", task.Id, " err: ", err.Error())
				} else {
					glog.Info(prefix, " 处理渠道成功", channel_id, " 任务id:", task.Id)
				}

			}
			if channel_id == services.ChannelJddjId {
				productChannel, err := service.MoveJddjCategoryProduct(context.Background(), &operationData, task)
				resData.ErrorData = append(resData.ErrorData, productChannel.ErrorData...)
				if err != nil {
					glog.Error(prefix, " 处理渠道失败", channel_id, " 任务id:", task.Id, " err: ", err.Error())
				} else {
					glog.Info(prefix, " 处理渠道成功", channel_id, " 任务id:", task.Id)
				}
			}

			if channel_id == services.ChannelDigitalHealth {
				productChannel, err := service.MoveDigitalHealthCategoryProduct(context.Background(), &operationData, task)
				resData.ErrorData = append(resData.ErrorData, productChannel.ErrorData...)

				if err != nil {
					glog.Error(prefix, " 处理渠道失败", channel_id, " 任务id:", task.Id, " err: ", err.Error())
				} else {
					glog.Info(prefix, " 处理渠道成功", channel_id, " 任务id:", task.Id)
				}
			}
		}

		oldName := service.GetCategoryName(resData.OldCategoryId)
		resData.OldCategoryName = oldName
		fileUrl, desc := resData.ExportMoveProductToExcel()
		glog.Info(prefix, " 导出excle数据信息： ", fileUrl, desc)
		marshal, _ := json.Marshal(resData)
		// 更新任务为完成
		updateModel = models.TaskList{
			Id:             task.Id,
			TaskStatus:     3,
			ResulteFileUrl: fileUrl,
			ContextData:    string(marshal),
		}
		services.UpdateTaskListInfo(updateModel)
	}

	glog.Info("moveCategoryProduct任务结束", task)

}

func syncElmCategory() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	//任务已在执行
	if !redis.SetNX(syncElmCategoryKey, time.Now().Unix(), 10*time.Hour).Val() {
		glog.Info("syncElmCategory already running")
		return
	}
	var p services.Product
	p.UpdateElmCategoryList(context.Background(), &pc.CategoryListRequest{})
}

func syncWxVideoCategory() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	//任务已在执行
	if !redis.SetNX(syncWxVideoCategoryKey, time.Now().Unix(), 10*time.Hour).Val() {
		glog.Info("syncWxVideoCategory already running")
		return
	}
	var p services.Product
	p.SaveWxVideoCategoryList(context.Background(), &pc.CategoryListRequest{})
}

// v6.4.0 新的同步分类任务
func SyncCategoryToThirdNew() {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("SyncCategoryToThirdNew捕获异常重启：", err)
		}
	}()
	var task = services.GetFirstUnFinishedTaskList(-1, services.SyncCategoryTaskContent)
	if task != nil {
		// 更新任务状态-进行中
		updateModel := models.TaskList{
			Id:         task.Id,
			TaskStatus: 2,
		}
		services.UpdateTaskListInfo(updateModel)
		var fileUrl, taskDetail string
		// 提取参数
		var syncCategoryParams []services.CategorySync
		err := json.Unmarshal([]byte(task.OperationFileUrl), &syncCategoryParams)

		var details []models.TaskDetail

		details = append(details, models.TaskDetail{ // 阿闻默认都是成功
			ChannelId:  services.ChannelAwenId,
			Error:      "操作成功1个, 操作失败0个",
			AllNum:     1,
			SuccessNum: 1,
			FailNum:    0,
		})

		if err != nil {
			glog.Info("序列化参数异常： ", err.Error())
			msg := "失败-序列化参数失败"
			for _, v := range services.IntsChannel {
				details = append(details, models.TaskDetail{
					ChannelId: v,
					Error:     msg,
				})
			}

		} else {
			// 同步至第三方
			failResult := []*services.SyncCategoryToThirdParams_Result{} // 公共错误提示

			for i := range syncCategoryParams {
				// 会同步到三个渠道去执行
				/**
				1：新增
					1.1：同步分类到第三方，注意一级分类和二级分类的传参
					1.2：mt和ele区分门店，jd的不区分
					1.3：将新增的分类写入到分类表channel_category_thirdid
				2：修改
					2.1： 同步更新第三方的分类
					2.2: 分类修改不需要更新channel_category_thirdid
				3：删除
					3.1: 同步删除第三方的分类
					3.2：删除channel_category_thirdi的数据
				*/
				sync := syncCategoryParams[i]
				//StoreMasterResult, err := services.GetThirdStoreMaster(int32(sync.ChannelId), "")
				//glog.Info("获取绑定的渠道门店数量：", len(StoreMasterResult), " 渠道名称：", sync.ChannelId, " err: ", err)
				//if err != nil {
				//	failResult = append(failResult, &services.SyncCategoryToThirdParams_Result{
				//		IsSuccess: false,
				//		Message:   "获取绑定的渠道门店数量失败",
				//		ChannelId: sync.ChannelId})
				//
				//	details = append(details, models.TaskDetail{
				//		ChannelId: sync.ChannelId,
				//		Error:     "获取绑定的渠道门店数量失败",
				//	})
				//	continue
				//}
				detail := models.TaskDetail{
					ChannelId: sync.ChannelId,
				}
				var StoreMasterResult []*dac.StoreInfo
				if sync.ChannelId == services.ChannelElmId || sync.ChannelId == services.ChannelMtId {
					StoreMasterResult, err = services.GetThirdStoreMaster(int32(sync.ChannelId), "")
					if err != nil {
						glog.Error("GetThirdStoreMaster err: ", err.Error())
						detail.Error = "获取绑定的渠道门店数量异常" + err.Error()
						details = append(details, detail)
						continue
					}
				} else if sync.ChannelId == services.ChannelJddjId {
					client := dac.GetDataCenterClient()
					req := dac.GetStoreMasterListRequest{
						InfoLevel: 99,
					}
					resp, err := client.RPC.GetStoreMasterList(context.Background(), &req)
					if err != nil {
						glog.Error("GetThirdStoreMaster err: ", err.Error())
						detail.Error = "获取绑定的渠道门店数量异常" + err.Error()
						details = append(details, detail)
						continue
					}
					for i := range resp.Data {
						info := resp.Data[i]
						if info.JddjAppId == "" || info.JddjAppSecret == "" || info.JddjAppMerchantId == "" {
							continue
						}
						StoreMasterResult = append(StoreMasterResult, &dac.StoreInfo{AppChannel: info.Id})
					}

				}
				glog.Info("获取绑定的渠道门店数量：", len(StoreMasterResult), " 渠道名称：", sync.ChannelId, " err: ", err)

				err = services.NewDbConn().Where("channel_id=?", sync.ChannelId).Find(&sync.ThirdCategoryList)
				if err != nil {
					glog.Error("获取第三方分类异常：", err)
				}

				detail.AllNum = len(StoreMasterResult)

				if sync.ChannelId == services.ChannelMtId {
					//获取绑定的美团门店
					mtResult := sync.DealWithMeituanChannel(StoreMasterResult)
					detail.FailNum = len(mtResult)
					detail.SuccessNum = detail.AllNum - detail.FailNum
					failResult = append(failResult, mtResult...)

					details = append(details, detail)

				}
				if sync.ChannelId == services.ChannelElmId {
					eleResult := sync.DealWithElemeChannel(StoreMasterResult)

					detail.FailNum = len(eleResult)
					detail.SuccessNum = detail.AllNum - detail.FailNum
					failResult = append(failResult, eleResult...)

					details = append(details, detail)
				}

				if sync.ChannelId == services.ChannelJddjId {
					// jd到家按照 appid来同步 分瑞鹏和代运营同步两次即可，不需要区分门店

					syncAppChannelMap := make(map[int32]struct{}, 0)
					for i := range StoreMasterResult {
						info := StoreMasterResult[i]
						syncAppChannelMap[info.AppChannel] = struct{}{}
					}

					jddjResult := sync.DealWithJddjChannel(syncAppChannelMap)
					//jddj绑定的appid数
					detail.AllNum = len(syncAppChannelMap)
					detail.FailNum = len(jddjResult)
					detail.SuccessNum = detail.AllNum - detail.FailNum
					failResult = append(failResult, jddjResult...)

					details = append(details, detail)
				}

			}

			sync := services.CategorySync{}
			sync.Result = failResult
			fileUrl, taskDetail = sync.ExportToExcel()
			glog.Info("taskdetail : ", taskDetail)
		}

		// 更新任务状态-已完成
		updateModel.TaskStatus = 3
		updateModel.TaskDetail = kit.JsonEncode(details)
		updateModel.ResulteFileUrl = fileUrl
		services.UpdateTaskListInfo(updateModel)
	}

}
