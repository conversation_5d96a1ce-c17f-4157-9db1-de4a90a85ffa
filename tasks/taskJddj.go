package tasks

import (
	"_/enum"
	"_/models"
	"_/proto/et"
	"_/services"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"

	kit "github.com/tricobbler/rp-kit"

	els "github.com/legofun/elasticsearch"
	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	"golang.org/x/time/rate"
)

func initJddjTask(c *cron.Cron) {
	//同步京东到家品牌信息到es
	c.AddFunc("@every 12h", syncJddjBrandToEs)

	// 每天五点同步Jddj分类,定时任务有问题先去掉，boos在调用的时候没有读取到redis会自动去拉取分类信息
	//c.AddFunc("0 5 * * *", AutoJddjCategoryId)

	// 每天5点开始自动上架京东到家门店仓商品
	c.AddFunc("0 5 * * *", AutoShelvesJddj)
	// 每天5点开始自动上架京东到家前置仓商品
	c.AddFunc("0 5 * * *", AutoShelvesJddjQz)
}

func AutoJddjCategoryId() {
	glog.Info("run task AutoJddjCategoryId...")
	redis := services.GetRedisConn()
	taskLock := "boss:external:RetailGetSpTagIds:jddj:lock"
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	//任务已在执行
	if !redis.SetNX(taskLock, 1, 10*time.Minute).Val() {
		glog.Info("AutoJddjCategoryId already running")
		return
	}
	defer redis.Del(taskLock)

	client := et.GetExternalClient()
	response, err := client.JddjProduct.GetJddjCategoryList(context.Background(), &et.QueryChildCategoriesForOPRequest{StoreMasterId: 1})
	if err != nil {
		glog.Info("task AutoJddjCategoryId : ", err.Error())
		return
	}

	if response != nil && len(response.Result) > 0 {
		jdKey := "boss:external:RetailGetSpTagIds:jddj"
		jsonData, _ := json.Marshal(response.Result)
		glog.Info("task AutoJddjCategoryId successful", string(jsonData))
		redis.Set(jdKey, jsonData, 0)
	}

}

//同步京东到家品牌信息到es
func syncJddjBrandToEs() {
	defer utils.CatchPanic()

	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:lock:syncJddjBrandToEs"
	//任务已在执行
	if !redis.SetNX(taskLock, 1, 12*time.Hour).Val() {
		glog.Info("syncJddjBrandToEs already running")
		return
	}
	defer redis.Del(taskLock)

	//分页查询京东到家品牌信息
	client := et.GetExternalClient()
	defer client.Close()
	ctx := context.Background()

	var totalCount int32

	if res, err := client.JddjProduct.QueryPageBrandInfo(ctx, &et.QueryPageBrandInfoRequest{
		Fields:   []string{"BRAND_ID"},
		PageNo:   1,
		PageSize: 1,
	}); err != nil {
		glog.Error("调用QueryPageBrandInfo失败，", err)
		return
	} else {
		totalCount = res.Count
	}

	es, err := els.NewEsClient()
	if err != nil {
		glog.Error(err)
		return
	}

	pageNo := int32(0)
	pageSize := int32(50)
	pageCount := int32(math.Ceil(float64(totalCount) / float64(pageSize)))

	limit := 15
	//限流器，200/min
	limiter := rate.NewLimiter(rate.Every(305*time.Millisecond), 1)
	wg := &sync.WaitGroup{}
	ch := make(chan int8, limit)
	for pageNo = 1; pageNo <= pageCount; pageNo++ {
		ch <- 1
		wg.Add(1)

		//限流
		limiter.Wait(ctx)

		go func(pageNo int32) {
			defer func() {
				<-ch
				wg.Done()
			}()

			//查询当前页的品牌数据
			m := map[string][]string{}
			brandInfo := map[string]*et.JddjBrandInfo{}
			params := &et.QueryPageBrandInfoRequest{
				Fields:   []string{"BRAND_ID", "BRAND_NAME", "BRAND_STATUS"},
				PageNo:   pageNo,
				PageSize: pageSize,
			}
			if res, err := client.JddjProduct.QueryPageBrandInfo(ctx, params); err != nil {
				glog.Error("调用QueryPageBrandInfo失败，", err, "，params：", params)
				if strings.Contains(err.Error(), "限频") {
					time.Sleep(60 * time.Second)
				}
				return
			} else {
				m[services.JddjBrandEsIndex] = make([]string, len(res.Result))
				for k, v := range res.Result {
					id := cast.ToString(v.Id)
					brandInfo[id] = v
					m[services.JddjBrandEsIndex][k] = id
				}
			}

			args := []elastic.BulkableRequest{}
			//批量查询当前页的品牌id在es中是否存在
			if res, err := es.MgetById(m); err != nil {
				glog.Error("调用es索引"+services.JddjBrandEsIndex+" mget方法失败，", err)
			} else {
				for _, v := range res.Docs {
					//es中不存在，新增
					if !v.Found {
						args = append(args, elastic.NewBulkIndexRequest().Index(services.JddjBrandEsIndex).Id(v.Id).Doc(brandInfo[v.Id]))
						continue
					}

					orgData := new(et.JddjBrandInfo)
					if err = json.Unmarshal(v.Source, orgData); err != nil {
						glog.Error("解析京东到家品牌json失败，", err, "，json: ", v.Source)
						continue
					}
					//有更新
					if orgData.BrandStatus != brandInfo[v.Id].BrandStatus || orgData.BrandName != brandInfo[v.Id].BrandName {
						args = append(args, elastic.NewBulkIndexRequest().Index(services.JddjBrandEsIndex).Id(v.Id).Doc(brandInfo[v.Id]))
						continue
					}
				}
			}

			if len(args) == 0 {
				return
			}

			if err = es.Bulk(args); err != nil {
				glog.Error("调用es索引"+services.JddjBrandEsIndex+" bulk方法失败，", err)
				return
			}
		}(pageNo)
	}
	wg.Wait()
	close(ch)
}

// AutoShelvesJddjQz 京东到家前置仓自动上架
func AutoShelvesJddjQz() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesJddjQz"
	// 任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 1*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)

	prefix := "AutoShelvesJddjQz"
	glog.Info(prefix + " 任务开始")

	cws, err := services.GetChannelWarehouseByCategory(services.ChannelJddjId, 4)
	if err != nil {
		glog.Error(prefix+" 前置仓仓渠道关系查询异常", err.Error())
		return
	}

	glog.Info(fmt.Sprintf("%s 待处理总数%d", prefix, len(cws)))

	for k, cw := range cws {
		if list, err := services.GetQzProductSimpleInfo(cw.ChannelId, cw.WarehouseId, cw.ShopId); err != nil {
			glog.Info(prefix, " GetDsProductSimpleInfo 出错：", err.Error(), "参数：", cw.ShopId, ",", cw.WarehouseId)
		} else if len(list) > 0 {
			skuIds := make([]int, len(list))
			for _, priceSync := range list {
				skuIds = append(skuIds, priceSync.Sku)
			}
			glog.Info(fmt.Sprintf("%s 第%d个前置仓门店%s，待执行商品%v", prefix, k+1, cw.ShopId, skuIds))

			l := 50
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(list) < l {
					l = len(list)
				}
				_list := list[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					_, _ = ItemToAutoJddj(prefix, _list, 4)
				}()
				list = list[l:]
				if len(list) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)

			glog.Info(fmt.Sprintf("%s 第%d个前置门店%s执行完成", prefix, k+1, cw.ShopId))
		}
	}

	glog.Info(prefix + " 任务结束")
}

// AutoShelvesJddj 京东到家门店仓自动上架
func AutoShelvesJddj() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesJddj"
	// 任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 1*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)

	prefix := "AutoShelvesJddjQz"
	glog.Info(prefix + " 任务开始")

	cws, err := services.GetChannelWarehouseByCategory(services.ChannelJddjId, 3)
	if err != nil {
		glog.Error(prefix+"仓渠道关系查询异常", err.Error())
		return
	}

	glog.Info(fmt.Sprintf("%s 待处理门店总数%d", prefix, len(cws)))
	for k, cw := range cws {
		lists, err := services.GetProductSimpleInfo(cw.ChannelId, cw.WarehouseId, cw.ShopId, cw.WarehouseCode)
		if err != nil {
			glog.Info(prefix, " GetProductSimpleInfo 出错：", err.Error(), "参数", cw.ShopId, ",", cw.WarehouseId)
		}

		for i, _ := range lists {
			lists[i].FinanceCode = cw.ShopId
		}

		if len(lists) > 0 {
			skuIds := make([]int, 0)
			for _, priceSync := range lists {
				skuIds = append(skuIds, priceSync.Sku)
			}
			glog.Info(fmt.Sprintf("%s 第%d个门店仓门店%s，待执行商品%v", prefix, k+1, cw.ShopId, skuIds))

			l := 50
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(lists) < l {
					l = len(lists)
				}
				_list := lists[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					_, _ = ItemToAutoJddj(prefix, _list, 3)
				}()
				lists = lists[l:]
				if len(lists) == 0 {
					break
				}
			}
			// Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)

			glog.Info(fmt.Sprintf("%s 第%d个门店仓门店%s，执行完成", prefix, k+1, cw.ShopId))
		}
	}
	glog.Info(prefix + " 任务结束")
}

// ItemToAutoJddj 逐条处理
func ItemToAutoJddj(prefix string, list []models.PriceSync, warehouseCategory int32) (err error, notices []string) {
	if len(list) < 1 {
		return
	}

	engine := services.NewDbConn()
	session := engine.NewSession()
	defer session.Close()

	defer func() {
		if err != nil || len(notices) > 0 {
			glog.Info(prefix, " ItemToAutoJddj 入参：,", kit.JsonEncode(list), "，警告：", notices, "，错误：", err)
		}
	}()

	type StoreInfo struct {
		AppChannel     string `json:"app_channel"`
		ChannelStoreId string `json:"channel_store_id"`
	}

	storeInfo := new(StoreInfo)
	if has, err := session.Table("datacenter.store").Alias("s").
		Join("inner", "datacenter.store_relation r", "r.finance_code = s.finance_code and r.channel_id = 4").
		Where("s.finance_code = ?", list[0].FinanceCode).
		Select("s.app_channel,r.channel_store_id").Get(storeInfo); err != nil {
		return err, notices
	} else if !has {
		return errors.New("查询编码查询appChannel未找到"), notices
	} else if len(storeInfo.ChannelStoreId) < 1 {
		return errors.New("京东id为空"), notices
	}

	isAgency := true // 是代运营
	for _, s := range strings.Split(config.GetString("id.config.agency.operation"), ",") {
		if s == storeInfo.AppChannel {
			isAgency = false
			break
		}
	}

	var service services.Product

	// 获取grpc链接
	var client = et.GetExternalClient()
	defer client.Close()

	storeMasterId := cast.ToInt32(storeInfo.AppChannel)
	if storeMasterId == 0 {
		storeMasterId = 1
	}

	var upInfos map[int]*services.AwenProductUpInfo

	if warehouseCategory == 3 || warehouseCategory == 4 {
		productIds := make([]int, len(list))
		for i, priceSync := range list {
			productIds[i] = priceSync.ProductId
		}
		if upInfos, err = services.QueryAwenProductUpInfoByIds(productIds, int(warehouseCategory)); err != nil {
			glog.Error("ItemToAuto QueryAwenProductUpInfoByIds 出错：", err.Error())
			return
		}
	}

	for _, v := range list {
		if isAgency {
			if has, qe := session.Table("agency_product").Where("app_channel = ? and sku_id = ?", storeInfo.AppChannel, v.Sku).Exist(); qe != nil {
				notices = append(notices, "查询代运营商品"+qe.Error())
			} else if has {
				notices = append(notices, fmt.Sprintf("商品%d是代运营商品", v.Sku))
				continue
			}
		}
		if upInfo, has := upInfos[cast.ToInt(v.Sku)]; has {

			////药品不允许上架
			//if upInfo.IsDrugs == 1 {
			//	glog.Error(fmt.Sprintf("药品不允许上架:financeCode%s:sku%d: product%d", v.FinanceCode, v.Sku, v.ProductId))
			//	continue
			//}

			if err := upInfo.Check(int(warehouseCategory), v.FinanceCode, 4, ""); err != nil {
				glog.Error(fmt.Sprintf("不可销不允许上架:financeCode%s:sku%d: product%d", v.FinanceCode, v.Sku, v.ProductId))
				continue
			}

		}

		_ = session.Begin()
		ctx := context.WithValue(context.Background(), "user_info", &models.LoginUserInfo{
			UserNo: "AutoShelf",
		})

		if success, _, stock, err := service.BatchOnTheShelf(
			v.FinanceCode, int32(v.ProductId), services.ChannelJddjId, 1, session, ctx, 1, v.Price,
		); !success {
			_ = session.Rollback()
			notices = append(notices, fmt.Sprintf("上架失败，商品Id：%d，原因：%s", v.ProductId, err.Error()))
			continue
		} else {
			syncUp := func(ps models.PriceSync) (err error) {
				// 第一步同步库存
				if res, err := client.JddjProduct.UpdateStock(client.Ctx, &et.UpdateStockRequest{
					StationNo: storeInfo.ChannelStoreId,
					UserPin:   "xrp",
					SkuStockList: []*et.SkuStockList{
						{
							OutSkuId: cast.ToString(ps.Sku),
							StockQty: stock,
						},
					},
					StoreMasterId: storeMasterId,
				}); err != nil {
					return err
				} else if strings.ContainsAny(res.RetMsg, "失败") {
					return errors.New("UpdateStock " + res.RetMsg)
				}
				client = et.GetExternalClient()
				// 第二步 更新价格
				if res, err := client.JddjProduct.UpdateStationPrice(client.Ctx, &et.UpdateStationPriceRequest{
					OutStationNo: ps.FinanceCode,
					StationNo:    storeInfo.ChannelStoreId,
					SkuPriceInfoList: []*et.JddjSkuPriceInfo{{
						OutSkuId: cast.ToString(ps.Sku),
						Price:    int64(ps.Price),
					}},
					StoreMasterId: storeMasterId,
				}); err != nil {
					return err
				} else if res.Code != "0" {
					return errors.New("UpdateStationPrice " + res.Msg)
				}

				// 第三步，上架
				if res, err := client.JddjProduct.BatchUpdateVendibility(client.Ctx, &et.BatchUpdateVendibilityRequest{
					StationNo:     storeInfo.ChannelStoreId,
					UserPin:       "xrp",
					StoreMasterId: storeMasterId,
					StockVendibilityList: []*et.JddjStockVendibility{{
						DoSale: true, OutSkuId: cast.ToString(ps.Sku),
					}},
				}); err != nil {
					return err
				} else if res.RetCode != "0" {
					return errors.New("BatchUpdateVendibility " + res.RetMsg)
				}
				return
			}

			if e := syncUp(v); e != nil {
				notices = append(notices, fmt.Sprintf("商品%d出错：%s", v.Sku, e.Error()))
				_ = session.Rollback()
				continue
			}
		}

		if e := session.Commit(); e != nil {
			notices = append(notices, e.Error())
		} else {
			// 保存上架日志
			go new(services.Product).SaveChannelProductLogDetail(services.ChannelJddjId, v.ProductId, enum.RecordTypeAutoUp,
				v.FinanceCode, "AutoShelf", "AutoShelf")
		}
	}
	return
}
