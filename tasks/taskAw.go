package tasks

import (
	"_/enum"
	"_/models"
	"_/services"
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

func initAwTask(c *cron.Cron) {
	//每天凌晨4点自动上架
	c.AddFunc("0 4 * * *", AutoShelvesAw)
	// 每天凌晨1点自动上架阿闻电商仓
	c.AddFunc("0 1 * * *", AutoShelvesAwDs)
	//go AutoShelvesAw()
	// 每天凌晨0点自动上架阿闻前置仓
	c.AddFunc("0 0 * * *", AutoShelvesAwQz)
	//go AutoShelvesAwQz()
}

//自动上架--阿闻门店仓
func AutoShelvesAw() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesAw"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	glog.Info("AutoShelvesAw任务开始")
	//查询阿闻和阿闻自提的门店仓
	var product services.Product

	warehouses := make([]models.ChannelWarehouse, 0)
	warehousesChannelAwen, err := product.GezZLWarehouseList(services.ChannelAwenId)
	if err != nil {
		glog.Error("阿闻门店仓商品关系查询异常", err.Error())
		return
	}

	// 去重使用
	awenMap := make(map[string]struct{}, 0)
	for _, v := range warehousesChannelAwen {
		key := v.ShopId + ":" + cast.ToString(v.WarehouseId) + ":" + cast.ToString(v.Category)
		awenMap[key] = struct{}{}
	}
	warehouses = append(warehouses, warehousesChannelAwen...)
	warehousesChannelAwenPickUp, err := product.GezZLWarehouseList(services.ChannelAwenPickUpId)
	if err != nil {
		glog.Error("阿闻自提门店仓商品关系查询异常", err.Error())
		return
	}

	// 加入阿闻竖屏自提的门店,去重加入
	for _, v := range warehousesChannelAwenPickUp {
		v1 := v
		v1.ChannelId = 1
		key := v1.ShopId + ":" + cast.ToString(v1.WarehouseId) + ":" + cast.ToString(v1.Category)
		if _, ok := awenMap[key]; !ok {
			warehouses = append(warehouses, v1)
		}
	}

	var service services.Product
	for _, v := range warehouses {
		lists := service.GetProductSimpleInfo(v.ShopId, services.ChannelAwenId, v.WarehouseId, v.WarehouseCode)
		//根据仓库编码查询门店财务数据
		//ids := service.GetChannelWarehousesShops(services.ChannelMtId, v.WarehouseId)
		for i, _ := range lists {
			lists[i].FinanceCode = v.ShopId
		}
		if len(lists) > 0 {
			//每次处理数据100条
			l := 100
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(lists) < l {
					l = len(lists)
				}
				_list := lists[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					ItemToAuto(_list, 1, 3)
				}()
				lists = lists[l:]
				if len(lists) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)
		}
		glog.Info(fmt.Sprintf("该阿闻门店仓仓库：%s已执行完成", v.ShopId))
	}
	glog.Info("AutoShelvesAw任务结束")
}

// 自动上架--阿闻前置仓
func AutoShelvesAwQz() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesAwQz"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	glog.Info("AutoShelvesAwQz任务开始")

	// 查询阿闻前置仓和阿闻竖屏前置仓
	var prodcut services.Product
	warehouses := make([]models.ChannelWarehouse, 0)
	warehousesChannelAwen, err := prodcut.GezQZWarehouseList(services.ChannelAwenId)
	if err != nil {
		glog.Error("阿闻前置仓商品关系查询异常", err.Error())
	}

	// 去重使用
	awenMap := make(map[string]struct{}, 0)
	for _, v := range warehousesChannelAwen {
		key := v.ShopId + ":" + cast.ToString(v.WarehouseId) + ":" + cast.ToString(v.Category)
		awenMap[key] = struct{}{}
	}

	warehouses = append(warehouses, warehousesChannelAwen...)
	warehousesChannelAwenPickUp, err := prodcut.GezQZWarehouseList(services.ChannelAwenPickUpId)
	if err != nil {
		glog.Error("阿闻竖屏自提前置仓商品关系查询异常", err.Error())
	}
	// 加入阿闻竖屏自提的门店,去重加入
	for _, v := range warehousesChannelAwenPickUp {
		v1 := v
		v1.ChannelId = 1
		key := v1.ShopId + ":" + cast.ToString(v1.WarehouseId) + ":" + cast.ToString(v1.Category)
		if _, ok := awenMap[key]; !ok {
			warehouses = append(warehouses, v1)
		}
	}

	var idList []int32
	idForCode := make(map[int32][]string)
	codeForWs := make(map[string][]models.ChannelWarehouse)
	for _, v := range warehouses {
		if _, ok := idForCode[int32(v.WarehouseId)]; !ok {
			idList = append(idList, int32(v.WarehouseId))
		}
		codeForWs[v.ShopId] = append(codeForWs[v.ShopId], v)
		idForCode[int32(v.WarehouseId)] = append(idForCode[int32(v.WarehouseId)], v.ShopId)
	}

	var service services.Product
	// 查询前置仓商品和价格
	a8List := service.GetProductQzInfo(services.ChannelAwenId, idList)
	var idForA8 = make(map[int][]models.QzcPriceSync)
	for _, v := range a8List {
		idForA8[v.WarehouseId] = append(idForA8[v.WarehouseId], v)
	}
	var priceMap = make(map[string][]models.PriceSync)
	for code, ws := range codeForWs {
		// 查询当前前置仓商品和价格
		for _, w := range ws {
			if len(idForA8[w.WarehouseId]) > 0 {
				priceSyncs := idForA8[w.WarehouseId]
				priceList := service.GetProductQzCodeInfo(code, services.ChannelAwenId, w.WarehouseId, priceSyncs)
				if len(priceList) > 0 {
					priceMap[code] = append(priceMap[code], priceList...)
				}
			}
		}
	}
	for code, list := range priceMap {
		if len(list) > 0 {
			//每次处理数据100条
			l := 100
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(list) < l {
					l = len(list)
				}
				_list := list[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					ItemToAuto(_list, services.ChannelAwenId, 4)
				}()
				list = list[l:]
				if len(list) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)
		}
		glog.Info(fmt.Sprintf("该阿闻前置门店：%s已执行完成", code))
	}
	glog.Info("AutoShelvesAwQz任务结束")
}

// AutoShelvesAwDs 自动上架--阿闻电商仓
func AutoShelvesAwDs() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesAwDs"
	// 任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 1*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	glog.Info("AutoShelvesAwDs任务开始")

	cws, err := services.GetChannelWarehouseByCategory(services.ChannelAwenId, 1)
	if err != nil {
		glog.Error("阿闻电商仓渠道关系查询异常", err.Error())
	}

	var service services.Product
	// 查询所有电商仓商品和价格
	var priceMap = make(map[string][]models.PriceSync)
	for _, cw := range cws {
		if info, err := service.GetDsProductSimpleInfo(cw.ShopId, services.ChannelAwenId, cw.WarehouseId); err != nil {
			glog.Info("AutoShelvesAwDs GetDsProductSimpleInfo 出错 :", cw.ShopId, ",", cw.WarehouseId)
		} else if len(info) > 0 {
			priceMap[cw.ShopId] = append(priceMap[cw.ShopId], info...)
		}
	}

	for code, list := range priceMap {
		if len(list) > 0 {
			//每次处理数据100条
			l := 100
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(list) < l {
					l = len(list)
				}
				_list := list[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					ItemToAuto(_list, services.ChannelAwenId, 1)
				}()
				list = list[l:]
				if len(list) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)
		}
		glog.Info(fmt.Sprintf("该阿闻前置门店：%s已执行完成", code))
	}
	glog.Info("AutoShelvesAwDs任务结束")
}

//逐条处理
// warehouseCategory 1电商仓、3门店仓、4前置仓
func ItemToAuto(list []models.PriceSync, channel_id, warehouseCategory int32) {
	var service services.Product
	engine := services.NewDbConn()

	session := engine.NewSession()
	defer session.Close()

	//v6.5.8 排除雀巢马氏等配置自动上架
	FinChannel := []models.FinanceAppChannel{}
	session.SQL("select finance_code, app_channel from datacenter.store s group by finance_code , app_channel ;").Find(&FinChannel)
	channelMap := make(map[string]int32, len(FinChannel))
	for i := range FinChannel {
		channelMap[FinChannel[i].FinanceCode] = FinChannel[i].AppChannel
	}
	p := new(services.Product)
	agencyConfig, err := p.GetAgencyConfig(context.Background(), &empty.Empty{})
	if err != nil {
		glog.Error("获取代运营配置异常:", err.Error())
		return
	}

	var upInfos map[int]*services.AwenProductUpInfo

	if channel_id != services.ChannelDigitalHealth && (warehouseCategory == 3 || warehouseCategory == 4) {
		productIds := make([]int, len(list))
		for i, priceSync := range list {
			productIds[i] = priceSync.ProductId
		}
		if upInfos, err = services.QueryAwenProductUpInfoByIds(productIds, int(warehouseCategory)); err != nil {
			glog.Error("ItemToAuto QueryAwenProductUpInfoByIds 出错：", err.Error())
			return
		}
	}

	for _, v := range list {

		code := v.FinanceCode
		var appChannel int32
		if data, ok := channelMap[code]; ok {
			appChannel = data
		}

		var flag bool
		for i := range agencyConfig.ConfigData {
			if appChannel == agencyConfig.ConfigData[i] {
				flag = true
			}
		}

		if !flag {
			product, err := p.IsUpProduct(context.Background(), appChannel, 0)
			if err != nil {
				glog.Error("获取代运营商品异常：", err.Error(), " financeCode:", code)
				continue
			}
			if _, ok := product[v.Sku]; !ok {
				glog.Error(fmt.Sprintf("该商品禁止上架，如需上架请联系总部运营:financeCode%s:sku%d: product%d", v.FinanceCode, v.Sku, v.ProductId))
				continue
			}
		}

		product := services.QueryProduct(v.ProductId)
		if product.ProductType == 2 { // 虚拟商品排除自动上架
			continue
		}
		if channel_id != services.ChannelDigitalHealth && (warehouseCategory == 3 || warehouseCategory == 4) {
			if upInfo, has := upInfos[v.Sku]; has {
				if err := upInfo.Check(int(warehouseCategory), v.FinanceCode, cast.ToInt(channel_id), ""); err != nil {
					glog.Error(fmt.Sprintf("AwenProductUpInfo 上架校验失败，financeCode：%s，sku：%d，product：%d，原因：%s", v.FinanceCode, v.Sku, v.ProductId, err.Error()))
					continue
				}
			}
		} else if product.IsDrugs == 1 { // 非门店仓、前置仓无法上架药品，无售药资质的前置仓药品不会取出
			continue
		}

		session.Begin()
		ctx := context.WithValue(context.Background(), "user_info", &models.LoginUserInfo{
			UserNo: "AutoShelf",
		})
		bt, _ := json.Marshal(v)
		result_bool, _, _, err := service.BatchOnTheShelf(v.FinanceCode, int32(v.ProductId), channel_id, 1, session, ctx, 1, v.Price)
		if !result_bool {
			session.Rollback()
			glog.Info("阿闻渠道自动上架失败,err:", err, "上架参数：", string(bt))
			continue
		}
		err = session.Commit()
		if err != nil {
			session.Rollback()
			glog.Info("阿闻渠道自动上架失败,提交事务,err:", err, "上架参数：", string(bt))
		}

		// 保存上架日志
		go new(services.Product).SaveChannelProductLogDetail(cast.ToInt(channel_id), v.ProductId, enum.RecordTypeAutoUp,
			code, "AutoShelf", "AutoShelf")

		glog.Info("阿闻渠道自动上架成功，上架参数：", string(bt))
	}
}
