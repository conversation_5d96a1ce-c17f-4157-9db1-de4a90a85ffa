package tasks

import (
	"_/enum"
	"_/models"
	"_/proto/et"
	"_/proto/pc"
	"_/services"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/BurntSushi/toml"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	"github.com/tricobbler/mqgo"
	kit "github.com/tricobbler/rp-kit"
)

var AppSettings AppSetting

type AppSetting struct {
	Server struct {
		ServerTag string //服务标记
	}
}

func init() {
	toml.DecodeFile("appsetting.toml", &AppSettings)
}

//定时任务
func InitTask() {
	//根据环境变量启动定时任务
	if kit.EnvCanCron() {
		glog.Info("InitTask run...")
		go updateTempSnapshotToChannelStoreProduct()
		c := cron.New()
		//(1-阿闻，2-美团，3-饿了么，4-京东到家)
		if AppSettings.Server.ServerTag == "" || AppSettings.Server.ServerTag == "1" {
			initAwTask(c)
		}
		if AppSettings.Server.ServerTag == "" || AppSettings.Server.ServerTag == "2" {
			// 自动上架商品到美团定时任务
			initMtTask(c)
		}
		if AppSettings.Server.ServerTag == "" || AppSettings.Server.ServerTag == "3" {
			// 饿了么定时任务
			initElmTask(c)
		}
		if AppSettings.Server.ServerTag == "" || AppSettings.Server.ServerTag == "4" {
			// 京东到家定时任务
			initJddjTask(c)
		}
		// 定时任务列表处理
		initPdTask(c)

		// 过期的虚拟商品自动下架任务
		initExpireProductDown(c)

		//价格同步
		initPriceSyncTask(c)

		// 商品分类同步到第三方
		initTaskCategory(c)

		// 同步oms的商品
		initTaskOmsProduct(c)

		// 医疗互联网自动上架
		initMedicalTask(c)

		// 7天无库存自动下架和恢复库存重新上架
		initAutomaticUpOrDownTask(c)

		// 全量同步子龙不可销
		initTaskSyncZlProduct(c)

		//会员商品导入
		initExportVipGoodsTask(c)

		c.Start()
	}
	initMqTask()
}

// 定时任务调度基于redis key,保证只有一个进入的任务运行
// lockKey 任务需要锁定的key,如果不传则不锁定
// f 任务需要定时运行的函数(保证该函数为同步，尽量不要异步)
// expiration 任务key 多久自动删除
func taskCron(lockKey string, f func(), expiration time.Duration) {
	defer func() {
		// 是否有未捕获的异常
		if err := recover(); err != nil {
			glog.Error(lockKey+"-定时任务异常 ", err)
		}
	}()
	if len(lockKey) > 0 {
		// 获取redis链接
		redisConn := services.GetRedisConn()
		if kit.EnvCanCron() {
			defer redisConn.Close()
		}
		//锁定key
		lockRes := redisConn.SetNX(lockKey, 1, expiration).Val()
		if !lockRes {
			return
		}
		defer func() {
			// 删除key
			redisConn.Del(lockKey)
		}()
	}
	// 执行任务函数
	f()
}

func initMqTask() {
	//价格同步消费处理--阿闻
	go SyncProductPriceAW()

	//价格同步消费处理--美团
	go SyncProductPriceMT()

	//价格同步消费处理--饿了么
	go SyncProductPriceElm()

	//价格同步消费处理--京东
	go SyncProductPriceJD()

	// 同步前置仓价格至四个渠道
	go SyncQzcPriceMq()

	//清理美团和我们本地的数据
	go CheckMtProductDetail()

	//清理饿了么和我们本地的数据
	go CheckELMProductDetail()

	//清空没有绑定第三方的上架数据
	go DelProductDetail()
}

//更新渠道商品上下架状态
func saveChannelStoreProduct() {
	defer utils.CatchPanic()

	queue := services.SaveChannelStoreProductQueue
	mqgo.NewMqByStr(config.GetString("mq.oneself"), config.GetString("mysql.dc_product")).Consume(queue, queue, services.DatacenterExchange, func(request string) (response string, err error) {
		glog.Info("mq in.. " + queue + ", request: " + request)
		params := []*pc.ChannelStoreProduct{}
		if err := json.Unmarshal([]byte(request), &params); err != nil {
			glog.Error(err)
			return "失败", nil
		}

		services.NewChannelStoreProductTask(params)

		return "成功", nil
	})
}

func paginate(len int, skip int, size int) (limit int, start int) {
	skip = ((skip - 1) * size)

	if skip+size > len {
		limit = len
	} else {
		limit = skip + size
	}
	if skip > len {
		start = len
	} else {
		start = skip
	}
	return limit, start
}

//获取库存结果
func getSkuStock(stockMap map[string]int32, appPoiCode, skuId string) string {
	return cast.ToString(stockMap[appPoiCode+":"+skuId])
}

//批量上下架
//deprecated 已废弃
func channelProductUpElm() {
	defer utils.CatchPanic()

	glog.Info("mq start..")
	queue := services.ChannelProductUpQueueElm
	//todo 什么地方写入的呢？
	mqgo.NewMqByStr(config.GetString("mq.oneself"), config.GetString("mysql.dc_product")).Consume(queue, queue, services.DatacenterExchange, func(request string) (response string, err error) {
		glog.Info("mq in.., ", request)
		builder := strings.Builder{}
		params := map[string]interface{}{}
		if err := json.Unmarshal([]byte(request), &params); err != nil {
			builder.WriteString("系统错误：" + err.Error() + "<br>")
			glog.Error(err)
			return err.Error(), nil
		}

		//channelId := cast.ToInt32(params["channelId"])
		operateType := cast.ToInt32(params["operateType"])
		userNo := cast.ToString(params["userNo"])
		productData := cast.ToString(params["productData"])
		storesData := cast.ToStringMapString(params["storesData"])
		//financeCodes := cast.ToStringSlice(params["financeCodes"])

		clientMt := services.GetMtProductClient()
		defer clientMt.Close()

		//productObj := new(services.Product)
		ctx := context.Background()
		ctx = context.WithValue(ctx, "user_info", &models.LoginUserInfo{
			UserNo: userNo,
		})

		var foodData []*et.RetailInfo
		if err := json.Unmarshal([]byte(productData), &foodData); err != nil {
			glog.Error("json解析失败：", err)
			return "失败", err
		}
		//根据foodData中的AppFoodCode获取门店信息

		for _, appPoiCode := range storesData {
			pageIndex := 0
			pageSize := 100
			foodDataLen := len(foodData)
			for {
				//每次截取100条数据
				pageIndex++

				if (pageIndex-1)*pageSize > foodDataLen {
					break
				}
				limit, start := paginate(foodDataLen, pageIndex, pageSize)
				pageData := foodData[start:limit]

				taskData := &et.MultipoisBatchinitdataRequest{
					InitData: &et.InitData{
						RetailInfo: pageData,
					},
				}

				var skuList string
				for _, v := range taskData.InitData.RetailInfo {
					for _, v2 := range v.Skus {
						skuList += fmt.Sprintf("%s,", v2.SkuId)
						glog.Info("taskData.InitData.RetailInfo.Skus ", v2.SkuId, v2.Stock)
					}
				}

				if operateType == 1 {
					res, err := clientMt.ELMPRODUCT.OfflineElmShopSku(ctx, &et.UpdateElmShopSkuPriceRequest{
						ShopId:      appPoiCode,
						CustomSkuId: skuList,
					})
					if err != nil {
						glog.Error(err)
						continue
					}
					if res.Code != 200 {
						glog.Error("饿了么接口失败，失败原因：" + res.Error)
						continue
					} else {
						glog.Info("饿了么商品下架成功！")
					}
				} else {
					res, err := clientMt.ELMPRODUCT.OnlineElmShopSku(ctx, &et.UpdateElmShopSkuPriceRequest{
						ShopId:      appPoiCode,
						CustomSkuId: skuList,
					})
					if err != nil {
						glog.Error(err)
						continue
					}
					if res.Code != 200 {
						glog.Error("饿了么接口失败，失败原因：" + res.Error)
						continue
					} else {
						glog.Info("饿了么商品上架成功！")
					}
				}

				//转换成数据库值
				/*
					upDownState := 1
					if operateType == 1 {
						upDownState = 0
					}

					var insertData []*pc.ChannelStoreProduct
					for _, vv := range foodData {
						insertData = append(insertData, &pc.ChannelStoreProduct{
							ChannelId: channelId ,
							FinanceCode: financeCode,
							ProductId:   cast.ToInt32(vv.AppFoodCode),
							UpDownState: cast.ToInt32(upDownState),
						})
					}

					//门店上下架成功商品写入数据库
					if out, err := productObj.NewChannelStoreProduct(ctx, &pc.NewChannelStoreProductRequest{Info: insertData}); err != nil {
						glog.Error(err)
						continue
					} else if out.Code != 200 {
						glog.Error(errors.New(out.Message))
						continue
					}*/

				time.Sleep(1 * time.Second)
			}
		}

		return "成功", nil
	})
}

//将更新的快照信息中的sku，商品中名称，价格等信息更新到渠道门店商品表
func updateTempSnapshotToChannelStoreProduct() {
	glog.Info("task run...")

	t := config.GetString("temp_snapshot.json_task")
	if t == "" {
		t = "0/30 * * * * ?"
	}

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc(t, func() {
		engine := services.NewDbConn()
		redis := services.GetRedisConn()
		if kit.EnvCanCron() {
			defer redis.Close()
		}
		lockKey := "task:product-center:updateTempSnapshotToChannelStoreProduct:lock"
		updateDateKey := "task:product-center:updateTempSnapshotToChannelStoreProduct:updateDate"

		if cmd := redis.SetNX(lockKey, "1", time.Minute*120); cmd.Val() {
			defer func() {
				redis.Del(lockKey)
			}()

			updateDate := redis.Get(updateDateKey).Val()
			if updateDate == "" {
				updateDate = time.Now().Add(time.Minute * -5).Format("2006-01-02 15:04:05")
			}

			//有新的数据的时候，才跑
			if has, err := engine.Table("channel_product_snapshot").Where("update_date > ?", updateDate).Exist(); err != nil {
				glog.Error("updateTempSnapshotToChannelStoreProduct", err)
			} else if has {
				glog.Info("有新数据")
				var maxUpdateDate []string
				if err := engine.Table("channel_product_snapshot").Select("MAX(update_date)").Find(&maxUpdateDate); err != nil {
					glog.Error(err)
				}

				if _, err := engine.Exec("delete from temp_snapshot;"); err != nil {
					glog.Error("updateTempSnapshotToChannelStoreProduct", err)
				}
				if _, err := engine.Exec("insert into temp_snapshot (SELECT channel_product_snapshot.id,JSON_EXTRACT(channel_product_snapshot.json_data, '$[0].product.name') as `name`,JSON_EXTRACT(channel_product_snapshot.json_data, '$[0].product.channel_category_name') as `channel_category_name`,JSON_EXTRACT(channel_product_snapshot.json_data, '$[0].sku_info[0].market_price') as market_price,JSON_EXTRACT(channel_product_snapshot.json_data, '$[0].sku_info[0].sku_id') as sku_id FROM channel_product_snapshot WHERE channel_product_snapshot.update_date > ?);", updateDate); err != nil {
					glog.Error("updateTempSnapshotToChannelStoreProduct", err)
				}
				if _, err := engine.Exec("UPDATE temp_snapshot SET `name`=REPLACE(`name`,'\"',''),channel_category_name=REPLACE(REPLACE(channel_category_name,'\\u003e','>'),'\"','');"); err != nil {
					glog.Error("updateTempSnapshotToChannelStoreProduct", err)
				}

				if _, err := engine.Exec("UPDATE channel_store_product INNER JOIN temp_snapshot ON channel_store_product.snapshot_id=temp_snapshot.id SET channel_store_product.`name`=temp_snapshot.`name`,channel_store_product.`sku_id`=temp_snapshot.`sku_id`,channel_store_product.`channel_category_name`=temp_snapshot.`channel_category_name`,channel_store_product.`market_price`=temp_snapshot.`market_price`;"); err != nil {
					glog.Error("updateTempSnapshotToChannelStoreProduct", err)
				}

				redis.Set(updateDateKey, maxUpdateDate[0], -1)
			} else {
				glog.Info("没有数据")
			}
		}

	}); err != nil {
		glog.Error(err)
	} else {
		task.Run()
	}

}

type TaskProduct struct {
}

// 调用定时任务的grpc方法，方便测试使用
func (t *TaskProduct) ChooseTaskRun(ctx context.Context, in *pc.TaskRunVo) (*pc.TaskRunBaseResponse, error) {
	response := pc.TaskRunBaseResponse{
		Code:    200,
		Message: "",
		Error:   "",
	}
	map_data := make(map[int32]func(), 0)

	map_data[1] = AutoShelvesAw   // 阿闻门店仓自动上架
	map_data[2] = AutoShelvesAwQz //  阿闻前置仓上架

	map_data[3] = AutoShelvesMt   //  mt门店仓自动上架
	map_data[4] = AutoShelvesMtQz // mt前置仓自动上架

	map_data[5] = AutoShelvesElmZL // ele门店仓自动上架
	map_data[6] = AutoShelvesElmQz // ele前置仓自动上架

	map_data[7] = AutoShelvesMedical   //  医疗门店仓自动上架
	map_data[8] = AutoShelvesMedicalQz // 医疗前置仓自动上架

	map_data[9] = SyncQzcPriceAwen  //  阿闻前置仓价格
	map_data[10] = SyncQzcPriceMt   //  mt前置仓价格
	map_data[11] = SyncQzcPriceElm  // ele前置仓价格同步
	map_data[12] = SyncQzcPriceJddj // jd前置仓价格同步

	map_data[13] = AutoSyncPriceMedical // 互联网医院价格同步

	map_data[14] = AutoShelvesAwDs // 电商仓自动上架
	map_data[15] = SyncDsPriceAwen // 电商仓价格同步

	map_data[16] = func() {
		AutoUpHasStock(1) // 已上架的商品7天无库存下架的自动上架 只是针对实物商品
	}
	map_data[17] = func() {
		AutoDown7DaysNoStock(1) //  已上架且超过7天库存为0的商品程序自动下架： 只是针对实物商品
	}

	map_data[18] = AutoShelvesJddjQz //  京东到家前置仓自动上架
	map_data[19] = AutoShelvesJddj   //  京东到家门店仓自动上架

	map_data[20] = PushmqgoToSZ //  修复门店仓价格表product_id =0 的数据

	map_data[21] = func() {
		AutoUpCansell() // 自动上架可销
	}
	map_data[22] = func() {
		AutoDown7DaysNoStock(2) // 自动下架不可销
	}

	map_data[24] = SyncZlProductnew // 不可销下架

	map_data[26] = SyncZlProductChild // 同步子龙商品数据copy

	map_data[27] = StoreIntoMT //美团 下载每个美团每个门店的数据到MQ然后去对比处理

	map_data[28] = StoreIntoELM // 饿了么  下载每个饿了么每个门店的数据到MQ然后去对比处理

	glog.Info("手动启动定时任务：", in.Data)
	if fun, ok := map_data[in.Data]; ok {
		fun()
	}

	return &response, nil
}

func StoreIntoMT() {
	StoreIntoMQ(enum.ChannelMtId)
}

func StoreIntoELM() {
	StoreIntoMQ(enum.ChannelElmId)
}
