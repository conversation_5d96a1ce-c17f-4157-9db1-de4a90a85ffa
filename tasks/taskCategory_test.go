package tasks

import (
	"_/services"
	"testing"
)

func Test_syncToElm(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{name: ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//syncToElm()
		})
	}
}

func Test_SyncCategoryToThirdNew(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{name: ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SyncCategoryToThirdNew()
		})
	}

}

func Test_moveCategoryProduct(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{name: ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			moveCategoryProduct()
		})
	}

}
func Test_DealAsyncTaskCategory(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{name: ""},
	}

	services.SetupDB()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			services.DealAsyncTaskCategory(22)
		})
	}

}
