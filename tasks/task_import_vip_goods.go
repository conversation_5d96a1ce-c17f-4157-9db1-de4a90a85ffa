package tasks

import (
	"_/models"
	"_/proto/sh"
	"_/services"
	"_/utils"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type shopProductItem struct {
	GoodsId     string
	VipDiscount string
	VipState    string
	Sort        int
}

type awenProductItem struct {
	GoodsId     string
	VipDiscount string
}

type ImportResult struct {
	ResultUrl    string
	SuccessCount int32
	FailCount    int32
}

func initExportVipGoodsTask(c *cron.Cron) {
	//会员商品导入
	c.AddFunc("@every 5s", func() {
		ExportVipGoodsTask()
	})
}

func ExportVipGoodsTask() {
	VipGoodsTaskLock := "productcenter:task:VipGoodsTask"
	//任务已在执行
	redis := services.KeepAliveRedisConn()
	if !redis.SetNX(VipGoodsTaskLock, time.Now().Unix(), 5*time.Minute).Val() {
		return
	}
	defer func() {
		_, err := redis.Del(VipGoodsTaskLock).Result()
		if err != nil {
			redis = services.KeepAliveRedisConn()
			redis.Del(VipGoodsTaskLock)
		}
	}()
	engine := services.NewDatacenterDbConn()
	var ImportLog []models.ImportLog
	if err := engine.SQL("select * from import_log where status = 1 and type in(3,4) and (effective_time is null "+
		"or effective_time <= ?) order by id asc;", time.Now().Format("2006-01-02 15:04:05")).Find(&ImportLog); err != nil {
		glog.Error("ExportVipGoodsTask 查询会员导入记录异常", err.Error())
	}
	//测试查询
	//if err := engine.SQL("select * from import_log where id = 82").Find(&ImportLog); err != nil {
	//	glog.Error("ExportVipGoodsTask 查询会员导入记录异常", err.Error())
	//}
	if len(ImportLog) > 0 {
		for _, v := range ImportLog {
			switch v.Type {
			case 3:
				VipGoodsImport(v)
			case 4:
				//AwenVipGoodsImport(v)
			}
		}
	}
}

func VipGoodsImport(v models.ImportLog) {
	// 下载Excel文件
	resp, err := http.Get(v.ImportUrl)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer resp.Body.Close()
	var fileName = "商城会员商品导入-下载数据(" + time.Now().Format("20060102150405") + ")" + ".xlsx"
	file, err := os.Create(fileName)
	if err != nil {
		glog.Error("商城会员商品导入" + err.Error())
		return
	}
	defer os.Remove(fileName)
	defer file.Close()

	_, err = io.Copy(file, resp.Body)
	if err != nil {
		glog.Error("商城会员商品导入" + err.Error())
		return
	}
	f, err := excelize.OpenFile(fileName)
	if err != nil {
		glog.Error("商城会员商品导入" + err.Error())
		return
	}
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		glog.Error("商城会员商品导入" + err.Error())
		return
	}

	ff := excelize.NewFile()
	defer ff.Close()

	writer, _ := ff.NewStreamWriter("Sheet1")
	_ = writer.SetRow("A1", []interface{}{"说明：商品id 指商品skuid,付费会员折扣填入0-1的范围"})
	_ = writer.SetRow("A2", []interface{}{"商品id", "错误内容"})
	countMap := make(map[string]int32, 0)
	//记录错误行数
	//var ImportOut []*ImportResultErr
	result := &ImportResult{}
	Db := services.UpetNewDbConn()

	//先更新已有会员商品为0
	if _, err = Db.Exec("update upet_goods set vip_discount = 0,vip_state = 0 where (vip_discount > 0 or vip_state=1) AND store_id=?;", v.StoreId); err != nil {
		glog.Error("初始化电商会员折扣异常" + err.Error())
		return
	}
	req := new(sh.GoodsToEsUpdateRequest)
	idsStr := ""
	for i, row := range rows {
		if i <= 1 {
			continue
		}
		isSuccessText := "是"
		failReason := "成功"
		rowData := &shopProductItem{
			GoodsId:     row[0],
			VipDiscount: row[1],
			Sort:        i + 1,
		}
		idsStr += row[0] + ","

		countMap[rowData.GoodsId]++
		if countMap[rowData.GoodsId] > 1 {
			isSuccessText = "否"
			failReason = "与其他行记录重复"
		} else if err := validateShopGoods(rowData, v.StoreId); err != nil {
			failReason = err.Error()
			isSuccessText = "否"
		}
		if isSuccessText == "否" {
			result.FailCount++
		} else {
			result.SuccessCount++
		}
		_ = writer.SetRow("A"+cast.ToString(i+1), []interface{}{row[0], failReason})
	}

	idsStr = strings.TrimRight(idsStr, ",")
	if len(idsStr) > 0 {
		// 获取grpc链接
		client := sh.GetUpetCenterClient()
		req.Ids = idsStr
		req.IsVip = 1
		if _, err := client.PS.UpdateGoodsToEs(client.Ctx, req); err != nil {
			glog.Error("会员商品导入ES出错：" + err.Error())
		}
	}

	//导出错误记录
	if err = writer.Flush(); err != nil {
		glog.Error("flush文件错误：" + err.Error())
		return
	}
	result.ResultUrl, err = utils.UploadExcelToQiNiu(ff, fmt.Sprintf("商城会员商品导入%s.xlsx", time.Now().Format("20060102150405")))
	if err != nil {
		glog.Error("上传文件错误：" + err.Error())
		return
	}

	//更新导入记录
	Result := fmt.Sprintf("本次共上传%d个商品，成功导入%d个，失败%d个", result.SuccessCount+result.FailCount, result.SuccessCount, result.FailCount)

	if _, err := Db.Exec("update datacenter.import_log set status=2,num = ?,fail_num=?,result=?,result_url=? where "+
		"id = ?;", result.SuccessCount, result.FailCount, Result, result.ResultUrl, v.Id); err != nil {
		glog.Error("上更新导入记录错误：" + err.Error())
		return
	}
}

func validateShopGoods(v *shopProductItem, storeId int32) error {
	Discount, err := strconv.ParseFloat(v.VipDiscount, 64)
	if err != nil {
		return errors.New("输入不是一个合法的数字")
	}
	if Discount <= 0 || Discount >= 1 {
		return errors.New("折扣设置错误")
	}
	newDiscount, _ := decimal.NewFromFloat(Discount).Mul(decimal.NewFromInt(10)).Round(2).Float64()
	var date models.VipGoods
	engine := services.UpetNewDbConn()
	if has, err := engine.SQL(`select g.goods_id,g.goods_commonid,g.goods_name,g.goods_image,g.enable_member_price,
       g.goods_price,g.vip_state,g.vip_discount,p.promotion_type,p.promotion_price from upet_goods g left join (
 select goods_id,promotion_type, promotion_price,store_id from upet_p_time where state =1 and end_time > unix_timestamp() 
 and (log_id is null or promotion_type = 2) AND store_id=?
 )p on g.goods_id = p.goods_id where goods_verify=1 and goods_state=1 and g.goods_id = ? AND g.store_id=?`, storeId, v.GoodsId, storeId).
		Get(&date); err != nil {
		return errors.New("查询电商会员数据异常错误：" + err.Error())
	} else if !has {
		return errors.New("商品不存在")
	}

	if date.VipState == 2 {
		return errors.New("商品已设置为医保价商品")
	}

	//价格计算
	MarketPrice := int32(decimal.NewFromFloat(date.GoodsPrice * 100).Round(0).IntPart())
	var goodsPromotionPrice int32
	if date.PromotionType > 0 && (date.PromotionType != 7) {
		goodsPromotionPrice = int32(decimal.NewFromFloat(date.PromotionPrice * 100).Round(0).IntPart())
	}
	if date.PromotionType == 0 || date.PromotionType == 2 {
		price := MarketPrice
		if goodsPromotionPrice > 0 { // 折后折
			price = goodsPromotionPrice
		}
		// 付费会员价
		vip := int32(decimal.NewFromFloat(newDiscount * float64(price)).Ceil().IntPart())
		if vip <= 0 {
			return errors.New("折算后的商品价格有误")
		}
	}

	//更新商品
	if _, err := engine.Exec("update upet_goods set vip_discount= ?,vip_state=1,sort=? where goods_id = ? AND store_id=?;", newDiscount, v.Sort, v.GoodsId, storeId); err != nil {
		return errors.New("更新电商会员折扣异常" + err.Error())
	}
	return nil
}

//func AwenVipGoodsImport(v models.ImportLog) {
//	// 下载Excel文件
//	resp, err := http.Get(v.ImportUrl)
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//	defer resp.Body.Close()
//	var fileName = "到家会员商品导入-下载数据(" + time.Now().Format("20060102150405") + ")" + ".xlsx"
//	file, err := os.Create(fileName)
//	if err != nil {
//		glog.Error("到家会员商品导入" + err.Error())
//		return
//	}
//	defer os.Remove(fileName)
//	defer file.Close()
//
//	_, err = io.Copy(file, resp.Body)
//	if err != nil {
//		glog.Error("到家会员商品导入" + err.Error())
//		return
//	}
//	f, err := excelize.OpenFile(fileName)
//	if err != nil {
//		glog.Error("到家会员商品导入" + err.Error())
//		return
//	}
//	rows, err := f.GetRows("Sheet1")
//	if err != nil {
//		glog.Error("到家会员商品导入" + err.Error())
//		return
//	}
//
//	ff := excelize.NewFile()
//	defer ff.Close()
//
//	writer, _ := ff.NewStreamWriter("Sheet1")
//	_ = writer.SetRow("A1", []interface{}{"说明：商品id 指商品skuid,付费会员折扣填入0-1的范围"})
//	_ = writer.SetRow("A2", []interface{}{"商品id", "错误内容"})
//	countMap := make(map[string]int32, 0)
//	//var ImportOut []*ImportResultErr
//	result := &ImportResult{}
//	for i, row := range rows {
//		if i <= 1 {
//			continue
//		}
//		isSuccessText := "是"
//		failReason := "成功"
//		rowData := &awenProductItem{
//			GoodsId:     row[0],
//			VipDiscount: row[1],
//		}
//		if countMap[rowData.GoodsId] > 1 {
//			isSuccessText = "否"
//			failReason = "与其他行记录重复"
//		} else if err := validateAwenGoods(rowData); err != nil {
//			failReason = err.Error()
//			isSuccessText = "否"
//		}
//		if isSuccessText == "否" {
//			result.FailCount++
//			//dataErr := &ImportResultErr{
//			//	GoodsId:    row[0],
//			//	failReason: failReason,
//			//}
//			//ImportOut = append(ImportOut, dataErr)
//		} else {
//			result.SuccessCount++
//		}
//		countMap[rowData.GoodsId]++
//		_ = writer.SetRow("A"+cast.ToString(i+1), []interface{}{row[0], failReason})
//	}
//	//导出错误记录
//	//if len(ImportOut) > 0 {
//	//	for i, k := range ImportOut {
//	//		i += 2
//	//		_ = writer.SetRow("A"+cast.ToString(i+1), []interface{}{
//	//			k.GoodsId, k.failReason,
//	//		})
//	//	}
//	//}
//	if err = writer.Flush(); err != nil {
//		glog.Error("flush文件错误：" + err.Error())
//		return
//	}
//	result.ResultUrl, err = utils.UploadExcelToQiNiu(ff, fmt.Sprintf("到家会员商品导入%s.xlsx", time.Now().Format("20060102150405")))
//	if err != nil {
//		glog.Error("上传文件错误：" + err.Error())
//		return
//	}
//	//更新导入记录
//	Result := fmt.Sprintf("本次共上传%d个商品，成功导入%d个，失败%d个", result.SuccessCount+result.FailCount, result.SuccessCount, result.FailCount)
//	Db := services.NewDatacenterDbConn()
//	if _, err := Db.Exec("update import_log set status = 2,num = ?,fail_num = ?,result = ?,result_url = ? where "+
//		"id = ?;", result.SuccessCount, result.FailCount, Result, result.ResultUrl, v.Id); err != nil {
//		glog.Error("上更新导入记录错误：" + err.Error())
//		return
//	}
//}
//
//func validateAwenGoods(v *awenProductItem) error {
//	Discount, err := strconv.ParseFloat(v.VipDiscount, 64)
//	if err != nil {
//		return errors.New("输入不是一个合法的数字")
//	}
//	if Discount <= 0 || Discount >= 1 {
//		return errors.New("折扣设置错误")
//	}
//	newDiscount, _ := decimal.NewFromFloat(Discount).Mul(decimal.NewFromInt(10)).Round(2).Float64()
//	engine := services.NewDbConn()
//	var vipGoods models.ChannelSku
//	if has, err := engine.SQL("select * from dc_product.channel_sku where id =?;", v.GoodsId).Get(&vipGoods); err != nil {
//		return errors.New("查询商品数据异常错误：" + err.Error())
//	} else if !has {
//		return errors.New("商品不存在")
//	}
//	vip := int32(decimal.NewFromFloat(newDiscount * float64(vipGoods.MarketPrice)).Ceil().IntPart())
//	if vip <= 0 {
//		return errors.New("折算后的商品价格有误")
//	}
//	//更新商品
//	if _, err := engine.Exec("update channel_sku set vip_discount= ? where id = ?;", newDiscount, v.GoodsId); err != nil {
//		return errors.New("更新到家会员折扣异常" + err.Error())
//	}
//	return nil
//}
