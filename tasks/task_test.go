package tasks

import (
	"_/models"
	"_/services"
	"testing"
)

//检查美团批量任务执行结果
func Test_checkMtTask(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "T1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//checkMtTask()
			db := services.NewDbConn()
			var model []*models.ChannelProduct
			//门店下获取需要创建的商品
			err := db.<PERSON>("t1").Select("t1.id").
				Join("left", "(SELECT product_id id FROM channel_store_product WHERE channel_id=2 AND finance_code='CX0005') AS t2", "t1.id= t2.id").
				Join("left", "(select product_id id from channel_product_snapshot where channel_id=2 AND user_no='U_HDEMHLE') as t3", "t1.id= t3.id").
				Where("t1.channel_id=? and t2.id is NULL and t3.id is NULL", 2).Limit(200, 1).Find(&model)
			if err != nil {
				t.Error(err)
			}
			t.Log(model)

			var productIdSlice []int32
			for _, v := range model {
				productIdSlice = append(productIdSlice, v.Id)
			}

			t.Log(productIdSlice)
		})
	}
}

func Test_initProductDown(t *testing.T) {
	expireProductDown()
}
