package tasks

import (
	"testing"
)

func Test_syncJddjBrandToEs(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "同步京东到家品牌信息到es",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			syncJddjBrandToEs()
		})
	}
}

func Test_AutoJddjCategoryId(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "同步京东到家品牌信息到es",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			AutoJddjCategoryId()
		})
	}
}

func TestAutoShelvesJddjQz(t *testing.T) {
	tests := []struct {
		name string
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			AutoShelvesJddjQz()
		})
	}
}

func TestAutoShelvesJddj(t *testing.T) {
	tests := []struct {
		name string
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			AutoShelvesJddj()
		})
	}
}
