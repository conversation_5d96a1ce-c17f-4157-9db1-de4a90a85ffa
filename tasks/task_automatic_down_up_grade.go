package tasks

import (
	"_/enum"
	"_/models"
	"_/services"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/maybgit/glog"
	"github.com/panjf2000/ants"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

func initAutomaticUpOrDownTask(c *cron.Cron) {

	// 已上架且超过7天库存为0的商品程序自动下架；
	c.AddFunc("0 1 * * *", func() {
		AutoDown7DaysNoStock(1)
	})

	// v6.10 子龙可销变为不可销自动下架
	c.AddFunc("@every 5m", func() {
		AutoDown7DaysNoStock(2)
	})

	// 已上架的商品7天无库存下架的自动上架：之前的自动上架任务会排除上架表中的数据，所以这里单独一个定时任务
	c.AddFunc("0 3 * * *", func() {
		AutoUpHasStock(1)
	})

	// v6.10 子龙不可销变为可销也自动上架
	c.AddFunc("@every 3m", func() {
		AutoUpCansell()
	})
}

var AntsDown *ants.Pool

func init() {
	AntsDown, _ = ants.NewPool(5)
	//defer p.Release()
}

// 已上架且超过7天库存为0的商品程序自动下架： 只是针对实物商品
/**
	分渠道按照门店一个一个的去执行定时任务
	1: 查询上架表的数据
    2： 查询对应的库存数据(阿闻渠道的需要查询两个仓库的库存之和)
	3： 判断库存数据书否大于7天没有库存
	4： 没有库存的执行下架操作同步下架到第三方
	5： 保存记录日志
*/
func AutoDown7DaysNoStock(tid int) {

	defer func() {
		if err := recover(); err != nil {
			glog.Error("--------------AutoDown7DaysNoStockErr-------------", err)
		}
	}()

	prefix := fmt.Sprintf("AutoDown7DaysNoStock任务执行%d", tid)
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := fmt.Sprintf("productcenter:task:AutoDown7DaysNoStock:%d", tid)
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 12*time.Hour).Val() {
		return
	}
	defer func() {
		redis.Del(taskLock)
	}()

	glog.Info("AutoDown7DaysNoStock任务开始")

	var service services.Product

	var channelIds = []int{services.ChannelAwenId, services.ChannelMtId, services.ChannelElmId, services.ChannelJddjId}

	if tid == 2 { //互联网也要自动下架
		channelIds = append(channelIds, services.ChannelDigitalHealth)
	}
	for i := range channelIds { // 按照渠道来跑任务
		chinnel_id := channelIds[i]

		err := AntsDown.Submit(func() {
			// 查询有上架记录的门店
			fincodes, err := service.GetUpProductsFinanceCodes(chinnel_id, tid)
			if err != nil {
				glog.Error(prefix, " 查询有上架记录的门店异常： ", chinnel_id, err.Error())
				return
			}
			if len(fincodes) <= 0 {
				glog.Info(prefix, "该渠道没有上架的门店商品：", " 渠道：", chinnel_id)
				return
			}
			// 获取渠道的门店仓库配置
			financeWarehouseIds, err := service.GetFinanceWarehouseIds([]string{}, chinnel_id)
			if err != nil {
				glog.Error(prefix, " 获取渠道的门店仓库配置异常：", err.Error())
				return
			}
			//查詢門店的倉庫集合，一个仓库一个仓库执行数据
			for vi := range fincodes {
				// 查询需要对应门店仓库下面下架的商品数据
				fincodeOne := fincodes[vi]
				var skuIds = []int{}
				if warehouseIds, ok := financeWarehouseIds[fincodeOne]; ok {
					var upProducts []*models.UpProduct
					if tid == 1 {
						stocks := []*models.GoodsStock{}
						// 查询满足7天无库存的仓库的数据
						stocks, err = service.WarehouseHasNoStock(warehouseIds)
						if err != nil {
							glog.Error(prefix, " 查询满足7天无库存的仓库的数据异常：", err.Error())
							continue
						}

						if chinnel_id == services.ChannelAwenId {
							// 需要特殊处理，两个仓库都要7天没有库存才行
							if len(warehouseIds) == 2 && (warehouseIds[0] != warehouseIds[1]) {
								// 外卖和自提不同仓库
								sku, _ := service.DifferSku(stocks, warehouseIds)
								skuIds = append(skuIds, sku...)

							} else { // 渠道同仓
								for vj1 := range stocks {
									skuIds = append(skuIds, stocks[vj1].Goodsid)
								}
							}

						} else { // 其他渠道
							for vj1 := range stocks {
								skuIds = append(skuIds, stocks[vj1].Goodsid)
							}
						}
						if len(skuIds) <= 0 {
							glog.Info("该门店没有需要下架的商品数据：", fincodeOne, " 仓库：", warehouseIds, " 渠道：", chinnel_id)
							continue
						}
						// 查询渠道需要下架的商品
						upProducts, err = service.GetAllUpProducts(chinnel_id, fincodeOne, skuIds)
					} else {
						// 查询子龙不可销下架的仓库的数据
						upProducts, err = service.ChannelHasUncansell(chinnel_id, fincodeOne)
					}
					if err != nil {
						glog.Error(prefix, " 查询渠道上架商品异常 ", err.Error())
						continue
					}
					if len(upProducts) <= 0 {
						glog.Info("该门店没有需要下架的商品数据：", fincodeOne, " 仓库：", warehouseIds, " 渠道：", chinnel_id)
						continue
					}

					// 执行下架操作，调用第三方接口
					_ = service.DownProducts(tid, chinnel_id, upProducts, fincodeOne)
				}
			}
		})
		if err != nil {
			glog.Error(prefix, " 提交异步任务异常")
			return
		}
	}
	glog.Info("AutoDown7DaysNoStock任务结束")
}

/**
	1: 先查询上架表中是7天无库存下架的商品自动上架
    2： 查询改商品的库存是都 >0
	3：库存大于0恢复上架

*/
func AutoUpHasStock(tid int) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("--------------AutoUpHasStockErr-------------", err)
		}
	}()

	prefix := fmt.Sprintf("AutoUpHasStock任务执行%d", tid)
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := fmt.Sprintf("productcenter:task:AutoUpHasStock:%d", tid)
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 12*time.Hour).Val() {
		return
	}
	defer func() {
		redis.Del(taskLock)
	}()

	glog.Info("AutoUpHasStock任务开始", tid)

	var service services.Product
	var channelIds = []int{services.ChannelAwenId, services.ChannelMtId, services.ChannelElmId, services.ChannelJddjId, services.ChannelDigitalHealth}

	for i := range channelIds {
		channel_id := channelIds[i]

		glog.Info(prefix, " 处理渠道类型channelId:", channel_id, " 开始上架任务")

		err := AntsDown.Submit(func() {

			// 获取有获取7天无库存下架的门店
			finances, err := service.GetAutoDownFinance(channel_id, tid)
			if err != nil {
				glog.Error(prefix, " 获取渠道的所有门店数据异常：", channel_id, err.Error())
				return
			}

			if len(finances) <= 0 {
				glog.Info(prefix, "该渠道没有要处理的数据：", channel_id)
				return
			}

			// 获取渠道的门店仓库配置
			financeWarehouseIds, err := service.GetFinanceWarehouseIds(finances, channel_id)
			if err != nil {
				glog.Error(prefix, " 获取渠道的门店仓库配置异常：", channel_id)
				return
			}

			// 获取门店下架商品数据 ,单个门店处理
			for pi := range finances {
				financeOne := finances[pi]

				glog.Info(prefix, " 门店执行有库存上架任务开始：", financeOne, " 渠道：", channel_id)
				products, err := service.GetAutoDownProduct(channel_id, financeOne, tid)
				if err != nil {
					glog.Error(prefix, " 获取门店下架商品数据：", channel_id, err.Error())
					continue
				}

				if len(products) <= 0 {
					glog.Info(prefix, " 该门店没有需要上架的商品：", " 门店：", financeOne, " 渠道：", channel_id)
					continue
				}
				var SkuIdsId = []int{}
				for si := range products {
					SkuIdsId = append(SkuIdsId, products[si].SkuId)
				}

				// 查询库存是否已经有了
				if warehouseIds, ok := financeWarehouseIds[financeOne]; ok {

					// 查询该仓库下的商品是否来库存了
					stocks, err := service.WarehouseHasHasStock(warehouseIds, SkuIdsId)
					if err != nil {
						glog.Error(prefix, " 查询仓库的库存数据异常：", " 门店：", financeOne, " 渠道：", channel_id, err.Error())
						continue
					}

					//阿闻渠道的库存需要单独处理
					shouldUpSkuIds := []int{}
					repeatedmap := make(map[int]struct{}) // 去重两个仓库一样的情况
					for oi := range stocks {
						stock := stocks[oi]
						if channel_id == services.ChannelAwenId {
							// 仓库两个仓库纸盒
							if len(warehouseIds) == 2 && (warehouseIds[0] != warehouseIds[1]) && tid == 1 {
								// 外卖和自提不同仓库取仓库之和
								sku, _ := service.AddWarehouseSku(stocks, warehouseIds)
								shouldUpSkuIds = append(shouldUpSkuIds, sku...)
							} else {
								if _, okr := repeatedmap[stock.Goodsid]; !okr {
									shouldUpSkuIds = append(shouldUpSkuIds, stock.Goodsid)
									repeatedmap[stock.Goodsid] = struct{}{}
								}
							}
						} else {
							shouldUpSkuIds = append(shouldUpSkuIds, stock.Goodsid)
						}
					}
					if len(shouldUpSkuIds) <= 0 {
						glog.Info(prefix, " 该门店没有可上架的商品：", financeOne, " 渠道：", channel_id, " sku数据：", kit.JsonEncode(shouldUpSkuIds))
						continue
					}

					// 执行下架操作，调用第三方接口
					_ = service.UpProducts(tid, channel_id, financeOne, shouldUpSkuIds, products)

					glog.Info(prefix, " 门店执行有库存上架任务结束：", financeOne, " 渠道：", channel_id)

				}

			}

		})
		if err != nil {
			glog.Error(prefix, " 提交自动上架异步任务异常", err.Error())
			return
		}

		glog.Info(prefix, " 处理渠道类型channelId:", channel_id, " 结束渠道上架任务")
	}

	glog.Info("AutoUpHasStock任务结束")
}

func AutoUpCansell() {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("--------------AutoUpCansell err-------------", err)
		}
	}()

	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := fmt.Sprintf("productcenter:task:AutoUpCansell")
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 5*time.Minute).Val() {
		return
	}

	defer func() {
		_, err := redis.Del(taskLock).Result()
		if err != nil {
			redis = services.KeepAliveRedisConn()
			redis.Del(taskLock)
		}
	}()

	glog.Info("AutoUpCansell 任务开始")
	session := services.NewDbConn()

	var list []models.UpProduct
	if err := session.SQL("select csp.id, csp.channel_id, csp.finance_code ,csp.product_id, csp.sku_id from dc_product.channel_store_product csp " +
		"INNER JOIN dc_product.product p ON csp.product_id=p.id INNER JOIN datacenter.store s ON s.finance_code=csp.finance_code " +
		" where csp.down_type = 7 and csp.up_down_state = 0 and (p.is_drugs=0 OR (s.sell_drugs=1 AND FIND_IN_SET(csp.channel_id, s.drugs_channel_ids))) limit 5000;").Find(&list); err != nil {
		glog.Error("AutoUpCansell 查询异常:", err.Error())
	}

	if len(list) > 0 {
		l := 1000
		wg := sync.WaitGroup{}
		var gNum = runtime.NumCPU()
		channelG := make(chan bool, gNum)
		for {
			channelG <- true
			wg.Add(1)
			if len(list) < l {
				l = len(list)
			}
			_list := list[:l]
			go func() {
				defer func() {
					<-channelG
					wg.Done()
				}()
				AutoUpCansellChild(_list)
			}()
			list = list[l:]
			if len(list) == 0 {
				break
			}
		}
		//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
		wg.Wait()
	}
	glog.Info("AutoUpCansell 任务结束")
}

func AutoUpCansellChild(list []models.UpProduct) {
	for _, v := range list {
		// 执行上架操作，调用第三方接口
		down := &services.ChannelProductUpDown{
			ProductIds:   []string{cast.ToString(v.ProductId)},
			ChannelId:    v.ChannelId,
			FinanceCodes: []string{v.FinanceCode},
			UserNo:       "UpTypeCansele",
			UserName:     "UpTypeCansele",
			IsSyncPrice:  true,
			UpType:       enum.RecordTypeAutoUp,
			DownType:     7,
		}
		down.UpPorudct()
	}
}
