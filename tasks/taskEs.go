package tasks

import (
	"_/proto/es"
	"_/proto/pc"
	"_/services"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	kit "github.com/tricobbler/rp-kit"
	"time"

	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
	"github.com/robfig/cron/v3"
)

const (
	lastSyncDateRedisKey = "productcenter:task:lastSyncEsProductUpDown"
	indexName            = "channel_store_product"
	taskLock             = "productcenter:task:lock:syncChannelProductUpDownToEs"
)

// sql 查询返回的实体
type esChannelStoreProduct struct {
	Id          int
	UpDownState int
	FinanceCode string
	JsonData    string
	SkuId       string
	UpdateDate  time.Time
	Tags        string
}

func initEsTask(c *cron.Cron) {
	//检查es和mysql数据量是否一致
	c.AddFunc("@every 30s", syncChannelProductUpDownToEs)
}

// 同步渠道商品上下架状态到ES
func syncChannelProductUpDownToEs() {
	var taskLog bytes.Buffer
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	defer func() {
		if taskLog.Len() > 0 {
			glog.Info(taskLog.String())
		}
		// 发生宕机时，获取panic传递的上下文并打印
		err := recover()
		if err != nil {
			redis.Del(taskLock)
			glog.Error("syncChannelProductUpDownToEs任务异常：", err)
		}
	}()
	//任务已在执行
	if !redis.SetNX(taskLock, 1, time.Hour).Val() {
		taskLog.WriteString("syncChannelProductUpDownToEs already running")
		return
	}
	defer func() {
		redis.Del(taskLock)
	}()

	taskLog.WriteString(" syncChannelProductUpDownToEs 开始同步")
	var db = services.NewDbConn()
	// 查询最后同步日期
	var lastSyncDate = getLastSyncDate()
	taskLog.WriteString(" 最近同步时间:" + lastSyncDate)
	// 查询修改的总记录数
	var total int
	_, err := db.SQL(`select count(1) FROM dc_product.channel_store_product csp where csp.update_date>? and csp.channel_id=? `, lastSyncDate, services.ChannelAwenId).Get(&total)
	if err != nil {
		taskLog.WriteString("查询需要更新数据出错:" + err.Error())
		glog.Error(err)
	} else if total > 0 {
		taskLog.WriteString(fmt.Sprintf("需要更新数据数量 %d ", total))
		var take int = 0
		var lastUpdate time.Time
		var pageSize int = 3000
		var upCount int = 0   // 上架总数量
		var downCount int = 0 // 下架总数量
		for true {
			// 查询分页数据
			var channelproducts []esChannelStoreProduct
			err := db.SQL(`select csp.Id,csp.up_down_state,csp.finance_code,csp.sku_id,cps.json_data,csp.update_date,
									REPLACE(REPLACE(CONCAT(pt.species,',', pt.varieties,',', pt.sex,',', pt.shape,',', pt.age,',', pt.special_stage,',', pt.is_sterilization,',', pt.content_type,',', pt.status),'不限,',''),',不限','') as tags
								FROM dc_product.channel_store_product csp INNER JOIN dc_product.channel_product_snapshot cps ON cps.id = csp.snapshot_id 
										LEFT JOIN dc_product.product_tag pt on pt.sku_id=csp.sku_id AND pt.product_type=3 
										where csp.update_date>? and csp.channel_id=?
										order by csp.Id asc limit ?,?`, lastSyncDate, services.ChannelAwenId, take, pageSize).Find(&channelproducts)
			if err != nil {
				glog.Error(err)
			} else if len(channelproducts) > 0 {
				take = take + len(channelproducts)

				client := es.NewEsClient()
				bulkRequest := client.Bulk()
				for _, channelproduct := range channelproducts {
					// 查询更新最后时间
					if channelproduct.UpdateDate.After(lastUpdate) {
						lastUpdate = channelproduct.UpdateDate
					}
					// 生成Id
					var id = fmt.Sprintf("%s-%d-%s", channelproduct.FinanceCode, services.ChannelAwenId, channelproduct.SkuId)
					// 保存到es
					if channelproduct.UpDownState == 1 {
						upCount++
						// es 文档对象
						var productData pc.ChannelProductRequest
						json.Unmarshal([]byte(channelproduct.JsonData), &productData)
						// 赋值标签
						productData.Tags = channelproduct.Tags               // 标签
						productData.FinanceCode = channelproduct.FinanceCode // 财务编码
						bulkRequest.Add(elastic.NewBulkIndexRequest().Index(indexName).Id(id).Doc(productData))
					} else {
						downCount++
						bulkRequest.Add(elastic.NewBulkDeleteRequest().Index(indexName).Id(id))
					}
				}
				// 批量执行
				if _, err := bulkRequest.Do(context.Background()); err != nil {
					glog.Error("批量写入es日志错误：" + err.Error())
				}
			}

			// 是否湿最后一页
			if len(channelproducts) < pageSize {
				break
			}
		}
		// 写入最后更新时间
		setLastSyncDate(lastUpdate)
		taskLog.WriteString(fmt.Sprintf("，上架%d个sku-shop,下架%d个sku-shop", upCount, downCount))
	} else {
		taskLog.WriteString("没有需要更新的数据")
	}

}

// 查询rediskey
func getLastSyncDate() string {
	redisConn := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}
	resCmd := redisConn.Get(lastSyncDateRedisKey)
	if resCmd != nil {
		_, err := time.ParseInLocation("2006-01-02 15:04:05", resCmd.Val(), time.Local)
		if err == nil {
			return resCmd.Val()
		}
	}
	// 返回到系统上线时间
	return time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local).Format("2006-01-02 15:04:05")
}

// 设置redis key
func setLastSyncDate(lastSyncDate time.Time) {
	redisConn := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}
	redisConn.Set(lastSyncDateRedisKey, lastSyncDate.Format("2006-01-02 15:04:05"), 0)
}
