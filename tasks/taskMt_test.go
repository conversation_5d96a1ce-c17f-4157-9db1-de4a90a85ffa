package tasks

import (
	"_/services"
	"_/utils"
	"fmt"
	"testing"

	"github.com/robfig/cron/v3"
)

func TestAutoShelvesMt(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func Test_initMtTask(t *testing.T) {
	type args struct {
		c *cron.Cron
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{name: "测试定时任务"},
	}
	isok := services.DealAsyncTaskListByChannel(2)
	isok()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func Test_syncCategoryToElm(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func Test_syncCategoryToMt(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{name: "测试"},
	}
	for _, tt := range tests {

		// 美团、饿了么、京东不能认领为药品的商品
		var drugsFailProductId []int32

		//未认领的组合ID
		drugsFailSet := utils.NewSet(drugsFailProductId...)
		fmt.Println(drugsFailSet)

		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func TestAutoShelvesMtQz(t *testing.T) {

	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{
			"每天3点自动上架",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			AutoShelvesMt()
		})
	}
}
