package tasks

import (
	"_/models"
	"_/proto/dac"
	"_/proto/dc"
	"_/services"
	"_/utils"
	"bytes"
	"encoding/json"
	"runtime"
	"strings"
	"sync"
	"time"

	kit "github.com/tricobbler/rp-kit"

	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"github.com/tricobbler/mqgo"
)

func initPriceSyncTask(c *cron.Cron) {

	//检查es和mysql数据量是否一致
	c.AddFunc("@every 60s", SyncProductPriceSync)
	//价格同步落地到本地
	c.AddFunc("@every 60s", Pushmqgo)
	//这里是处理北京有，深圳没有的数据，如果后面深圳也有了，需要做处理的 每天11点执行
	c.AddFunc("0 23 * * *", PushmqgoToSZ)

	//开启定时任务做推送到mq
	c.AddFunc("@every 60s", PushMqData)

	// 前置仓同步失败的价格信息推送至mq
	c.AddFunc("@every 60s", PushQzcPriceToMq)

	// 每晚0点更新前一天有更新过的前置仓价格
	c.AddFunc("0 0 * * *", SyncQzcPriceAwen) // 阿闻
	c.AddFunc("0 0 * * *", SyncDsPriceAwen)  // 阿闻电商仓价格同步

	c.AddFunc("0 0 * * *", SyncQzcPriceMt)   // 美团
	c.AddFunc("0 0 * * *", SyncQzcPriceElm)  // 饿了么
	c.AddFunc("0 0 * * *", SyncQzcPriceJddj) // 京东
}

// 北京价格同步，把数据全部一条一条处理好
func SyncProductPriceSync() {
	var taskLog bytes.Buffer
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "product-center:task-lock:product-price"
	//任务已在执行
	if !redis.SetNX(taskLock, 30, time.Minute).Val() {
		glog.Info("SyncProductPriceSync already running")
		return
	}
	defer func() {
		redis.Del(taskLock)
		if taskLog.Len() > 0 {
			glog.Info(taskLog.String())
		}
	}()
	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		err := recover()
		if err != nil {
			redis.Del(taskLock)
			glog.Error("SyncProductPriceSync：", err)
		}
	}()
	glog.Info(" SyncProductPriceSync 开始处理")

	var list []models.PriceSyncResponse
	var db = services.NewDbConn()
	db.Where("enable=1").Limit(100).Find(&list)

	if len(list) <= 0 {
		redis.Del(taskLock)
		glog.Info("SyncProductPriceSync无数据处理,", time.Now())
	}

	glog.Info("SyncProductPriceSync 开始处理,共:", list)
	gNum := runtime.NumCPU()
	channelG := make(chan bool, gNum)
	wg := new(sync.WaitGroup)

	for _, v := range list {
		channelG <- true
		wg.Add(1)
		v := v
		go func() {
			defer func() {
				<-channelG
				wg.Done()
			}()
			DataOpeater(v)
		}()
	}

	wg.Wait()
	close(channelG)
}

func DataOpeater(Response models.PriceSyncResponse) {
	var db = services.NewDbConn()
	querysession := db.NewSession()
	defer querysession.Close()
	var insert_list []models.PriceSync
	var update_list []models.PriceSync
	var params []models.SyncProductPrice
	err := json.Unmarshal([]byte(Response.Response), &params)
	if err != nil {
		glog.Error("反序列化失败 error:", err.Error())
		return
	}

	glog.Info("反序列化结果：", params)
	var ProductCodeList []string
	for _, n := range params {
		for _, i := range n.ProductInfo {
			ProductCodeList = append(ProductCodeList, i.ProductCode)
		}
	}
	if len(ProductCodeList) <= 0 {
		return
	}
	var SkuThirdList []models.SkuThird
	l := 2
	for {
		if len(ProductCodeList) < l {
			l = len(ProductCodeList)
		}
		_ProductCodeList := ProductCodeList[:l]
		var _SkuThirdList []models.SkuThird
		if len(_ProductCodeList) > 0 {
			querysession.Where("1=1").And("erp_id=4").In("third_sku_id", _ProductCodeList).Find(&_SkuThirdList)
			for _, s := range _SkuThirdList {
				SkuThirdList = append(SkuThirdList, s)
			}
		}
		ProductCodeList = ProductCodeList[l:]
		if len(ProductCodeList) <= 0 {
			break
		}
	}
	/*if len(SkuThirdList) <= 0 {
		return
	}*/

	//查询子龙在本地是否存在的信息
	var exist_list []models.PriceSync

	//财务编码的长度限制20
	list_map := make([]map[string]string, 0)
	//params只有一个
	for _, p := range params {
		for _, s := range p.StructCode {
			for _, r := range p.ProductInfo {
				mapStr := make(map[string]string, 0)
				mapStr[s+":"+r.ProductCode] = r.ProductCode
				list_map = append(list_map, mapStr)
			}
		}
	}
	if len(list_map) <= 0 {
		return
	}
	s_l := 20
	for {
		if len(list_map) < s_l {
			s_l = len(list_map)
		}
		_list_map := list_map[:s_l]

		var finance_code_array []string
		var ZlProductid []string
		for _, m := range _list_map {
			for s, s2 := range m {
				strarr := strings.Split(s, ":")
				finance_code_array = append(finance_code_array, strarr[0])
				ZlProductid = append(ZlProductid, s2)
			}
		}
		var sql_list []models.PriceSync
		sql_finance_code_array := strings.Join(finance_code_array, "','")
		sql_ZlProductid := strings.Join(ZlProductid, "','")
		querysession.Where("finance_code in ('" + sql_finance_code_array + "')").And(" zl_productid in ('" + sql_ZlProductid + "') ").Find(&sql_list)
		for _, v := range sql_list {
			exist_list = append(exist_list, v)
		}
		list_map = list_map[s_l:]
		if len(list_map) <= 0 {
			break
		}
	}
	exist_list_map := make(map[string]models.PriceSync, len(exist_list))
	for _, v := range exist_list {
		exist_list_map[v.FinanceCode+":"+v.ZlProductid] = v
	}
	SkuThirdListMap := make(map[string]models.SkuThird, len(SkuThirdList))
	for _, t := range SkuThirdList {
		SkuThirdListMap[t.ThirdSkuId] = t
	}
	defer querysession.Close()

	for _, p := range params {
		for _, s := range p.StructCode {
			for _, r := range p.ProductInfo {
				taxRate, _ := decimal.NewFromString(r.SellPrice)
				kk := taxRate.Mul(decimal.NewFromInt(100))
				newstring := cast.ToString(kk)
				price_int := cast.ToInt(newstring)
				//直接修改
				if ex, ok := exist_list_map[s+":"+r.ProductCode]; ok {
					ex.Price = price_int
					ex.Enable = 1
					//_, err := session.Id(ex.Id).Update(ex)
					update_list = append(update_list, ex)
					if err != nil {
						glog.Error("修改失败，err=", err, "具体数据=", r)
						continue
					}
				} else {
					if res, ok := SkuThirdListMap[r.ProductCode]; ok {
						var model models.PriceSync
						model.FinanceCode = s
						model.ZlProductid = r.ProductCode
						model.Price = price_int
						model.ProductId = cast.ToInt(res.ProductId)
						model.Enable = 1
						model.Sku = cast.ToInt(res.SkuId)
						insert_list = append(insert_list, model)
					} else {
						var model models.PriceSync
						model.FinanceCode = s
						model.ZlProductid = r.ProductCode
						model.Price = price_int
						model.ProductId = 0
						model.Sku = 0
						model.Enable = 1
						insert_list = append(insert_list, model)
					}
				}
			}
		}
	}
	//处理新增的数据
	if len(insert_list) > 0 {
		add_l := 100
		for {
			if len(insert_list) < add_l {
				add_l = len(insert_list)
			}
			_insert_list := insert_list[:add_l]
			session := db.NewSession()
			session.Begin()
			defer session.Close()
			_, err := session.Insert(&_insert_list)
			if err != nil {
				glog.Error("处理新增的数据失败--新增,err=", err)
				session.Rollback()
			}
			err = session.Commit()
			if err != nil {
				glog.Error("处理新增的数据失败--提交,err=", err)
				session.Rollback()
			}
			insert_list = insert_list[add_l:]
			if len(insert_list) <= 0 {
				break
			}
		}
	}

	//处理修改的数据
	if len(update_list) > 0 {
		update_l := 100
		for {
			if len(update_list) <= update_l {
				update_l = len(update_list)
			}
			session := db.NewSession()
			session.Begin()
			defer session.Close()
			_update_list := update_list[:update_l]
			for _, v := range _update_list {
				_, err := session.Id(v.Id).Cols("price,enable").Update(&v)
				if err != nil {
					session.Rollback()
					glog.Error("处理修改的数据失败--修改,err=", err)
					break
				}
			}
			err = session.Commit()
			if err != nil {
				glog.Error("处理修改的数据失败--提交,err=", err)
				session.Rollback()
			}
			update_list = update_list[update_l:]
			if len(update_list) <= 0 {
				break
			}
		}
	}
	Response.Enable = 0
	db.Id(Response.Id).Cols("enable", "last_time").Update(&Response)
}

// todo 把要推送的数据落地到本地然后开启定时任务自己去查询推送
// 这里是处理北京和深圳都有的数据
func Pushmqgo() {
	var taskLog bytes.Buffer
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "product-center:task-lock-local:pushmq-product-price"
	//任务已在执行
	if !redis.SetNX(taskLock, 1, time.Minute).Val() {
		glog.Info("Pushmqgo already running")
		return
	}
	defer func() {
		redis.Del(taskLock)
		if taskLog.Len() > 0 {
			glog.Info(taskLog.String())
		}
	}()
	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		err := recover()
		if err != nil {
			redis.Del(taskLock)
			glog.Error("Pushmqgo：", err)
		}
	}()
	glog.Info(" Pushmqgo 开始处理")

	//推送当天的数据，防止处理出错，需要增加手动处理，可以把未处理数据的时间修改为当天，让定时任务重新跑一次
	var list []models.PriceSync
	var db = services.NewDbConn()
	//每次从数据库里查询4000条数据执行
	db.Where("enable=1").Where("product_id>0").Where("sku>0").Limit(4000).Find(&list)
	if len(list) <= 0 {
		//无数据处理
		return
	}
	gNum := runtime.NumCPU()
	channelG := make(chan bool, gNum)
	var wg sync.WaitGroup
	for _, v := range list {
		channelG <- true
		wg.Add(1)
		v := v
		go func() {
			defer func() {
				<-channelG
				wg.Done()
			}()
			//todo 处理业务逻辑
			PushMqToLocal(v)
		}()
	}
	wg.Wait()
	close(channelG)
}

// 这里是处理北京有，深圳没有的数据，如果后面深圳也有了，需要做处理的
func PushmqgoToSZ() {
	var taskLog bytes.Buffer
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "product-center:task-lock-local-tosz:pushmq-product-price"
	//任务已在执行
	if !redis.SetNX(taskLock, 1, time.Minute).Val() {
		glog.Info("Pushmqgo already running")
		return
	}
	defer func() {
		redis.Del(taskLock)
		if taskLog.Len() > 0 {
			glog.Info(taskLog.String())
		}
	}()
	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		err := recover()
		if err != nil {
			redis.Del(taskLock)
			glog.Error("Pushmqgo：", err)
		}
	}()
	glog.Info(" PushmqgoToSZ 开始处理")
	var conn = services.NewDbConn()

	// 以前的代码处理不了北京有的深圳没有数据，直接join数据update
	sql := `update dc_product.price_sync ps
join dc_product.sku_third st on ps.zl_productid = st.third_sku_id and st.erp_id =4
set ps.product_id = st.product_id, ps.sku = st.sku_id 
where ps.product_id = 0 ; `
	exec, err := conn.Exec(sql)
	if err != nil {
		glog.Error("更新价格表和本地商品数据关联关系：", " 异常: ", err.Error())
		return
	}

	glog.Info("更新价格表和本地商品数据关联关系：", " 结束: ", exec)

}

// todo 把要推送的数据落地到本地然后开启定时任务自己去查询推送
func PushMqToLocal(v models.PriceSync) {
	var db = services.NewDbConn()
	session := db.NewSession()
	session.Begin()
	defer session.Close()
	//1. 根据仓库编码查询绑定门店记录
	var warehouserList = make([]*models.WarehouseRelationShop, 0)
	if err := db.SQL(`select wrs.* from dc_dispatch.warehouse_relation_shop wrs left join dc_dispatch.warehouse w on 
    wrs.warehouse_id = w.id where w.code =? and w.category=3 and wrs.channel_id < 5 group by wrs.shop_id,wrs.channel_id;`, v.FinanceCode).Find(&warehouserList); err != nil {
		glog.Error("PushMqToLocal 仓库编码查询绑定门店失败 err:", err)
	}
	v.Enable = 0
	session.ID(v.Id).Cols("enable").Update(&v)
	if len(warehouserList) > 0 {
		for _, vv := range warehouserList {
			v.FinanceCode = vv.ShopId
			data_json, err := json.Marshal(v)
			if err != nil {
				glog.Error("推送mq 反序列化 err:", err.Error())
			}
			var insert_model []models.PriceSyncRecord
			insert_model = append(insert_model, models.PriceSyncRecord{
				PriceSyncId: int(v.Id),
				ChannelId:   vv.ChannelId,
				Content:     string(data_json),
				IsPush:      0,
			})

			var list []models.PriceSyncRecord
			session.Where("price_sync_id=?", v.Id).Get(&list)
			if len(list) <= 0 {
				_, err = session.Insert(&insert_model)
				if err != nil {
					session.Rollback()
					glog.Error("PushMqToLocal 事务insert err:", err.Error())
				}
			}
		}
	}
	err := session.Commit()
	if err != nil {
		session.Rollback()
		glog.Error("PushMqToLocal 事务提交 err:", err.Error())
	}
}

// 开启定时任务做推送到mq
func PushMqData() {
	var taskLog bytes.Buffer
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "product-center:task-lock-mq:pushmq-product-price"
	//任务已在执行
	if !redis.SetNX(taskLock, 1, time.Minute).Val() {
		glog.Info("Pushmqgo already running")
		return
	}
	defer func() {
		redis.Del(taskLock)
		if taskLog.Len() > 0 {
			glog.Info(taskLog.String())
		}
	}()
	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		err := recover()
		if err != nil {
			redis.Del(taskLock)
			glog.Error("Pushmqgo：", err)
		}
	}()
	glog.Info(" Pushmqgo 开始处理")
	var engine = services.NewDbConn()
	var list []models.PriceSyncRecord
	engine.SQL("SELECT id,price_sync_id,channel_id,content,is_push,create_time,last_time FROM price_sync_record " +
		"WHERE is_push=0 and channel_id < 5 ORDER BY id DESC LIMIT 4000").Find(&list)

	glog.Info("价格同步需要推送mq的数据有：", len(list), list)
	if len(list) == 0 {
		return
	}
	//每次处理数据100条
	l := 100
	//写协程
	var wg sync.WaitGroup
	var gNum = runtime.NumCPU()
	channelG := make(chan bool, gNum)
	for {
		channelG <- true
		wg.Add(1)
		if len(list) < l {
			l = len(list)
		}
		_list := list[:l]
		go func() {
			defer func() {
				<-channelG
				wg.Done()
			}()
			PushSyncData(_list, engine)
		}()
		list = list[l:]
		if len(list) == 0 {
			break
		}
	}
	//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
	wg.Wait()
	close(channelG)
}

// 正式推送到mq，并修改推送状态
func PushSyncData(list []models.PriceSyncRecord, engine *xorm.Engine) {
	var mapMQId = make([]int, len(list))
	mqmap := make(map[int]string, 4)
	mqmap[1] = services.ProductPriceSyncAW
	mqmap[2] = services.ProductPriceSyncMT
	mqmap[3] = services.ProductPriceSyncElm
	mqmap[4] = services.ProductPriceSyncJD
	for _, v := range list {
		m := mqgo.SyncMqInfo{
			Exchange: "datacenter",
			Queue:    mqmap[v.ChannelId],
			RouteKey: mqmap[v.ChannelId],
			Request:  v.Content,
		}
		if m.RouteKey == "" || m.Queue == "" {
			glog.Error("推送mq未成功,err:RouteKey or Queue 不能为空")
			continue
		}
		if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
			//未成功,跳过推送下一个
			glog.Error("推送mq未成功,err:", err)
			continue
		}
		mapMQId = append(mapMQId, v.Id)
	}
	if len(mapMQId) > 0 {
		session := engine.NewSession()
		defer session.Close()
		session.Begin()
		session.In("id", mapMQId).Cols("is_push").Update(&models.PriceSyncRecord{IsPush: 1})
		session.Commit()
	}
}

// 消费处理数据--阿闻
func SyncProductPriceAW() {
	defer utils.CatchPanic()
	glog.Info("mq start..SyncProductPriceAW")
	queue := services.ProductPriceSyncAW
	mqgo.NewMqByStr(config.GetString("mq.oneself"), config.GetString("mysql.dc_product")).Consume(queue, queue, services.DatacenterExchange, func(request string) (response string, err error) {
		glog.Info("mq in.., SyncProductPriceAW", request)
		params := models.PriceSync{}
		if err := json.Unmarshal([]byte(request), &params); err != nil {
			glog.Error("SyncProductPriceAW=", err)
			return err.Error(), nil
		}

		disclient := services.GetDispatchClient()
		defer disclient.Close()

		res, err := disclient.RPC.GetShopWarehouseInfoByFinanceCode(disclient.Ctx, &dc.GetShopWarehouseInfoByFinanceCodeRequest{
			ChannelId:   services.ChannelAwenId,
			FinanceCode: params.FinanceCode,
		})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCode失败，", err)
			return "失败", nil
		}
		if res.Code == 400 { // 没有查询到,再查询渠道自提
			res, err = disclient.RPC.GetShopWarehouseInfoByFinanceCode(disclient.Ctx, &dc.GetShopWarehouseInfoByFinanceCodeRequest{
				ChannelId:   services.ChannelAwenPickUpId,
				FinanceCode: params.FinanceCode,
			})
		}

		if res.Warehouseinfo.Category == 3 {
			var channelProduct services.ChannelProductPriceSync
			channelProduct.ChannelId = 1
			channelProduct.ProductId = cast.ToString(params.ProductId)
			channelProduct.FinanceCode = params.FinanceCode
			channelProduct.ProductSkuId = cast.ToString(params.Sku)
			channelProduct.ProductThirdSkuId = params.ZlProductid
			channelProduct.ProductSyncPrice = params.Price
			channelProduct.SyncPrice()
		}

		return "成功", nil
	})
}

// 消费处理数据--美团
func SyncProductPriceMT() {
	defer utils.CatchPanic()
	glog.Info("mq start..SyncProductPriceMT")
	queue := services.ProductPriceSyncMT
	mqgo.NewMqByStr(config.GetString("mq.oneself"), config.GetString("mysql.dc_product")).Consume(queue, queue, services.DatacenterExchange, func(request string) (response string, err error) {
		glog.Info("mq in..SyncProductPriceMT, ", request)
		params := models.PriceSync{}
		if err := json.Unmarshal([]byte(request), &params); err != nil {
			glog.Error(err)
			return err.Error(), nil
		}
		if params.Id <= 0 {
			glog.Info("消费处理数据无数据处理：", request)
			return "成功", nil
		}

		disclient := services.GetDispatchClient()
		defer disclient.Close()

		res, err := disclient.RPC.GetShopWarehouseInfoByFinanceCode(disclient.Ctx, &dc.GetShopWarehouseInfoByFinanceCodeRequest{
			ChannelId:   services.ChannelMtId,
			FinanceCode: params.FinanceCode,
		})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCode失败，", err)
			return "失败", nil
		}

		if res.Warehouseinfo.Category == 3 {
			services := services.Product{}
			services.MTPriceEx(params)
			return "成功", nil
		}

		return "成功", nil
	})
}

// 消费处理数据--饿了么
func SyncProductPriceElm() {
	//defer utils.CatchPanic()
	glog.Info("mq start..SyncProductPriceElm")
	defer func() {
		// 是否有未捕获的异常
		if err := recover(); err != nil {
			glog.Error("周翔错误捕获1", err)
		}
	}()
	queue := services.ProductPriceSyncElm
	mqgo.NewMqByStr(config.GetString("mq.oneself"), config.GetString("mysql.dc_product")).Consume(queue, queue, services.DatacenterExchange, func(request string) (response string, err error) {

		glog.Info("mq in..SyncProductPriceElm, ", request)
		params := models.PriceSync{}
		if err := json.Unmarshal([]byte(request), &params); err != nil {
			glog.Error(err)
			return err.Error(), nil
		}
		if params.Id <= 0 {
			glog.Info("消费处理数据无数据处理：", request)
			return "成功", nil
		}
		disClient := services.GetDispatchClient()
		defer disClient.Close()
		/*
			res, err := disClient.RPC.GetWarehouseInfoByFanceCodes(disClient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{params.FinanceCode}})
			if err != nil {
				glog.Info(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
				if strings.Contains(err.Error(), "NotFound") {
					return "成功", nil
				}
				return "失败", err
			}
			warehouseMap := make(map[string]int32, len(res.Data))
			for _, v := range res.Data {
				if v.Category == 3 {
					warehouseMap[v.Code] = v.Category
				}
			}
			if _, ok := warehouseMap[params.FinanceCode]; ok {
				productServices := services.Product{}
				_, err = productServices.ELMPriceEx(params)
				if err != nil {
					glog.Info("修改饿了么价格报错", err.Error())
				}
			}
		*/
		res, err := disClient.RPC.GetShopWarehouseInfoByFinanceCode(disClient.Ctx, &dc.GetShopWarehouseInfoByFinanceCodeRequest{
			ChannelId:   services.ChannelElmId,
			FinanceCode: params.FinanceCode,
		})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCode失败，", err)
			return "失败", nil
		}

		if res.Warehouseinfo.Category == 3 {
			productServices := services.Product{}
			_, err = productServices.ELMPriceEx(params)
			if err != nil {
				glog.Info("修改饿了么价格报错", err.Error())
			}
		}
		return "成功", nil
	})
}

// 消费处理数据--京东
func SyncProductPriceJD() {
	defer utils.CatchPanic()
	glog.Info("mq start..SyncProductPriceJD")
	queue := services.ProductPriceSyncJD
	mqgo.NewMqByStr(config.GetString("mq.oneself"), config.GetString("mysql.dc_product")).Consume(queue, queue, services.DatacenterExchange, func(request string) (response string, err error) {
		glog.Info("mq in..SyncProductPriceJD, ", request)
		params := models.PriceSync{}
		if err := json.Unmarshal([]byte(request), &params); err != nil {
			glog.Error(err)
			return err.Error(), nil
		}

		disclient := services.GetDispatchClient()
		defer disclient.Close()
		/*
			res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{params.FinanceCode}})
			if err != nil {
				glog.Info(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
				if strings.Contains(err.Error(), "NotFound") {
					return "成功", nil
				}
				return "失败", err
			}
			warehouseMap := make(map[string]int32, len(res.Data))
			for _, v := range res.Data {
				if v.Category == 3 {
					warehouseMap[v.Code] = v.Category
				}
			}

			if _, ok := warehouseMap[params.FinanceCode]; ok {
				if params.Id <= 0 {
					glog.Info("消费处理数据无数据处理：", request)
					return "成功", nil
				}

				services := services.Product{}
				services.JDPriceEx(params)
			}
		*/
		res, err := disclient.RPC.GetShopWarehouseInfoByFinanceCode(disclient.Ctx, &dc.GetShopWarehouseInfoByFinanceCodeRequest{
			ChannelId:   services.ChannelJddjId,
			FinanceCode: params.FinanceCode,
		})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCode失败，", err)
			return "失败", nil
		}

		if res.Warehouseinfo.Category == 3 {
			if params.Id <= 0 {
				glog.Info("消费处理数据无数据处理：", request)
				return "成功", nil
			}

			services := services.Product{}
			services.JDPriceEx(params)
		}
		return "成功", nil
	})
}

func SyncProductTest() {
	//SyncProductPriceAW()
	//SyncProductPriceMT()
	//SyncProductPriceElm()
	SyncProductPriceJD()
}

// 1. 首先查出前一天有更新的价格信息
func GetQzcUpdatePrice() []models.QzcPriceSync {
	conn := services.NewDbConn()
	var priceList []models.QzcPriceSync
	t, _ := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	yesterdayTime := t.AddDate(0, 0, -1).Unix() - 28800
	startTime := time.Unix(yesterdayTime, 0).Format("2006-01-02 15:04:05")
	conn.Where("last_time >= ?", startTime).And("last_time < NOW()").And("product_id > 0").OrderBy("last_time asc").Find(&priceList)
	return priceList
}

// 2. 查询涉及到的仓库id对应的门店id
// 3. 将门店对应修改的商品价格信息，并查询该商品是否在该门店上架
// 4. 将有上架的商品在当前门店更新线上价格
func GetShopIdWithWarehouseId(channel_id int32) map[string][]models.QzcPriceSync {
	priceList := GetQzcUpdatePrice()
	if len(priceList) == 0 {
		return nil
	}

	// 仓库id对应的有更新的价格信息
	var idForQzcPrice = make(map[int][]models.QzcPriceSync)
	for _, v := range priceList {
		idForQzcPrice[v.WarehouseId] = append(idForQzcPrice[v.WarehouseId], v)
	}

	// 获取仓库id对应的门店id
	clientDis := services.GetDispatchClient()
	defer clientDis.Close()
	// 查询前置仓门店
	//resDis, err := clientDis.RPC.GetWarehouseRelation(clientDis.Ctx, &dc.Empty{})
	//if err != nil {
	//	glog.Error("查询GetWarehouseRelation失败，err:", err)
	//	return nil
	//}

	product := new(services.Product)
	warehouses := make([]models.ChannelWarehouse, 0)
	warehousesChannelAwen, err := product.GetChannelWarehouses([]string{}, channel_id)
	if err != nil {
		glog.Error("查询GetWarehouseRelation失败，err:", err)
		return nil
	}

	// 去重使用
	awenMap := make(map[string]struct{}, 0)
	for _, v := range warehousesChannelAwen {
		key := v.ShopId + ":" + cast.ToString(v.WarehouseId) + ":" + cast.ToString(v.Category)
		awenMap[key] = struct{}{}
	}
	warehouses = append(warehouses, warehousesChannelAwen...)

	if channel_id == services.ChannelAwenId {
		warehousesChannelAwenPickUp, _ := product.GetChannelWarehouses([]string{}, services.ChannelAwenPickUpId)
		// 加入阿闻竖屏自提的门店,去重加入
		for _, v := range warehousesChannelAwenPickUp {
			v1 := v
			v1.ChannelId = 1
			key := v1.ShopId + ":" + cast.ToString(v1.WarehouseId) + ":" + cast.ToString(v1.Category)
			if _, ok := awenMap[key]; !ok {
				warehouses = append(warehouses, v1)
			}
		}
	}

	// 仓库id对应的门店id
	var idForCode = make(map[int][]string)
	for _, dis := range warehouses {
		if _, ok := idForQzcPrice[int(dis.WarehouseId)]; ok {
			idForCode[int(dis.WarehouseId)] = append(idForCode[int(dis.WarehouseId)], dis.ShopId)
		}
	}

	// 门店id对应的有更新的价格信息
	var codeForQzcPrice = make(map[string][]models.QzcPriceSync)
	for id, codes := range idForCode {
		for _, code := range codes {
			codeForQzcPrice[code] = idForQzcPrice[id]
		}
	}
	return codeForQzcPrice
}

// 更新阿闻前置仓价格
func SyncQzcPriceAwen() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:SyncQzcPriceAwen"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	codeForPrice := GetShopIdWithWarehouseId(services.ChannelAwenId)
	if codeForPrice == nil {
		return
	}
	var codeList []string
	for code, _ := range codeForPrice {
		codeList = append(codeList, code)
	}
	for code, prices := range codeForPrice {
		glog.Info("更新阿闻上架商品前置仓价格, 门店: ", code)
		getAwenQzcOrDsStoreUp(code, 4, prices)
	}
}

// 查询阿闻 前置仓/电商仓 门店上架并更新价格
func getAwenQzcOrDsStoreUp(code string, warehouseCategory int32, qzcPrices []models.QzcPriceSync) {
	var productIds []int
	var proIdForQzcPrice = make(map[int]models.QzcPriceSync)
	for _, v := range qzcPrices {
		productIds = append(productIds, v.ProductId)
		proIdForQzcPrice[v.ProductId] = v
	}

	conn := services.NewDbConn()
	session := conn.NewSession()
	defer session.Close()
	var upProIds []int // 实物商品
	err := conn.Table("channel_store_product").Select("product_id").
		Where("up_down_state = 1 and channel_id = 1 and finance_code = ?", code).
		In("product_id", productIds).Find(&upProIds)
	if err != nil {
		glog.Errorf("查询阿闻上架失败, 门店: %s, err: %s", code, err.Error())
		return
	}
	// 上架的实物商品
	var upProMap = make(map[int]int)
	for _, v := range upProIds {
		upProMap[v] = 1
	}

	var upGroupProIds []models.ChannelSkuGroup // 组合商品
	err = conn.Table("channel_store_product").Alias("a").
		Join("inner", "channel_sku_group b", "a.product_id = b.product_id").
		Where("a.up_down_state = 1 and a.channel_id = 1 and b.channel_id = 1 and a.finance_code = ?", code).
		In("group_product_id", productIds).Select("b.*").Find(&upGroupProIds)
	if err != nil {
		glog.Errorf("查询阿闻上架失败, 门店: %s, err: %s", code, err.Error())
		return
	}
	var groupProMap = make(map[int][]int32)
	for _, v := range upGroupProIds {
		groupProMap[v.GroupProductId] = append(groupProMap[v.GroupProductId], int32(v.ProductId))
	}

	// 更新线上实物商品价格
	for _, v := range productIds {
		if value, ok := proIdForQzcPrice[v]; ok {
			session.Begin()
			if _, ok := upProMap[v]; ok {
				// 更新快照和上下架信息
				err = services.UpdateSnapshotAndStoreUp(session, code, services.ChannelAwenId, int(warehouseCategory), v, value.Price)
				if err != nil {
					session.Rollback()
					if warehouseCategory == 4 {
						err = services.QzcPriceErrToMq(code, "", services.ChannelAwenId, v, value.SkuId, value.Price)
						if err != nil {
							glog.Error("阿闻前置仓价格同步失败，推入mq失败")
						}
					}
					continue
				}
				services.UpdateGroupProductPrice(code, services.ChannelAwenId, warehouseCategory, int32(value.ProductId), int32(value.Price), nil, cast.ToInt32(value.SkuId))
			} else if proList, ok := groupProMap[v]; ok { // 更新该子商品的组合商品的价格
				services.UpdateGroupProductPrice(code, services.ChannelAwenId, warehouseCategory, int32(value.ProductId), int32(value.Price), proList, cast.ToInt32(value.SkuId))
			}

			if err := session.Commit(); err != nil {
				session.Rollback()
				glog.Error("事务提交失败,", err)
			}
		}
	}
}

// 更新阿闻电商仓价格
func SyncDsPriceAwen() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:SyncDsPriceAwen"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)

	var priceSyncs []models.QzcPriceSync
	var err error

	defer func() {
		if err != nil {
			glog.Info("SyncDsPriceAwen 出错：" + err.Error())
		}
	}()

	db := services.NewDbConn()

	if err = db.Table("dc_product.mall_product_price").Alias("mpp").
		Join("inner", "dc_dispatch.warehouse w", "w.code = mpp.warehouse_code and w.category = 1").
		Join("inner", "dc_product.sku s", "s.id = mpp.sku_id").
		Select("w.id as warehouse_id,w.name as warehouse_name,s.product_id,mpp.price,mpp.sku_id").
		Where("mpp.update_date >= ?", time.Now().AddDate(0, 0, -1).Add(-10*time.Minute).Format(kit.DATETIME_LAYOUT)). // 最近一天有更新的
		Find(&priceSyncs); err != nil {
		return
	}

	syncMaps := make(map[int][]models.QzcPriceSync)
	// 按仓处理
	for _, price := range priceSyncs {
		syncMaps[price.WarehouseId] = append(syncMaps[price.WarehouseId], price)
	}

	for warehouseId, sm := range syncMaps {
		var shopIds []string
		if err = db.Table("dc_dispatch.warehouse_relation_shop").Where("warehouse_id = ? and channel_id = 1", warehouseId).
			Select("shop_id").Find(&shopIds); err != nil {
			return
		}
		for _, shopId := range shopIds {
			getAwenQzcOrDsStoreUp(shopId, 1, sm)
		}
	}
}

// 更新美团前置仓价格
func SyncQzcPriceMt() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:SyncQzcPriceMt"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	codeForPrice := GetShopIdWithWarehouseId(services.ChannelMtId)
	if codeForPrice == nil {
		return
	}
	var codeList []string
	for code, _ := range codeForPrice {
		codeList = append(codeList, code)
	}

	clientDac := dac.GetDataCenterClient()
	defer clientDac.Close()

	var codeMap = make(map[string]string)
	if out, err := clientDac.RPC.QueryStoresChannelId(clientDac.Ctx, &dac.StoreRelationUserRequest{
		FinanceCode: codeList,
		Psize:       int32(len(codeList)),
		ChannelId:   services.ChannelMtId,
	}); err != nil {
		glog.Errorf("获取美团id失败; err: %s", err.Error())
		return
	} else {
		if out.Code == 200 && len(out.Data) > 0 {
			for _, v := range out.Data {
				codeMap[v.FinanceCode] = v.ChannelStoreId
			}
		}
	}
	for code, prices := range codeForPrice {
		if _, ok := codeMap[code]; ok {
			glog.Info("更新美团上架商品前置仓价格, 门店: ", code)
			getMtQzcStoreUp(code, codeMap[code], prices)
		}
	}
}

// 查询美团前置仓门店上架并更新价格
func getMtQzcStoreUp(code, storeId string, qzcPrices []models.QzcPriceSync) {
	var productIds []int
	var proIdForQzcPrice = make(map[int]models.QzcPriceSync)
	for _, v := range qzcPrices {
		productIds = append(productIds, v.ProductId)
		proIdForQzcPrice[v.ProductId] = v
	}

	conn := services.NewDbConn()
	session := conn.NewSession()
	defer session.Close()
	var upProIds []int
	err := conn.Table("channel_store_product").Select("product_id").
		Where("up_down_state = 1 and channel_id = 2 and finance_code = ?", code).
		In("product_id", productIds).Find(&upProIds)
	if err != nil {
		glog.Errorf("查询美团上架失败, 门店: %s, err: %s", code, err.Error())
		return
	}

	//var upGroupProIds []models.ChannelSkuGroup // 组合商品
	//err = conn.Table("channel_store_product").Alias("a").
	//	Join("inner", "channel_sku_group b", "a.product_id = b.product_id").
	//	Where("a.up_down_state = 1 and a.channel_id = 1 and b.channel_id = 1 and a.finance_code = ?", code).
	//	In("group_product_id", productIds).Select("b.*").Find(&upGroupProIds)
	//if err != nil {
	//	glog.Errorf("查询阿闻上架失败, 门店: %s, err: %s", code, err.Error())
	//	return
	//}
	//var groupProMap = make(map[int][]int32)
	//for _, v := range upGroupProIds {
	//	groupProMap[v.GroupProductId] = append(groupProMap[v.GroupProductId], int32(v.ProductId))
	//}

	// 更新线上价格

	for _, v := range upProIds {
		if value, ok := proIdForQzcPrice[v]; ok {
			err = services.UpdateMtQzc(session, code, storeId, v, value.SkuId, value.Price)
			if err != nil {
				err = services.QzcPriceErrToMq(code, storeId, services.ChannelMtId, v, value.SkuId, value.Price)
				if err != nil {
					glog.Error("美团前置仓价格同步失败，推入mq失败")
				}
			}
			// 组合商品更新
			glog.Info("mt update data product_id : ", v)
			services.UpdateGroupProductPrice(code, services.ChannelMtId, 4, int32(value.ProductId), int32(value.Price), nil, cast.ToInt32(value.SkuId))

		}
	}
}

// 更新饿了么前置仓价格
func SyncQzcPriceElm() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:SyncQzcPriceElm"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	codeForPrice := GetShopIdWithWarehouseId(services.ChannelElmId)
	if codeForPrice == nil {
		return
	}
	var codeList []string
	for code, _ := range codeForPrice {
		codeList = append(codeList, code)
	}

	clientDac := dac.GetDataCenterClient()
	defer clientDac.Close()

	var codeMap = make(map[string]string)
	if out, err := clientDac.RPC.QueryStoresChannelId(clientDac.Ctx, &dac.StoreRelationUserRequest{
		FinanceCode: codeList,
		Psize:       int32(len(codeList)),
		ChannelId:   services.ChannelElmId,
	}); err != nil {
		glog.Errorf("获取饿了么id失败; err: %s", err.Error())
		return
	} else {
		if out.Code == 200 && len(out.Data) > 0 {
			for _, v := range out.Data {
				codeMap[v.FinanceCode] = v.ChannelStoreId
			}
		}
	}
	for code, prices := range codeForPrice {
		if _, ok := codeMap[code]; ok {
			glog.Info("更新饿了么上架商品前置仓价格, 门店: ", code)
			getElmQzcStoreUp(code, codeMap[code], prices)
		}
	}
}

// 查询饿了么前置仓门店上架并更新价格
func getElmQzcStoreUp(code, storeId string, qzcPrices []models.QzcPriceSync) {
	var productIds []int
	var proIdForQzcPrice = make(map[int]models.QzcPriceSync)
	for _, v := range qzcPrices {
		productIds = append(productIds, v.ProductId)
		proIdForQzcPrice[v.ProductId] = v
	}

	conn := services.NewDbConn()
	session := conn.NewSession()
	defer session.Close()
	var upProIds []int
	err := conn.Table("channel_store_product").Select("product_id").
		Where("up_down_state = 1 and channel_id = 3 and finance_code = ?", code).
		In("product_id", productIds).Find(&upProIds)
	if err != nil {
		glog.Errorf("查询饿了么上架失败, 门店: %s, err: %s", code, err.Error())
		return
	}

	// 更新线上价格

	for _, v := range upProIds {
		if value, ok := proIdForQzcPrice[v]; ok {
			err = services.UpdateElmQzc(session, code, storeId, v, value.SkuId, value.Price)

			//todo ele价格
			services.UpdateGroupProductPrice(code, services.ChannelElmId, 4, int32(v), int32(value.Price), nil, cast.ToInt32(value.SkuId))
			if err != nil {
				err = services.QzcPriceErrToMq(code, storeId, services.ChannelElmId, v, value.SkuId, value.Price)
				if err != nil {
					glog.Error("饿了么前置仓价格同步失败，推入mq失败")
				}
			}
		}
	}
}

// 更新京东前置仓价格
func SyncQzcPriceJddj() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:SyncQzcPriceJddj"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	codeForPrice := GetShopIdWithWarehouseId(services.ChannelJddjId)
	if codeForPrice == nil {
		return
	}
	var codeList []string
	for code, _ := range codeForPrice {
		codeList = append(codeList, code)
	}

	clientDac := dac.GetDataCenterClient()
	defer clientDac.Close()

	var codeMap = make(map[string]string)
	if out, err := clientDac.RPC.QueryStoresChannelId(clientDac.Ctx, &dac.StoreRelationUserRequest{
		FinanceCode: codeList,
		Psize:       int32(len(codeList)),
		ChannelId:   services.ChannelJddjId,
	}); err != nil {
		glog.Errorf("获取京东id失败; err: %s", err.Error())
		return
	} else {
		if out.Code == 200 && len(out.Data) > 0 {
			for _, v := range out.Data {
				codeMap[v.FinanceCode] = v.ChannelStoreId
			}
		}
	}
	for code, prices := range codeForPrice {
		if _, ok := codeMap[code]; ok {
			glog.Info("更新京东上架商品前置仓价格, 门店: ", code)
			getJddjQzcStoreUp(code, codeMap[code], prices)
		}
	}
}

// 查询京东前置仓门店上架并更新价格
func getJddjQzcStoreUp(code, storeId string, qzcPrices []models.QzcPriceSync) {
	var productIds []int
	var proIdForQzcPrice = make(map[int]models.QzcPriceSync)
	for _, v := range qzcPrices {
		productIds = append(productIds, v.ProductId)
		proIdForQzcPrice[v.ProductId] = v
	}

	conn := services.NewDbConn()
	session := conn.NewSession()
	defer session.Close()
	var upProIds []int
	err := conn.Table("channel_store_product").Select("product_id").
		Where("up_down_state = 1 and channel_id = 4 and finance_code = ?", code).
		In("product_id", productIds).Find(&upProIds)
	if err != nil {
		glog.Errorf("查询京东上架失败, 门店: %s, err: %s", code, err.Error())
		return
	}

	// 更新线上价格

	for _, v := range upProIds {
		if value, ok := proIdForQzcPrice[v]; ok {
			err = services.UpdateJddjQzc(session, code, storeId, v, value.SkuId, value.Price)
			//todo jddj组合价格
			services.UpdateGroupProductPrice(code, services.ChannelJddjId, 4, int32(v), int32(value.Price), nil, cast.ToInt32(value.SkuId))
			if err != nil {
				err = services.QzcPriceErrToMq(code, storeId, services.ChannelJddjId, v, value.SkuId, value.Price)
				if err != nil {
					glog.Error("京东前置仓价格同步失败，推入mq失败")
				}
			}
		}
	}
}

func PushQzcPriceToMq() {
	var taskLog bytes.Buffer
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "product-center:task-lock-qzc:push-qzc-price"
	//任务已在执行
	if !redis.SetNX(taskLock, 1, time.Minute).Val() {
		glog.Info("PushQzcPriceToMq already running")
		return
	}
	defer func() {
		redis.Del(taskLock)
		if taskLog.Len() > 0 {
			glog.Info(taskLog.String())
		}
	}()
	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		err := recover()
		if err != nil {
			redis.Del(taskLock)
			glog.Error("PushQzcPriceToMq：", err)
		}
	}()
	glog.Info("PushQzcPriceToMq 开始处理")
	var engine = services.NewDbConn()
	var list []models.QzcPriceSyncMq
	engine.SQL("SELECT id,channel_id,content,is_push,create_time,update_time FROM qzc_price_sync_mq WHERE is_push=0 ORDER BY id DESC LIMIT 4000").Find(&list)

	if len(list) == 0 {
		return
	}
	//每次处理数据100条
	l := 100
	//写协程
	var wg sync.WaitGroup
	var gNum = runtime.NumCPU()
	channelG := make(chan bool, gNum)
	for {
		channelG <- true
		wg.Add(1)
		if len(list) < l {
			l = len(list)
		}
		_list := list[:l]
		go func() {
			defer func() {
				<-channelG
				wg.Done()
			}()
			PushQzcSyncData(_list, engine)
		}()
		list = list[l:]
		if len(list) == 0 {
			break
		}
	}
	//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
	wg.Wait()
	close(channelG)
}

// 正式推送到mq，并修改推送状态
func PushQzcSyncData(list []models.QzcPriceSyncMq, engine *xorm.Engine) {
	var mapMQId = make([]int, len(list))
	for _, v := range list {
		m := mqgo.SyncMqInfo{
			Exchange: "datacenter",
			Queue:    services.A8PriceSync,
			RouteKey: services.A8PriceSync,
			Request:  v.Content,
		}
		if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
			//未成功,跳过推送下一个
			glog.Error("推送mq未成功,err:", err)
			continue
		}
		mapMQId = append(mapMQId, v.Id)
	}
	if len(mapMQId) > 0 {
		session := engine.NewSession()
		defer session.Close()
		session.Begin()
		session.In("id", mapMQId).Cols("is_push").Update(&models.QzcPriceSyncMq{IsPush: 1})
		session.Commit()
	}
}

// 处理同步失败的前置仓价格
func SyncQzcPriceMq() {
	defer utils.CatchPanic()
	glog.Info("mq start..SyncQzcPriceMq")
	queue := services.A8PriceSync
	mqgo.NewMqByStr(config.GetString("mq.oneself"), config.GetString("mysql.dc_product")).Consume(queue, queue, services.DatacenterExchange, func(request string) (response string, err error) {
		glog.Info("mq in..SyncQzcPriceMq, ", request)
		params := models.QzcMqContent{}
		if err := json.Unmarshal([]byte(request), &params); err != nil {
			glog.Error(err)
			return err.Error(), nil
		}
		if params.Price <= 0 {
			glog.Info("消费处理数据无数据处理：", request)
			return "成功", nil
		}

		product := new(services.Product)

		session := services.NewDbConn().NewSession()
		defer session.Close()
		switch params.ChannelId {
		case services.ChannelAwenId:
			warehousesAwen, _ := product.GetChannelWarehouses([]string{params.FinanceCode}, services.ChannelAwenId)
			warehousesAwenPickUp, _ := product.GetChannelWarehouses([]string{params.FinanceCode}, services.ChannelAwenPickUpId)
			warehousesAwen = append(warehousesAwen, warehousesAwenPickUp...)

			flag := false
			for i := range warehousesAwen {
				warehouse := warehousesAwen[i]
				if warehouse.Category == 4 || warehouse.Category == 5 {
					flag = true
				}
			}
			if flag {
				err = services.UpdateSnapshotAndStoreUp(session, params.FinanceCode, services.ChannelAwenId, 4, params.ProductId, params.Price)
				if err != nil {
					return "失败", err
				}
			}

		case services.ChannelMtId:
			warehouses, _ := product.GetChannelWarehouses([]string{params.FinanceCode}, services.ChannelMtId)
			flag := false
			for i := range warehouses {
				warehouse := warehouses[i]
				if warehouse.Category == 4 || warehouse.Category == 5 {
					flag = true
				}
			}
			if flag {
				err = services.UpdateMtQzc(session, params.FinanceCode, params.StoreId, params.ProductId, params.SkuId, params.Price)
				if err != nil {
					return "失败", err
				}
			}

		case services.ChannelElmId:
			warehouses, _ := product.GetChannelWarehouses([]string{params.FinanceCode}, services.ChannelElmId)
			flag := false
			for i := range warehouses {
				warehouse := warehouses[i]
				if warehouse.Category == 4 || warehouse.Category == 5 {
					flag = true
				}
			}
			if flag {
				err = services.UpdateElmQzc(session, params.FinanceCode, params.StoreId, params.ProductId, params.SkuId, params.Price)
				if err != nil {
					return "失败", err
				}
			}
		case services.ChannelJddjId:
			warehouses, _ := product.GetChannelWarehouses([]string{params.FinanceCode}, services.ChannelJddjId)
			flag := false
			for i := range warehouses {
				warehouse := warehouses[i]
				if warehouse.Category == 4 || warehouse.Category == 5 {
					flag = true
				}
			}
			if flag {
				err = services.UpdateJddjQzc(session, params.FinanceCode, params.StoreId, params.ProductId, params.SkuId, params.Price)
				if err != nil {
					return "失败", err
				}
			}
		}
		return "成功", nil
	})
}
