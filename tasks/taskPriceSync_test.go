package tasks

import (
	"_/models"
	"_/services"
	"testing"
	"time"
)

func TestSyncProductPriceSync(t *testing.T) {
	tests := []struct {
		name string
	}{

		{
			name: "同步价格",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SyncProductPriceSync()
		})
	}
}

func TestPushmqgo(t *testing.T) {
	tests := []struct {
		name string
	}{

		{
			name: "落地到本地",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			Pushmqgo()
		})
	}
}

func TestPushMqData(t *testing.T) {
	tests := []struct {
		name string
	}{

		{
			name: "落地到MQ",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			PushMqData()
		})
	}
}

func TestSyncProductPriceAW(t *testing.T) {
	tests := []struct {
		name string
	}{

		{
			name: "阿闻消费",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SyncProductPriceAW()
		})
	}
}

func TestSyncProductTest(t *testing.T) {
	tests := []struct {
		name string
	}{

		{
			name: "测试mq消费",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SyncProductTest()
		})
	}
}

func TestPushmqgoToSZ(t *testing.T) {
	tests := []struct {
		name string
	}{

		{
			name: "处理深圳数据",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			PushmqgoToSZ()
		})
	}
}

func TestDataOpeater(t *testing.T) {
	type args struct {
		Response models.PriceSyncResponse
	}
	tests := []struct {
		name string
		args args
	}{

		{
			name: "处理本地数据",
		},
	}

	var params models.PriceSyncResponse
	params.Id = 12
	params.Response = `[{"struct_code":["ZLH0001"],"product_info":[{"product_code":"C0102XX010","sell_price":"5.00"},{"product_code":"C000QEWH8R","sell_price":"55.00"}]}]`
	params.Enable = 1
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			DataOpeater(params)
		})
	}
}

func TestSyncDsPriceAwen(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SyncDsPriceAwen()
		})
	}
}

func TestDataOpeater1(t *testing.T) {
	type args struct {
		Response models.PriceSyncResponse
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			name: "TestDataOpeater1",
			args: args{
				Response: models.PriceSyncResponse{
					Id:         1,
					Response:   "[{\"struct_code\":[\"CX0036\"],\"product_info\":[{\"product_code\":\"C1033XX206\",\"sell_price\":\"10.00\"},{\"product_code\":\"L000008987\",\"sell_price\":\"50.00\"},{\"product_code\":\"C3056XX490\",\"sell_price\":\"90.00\"},{\"product_code\":\"C24XXXX386\",\"sell_price\":\"200.00\"},{\"product_code\":\"L000008291\",\"sell_price\":\"5.00\"},{\"product_code\":\"C14XXXX277\",\"sell_price\":\"10.00\"},{\"product_code\":\"L000008343\",\"sell_price\":\"6.00\"},{\"product_code\":\"C05XXXX076\",\"sell_price\":\"5.00\"},{\"product_code\":\"S0106XX013\",\"sell_price\":\"30.00\"}]}]",
					Enable:     1,
					CreateTime: time.Now(),
					LastTime:   time.Now(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			DataOpeater(tt.args.Response)
		})
	}
}

func TestPushmqgo1(t *testing.T) {
	Pushmqgo()
}

func TestPushMqToLocal(t *testing.T) {
	type args struct {
		v models.PriceSync
	}
	var list models.PriceSync
	session := services.NewDbConn()
	session.Where("id=4634002").Get(&list)

	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			name: "TestPushMqToLocal",
			args: args{
				v: list,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			PushMqToLocal(tt.args.v)
		})
	}
}
