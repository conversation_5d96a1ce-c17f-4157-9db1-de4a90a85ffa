package tasks

import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/et"
	"_/proto/ic"
	"_/proto/pc"
	"_/services"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/golang/protobuf/ptypes/empty"

	kit "github.com/tricobbler/rp-kit"

	"github.com/limitedlee/microservice/common/config"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	"github.com/tricobbler/mqgo"
)

func initMtTask(c *cron.Cron) {
	//检查美团批量任务执行结果 修改为支持所有店铺主体
	c.AddFunc("@every 10s", checkMtTask)

	// 每天3点开始自动上架美团门店仓商品
	c.AddFunc("0 3 * * *", AutoShelvesMt)

	//每天0点开始自动上架美团前置仓商品
	c.AddFunc("0 0 * * *", AutoShelvesMtQz)
}

// 检查美团批量任务执行结果
// 修改为支持所有店铺主体
func checkMtTask() {
	defer utils.CatchPanic()
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:checkMtTaskLock"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 3*time.Second).Val() {
		//glog.Info("already running")
		return
	}
	defer redis.Del(taskLock)

	//查询是否有需要处理的任务
	db := services.NewDbConn()

	var result []*models.MtRetailBatchtask // and app_channel=1"
	if err := db.Select("task_id").Where("task_status=0").Find(&result); err != nil {
		glog.Error(err)
		return
	}

	//没有可处理任务
	if len(result) == 0 {
		//glog.Info("no task")
		return
	}

	//将美团任务id每50个放在一起（美团限制一次最多只能查询50个）
	var tasks []string
	var taskChunk []string
	for k, v := range result {
		taskChunk = append(taskChunk, cast.ToString(v.TaskId))
		if k == len(result)-1 || len(taskChunk) == 50 {
			tasks = append(tasks, strings.Join(taskChunk, ","))
			taskChunk = []string{}
		}
	}

	insertData := &pc.NewChannelStoreProductRequest{
		Info: []*pc.ChannelStoreProduct{},
	}
	type mtData struct {
		Data []struct {
			TaskId     int `json:"taskId"`
			TaskResult struct {
				TotalPoiCount  int `json:"total_poi_count"`
				FailedPoiCount int `json:"failed_poi_count"`
				ErrorList      []struct {
					ErrorList []struct {
						AppFoodCode string `json:"app_food_code"`
						Error       string `json:"error"`
					} `json:"errorList"`
				} `json:"errorList"`
			} `json:"taskResult"`
		} `json:"data"`
	}

	client := services.GetMtProductClient()
	defer client.Close()

	clientData := services.GetDataCenterClient()
	defer clientData.Close()

	ctx := context.Background()
	mtResponse := new(mtData)
	for index, task := range tasks {
		res, err := client.RPC.TaskStatus(ctx, &et.TaskStatusRequest{
			TaskIds:    task,
			AppChannel: result[index].AppChannel,
		})
		if err != nil {
			glog.Error(err)
			continue
		}
		if res.Code != 200 || len(res.Data) == 0 {
			glog.Error(res)
			continue
		}

		if err = json.Unmarshal([]byte(res.Data), mtResponse); err != nil {
			glog.Error(err)
			continue
		}

		productObj := new(services.Product)
		ctx := context.Background()
		ss := db.NewSession()
		defer ss.Close()
		for _, v := range mtResponse.Data {
			//记录美团返回到数据库
			resp, _ := json.Marshal(v)
			if _, err = db.Cols("mt_response").Where("task_id=?", v.TaskId).Update(&models.MtRetailBatchtask{
				MtResponse: string(resp),
			}); err != nil {
				glog.Error(services.GetDBError(err))
			}

			//查询任务当时的参数
			model := new(models.MtRetailBatchtask)
			if has, err := ss.Select("task_data,user_no").Where("task_id=? and task_status=0", v.TaskId).Get(model); err != nil {
				glog.Error(services.GetDBError(err))
				continue
			} else if !has {
				glog.Errorf("task_id: %v 不存在", v.TaskId)
				continue
			}

			taskData := new(et.MultipoisBatchinitdataRequest)
			if err = json.Unmarshal([]byte(model.TaskData), taskData); err != nil {
				glog.Error("解析json失败，", err, "，json: ", model.TaskData)
				continue
			}

			appPoiCodes := []string{}
			if taskData.InitData.IsAllPois != 1 {
				appPoiCodes = strings.Split(taskData.InitData.AppPoiCodes, ",")
			}
			financeCodeMap, err := services.GetFinanceCodeByStoresChannelId(appPoiCodes, services.ChannelMtId)
			if err != nil {
				continue
			}

			ss.Begin()
			var productIds []int32
			var financeCodes []string
			builder := new(strings.Builder)
			//遍历判断哪些门店失败，哪些成功
			for _, financeCode := range financeCodeMap {
				//商品在失败列表不存在表示成功完成，写入数据库
				for _, product := range taskData.InitData.RetailInfo {
					var pId int32
					for _, vvv := range v.TaskResult.ErrorList {
						for _, vvvv := range vvv.ErrorList {
							glog.Infof("errorList1: %v, %v", vvvv.AppFoodCode, vvvv.Error)
							//在失败列表存在，跳回一层循环
							if vvvv.AppFoodCode == product.AppFoodCode {
								builder.WriteString(vvvv.AppFoodCode)
								builder.WriteString("，")
								builder.WriteString(vvvv.Error)
								builder.WriteString("<br>")
								goto I
							}
						}
					}

					pId = cast.ToInt32(product.AppFoodCode)
					insertData.Info = append(insertData.Info, &pc.ChannelStoreProduct{
						ChannelId:   services.ChannelMtId,
						FinanceCode: financeCode,
						ProductId:   pId,
						UpDownState: 1,
					})
					productIds = append(productIds, pId)
					financeCodes = append(financeCodes, financeCode)
				I:
				}
			}

			errMsgs := builder.String()
			if len(errMsgs) > 0 {
				if len([]rune(errMsgs)) > 500 {
					errMsgs = string([]rune(errMsgs)[0:500])
				}
				errMsgsJson, err := json.Marshal(map[string]interface{}{
					"MessageType": -1,
					"Msg":         errMsgs,
					"Title":       "多门店批量新增/更新商品结果通知",
				})
				if err != nil {
					glog.Error(err)
				} else {
					message := &dac.MessageCreateRequest{
						Content:     string(errMsgsJson),
						MessageType: -1,
						MemberMain:  model.UserNo,
					}
					if err := services.SendWsMessage(message); err != nil {
						glog.Error(err)
					}
				}
			}

			//门店上架成功商品写入数据库
			if len(insertData.Info) > 0 {
				//新增则会增加商品关联，更新则不操作
				if taskData.InitData.Type == 1 {
					if _, err := productObj.NewChannelStoreProduct(ctx, insertData); err != nil {
						ss.Rollback()
						glog.Error(err)
						continue
					}

				}
				ctx = context.WithValue(ctx, "user_info", &models.LoginUserInfo{
					UserNo: model.UserNo,
				})
				//更新快照
				if re, err := productObj.UpdateSnapShot(ctx, &pc.UpdateSnapShotRequest{
					ChannelId:   services.ChannelMtId,
					ProductId:   productIds,
					FinanceCode: financeCodes,
				}); err != nil {
					ss.Rollback()
					glog.Error(err)
					continue
				} else if re.Code != 200 {
					ss.Rollback()
					glog.Errorf(re.Message)
					continue
				}
			}

			//当前任务标记成功
			if _, err = ss.Where("task_id=?", v.TaskId).Update(&models.MtRetailBatchtask{
				TaskStatus: 1,
			}); err != nil {
				ss.Rollback()
				glog.Error(err)
				continue
			}

			ss.Commit()

			//推库存
			if len(productIds) > 0 {
				snapShots, err := productObj.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
					ProductId: productIds,
				})
				if err != nil {
					glog.Error(err)
					continue
				}
				if snapShots.Code != 200 {
					glog.Errorf(snapShots.Message)
					continue
				}

				skuCodes := []*ic.SkuCodeInfo{}
				for _, financeCode := range financeCodes {
					warehouse := utils.LoadChannelWarehouseCache(redis, financeCode, enum.ChannelMtId)
					if warehouse == nil {
						continue
					}

					var warehouseId int32 = warehouse.WarehouseId
					for _, v := range snapShots.Details {
						data := new(pc.ChannelProductRequest)
						if err := json.Unmarshal([]byte(v.JsonData), data); err != nil {
							glog.Error(err)
							continue
						}
						for _, sku := range data.SkuInfo {
							skuCodes = append(skuCodes, &ic.SkuCodeInfo{
								FinanceCode:    financeCode,
								Sku:            cast.ToString(sku.SkuId),
								StockWarehouse: []int32{warehouseId},
							})
						}
					}
				}

				//查询库存
				stockMap, _ := services.GetStockInfoBySkuCode(0, skuCodes, services.ChannelMtId)
				glog.Info("批量美团接口stockMap，", stockMap)

				dc_sz_stock_mq := "dc_sz_stock_mq"
				//遍历门店推到库存处理队列
				for appPoiCode, financeCode := range financeCodeMap {
					if len(appPoiCode) == 0 {
						continue
					}

					storeMasterId, err := services.GetAppChannelByFinanceCode(financeCode)
					if err != nil {
						glog.Error("checkMtTask,", "GetAppChannelByFinanceCode failed,", financeCode, err)
						continue
					}

					rstock := et.RetailSkuStockRequest{
						AppPoiCode:    appPoiCode,
						StoreMasterId: storeMasterId,
					}

					for _, v := range snapShots.Details {
						data := new(pc.ChannelProductRequest)
						if err := json.Unmarshal([]byte(v.JsonData), data); err != nil {
							glog.Error(err)
							continue
						}

						skus := []*et.Skus{}
						for _, sku := range data.SkuInfo {
							skus = append(skus, &et.Skus{
								SkuId: cast.ToString(sku.SkuId),
								Stock: getSkuStock(stockMap, financeCode, cast.ToString(sku.SkuId)),
							})
						}

						rstock.FoodData = append(rstock.FoodData, &et.FoodData{
							AppFoodCode: cast.ToString(data.Product.Id),
							Skus:        skus,
						})
					}

					//把消息转成json并丢到mq里面
					mqstring, _ := json.Marshal(rstock)
					glog.Info("库存队列数据：", string(mqstring))
					m := mqgo.SyncMqInfo{
						Exchange: services.DatacenterExchange,
						Queue:    dc_sz_stock_mq,
						RouteKey: dc_sz_stock_mq,
						Request:  string(mqstring),
					}
					if err = mqgo.NewMqByStr(config.GetString("mq.oneself"), config.GetString("mysql.dc_product")).Publish(m); err != nil {
						glog.Error(err)
					}
				}
			}
		}

		//美团查询接口每秒最多查5次
		time.Sleep(200 * time.Millisecond)
	}
}

// 自动上架--美团门店仓
func AutoShelvesMt() {
	defer kit.CatchPanic()
	glog.Info("AutoShelvesMt-获取锁")
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock1 := "productcenter:task:AutoShelvesMt"
	//任务已在执行
	if !redis.SetNX(taskLock1, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock1)
	glog.Info("AutoShelvesMt-获取到锁-任务开始")

	var product services.Product
	warehouses, err := product.GezZLWarehouseList(services.ChannelMtId)
	if err != nil {
		glog.Error("医疗互联网门店仓商品关系查询异常", err.Error())
		return
	}

	var codeList []string
	for _, v := range warehouses {
		codeList = append(codeList, v.ShopId)
	}

	var service services.Product
	clientDac := dac.GetDataCenterClient()
	defer clientDac.Close()

	var codeMap = make(map[string]string)
	if out, err := clientDac.RPC.QueryStoresChannelId(clientDac.Ctx, &dac.StoreRelationUserRequest{
		FinanceCode: codeList,
		Psize:       int32(len(codeList)),
		ChannelId:   2,
	}); err != nil {
		glog.Errorf("获取美团id失败; err: %s", err.Error())
		return
	} else {
		if out.Code == 200 && len(out.Data) > 0 {
			for _, v := range out.Data {
				codeMap[v.FinanceCode] = v.ChannelStoreId
			}
		}
	}
	for _, v := range warehouses {
		appPoiCode := codeMap[v.ShopId]
		if len(appPoiCode) <= 0 {
			glog.Error("无美团id; 门店：", v.ShopId)
			continue
		}
		storeMasterId, err := services.GetAppChannelByFinanceCode(v.ShopId)
		if err != nil {
			glog.Error("AutoShelvesMt,", "GetAppChannelByFinanceCode failed,", v.ShopId, err)
			continue
		}

		lists := service.GetProductSimpleInfo(v.ShopId, services.ChannelMtId, v.WarehouseId, v.WarehouseCode)
		//根据仓库编码查询门店财务数据
		//ids := service.GetChannelWarehousesShops(services.ChannelMtId, v.WarehouseId)
		for i, _ := range lists {
			lists[i].FinanceCode = v.ShopId
		}

		if len(lists) > 0 {
			//每次处理数据100条
			l := 100
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(lists) < l {
					l = len(lists)
				}
				_list := lists[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					itemToAutoMt(_list, services.ChannelMtId, appPoiCode, storeMasterId, 3)
				}()
				lists = lists[l:]
				if len(lists) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)
		}
		glog.Info(fmt.Sprintf("该美团门店仓门店：%s已执行完成", v.ShopId))
	}

	glog.Info("AutoShelvesMt任务结束")
}

// 自动上架--美团前置仓
func AutoShelvesMtQz() {
	defer kit.CatchPanic()
	glog.Info("AutoShelvesMtQz-获取锁")
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesMtQz"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	glog.Info("AutoShelvesMtQz-获取锁成功-任务开始")

	var product services.Product
	warehouses, err := product.GezQZWarehouseList(services.ChannelMtId)
	if err != nil {
		glog.Error("医疗互联网门店仓商品关系查询异常", err.Error())
		return
	}

	var codeList []string
	var idList []int32
	idForCode := make(map[int32][]string)
	codeForId := make(map[string][]int32)
	for _, v := range warehouses {
		codeList = append(codeList, v.ShopId)
		if _, ok := idForCode[int32(v.WarehouseId)]; !ok {
			idList = append(idList, int32(v.WarehouseId))
		}
		codeForId[v.ShopId] = append(codeForId[v.ShopId], int32(v.WarehouseId))
		idForCode[int32(v.WarehouseId)] = append(idForCode[int32(v.WarehouseId)], v.ShopId)
	}

	var service services.Product
	clientDac := dac.GetDataCenterClient()
	defer clientDac.Close()

	var codeMap = make(map[string]string)
	if out, err := clientDac.RPC.QueryStoresChannelId(clientDac.Ctx, &dac.StoreRelationUserRequest{
		FinanceCode: codeList,
		Psize:       int32(len(codeList)),
		ChannelId:   2,
	}); err != nil {
		glog.Errorf("获取美团id失败; err: %s", err.Error())
		return
	} else {
		if out.Code == 200 && len(out.Data) > 0 {
			for _, v := range out.Data {
				codeMap[v.FinanceCode] = v.ChannelStoreId
			}
		}
	}
	// 查询前置仓商品和价格
	a8List := service.GetProductQzInfo(services.ChannelMtId, idList)
	var idForA8 = make(map[int][]models.QzcPriceSync)
	for _, v := range a8List {
		idForA8[v.WarehouseId] = append(idForA8[v.WarehouseId], v)
	}
	var priceMap = make(map[string][]models.PriceSync)
	for code, ids := range codeForId {
		appPoiCode := codeMap[code]
		if len(appPoiCode) <= 0 {
			glog.Error("无美团id; 门店：", code)
			continue
		}
		for _, id := range ids {
			if len(idForA8[int(id)]) == 0 {
				continue
			}
			priceList := service.GetProductQzCodeInfo(code, services.ChannelMtId, int(id), idForA8[int(id)])
			priceMap[code] = priceList
		}

	}
	for code, list := range priceMap {
		storeMasterId, err := services.GetAppChannelByFinanceCode(code)
		if err != nil {
			glog.Error("AutoShelvesMtQz,", "GetAppChannelByFinanceCode failed,", code, err)
			continue
		}
		if len(list) > 0 {
			//每次处理数据100条
			l := 100
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(list) < l {
					l = len(list)
				}
				_list := list[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					itemToAutoMt(_list, services.ChannelMtId, codeMap[code], storeMasterId, 4)
				}()
				list = list[l:]
				if len(list) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)
		}
		glog.Info(fmt.Sprintf("该美团前置门店：%s已执行完成", code))
	}
	glog.Info("AutoShelvesMtQz任务结束")
}

// 逐条处理
func itemToAutoMt(list []models.PriceSync, channel_id int32, appPoiCode string, storeMasterId int32, warehouseCategory int32) {
	logPrefix := fmt.Sprintf("自动上架到美团itemToAutoMt渠道%d,美团门店id%s,storeMasterId为%d,warehouseCategory为%d==========", channel_id, appPoiCode, storeMasterId, warehouseCategory)
	glog.Info(logPrefix, "list为", kit.JsonEncode(list))
	var service services.Product
	engine := services.NewDbConn()
	conn, _ := services.ExGrpcPool.Get()
	defer conn.Close()
	clientIc := et.NewMtProductServiceClient(conn.Value())
	ctxMt, cf := context.WithTimeout(context.Background(), time.Second*300)
	defer cf()

	session := engine.NewSession()
	defer session.Close()

	//v6.5.8 排除雀巢马氏等配置自动上架
	p := new(services.Product)
	agencyConfig, err := p.GetAgencyConfig(context.Background(), &empty.Empty{})
	if err != nil {
		glog.Error(logPrefix, "获取代运营配置异常:", err.Error())
		return
	}

	var upInfos map[int]*services.AwenProductUpInfo

	if channel_id != services.ChannelDigitalHealth && (warehouseCategory == 3 || warehouseCategory == 4) {
		productIds := make([]int, len(list))
		for i, priceSync := range list {
			productIds[i] = priceSync.ProductId
		}
		if upInfos, err = services.QueryAwenProductUpInfoByIds(productIds, int(warehouseCategory)); err != nil {
			glog.Error(logPrefix, "ItemToAuto QueryAwenProductUpInfoByIds 出错：", err.Error())
			return
		}
	}

	for _, v := range list {
		session.Begin()
		ctx := context.WithValue(context.Background(), "user_info", &models.LoginUserInfo{
			UserNo: "AutoShelf",
		})

		code := v.FinanceCode

		var flag bool
		for i := range agencyConfig.ConfigData {
			if storeMasterId == agencyConfig.ConfigData[i] {
				flag = true
			}
		}

		if !flag {
			product, err := p.IsUpProduct(context.Background(), storeMasterId, 0)
			if err != nil {
				session.Rollback()
				glog.Error(logPrefix, "获取代运营商品异常：", err.Error(), " financeCode:", code)
				continue
			}
			if _, ok := product[v.Sku]; !ok {
				session.Rollback()
				glog.Error(fmt.Sprintf("%s该商品禁止上架，如需上架请联系总部运营:financeCode%s:sku%d: product%d", logPrefix, v.FinanceCode, v.Sku, v.ProductId))
				continue
			}
		}

		if upInfo, has := upInfos[cast.ToInt(v.Sku)]; has {

			////药品不允许上架
			//if upInfo.IsDrugs == 1 {
			//	glog.Error(fmt.Sprintf("药品不允许上架:financeCode%s:sku%d: product%d", v.FinanceCode, v.Sku, v.ProductId))
			//	continue
			//}

			if err := upInfo.Check(int(warehouseCategory), v.FinanceCode, cast.ToInt(channel_id), ""); err != nil {
				session.Rollback()
				glog.Error(fmt.Sprintf("%s不可销不允许上架:financeCode%s:sku%d: product%d", logPrefix, v.FinanceCode, v.Sku, v.ProductId))
				continue
			}

		}

		result_bool, _, stock, err := service.BatchOnTheShelf(v.FinanceCode, int32(v.ProductId), channel_id, 1, session, ctx, 1, v.Price)
		if !result_bool {
			session.Rollback()
			glog.Error(logPrefix, "美团商品自动上架失败,err:", kit.JsonEncode(err))
			continue
		}
		gmdr := pc.GetMtProductDataRequest{
			ProductId:   []int32{int32(v.ProductId)},
			ChannelId:   2,
			FinanceCode: []string{v.FinanceCode},
			Type:        0,
		}
		out, err := service.GetMtProductData(ctx, &gmdr)
		if err != nil {
			glog.Errorf("%s美团商品自动上架失败, 查询美团商品失败; 门店id: %s, 商品id: %d; err: %s", logPrefix, v.FinanceCode, v.ProductId, err.Error())
			session.Rollback()
			continue
		} else if out.Code != 200 || len(out.Data) == 0 {
			session.Rollback()
			glog.Errorf("%s美团商品自动上架失败, 查询美团商品失败; 门店id: %s, 商品id: %d; err: %s", logPrefix, v.FinanceCode, v.ProductId, out.Message)
			continue
		}

		foodData := []*et.RetailInitdataRequest{}
		if json.Unmarshal([]byte(out.Data), &foodData) != nil {
			session.Rollback()
			glog.Errorf("%s,美团商品自动上架失败, json解析失败, 门店id: %s, product: %d", logPrefix, v.FinanceCode, v.ProductId)
			continue
		}

		priceStr := cast.ToString(float64(v.Price) / 100)
		// 创建的时候需要构建价格【自动上架取得价格 就是准确得不需要额外去取】
		// getPrice, err1 := services.GetPrice(int(services.ChannelMtId), cast.ToString(foodData[0].Skus[0].SkuId), v.FinanceCode)
		// if err1 != nil {
		// 	glog.Error(logPrefix, "获取商品价格失败", err1)
		// } else {
		// 	priceStr = cast.ToString(getPrice)
		// }

		for _, v := range foodData {
			for _, sku := range v.Skus {
				sku.Price = priceStr
				sku.Stock = cast.ToString(stock)
			}
		}

		// 设置上架状态
		foodData[0].IsSoldOut = 0
		foodData[0].AppPoiCode = appPoiCode
		foodData[0].StoreMasterId = storeMasterId //appPoiCode

		glog.Errorf("%s周翔测试自动上架次数，门店id: %s , product: %d;", logPrefix, v.FinanceCode, v.ProductId)
		res, err := clientIc.RetailInitdata(ctxMt, foodData[0])
		glog.Info(logPrefix, "9999美团创建/更新商品返回数据为：", kit.JsonEncode(res), "请求数据为：", kit.JsonEncode(foodData[0]))
		if err != nil {
			session.Rollback()
			glog.Errorf("%s,美团商品自动上架失败，门店id: %s , product: %d;失败原因1：%s", logPrefix, v.FinanceCode, v.ProductId, err.Error())
			continue
		}

		if res.Code != 200 {
			session.Rollback()
			glog.Errorf("%s,美团商品自动上架失败，门店id: %s, product: %d ;失败原因2：%s", logPrefix, v.FinanceCode, v.ProductId, res)
			continue
		}
		// v7.0.11 同步第三方商品ID回来

		err = session.Commit()
		if err != nil {
			session.Rollback()
			glog.Error(logPrefix, "美团商品自动上架失败,提交事务,err:", err)
		} else {
			glog.Infof("%s,美团商品自动上架成功, 门店: %s, 商品: %d", logPrefix, v.FinanceCode, v.ProductId)
			// 记录日志信息
			go new(services.Product).SaveChannelProductLogDetail(cast.ToInt(channel_id), v.ProductId, enum.RecordTypeAutoUp,
				code, "AutoShelf", "AutoShelf")
		}
		services.MtProductThirdId(enum.UpdateProductThirdIdFrom14, res, []*et.RetailBatchinitdata{&et.RetailBatchinitdata{AppFoodCode: foodData[0].AppFoodCode}}, v.FinanceCode, 1)
	}
	time.Sleep(100 * time.Millisecond)
}
