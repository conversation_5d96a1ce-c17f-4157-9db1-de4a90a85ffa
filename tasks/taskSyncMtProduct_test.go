package tasks

import (
	"encoding/json"
	"fmt"
	"testing"
)

func TestStoreIntoMQ(t *testing.T) {
	type args struct {
		channel_id int
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{name: "写入MQ测试"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			StoreIntoMQ(2)
		})
	}
}

func TestCheckMtProductDetail(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{name: "跑美团数据"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			CheckMtProductDetail()
		})
	}
}

type AutoGenerated struct {
	Body struct {
		Errno int `json:"errno"`
		Data  struct {
			Total       int   `json:"total"`
			Pages       int   `json:"pages"`
			SkuIDOffset int64 `json:"sku_id_offset"`
			Page        int   `json:"page"`
			List        []struct {
				SaleUnit          string `json:"sale_unit"`
				SevenDaysNoReason bool   `json:"seven_days_no_reason,omitempty"`
				WeightFlag        int    `json:"weight_flag"`
				DurationSaleFlag  bool   `json:"duration_sale_flag"`
				Photos            []struct {
					IsMaster int    `json:"is_master"`
					URL      string `json:"url"`
				} `json:"photos"`
				ProductionAddr3  string        `json:"production_addr3"`
				SkuProperty      []interface{} `json:"sku_property"`
				ProductionAddr1  string        `json:"production_addr1"`
				ProductionAddr2  string        `json:"production_addr2"`
				CateName         string        `json:"cate_name"`
				IsInActivity     int           `json:"is_in_activity"`
				PreparationTime  int           `json:"preparation_time"`
				ProcessType      int           `json:"process_type"`
				CateName1        string        `json:"cate_name1"`
				PackageFlag      bool          `json:"package_flag"`
				CateName2        string        `json:"cate_name2"`
				Summary          string        `json:"summary"`
				Rtf              string        `json:"rtf,omitempty"`
				ItemID           int64         `json:"item_id"`
				PreminusWeight   int           `json:"preminus_weight"`
				Upc              string        `json:"upc"`
				Weight           string        `json:"weight"`
				SkuID            int64         `json:"sku_id"`
				CateID           int           `json:"cate_id"`
				SalePrice        int           `json:"sale_price"`
				ProcessDetail    []interface{} `json:"process_detail"`
				UpcType          int           `json:"upc_type"`
				CustomSkuID      string        `json:"custom_sku_id"`
				Name             string        `json:"name"`
				CateID2          int           `json:"cate_id2"`
				MarketPrice      int           `json:"market_price"`
				PrescriptionType string        `json:"prescription_type"`
				CateID1          int           `json:"cate_id1"`
				LeftNum          int           `json:"left_num"`
				NeedIce          string        `json:"need_ice"`
				Minimum          int           `json:"minimum"`
				CustomCatIds     string        `json:"custom_cat_ids"`
				Status           string        `json:"status"`
				SaleStep         int           `json:"sale_step"`
			} `json:"list"`
		} `json:"data"`
		Error string `json:"error"`
	} `json:"body"`
	Cmd       string `json:"cmd"`
	Encrypt   string `json:"encrypt"`
	Sign      string `json:"sign"`
	Source    string `json:"source"`
	Ticket    string `json:"ticket"`
	Timestamp int    `json:"timestamp"`
	Traceid   string `json:"traceid"`
	Version   string `json:"version"`
}

func TestCheckELMProductDetail(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{name: "跑饿了么数据"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			ss := `{"body":{"errno":0,"data":{"total":505,"pages":6,"sku_id_offset":846370396649,"page":6,"list":[{"sale_unit":"","seven_days_no_reason":true,"weight_flag":2,"duration_sale_flag":false,"photos":[{"is_master":1,"url":"https:\/\/img.alicdn.com\/imgextra\/i2\/2208328582656\/O1CN01x3z8jS1VUUZl3juwg_!!2208328582656-0-eleretail.jpg"},{"is_master":0,"url":"https:\/\/img.alicdn.com\/imgextra\/i1\/2208328582656\/O1CN01QfR3Ox1VUUZlfCi4d_!!2208328582656-0-eleretail.jpg"}],"production_addr3":"","sku_property":[],"production_addr1":"","production_addr2":"","cate_name":"\u5176\u4ed6\u732b\/\u72d7\u536b\u751f\u62a4\u7406\u7528\u54c1","is_in_activity":0,"preparation_time":0,"process_type":0,"cate_name1":"\u5ba0\u7269\u7528\u54c1","package_flag":false,"cate_name2":"\u732b\/\u72d7\u536b\u751f\u62a4\u7406","summary":"","item_id":846370396648,"preminus_weight":0,"upc":"6958401303395","weight":"100","sku_id":172956330622500,"cate_id":201531803,"sale_price":1400,"process_detail":[],"upc_type":0,"custom_sku_id":"1006618001","name":"CLOUD-UE\u6363\u86cb\u9b3c\u5c0f\u53f7\u732b\u7802\u76c6BP249","cate_id2":201540101,"market_price":0,"prescription_type":"","cate_id1":201229611,"left_num":0,"need_ice":"0","minimum":1,"custom_cat_ids":"","status":"0","sale_step":0},{"sale_unit":"","seven_days_no_reason":true,"weight_flag":2,"duration_sale_flag":false,"photos":[{"is_master":1,"url":"https:\/\/img.alicdn.com\/imgextra\/i4\/2208309648055\/O1CN011CJBpO29NEvNt7e29_!!2208309648055-0-eleretail.jpg"}],"production_addr3":"","sku_property":[],"production_addr1":"","production_addr2":"","cate_name":"\u6c34\u65cf\u7528\u54c1\u53ca\u8bbe\u5907","is_in_activity":0,"preparation_time":0,"process_type":0,"cate_name1":"\u5ba0\u7269\u7528\u54c1","package_flag":false,"cate_name2":"\u6c34\u65cf\u52a8\u7269\u53ca\u5176\u7528\u54c1","summary":"\n","item_id":845884106468,"preminus_weight":0,"upc":"5878691396655","weight":"60","sku_id":172956330622503,"cate_id":201218464,"sale_price":1000,"process_detail":[],"upc_type":0,"custom_sku_id":"1046713001","name":"CLOUD-UE-\u81f3\u5ba0\u6069\u8bfa\u6c99\u661f\u6ce8\u5c04\u6db2100ml","cate_id2":201221528,"market_price":0,"prescription_type":"","cate_id1":201229611,"left_num":0,"need_ice":"0","minimum":1,"custom_cat_ids":"","status":"0","sale_step":0},{"sale_unit":"","seven_days_no_reason":true,"weight_flag":2,"duration_sale_flag":false,"photos":[{"is_master":1,"url":"https:\/\/img.alicdn.com\/imgextra\/i2\/2207515743174\/O1CN01f7mhDc1ZJjhIh1vMw_!!2207515743174-0-eleretail.jpg"}],"production_addr3":"","sku_property":[],"production_addr1":"","production_addr2":"","cate_name":"\u72d7\u96f6\u98df","is_in_activity":0,"preparation_time":0,"process_type":0,"cate_name1":"\u5ba0\u7269\u7528\u54c1","package_flag":false,"cate_name2":"\u732b\/\u72d7\u98df\u54c1","summary":"","rtf":"https:\/\/img.alicdn.com\/imgextra\/desc\/icoss!0845730838864!134****8463","item_id":845730838864,"preminus_weight":0,"upc":"6927749808695","weight":"160","sku_id":17295061502243618,"cate_id":201228844,"sale_price":1000,"process_detail":[],"upc_type":0,"custom_sku_id":"1004194001","name":"wanpy \u9e21\u8089+\u7c73+\u852c\u83dc+\u9e21\u8f6f\u9aa8\u72d7\u7f50\u5934160g","cate_id2":201535902,"market_price":0,"prescription_type":"","cate_id1":201229611,"left_num":0,"need_ice":"0","minimum":1,"custom_cat_ids":"","status":"0","sale_step":0},{"sale_unit":"","seven_days_no_reason":true,"weight_flag":2,"duration_sale_flag":false,"photos":[{"is_master":1,"url":"https:\/\/img.alicdn.com\/imgextra\/i2\/2208309648055\/O1CN01IrBCW529NEvWtir9u_!!2208309648055-2-eleretail.png"},{"is_master":0,"url":"https:\/\/img.alicdn.com\/imgextra\/i2\/2209498688753\/O1CN01Ori1Ye2EWvTT2bo7G_!!2209498688753-2-eleretail.png"},{"is_master":0,"url":"https:\/\/img.alicdn.com\/imgextra\/i3\/2209498688753\/O1CN01BvPMnp2EWvTRo7IAS_!!2209498688753-2-eleretail.png"},{"is_master":0,"url":"https:\/\/img.alicdn.com\/imgextra\/i1\/2209498688753\/O1CN01pSy7Ou2EWvTYVuenm_!!2209498688753-2-eleretail.png"}],"production_addr3":"","sku_property":[],"production_addr1":"","production_addr2":"","cate_name":"\u732b\u96f6\u98df","is_in_activity":0,"preparation_time":0,"process_type":0,"cate_name1":"\u5ba0\u7269\u7528\u54c1","package_flag":false,"cate_name2":"\u732b\/\u72d7\u98df\u54c1","summary":"","rtf":"https:\/\/img.alicdn.com\/imgextra\/desc\/icoss!0845731206242!121****8061","item_id":845731206242,"preminus_weight":0,"upc":"9421019152008","weight":"100","sku_id":17295061502243621,"cate_id":201219439,"sale_price":8500,"process_detail":[],"upc_type":0,"custom_sku_id":"105205","name":"ZEAL\u8089\u7c7b\u5ba0\u7269\u96f6\u98df\u732b\u7528\u51bb\u5e72\u7f8a\u8089\u9e7f\u8089\u5c0f\u70b9100g","cate_id2":201535902,"market_price":0,"prescription_type":"","cate_id1":201229611,"left_num":0,"need_ice":"0","minimum":1,"custom_cat_ids":"","status":"0","sale_step":0},{"sale_unit":"","seven_days_no_reason":true,"weight_flag":2,"duration_sale_flag":false,"photos":[{"is_master":1,"url":"https:\/\/img.alicdn.com\/imgextra\/i4\/2209475386960\/O1CN01HRXDNs21HjISNNBOR_!!2209475386960-0-eleretail.jpg"},{"is_master":0,"url":"https:\/\/img.alicdn.com\/imgextra\/i2\/2209434167410\/O1CN01IaS1ic24bpZDh1XsY_!!2209434167410-0-eleretail.jpg"},{"is_master":0,"url":"https:\/\/img.alicdn.com\/imgextra\/i4\/2209434167410\/O1CN01tMvNru24bpZCkQ5ti_!!2209434167410-0-eleretail.jpg"}],"production_addr3":"","sku_property":[],"production_addr1":"","production_addr2":"","cate_name":"\u5176\u4ed6\u732b\/\u72d7\u65e5\u7528\u54c1","is_in_activity":0,"preparation_time":0,"process_type":0,"cate_name1":"\u5ba0\u7269\u7528\u54c1","package_flag":false,"cate_name2":"\u732b\/\u72d7\u65e5\u7528\u54c1","summary":"","rtf":"https:\/\/img.alicdn.com\/imgextra\/desc\/icoss!0846018793153!1593427297","item_id":846018793153,"preminus_weight":0,"upc":"4712523121167","weight":"50","sku_id":17295061502243628,"cate_id":201229765,"sale_price":3100,"process_detail":[],"upc_type":0,"custom_sku_id":"1009926001","name":"\u4e50\u6bd4\u5ba0\u7269\u6307\u7532\u526a","cate_id2":201531702,"market_price":0,"prescription_type":"","cate_id1":201229611,"left_num":0,"need_ice":"0","minimum":1,"custom_cat_ids":"","status":"0","sale_step":0}]},"error":"success"},"cmd":"resp.sku.list","encrypt":"","sign":"4027EDD90C0D8BF3D28C42C3DCC1B210","source":"49101183","ticket":"40B4E779-B394-4665-BA70-6FD9BF5D9645","timestamp":1729669985,"traceid":"213c740d17296699850166888e1f5d","version":"3"}  `

			//CheckELMProductDetail()
			var productData AutoGenerated
			json.Unmarshal([]byte(ss), &productData)
			sku := ""
			for _, x := range productData.Body.Data.List {
				sku += x.CustomSkuID + ","
			}
			fmt.Println(11)
		})
	}
}
