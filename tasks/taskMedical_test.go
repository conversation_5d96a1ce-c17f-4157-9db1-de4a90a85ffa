package tasks

import "testing"

func TestAutoShelvesMedicalQz(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
	}
	AutoShelvesMedicalQz()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

		})
	}
}

func TestAutoShelvesMedical(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
	}
	AutoShelvesMedical()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//AutoShelvesMedical()
		})
	}
}
