package tasks

import (
	"_/services"
	"_/utils"

	"github.com/go-redis/redis"

	//"github.com/limitedlee/microservice/common/logger"
	"strconv"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

const (
	batchHandleProductLockAll      = "task:productcenter:all"
	batchHandleProductLockOne      = "task:productcenter:one"
	batchHandleProductLockTwo      = "task:productcenter:two"
	batchHandleProductLockThree    = "task:productcenter:three"
	batchHandleProductLockFour     = "task:productcenter:four"
	batchHandleProductLockNine     = "task:productcenter:nine"
	batchHandleProductLockCopy     = "task:productcenter:copy"
	batchHandleProductLockCopyAll  = "task:productcenter:copyall"
	batchHandleQzcPrice            = "task:productcenter:qzc:price"
	batchHandleSwitchWarehouse     = "task:productcenter:switch-warehouse"
	batchHandleSyncChannelCategory = "task:productcenter:sync-channel-category"
)

func initPdTask(c *cron.Cron) {
	/*
		异步处理任务信息（一）
		<1>.类型:1.商品——批量新建;2.商品——批量更新;3.渠道——批量新建;11.渠道——商品导出;13.渠道——批量新建;35.互联网医院商品价格导入
			36.互联网医院商品价格导出 37.仓库白名单导入  38.仓库白名单导出
		<2>.不按渠道拆分;
	*/
	c.AddFunc("@every 5s", pubilchProductTask(batchHandleProductLockAll, services.DealAsyncTaskListAll))

	/*
		异步处理任务信息（二）
		<1>.类型:4.渠道——批量上架;12.渠道——批量更新;
		<2>.按4个渠道拆分;
	*/
	c.AddFunc("@every 5s", pubilchProductTask(batchHandleProductLockOne, services.DealAsyncTaskListByChannel(1)))
	c.AddFunc("@every 5s", pubilchProductTask(batchHandleProductLockTwo, services.DealAsyncTaskListByChannel(2)))
	c.AddFunc("@every 5s", pubilchProductTask(batchHandleProductLockThree, services.DealAsyncTaskListByChannel(3)))
	c.AddFunc("@every 5s", pubilchProductTask(batchHandleProductLockFour, services.DealAsyncTaskListByChannel(4)))
	c.AddFunc("@every 5s", pubilchProductTask(batchHandleProductLockNine, services.DealAsyncTaskListByChannel(9)))

	/*
		异步处理任务信息（三）
		<1>.类型:5.管家——批量认领;6.管家——全部认领;
		<2>.按批量认领和全部认领拆分;
	*/
	c.AddFunc("@every 5s", pubilchProductTask(batchHandleProductLockCopy, services.DealAsyncTaskListByCopyChannel(5), 30))
	c.AddFunc("@every 5s", pubilchProductTask(batchHandleProductLockCopyAll, services.DealAsyncTaskListByCopyChannel(6), 6*60))

	// 异步处理任务信息（四）
	// 处理批量导入前置仓价格信息
	c.AddFunc("@every 5s", pubilchProductTask(batchHandleQzcPrice, services.DealAsyncTaskListByQzcPrice(19), 30))

	//异步处理门店切仓定时任务
	c.AddFunc("@every 10s", pubilchProductTask(batchHandleSwitchWarehouse, services.DealAsyncTaskSwitchWarehouse(67), 30))
	//异步处理门店分类同步任务
	c.AddFunc("@every 10s", pubilchProductTask(batchHandleSyncChannelCategory, services.DealAsyncChannelCategory(31), 30))
}

//商品中心定时任务公共入口
func pubilchProductTask(lock string, f func() bool, args ...interface{}) func() {
	//初始化redis
	return func() {
		defer func() {
			if err := recover(); err != nil {
				glog.Errorf("%s-定时任务异常:%+v \n %s", lock, err, utils.PanicTrace())
			}
		}()
		var lockTime int32 = 15
		if len(args) > 0 && cast.ToInt32(args[0]) > 0 {
			lockTime = cast.ToInt32(args[0])
		}
		redisConn := services.GetRedisConn()
		if kit.EnvCanCron() {
			defer redisConn.Close()
		}
		delRedisSetNx := DelRedisSetNx(redisConn, lock, lockTime) //判断是否锁定5分钟
		if delRedisSetNx {
			lockRes := redisConn.SetNX(lock, time.Now().Unix(), 0).Val()
			if !lockRes {
				return
			}
		} else {
			return
		}
		defer func() {
			i := 0
			for {
				_, err := redisConn.Del(lock).Result()
				if err != nil {
					i++
					glog.Error("syncBatchTask删除锁出错:" + err.Error())
					if i == 10 {
						redisConn = services.GetRedisConn()
						redisConn.Del(lock).Result()
						break
					}
					time.Sleep(time.Second * 5)

				} else {
					break
				}
			}
		}()
		f()
		//if f() {
		//	glog.Info(lock + "定时任务执行成功---释放了锁zx3")
		//	redisConn.Del(lock)
		//} else {
		//	glog.Info(lock + "定时任务执行失败---释放了锁zx4")
		//	redisConn.Del(lock)
		//}
	}
}

//处理redis的setnx的返回结果。如果锁定时间已经超过默认时间5分钟，则自动删除。默认时间可更改
func DelRedisSetNx(redisConn *redis.Client, redisKey string, timeMinute int32) bool {
	if redisConn.Exists(redisKey).Val() > 0 {
		timeUnix, _ := strconv.Atoi(redisConn.Get(redisKey).Val())
		//与当前时间比较 如果没有设置时间 则默认为5分钟
		timeNowUnix := time.Now().Add(-1 * time.Minute * 5).Unix() // 5分钟
		if timeMinute > 0 {
			timeDuration := time.Duration(-1*timeMinute) * time.Minute
			timeNowUnix = time.Now().Add(timeDuration).Unix()
		}
		//已经超时了
		if timeNowUnix >= int64(timeUnix) {
			//超过5分钟，则自动删除
			redisConn.Del(redisKey)
			return true
		}
		return false
	}
	return true
}
