package tasks

import (
	"_/models"
	"_/services"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

//查询所有门店仓所有上架商品，去调用子龙接口判断是否可销，不是的话，需要执行4个渠道的下架
func initTaskSyncZlProduct(c *cron.Cron) {
	c.AddFunc("@every 2s", func() {
		SyncZlProductnew()
	})
	c.AddFunc("@every 2s", func() {
		SyncZlProductChild()
	})
}

func SyncZlProductnew() {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("--------------SyncZlProductnew error-------------", err)
		}
	}()

	SyncZlProductnewLock := "productcenter:task:SyncZlProductnew"
	//任务已在执行
	redis := services.KeepAliveRedisConn()
	if !redis.SetNX(SyncZlProductnewLock, time.Now().Unix(), 5*time.Minute).Val() {
		return
	}
	defer func() {
		_, err := redis.Del(SyncZlProductnewLock).Result()
		if err != nil {
			redis = services.KeepAliveRedisConn()
			redis.Del(SyncZlProductnewLock)
		}
	}()

	glog.Info(fmt.Sprintf("SyncZlProductnew 任务开始"))

	betinTime := time.Now().Add(-5 * 24 * time.Hour).Format(kit.DATETIME_LAYOUT)

	session := services.NewDbConn()
	var list []models.SyncZlProductRecordExtend
	if err := session.SQL(`select r.*,s.finance_code,t.product_id,p.is_drugs,p.is_prescribed_drug from dc_product.sync_zl_product_record r 
		left join datacenter.store s on s.zilong_id = r.zilong_id 
		left join sku_third t on t.third_sku_id = r.item_code 
		left join dc_product.product p on p.id = t.product_id 
		where r.update_time>? and r.deal_status =0 limit 5000;`, betinTime).Find(&list); err != nil {
		glog.Info("SyncZlProductnew 查询数据失败", err.Error())
	}
	channelShopKey := services.GetWarehouseShop()
	if len(list) > 0 {
		l := 1000
		//写协程
		wg := sync.WaitGroup{}
		//var gNum = runtime.NumCPU()
		//channelG := make(chan bool, gNum)
		//glog.Info("SyncZlProductnew 111")
		for {
			//channelG <- true
			if len(list) < l {
				l = len(list)
			}
			_list := list[:l]
			wg.Add(1)
			go func(_listItem []models.SyncZlProductRecordExtend) {
				SyncZlProductnewChild(_listItem, channelShopKey)
				wg.Done()
			}(_list)
			list = list[l:]
			if len(list) == 0 {
				break
			}
		}
		//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
		wg.Wait()
	}
}

func SyncZlProductnewChild(list []models.SyncZlProductRecordExtend, channelShopKey map[string]string) {
	session := services.NewDbConn()

	for _, v := range list {
		if v.FinanceCode == "" {
			if _, err := session.Exec("update sync_zl_product_record set deal_status = 3 where id = ?;", v.Id); err != nil {
				glog.Error("SyncZlProductnewchild,更新子龙商品表失败,ID:", v.Id, ",,error:", err.Error())
			}
			continue
		}
		if v.ProductId == 0 {
			if _, err := session.Exec("update sync_zl_product_record set deal_status = 2 where id = ?;", v.Id); err != nil {
				glog.Error("SyncZlProductnewchild,更新子龙商品表失败,ID:", v.Id, ",,error:", err.Error())
			}
			continue
		}
		//查询是否有组合
		var pid []string
		if err := session.SQL("select distinct product_id from dc_product.channel_sku_group where group_product_id =?;", v.ProductId).
			Find(&pid); err != nil {
			glog.Error("SyncZlProductnewchild,查询渠道商品失败,商品id:", v.ProductId, ",error:", err.Error())
			continue
		}
		var productIds []int
		productIds = append(productIds, v.ProductId)
		if len(pid) > 0 {
			productIds = append(productIds, cast.ToInt(pid))
		}
		//根据仓库编码查询对应的门店
		var FinanceChannle = make([]models.WarehouseRelationShop, 0)
		if err := session.SQL(`select wrs.shop_id,wrs.channel_id from dc_dispatch.warehouse_relation_shop wrs 
    left join dc_dispatch.warehouse w on wrs.warehouse_id = w.id where w.code =? and w.category =3 
	group by wrs.shop_id,wrs.channel_id;`, v.FinanceCode).Find(&FinanceChannle); err != nil {
			glog.Error("SyncZlProductnewchild,根据仓库编码查询对应的门店,id:", v.Id, ",error:", err.Error())
			continue
		}
		if len(FinanceChannle) == 0 {
			glog.Error("SyncZlProductnewchild,根据仓库编码查询对应的门店，无记录,id:", v.Id)
			continue
		}

		for _, vv := range FinanceChannle {

			channelProduct := make([]models.ChannelStoreProduct, 0)

			if err := session.Table("channel_store_product").Where("finance_code = ? and channel_id =?", vv.ShopId, vv.ChannelId).In("product_id", productIds).
				Find(&channelProduct); err != nil {
				glog.Error("SyncZlProductnewchild,查询渠道商品失败,商品id:", v.ProductId, ",error:", err.Error())
				continue
			}

			if len(channelProduct) > 0 {
				var ids []string
				for _, val := range channelProduct {
					////过滤第三方渠道药品
					//if v.IsDrugs == 1 && (val.ChannelId == 2 || val.ChannelId == 3 || val.ChannelId == 4 || v.IsPrescribedDrug == 0) {
					//	glog.Info("SyncZlProductnewchild,过滤第三方渠道药品,产品ID:", v.ProductId, "channel_id:", val.ChannelId)
					//	continue
					//}

					key := fmt.Sprintf("%s%d", val.FinanceCode, val.ChannelId)
					if _, has := channelShopKey[key]; has {
						ids = append(ids, cast.ToString(val.Id))
					}
				}
				if len(ids) > 0 {
					idStr := strings.Join(ids, ",")
					if v.CanSell == 0 {
						//不可销下架
						if _, err := session.Exec("update channel_store_product set down_type = 5 " +
							"where up_down_state = 1 and down_type not in (-1,2) and id in (" + idStr + ");"); err != nil {
							glog.Error("SyncZlProductnewchild,更新商品状态异常,产品编号:", ids, ",error:", err)
							continue
						}
					} else if v.CanSell == 1 {
						//可销上架
						if _, err := session.Exec("update channel_store_product set down_type = 7 " +
							"where up_down_state =0 and down_type not in (-1,2) and id in (" + idStr + ");"); err != nil {
							glog.Error("SyncZlProductnewchild,更新商品状态失败,:", ids, ",error:", err)
							continue
						}
					}
				}
			}
			//更新为已处理状态
			if _, err := session.Exec("update sync_zl_product_record set deal_status=1 where id = ?;", v.Id); err != nil {
				glog.Error("SyncZlProductnewchild,更新子龙商品表失败,商品货号:", v.ItemCode, ",,error:", err.Error())
				continue
			}
		}
	}
}

func SyncZlProductChild() {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("--------------SyncZlProductChild error-------------", err)
		}
	}()

	SyncZlProductChildLock := "productcenter:task:SyncZlProductChild"
	//任务已在执行
	redis := services.KeepAliveRedisConn()
	if !redis.SetNX(SyncZlProductChildLock, time.Now().Unix(), 1*time.Minute).Val() {
		return
	}
	defer func() {
		_, err := redis.Del(SyncZlProductChildLock).Result()
		if err != nil {
			redis = services.KeepAliveRedisConn()
			redis.Del(SyncZlProductChildLock)
		}
	}()

	glog.Info(fmt.Sprintf("SyncZlProductChildLock 任务开始"))
	betinTime := time.Now().Add(-5 * 24 * time.Hour).Format(kit.DATETIME_LAYOUT)
	session := services.NewDbConn()
	list := make([]models.SyncZlProduct, 0)
	session.SQL("select * from dc_product.sync_zl_product where update_time>? and deal_status = 0 limit 5000;", betinTime).Find(&list)
	if len(list) == 0 {
		return
	}

	if len(list) > 0 {
		l := 1000
		//写协程
		wg := sync.WaitGroup{}
		var gNum = runtime.NumCPU()
		channelG := make(chan bool, gNum)
		for {
			channelG <- true
			wg.Add(1)
			if len(list) < l {
				l = len(list)
			}
			_list := list[:l]
			go func() {
				defer func() {
					<-channelG
					wg.Done()
				}()
				syncChild(_list)
			}()
			list = list[l:]
			if len(list) == 0 {
				break
			}
		}
		//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
		wg.Wait()
		close(channelG)
	}
	glog.Info(fmt.Sprintf("SyncZlProductChild 任务结束"))
}

type SyncZlProductRecord struct {
	ItemCode   string `json:"item_code"`
	ZilongId   string `json:"zilong_id"`
	CanSell    int    `json:"can_sell"`
	DealStatus int    `json:"deal_status"`
}

func syncChild(list []models.SyncZlProduct) {
	session := services.NewDbConn()
	record := new(models.SyncZlProductRecord)
	for _, v := range list {
		if v.ItemCode == "" || v.ItemCode == "0" {
			if _, err := session.Exec("update dc_product.sync_zl_product set deal_status=2 where id = ?;", v.Id); err != nil {
				glog.Error("syncChild,更新子龙商品表失败,error:", err.Error())
			}
			continue
		}
		if has, err := session.SQL("select * from dc_product.sync_zl_product_record where item_code=? and zilong_id =?;", v.ItemCode, v.ZilongId).Get(record); err != nil {
			glog.Error("syncChild,查询子龙商品记录失败,error:", err.Error())
			continue
		} else if !has {
			data := SyncZlProductRecord{
				ItemCode:   v.ItemCode,
				ZilongId:   v.ZilongId,
				CanSell:    cast.ToInt(v.CanSell),
				DealStatus: cast.ToInt(v.DealStatus),
			}
			if _, err := session.Insert(&data); err != nil {
				glog.Error("syncChild,添加子龙商品数据失败,添加数据:", kit.JsonEncode(data), ",error:", err.Error())
				continue
			}
		} else {
			if record.CanSell != v.CanSell {
				if _, err := session.Exec("update dc_product.sync_zl_product_record set deal_status=0,can_sell =? where id = ?;", v.CanSell, record.Id); err != nil {
					glog.Error("syncChild,更新子龙商品记录表失败,error:", err.Error())
					continue
				}
			}
		}
		if _, err := session.Exec("update dc_product.sync_zl_product set deal_status=1 where id = ?;", v.Id); err != nil {
			glog.Error("syncChild,更新子龙商品表失败,error:", err.Error())
			continue
		}
	}
}
