package tasks

import (
	"fmt"
	"testing"
)

func Test_syncSnapshotToRedis(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "初始化商品快照到redis",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//syncSnapshotToRedis()
		})
	}
}

func Test_syncSkuPromotionToRedis(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "初始化优惠商品快照到redis",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//syncSkuPromotionToRedis()
		})
	}
}

func Test_syncAllSnapshotToRedis(t *testing.T) {
	tests := []struct {
		name string
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//syncAllSnapshotToRedis()
		})
	}
}

func TestAutoShelvesAw(t *testing.T) {
	//tests := []struct {
	//	name string
	//}{
	//	// TODO: Add test cases.
	//}
	//AutoShelvesMt()
	fmt.Println("111")
	AutoShelvesAw()
	//for _, tt := range tests {
	//	t.Run(tt.name, func(t *testing.T) {
	//		AutoShelvesAw()
	//	})
	//}
}
