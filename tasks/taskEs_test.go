package tasks

import (
	"_/proto/pc"
	"_/services"
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
	"github.com/robfig/cron/v3"
)

func Test_syncChannelProductUpDownToEs(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "11",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			syncChannelProductUpDownToEs()
		})
	}
}

func Test_initEsTask(t *testing.T) {
	type args struct {
		c *cron.Cron
	}
	var c cron.Cron
	tests := []struct {
		name string
		args args
	}{
		{
			name: "11",
			args: args{
				c: &c,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			initEsTask(tt.args.c)
		})
	}
}

func TestTemp(t *testing.T) {
	client, _ := elastic.NewClient(elastic.SetSniff(false), elastic.SetURL("http://10.1.1.242:9201"), elastic.SetBasicAuth("elastic", "kCC6y672Wexjw44a"))
	client.DeleteByQuery(indexName).Query(elastic.NewMatchAllQuery()).Do(context.Background())
	return
	for i := 1; i < 999; i++ {
		db := services.NewDbConn()
		var channelproducts []esChannelStoreProduct
		err := db.SQL("select csp.Id,csp.up_down_state,csp.finance_code,csp.sku_id,cps.json_data,csp.update_date, REPLACE(REPLACE(CONCAT(pt.species,',', pt.varieties,',', pt.sex,',', pt.shape,',', pt.age,',', pt.special_stage,',', pt.is_sterilization,',', pt.content_type,',', pt.status),'不限,',''),',不限','') as tags FROM channel_store_product csp INNER JOIN channel_product_snapshot cps ON cps.id = csp.snapshot_id LEFT JOIN product_tag pt on pt.sku_id=csp.sku_id AND pt.product_type=3 where csp.channel_id=1 limit ?,10000", i*10000-10000).Find(&channelproducts)

		if len(channelproducts) == 0 {
			break
		}
		// println(len(channelproducts))
		// continue

		if err != nil {
			glog.Error(err)
		} else if len(channelproducts) > 0 {

			// client := es.NewEsClient()
			bulkRequest := client.Bulk()
			for _, channelproduct := range channelproducts {

				// 生成Id
				var id = fmt.Sprintf("%s-%d-%s", channelproduct.FinanceCode, services.ChannelAwenId, channelproduct.SkuId)
				// 保存到es
				if channelproduct.UpDownState == 1 {
					var productData pc.ChannelProductRequest
					json.Unmarshal([]byte(channelproduct.JsonData), &productData)
					// 赋值标签
					productData.Tags = channelproduct.Tags
					productData.FinanceCode = channelproduct.FinanceCode
					bulkRequest.Add(elastic.NewBulkIndexRequest().Index(indexName).Id(id).Doc(productData))
				}
			}
			res, err := bulkRequest.Do(context.Background())
			glog.Info(err, res.Errors)

		}
	}
}
