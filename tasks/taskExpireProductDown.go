package tasks

import (
	"_/enum"
	"_/models"
	"_/services"
	"fmt"
	"strings"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
)

func initExpireProductDown(c *cron.Cron) {
	// 检查是否有过期商品
	c.AddFunc("@every 10s", func() {
		taskCron("productcenter:task:expireProductDown", func() {
			expireProductDown()
		}, time.Hour*6)
	})
}

// 过期的虚拟商品/组合商品自动
func expireProductDown() {

	glog.Info("productcenter:task:expireProductDown run ...")
	var db = services.NewDbConn()
	var expireChannelProductQuery = db.Table(&models.ChannelProduct{}).Where("term_type=1").And("now()>from_unixtime(term_value-86400)")
	// 记录任务执行日志
	var taskLog strings.Builder
	taskLog.WriteString("expireProductDown 开始执行")
	defer func() {
		if err := recover(); err != nil {
			taskLog.WriteString("任务出现了未处理异常")
			glog.Error("expireProductDown ", err)
		}
		glog.Info(taskLog.String())

	}()

	// A.查询是否有过期的商品数量
	expireCount, err := expireChannelProductQuery.Clone().Count()
	if err != nil {
		taskLog.WriteString(fmt.Sprintf("查询过期商品数量出现了未处理的异常:%s", err.Error()))
		return
	}
	if expireCount > 0 {
		// 先处理单个商品
		var size = 1000
		var start = 0
		for {
			// 分页查询商品信息
			var channelProducts []*models.ChannelProduct
			err := expireChannelProductQuery.OrderBy("id desc").Limit(size, start).Find(&channelProducts)
			var productsList []int32
			for _, v := range channelProducts {
				productsList = append(productsList, v.Id)
			}
			glog.Info("查询的过期商品列表productsList： ", productsList)
			if err != nil {
				taskLog.WriteString(fmt.Sprintf("分页查询过期商品出现了未处理的异常:%s", err.Error()))
				break
			}
			// 处理渠道商品
			for _, channelProduct := range channelProducts {
				// 需要处理的商品列表
				var productIds []string
				// 添加组合商品
				err = db.Table(&models.ChannelSkuGroup{}).Where("channel_id=?", channelProduct.ChannelId).Where("group_product_id=?", channelProduct.Id).Distinct("product_id").Select("product_id").Find(&productIds)
				if err != nil {
					taskLog.WriteString(fmt.Sprintf("查询过期商品的组合商品出现了未处理的异常:%s", err.Error()))
					continue
				}
				// 添加本地商品
				productIds = append(productIds, cast.ToString(channelProduct.Id))

				taskLog.WriteString(fmt.Sprintf(" 开始执行下架:%s,", strings.Join(productIds, "-")))

				// 依次处理商品西甲
				for _, productId := range productIds {
					// 查询过期商品的上架信息
					var financeCodes []string
					err := db.Table(&models.ChannelStoreProduct{}).Where("channel_id=?", channelProduct.ChannelId).And("product_id=?", productId).And("up_down_state=1").Select("finance_code").Find(&financeCodes)
					if err != nil {
						taskLog.WriteString(fmt.Sprintf("查询过期商品上架出现了未处理的异常:%s", err.Error()))
						continue
					}
					// 查询单个商品组成的组合商品
					if len(financeCodes) > 0 {
						// 开始下架
						var channelProductUpDown services.ChannelProductUpDown
						channelProductUpDown.ProductIds = append(channelProductUpDown.ProductIds, productId)
						channelProductUpDown.ChannelId = int(channelProduct.ChannelId)
						channelProductUpDown.FinanceCodes = append(channelProductUpDown.FinanceCodes, financeCodes...)
						channelProductUpDown.UserNo = "expireProductDown"
						channelProductUpDown.UserName = "expireProductDown"
						channelProductUpDown.DownType = enum.DownRecordTypeExpire // 过期商品自动下架
						channelProductUpDown.DownPorudct()
						if channelProductUpDown.UnknownError != nil {
							taskLog.WriteString(fmt.Sprintf("渠道:%d,商品：%d,门店:%s 过期下架出现了未处理异常:%s",
								channelProduct.ChannelId, channelProduct.Id, strings.Join(financeCodes, "-"), err))
						}
						for _, v := range channelProductUpDown.UpResult {
							if v.IsSuccess == false {
								taskLog.WriteString(fmt.Sprintf("渠道:%d,商品：%d,门店:%s 下架失败:%s", channelProduct.ChannelId, channelProduct.Id, v.StoreProduct.StoreFinanceCode, v.Message))
							}
						}
					}

					//todo 处理组合商品下架
					glog.Info("处理组合商品下架 run ")
					var dataProduct = make([]models.ChannelStoreProduct, 0)
					db.Table("channel_store_product").SQL(`SELECT * FROM dc_product.channel_store_product b WHERE b.product_id IN (
							SELECT DISTINCT product_id FROM dc_product.channel_sku_group a WHERE a.group_product_id = ? ) AND b.up_down_state = 1 ;`, productId).Find(&dataProduct)
					glog.Info("处理组合商品下架列表： ", len(dataProduct))
					for _, v_group := range dataProduct {
						// 开始下架
						glog.Info("下架组合商品。。。", len(dataProduct), " 組合商品的id： ", v_group.ProductId, " 下架渠道：", v_group.ChannelId)
						var channelProductUpDown services.ChannelProductUpDown
						channelProductUpDown.ProductIds = append(channelProductUpDown.ProductIds, cast.ToString(v_group.ProductId))
						channelProductUpDown.ChannelId = int(v_group.ChannelId)
						channelProductUpDown.FinanceCodes = append(channelProductUpDown.FinanceCodes, v_group.FinanceCode)
						channelProductUpDown.UserNo = "expireProductDown"
						channelProductUpDown.UserName = "expireProductDown"
						channelProductUpDown.DownType = enum.DownRecordTypeExpire // 过期商品自动下架
						channelProductUpDown.DownPorudct()
						if channelProductUpDown.UnknownError != nil {
							taskLog.WriteString(fmt.Sprintf("渠道组合商品下架:%d,商品：%d,门店:%s 过期下架出现了未处理异常:%s", channelProduct.ChannelId, channelProduct.Id, strings.Join(financeCodes, "-"), err))
						}
						for _, v := range channelProductUpDown.UpResult {
							if v.IsSuccess == false {
								taskLog.WriteString(fmt.Sprintf("渠道组合商品下架:%d,商品：%d,门店:%s 下架失败:%s", channelProduct.ChannelId, channelProduct.Id, v.StoreProduct.StoreFinanceCode, v.Message))
							}
						}
					}

				}
			}

			// 查询组合商品

			if len(channelProducts) < size {
				break
			}

			// 取下一页数据
			start = start + size
		}
		// 处理组合商品

	}

}
