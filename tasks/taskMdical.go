package tasks

import (
	"_/models"
	"_/services"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

func initMedicalTask(c *cron.Cron) {
	// 每天1点开始自动上架医疗互联网门店仓商品
	c.AddFunc("0 1 * * *", AutoShelvesMedical)

	//每天0点开始自动上架医疗互联网前置仓商品
	c.AddFunc("0 0 * * *", AutoShelvesMedicalQz)

	//每天1点开始自动同步互联网医疗商品价格
	c.AddFunc("0 1 * * *", AutoSyncPriceMedical)

}

//自动上架--医疗互联网门店仓商品
func AutoShelvesMedical() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesMedical"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer func() {
		redis.Del(taskLock)
	}()
	glog.Info("AutoShelvesMedical任务开始")

	var service services.Product
	warehouses, err := service.GezZLWarehouseList(services.ChannelDigitalHealth)
	if err != nil {
		glog.Error("医疗互联网门店仓商品关系查询异常", err.Error())
		return
	}

	for _, v := range warehouses {
		//根据仓库编码查询门店财务数据
		list := service.GetMedicalProductSimpleInfo(v.ShopId, services.ChannelDigitalHealth, v.WarehouseId, 4)

		if len(list) > 0 {
			//每次处理数据100条
			l := 100
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(list) < l {
					l = len(list)
				}
				_list := list[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					ItemToAuto(_list, services.ChannelDigitalHealth, 3)
				}()
				list = list[l:]
				if len(list) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)
		}
		glog.Info(fmt.Sprintf("该医疗门店仓仓库：%s已执行完成", v.ShopId))
	}
	glog.Info("AutoShelvesMedical任务结束")
}

// 自动上架--医疗互联网前置仓商品
func AutoShelvesMedicalQz() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesMedicalQz"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	glog.Info("AutoShelvesMedicalQz任务开始")

	var prodcut services.Product
	warehouses, err := prodcut.GezQZWarehouseList(services.ChannelDigitalHealth)
	if err != nil {
		glog.Error("医疗互联网前置仓商品关系查询异常", err.Error())
		return
	}

	var idList []int32                    // 所有的仓库id列表
	idForCode := make(map[int32][]string) // 仓库id和门店code的关系
	codeForId := make(map[string][]int32) // 门店code和仓库id的关系
	for _, v := range warehouses {
		if _, ok := idForCode[int32(v.WarehouseId)]; !ok {
			idList = append(idList, int32(v.WarehouseId))
		}
		codeForId[v.ShopId] = append(codeForId[v.ShopId], int32(v.WarehouseId))
		idForCode[int32(v.WarehouseId)] = append(idForCode[int32(v.WarehouseId)], v.ShopId)
	}

	var service services.Product
	// 查询所有前置仓商品和价格
	var priceMap = make(map[string][]models.PriceSync)
	for code, ids := range codeForId {
		for i, _ := range ids {
			info := service.GetMedicalProductSimpleInfo(code, services.ChannelDigitalHealth, int(ids[i]), 2)
			// 查询当前前置仓商品和价格
			if len(info) > 0 {
				priceMap[code] = append(priceMap[code], info...)
			}
		}
	}

	for code, list := range priceMap {
		if len(list) > 0 {
			//每次处理数据100条
			l := 100
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(list) < l {
					l = len(list)
				}
				_list := list[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					ItemToAuto(_list, services.ChannelDigitalHealth, 4)
				}()
				list = list[l:]
				if len(list) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)
		}
		glog.Info(fmt.Sprintf("该医疗前置仓库：%s已执行完成", code))
	}
	glog.Info("AutoShelvesMedicalQz任务结束")
}

func AutoSyncPriceMedical() {
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoSyncPriceMedical"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	glog.Info("AutoSyncPriceMedical任务开始")

	now := time.Now()
	last := now.AddDate(0, 0, -1)
	nowStr := now.Format("2006-01-02")
	lastStr := last.Format("2006-01-02")
	db := services.NewDbConn()
	var prices []models.HospitalProductPriceDto
	db.Table("hospital_product_price").
		Join("inner", "channel_sku", "channel_sku.id = hospital_product_price.sku_id").
		Select("hospital_product_price.id id,hospital_product_price.price price,channel_sku.id sku_id,channel_sku.product_id product_id").
		Where("channel_sku.channel_id = ? and hospital_product_price.update_date between ? and ?", services.ChannelDigitalHealth, lastStr, nowStr).Find(&prices)
	session := db.NewSession()
	session.Begin()
	defer session.Close()
	for _, v := range prices {
		// 更新快照价格
		sql := "UPDATE channel_product_snapshot SET json_data = JSON_SET(json_data, '$.sku_info[0].prepose_price', ?)," +
			"json_data = JSON_SET(json_data, '$.sku_info[0].retail_price', ?)," +
			"json_data = JSON_SET(json_data, '$.sku_info[0].market_price', ?)," +
			"json_data = JSON_SET(json_data, '$.sku_info[0].store_price', ?) WHERE channel_id = ?  AND product_id = ?"
		_, err := db.Exec(sql, v.Price, v.Price, v.Price, v.Price, services.ChannelDigitalHealth, v.ProductId)
		if err != nil {
			session.Rollback()
			glog.Errorf("%s更新快照价格, skuId: %v, err: %s", "AutoSyncPriceMedical", v.SkuId, err.Error())
			continue
		}
		// 更新上架价格
		_, err = session.Where("channel_id = ? and product_id = ?",
			services.ChannelDigitalHealth, v.ProductId).Cols("store_price", "market_price", "retail_price", "prepose_price").Update(&models.ChannelSku{
			PreposePrice: cast.ToInt32(v.Price),
			StorePrice:   cast.ToInt32(v.Price),
			MarketPrice:  cast.ToInt32(v.Price),
			RetailPrice:  cast.ToInt32(v.Price),
		})
		if err != nil {
			session.Rollback()
			glog.Errorf("%s更新上架价格, skuId: %v, err: %s", "AutoSyncPriceMedical", v.SkuId, err.Error())
			continue
		}
		err = session.Commit()
		if err != nil {
			session.Rollback()
			glog.Errorf("%s提交更新, skuId: %v, err: %s", "AutoSyncPriceMedical", v.SkuId, err.Error())
			continue
		}
		glog.Info(fmt.Sprintf("该医疗商品更新价格：%v已执行完成", v.SkuId))
	}
	glog.Info("AutoShelvesMedical任务结束")
}
