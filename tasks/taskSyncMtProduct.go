package tasks

import (
	"_/enum"
	"_/models"
	"_/proto/et"
	"_/services"
	"_/utils"
	"context"
	"encoding/json"
	"strings"
	"sync"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/streadway/amqp"
	kit "github.com/tricobbler/rp-kit"
)

//插入MQ的实体结构

type ChannelStore struct {
	FinanceCode     string `json:"finance_code" xorm:"not null comment('财务编码') VARCHAR(50) 'finance_code'"`
	ChannelStoreId  string `json:"channel_store_id" xorm:"default 'null' comment('渠道门店id') VARCHAR(50) 'channel_store_id'"`
	ChannelId       int    `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)') INT 'channel_id'"`
	AppChannel      int    `json:"app_channel" xorm:"default 0 comment('1.阿闻自有,2.TP代运营') INT 'app_channel'"`
	ChannelStoreId1 string `json:"channel_store_id1" xorm:"default 'null' comment('渠道门店id') VARCHAR(50) 'channel_store_id1'"`
}

// 根据传进来的参数获取的要处理的门店，丢到MQ处理
func StoreIntoMQ(channel_id int) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("--------------StoreIntoMQ error-------------", err)
		}
	}()

	glog.Info("检测数据开始 StoreIntoMQ channel_id:", channel_id)
	redis := services.KeepAliveRedisConn()

	MtStoretaskLock := "product-center:task-lock-StoreIntoMQ" + cast.ToString(channel_id)
	//任务已在执行
	if !redis.SetNX(MtStoretaskLock, 1, time.Minute).Val() {
		glog.Info("Pushmqgo already running")
		return
	}
	defer func() {
		redis.Del(MtStoretaskLock)
	}()

	var stores []ChannelStore
	//先查询所有要处理的美团门店
	var conn = services.NewDatacenterDbConn()
	//conn.ShowSQL()
	err := conn.Table("store").Alias("b").
		Join("left", "store_relation a", "b.finance_code=a.finance_code and a.channel_id=2").
		Join("left", "store_relation c", "c.finance_code=a.finance_code and c.channel_id=3").
		Where(" b.org_id=1 and do_status=1 ").
		Select("b.finance_code,COALESCE(a.channel_store_id, '') AS channel_store_id,COALESCE(c.channel_store_id, '') AS channel_store_id1,b.app_channel").Find(&stores)
	if err != nil {
		glog.Error("StoreIntoMQ 查询要处理的数据失败")
	}

	for _, x := range stores {

		if x.ChannelStoreId == "" {
			mqInfo := new(models.MqInfo)
			copyMode := x
			copyMode.ChannelId = 2
			mqInfo.Quene = "Store-product-check" + "-del"
			mqInfo.Content = kit.JsonEncode(copyMode)
			mqInfo.Exchange = "product"
			mqInfo.Ispush = 0
			_, err := conn.Table("dc_order.mq_info").Insert(mqInfo)
			if err != nil {
				glog.Error("ExpireUserSearch 过期用户查询插入MQ出错： ", err.Error())
			}

		} else {
			mqInfo := new(models.MqInfo)
			copyMode := x
			copyMode.ChannelId = 2
			mqInfo.Quene = "Store-product-check" + "-mt"
			mqInfo.Content = kit.JsonEncode(copyMode)
			mqInfo.Exchange = "product"
			mqInfo.Ispush = 0
			_, err := conn.Table("dc_order.mq_info").Insert(mqInfo)
			if err != nil {
				glog.Error("ExpireUserSearch 过期用户查询插入MQ出错： ", err.Error())
			}

		}

		if x.ChannelStoreId1 == "" {
			mqInfo := new(models.MqInfo)
			copyMode := x
			copyMode.ChannelId = 3
			mqInfo.Quene = "Store-product-check" + "-del"
			mqInfo.Content = kit.JsonEncode(copyMode)
			mqInfo.Exchange = "product"
			mqInfo.Ispush = 0
			_, err := conn.Table("dc_order.mq_info").Insert(mqInfo)
			if err != nil {
				glog.Error("ExpireUserSearch 过期用户查询插入MQ出错： ", err.Error())
			}

		} else {
			mqInfo := new(models.MqInfo)
			copyMode := x
			copyMode.ChannelId = 3
			copyMode.ChannelStoreId = x.ChannelStoreId1
			mqInfo.Quene = "Store-product-check" + "-elm"
			mqInfo.Content = kit.JsonEncode(copyMode)
			mqInfo.Exchange = "product"
			mqInfo.Ispush = 0
			_, err := conn.Table("dc_order.mq_info").Insert(mqInfo)
			if err != nil {
				glog.Error("ExpireUserSearch 过期用户查询插入MQ出错： ", err.Error())
			}

		}

		////如果是美团的
		//if x.ChannelId == enum.ChannelMtId {
		//	mqInfo.Quene = "Store-product-check" + "-mt"
		//} else { //饿了么的
		//	mqInfo.Quene = "Store-product-check" + "-elm"
		//}
		////如果第三方ID是空的。那么进入删除本地数据队列
		//if x.ChannelStoreId == "" {
		//	mqInfo.Quene = "Store-product-check" + "-del"
		//}

		//_, err := conn.Table("dc_order.mq_info").Insert(mqInfo)
		//if err != nil {
		//	glog.Error("ExpireUserSearch 过期用户查询插入MQ出错： ", err.Error())
		//}
	}
	glog.Error("StoreIntoMQ MQ插入完成")
}

// 消费上面的MQ，的用户，一个一个的处理
func CheckMtProductDetail() {
	utils.ConsumeNew("Store-product-check-mt", "Store-product-check-mt", "product", func(d amqp.Delivery, channelGAll <-chan bool) (response string, err error) {
		//return "fail", err
		defer func() {
			<-channelGAll
		}()

		request := string(d.Body)
		var modelMqInfo ChannelStore
		if err := json.Unmarshal([]byte(request), &modelMqInfo); err != nil {
			glog.Error("Store-product-check-mt 出错", err.Error(), string(request))
			d.Ack(false)
			return "success", nil
		}
		if modelMqInfo.AppChannel == 2 || modelMqInfo.AppChannel == 11 || modelMqInfo.AppChannel == 12 || modelMqInfo.AppChannel == 13 {
			d.Ack(false)
			return "success", nil
		}
		defer glog.Info("跑美团数据结束 ", modelMqInfo.FinanceCode)

		//先判断是否限流了
		allowed := LimitRate("RetailList", 15, 1*time.Second, 1)
		if !allowed {
			glog.Info("CheckMtProductDetail 被限流")
			d.Nack(false, true)
			return "success", nil
		}

		glog.Info("跑美团数据开始 ", modelMqInfo.FinanceCode)
		//调用第三方接口查询这个门店的数据
		queryErrors := 0
		externalGrpc := et.GetExternalClient()

		getRequest := new(et.RetailListRequest)
		getRequest.StoreMasterId = cast.ToInt32(modelMqInfo.AppChannel)
		getRequest.AppPoiCode = modelMqInfo.ChannelStoreId
		getRequest.FinanceCode = modelMqInfo.FinanceCode
		getRequest.PageIndex = 1
		getRequest.PageSize = 200 //todo 写成配置

		dataResponse, err := externalGrpc.RPC.RetailList(externalGrpc.Ctx, getRequest)
		if err != nil {

			//门店不存在，删除所有的上架信息
			if strings.Contains(err.Error(), "不存在") {
				var conn = services.NewDbConn()
				_, err1 := conn.Exec("delete from channel_store_product where finance_code=?  and channel_id=?", modelMqInfo.FinanceCode, modelMqInfo.ChannelId)
				if err1 != nil {
					glog.Error("CheckMtProductDetail 删除本地product_third_id为数据失败：", modelMqInfo.FinanceCode, modelMqInfo.FinanceCode)
					d.Nack(false, true)
				} else {
					_, err1 = conn.Exec("delete from channel_product_snapshot where finance_code=?  and channel_id=?", modelMqInfo.FinanceCode, modelMqInfo.ChannelId)
					if err1 != nil {
						glog.Error("CheckMtProductDetail 删除本地product_third_id为数据失败：", modelMqInfo.FinanceCode, modelMqInfo.FinanceCode)
						d.Nack(false, true)
					} else {
						glog.Info("CheckMtProductDetail 不存在数据，删除本地数据：", modelMqInfo.FinanceCode, modelMqInfo.FinanceCode)
						d.Ack(false)
					}
				}
				_, err1 = conn.Exec("UPDATE datacenter.`store` SET do_status=do_status+1 WHERE finance_code=?", modelMqInfo.FinanceCode)
				if err1 != nil {
					glog.Error("CheckMtProductDetail 修改跑数据次数+1失败：", modelMqInfo.FinanceCode)
				}

			} else {
				d.Nack(false, true)
				glog.Error("CheckMtProductDetail 查询美团数据出错：", err.Error(), modelMqInfo.FinanceCode)
			}
			return "success", nil
		}
		if dataResponse == nil || len(dataResponse.Data) == 0 {
			d.Ack(false)
			glog.Error("CheckMtProductDetail 查询美团数据出错：")
			var conn = services.NewDbConn()
			_, err1 := conn.Exec("UPDATE datacenter.`store` SET do_status=do_status+1 WHERE finance_code=?", modelMqInfo.FinanceCode)
			if err1 != nil {
				glog.Error("CheckMtProductDetail 修改跑数据次数+1失败：", modelMqInfo.FinanceCode)
			}
			return "success", nil
		}

		//总数
		totalPage := int(dataResponse.ExtraInfo.TotalCount/getRequest.PageSize + 1)
		var List []*et.StoreRetailInfo
		//一个循环将所有的数据查询完
		wg := new(sync.WaitGroup)
		var mu sync.Mutex
		var muerr sync.Mutex
		channelG := make(chan bool, 3)
		for i := 1; i <= totalPage; i++ {

			//fmt.Println("查询++++++", syncRequest.PageIndex)
			if LimitRate("RetailList", 15, 1*time.Second, 1) {
				//没有数据或者报错了则结束循环
				wg.Add(1)
				go func(page int) {
					defer wg.Done()
					syncRequest := new(et.RetailListRequest)
					syncRequest.StoreMasterId = cast.ToInt32(modelMqInfo.AppChannel)
					syncRequest.AppPoiCode = modelMqInfo.ChannelStoreId
					syncRequest.FinanceCode = modelMqInfo.FinanceCode
					syncRequest.PageIndex = int32(page)
					syncRequest.PageSize = 200 //todo 写成配置

					deadLineCtx, _ := context.WithTimeout(context.Background(), 12*time.Hour)
					responseLeft, errRpc := externalGrpc.RPC.RetailList(deadLineCtx, syncRequest)
					if errRpc != nil {
						muerr.Lock() // 加锁
						queryErrors++
						muerr.Unlock() // 解锁
					}
					if responseLeft == nil {
						muerr.Lock() // 加锁
						queryErrors++
						muerr.Unlock() // 解锁
					}
					if len(responseLeft.Data) > 0 {
						mu.Lock() // 加锁
						List = append(List, responseLeft.Data...)
						mu.Unlock() // 解锁

					} else if len(responseLeft.Data) == 0 {
						//没有数据了
					}
				}(i)

			} else {
				queryErrors++
				d.Nack(false, true)
				glog.Error("CheckMtProductDetail 因为限流丢失了数据", modelMqInfo.FinanceCode)
				return "success", nil
				break
			}
			//	}(i)
		}

		//处理逻辑
		wg.Wait()
		wgdel := new(sync.WaitGroup)
		//直接Update数据，如果返回大于0，说明有数据，否则删除数据
		for _, x := range List {
			var conn = services.NewDbConn()
			result, err1 := conn.Exec("update channel_product_snapshot set product_third_id=product_id where finance_code=? and product_id=? and product_third_id='' and channel_id=?", modelMqInfo.FinanceCode, cast.ToInt32(x.AppFoodCode), enum.ChannelMtId)
			if err1 != nil {
				queryErrors++
				glog.Error("CheckMtProductDetail 修改数据出错：", modelMqInfo.FinanceCode, x.AppFoodCode)
				break
			}
			count, _ := result.RowsAffected()
			//如果没有修改成功
			if count == 0 {
				//查询一下我们这边是否存在，不存在的话，删除第三方
				ishave := 0
				_, err := conn.SQL("SELECT 1 FROM channel_product_snapshot  where finance_code=? and product_id=? and channel_id=? ", modelMqInfo.FinanceCode, cast.ToInt32(x.AppFoodCode), enum.ChannelMtId).Get(&ishave)
				if err != nil {
					queryErrors++
					glog.Error("CheckMtProductDetail 查询是否存在数据出错：", modelMqInfo.FinanceCode, x.AppFoodCode)
					//报错了就先不删除第三方数据
					ishave = 1
					break
				}
				//我们这边没有查询到，删除第三方
				if ishave == 0 {

					var skuInfo []*MtSku

					_ = json.Unmarshal([]byte(x.Skus), &skuInfo)

					requestDel := et.RetailDeleteReq{
						AppPoiCode:        modelMqInfo.ChannelStoreId,
						AppFoodCode:       cast.ToString(x.AppFoodCode),
						IsDeleteRetailCat: 2,
					}
					channelG <- true
					wgdel.Add(1)
					//if LimitRate("RetailSkuDelete", 30, 1*time.Second, 1) {
					go func(parIn et.RetailDeleteReq) {
						defer func() {
							<-channelG
							wgdel.Done()
						}()
						externalGrpc = et.GetExternalClient()
						skuDelete, err := externalGrpc.RPC.RetailDelete(context.Background(), &parIn)
						glog.Info("删除美团商品： ", " 入参： ", kit.JsonEncode(parIn), " 返回：", kit.JsonEncode(skuDelete))
						if err != nil {
							glog.Error("删除美团商品异常： ", kit.JsonEncode(parIn), " err: ", err.Error())
						}
					}(requestDel)

					//} else {
					//	glog.Info("CheckMtProductDetail 删除饿了么商品限流导致未删除数据： ", " 入参： ", kit.JsonEncode(requestDel), x.Name)
					//}

				}

			}
		}
		//wg.Wait()
		wgdel.Wait()
		glog.Info("CheckMtProductDetail 最终错误数：", queryErrors, " "+modelMqInfo.FinanceCode)
		if queryErrors == 0 {
			//删除本地所有第三方ID为空的数据
			var conn = services.NewDbConn()
			_, err1 := conn.Exec("delete from channel_product_snapshot where finance_code=? and product_third_id='' and channel_id=?", modelMqInfo.FinanceCode, enum.ChannelMtId)
			if err1 != nil {
				glog.Error("CheckMtProductDetail 删除本地product_third_id为数据失败：", modelMqInfo.FinanceCode)
				d.Nack(false, true)
			} else {
				//删除快照
				_, err1 := conn.Exec("UPDATE channel_store_product csp  LEFT JOIN channel_product_snapshot cp ON csp.snapshot_id = cp.ID  SET csp.up_down_state = 0  WHERE cp.ID IS NULL AND cp.finance_code = ? AND cp.channel_id = ?", modelMqInfo.FinanceCode, enum.ChannelMtId)
				if err1 != nil {
					glog.Error("CheckMtProductDetail 删除本地product_third_id为数据失败：", modelMqInfo.FinanceCode)
					d.Nack(false, true)
				} else {
					_, err1 = conn.Exec("UPDATE datacenter.`store` SET do_status=do_status+1 WHERE finance_code=?", modelMqInfo.FinanceCode)
					if err1 != nil {
						glog.Error("CheckMtProductDetail 修改跑数据次数+1失败：", modelMqInfo.FinanceCode)
					}
					//删除快照
					d.Ack(false)
				}

			}
		} else {
			d.Ack(false)
			glog.Info("CheckMtProductDetail 有错误数据：", queryErrors, " "+modelMqInfo.FinanceCode)
			//d.Nack(false, true)
		}
		return "success", nil
	})
}

// 消费上面的MQ，的用户，一个一个的处理
func CheckELMProductDetail() {
	utils.ConsumeNew("Store-product-check-elm", "Store-product-check-elm", "product", func(d amqp.Delivery, channelGAll <-chan bool) (response string, err error) {
		//return "fail", err
		defer func() {
			<-channelGAll
		}()

		request := string(d.Body)
		var modelMqInfo ChannelStore
		if err := json.Unmarshal([]byte(request), &modelMqInfo); err != nil {
			glog.Error("Store-product-check-elm 出错", err.Error(), string(request))
			d.Ack(false)
			return "success", nil
		}
		if modelMqInfo.AppChannel == 2 || modelMqInfo.AppChannel == 11 || modelMqInfo.AppChannel == 12 || modelMqInfo.AppChannel == 13 {
			d.Ack(false)
			return "success", nil
		}
		defer glog.Info("跑饿了么数据结束 ", modelMqInfo.FinanceCode)
		////先判断是否限流了
		//allowed := LimitRate("GetElmProductList", 15, 1*time.Second, 1)
		//
		//if !allowed {
		//	glog.Info("GetElmProductList 被限流")
		//	d.Nack(false, true)
		//	return "success", nil
		//}
		glog.Info("跑饿了么数据开始 ", modelMqInfo.FinanceCode)
		//调用第三方接口查询这个门店的数据
		queryErrors := 0
		externalGrpc := et.GetExternalClient()

		req := &et.ElmGetProductListRequest{
			ShopId:     modelMqInfo.ChannelStoreId,
			Page:       1,
			Pagesize:   100,
			AppChannel: cast.ToInt32(modelMqInfo.AppChannel),
		}
		dataResponse, err := externalGrpc.ELMPRODUCT.GetElmProductList(externalGrpc.Ctx, req)
		if err != nil {

			d.Nack(false, true)
			glog.Error("CheckELMProductDetail 查询饿了么数据出错：", req, err, dataResponse)
			return "success", nil
		}
		//门店不存在，删除所有的上架信息
		if strings.Contains(dataResponse.Error, "未查询到商户信息") || dataResponse.Error == "gw.AppKeyMissing" {
			var conn = services.NewDbConn()
			_, err1 := conn.Exec("delete from channel_store_product where finance_code=?  and channel_id=?", modelMqInfo.FinanceCode, modelMqInfo.ChannelId)
			if err1 != nil {
				glog.Error("CheckELMProductDetail 删除本地product_third_id为数据失败：", modelMqInfo.FinanceCode)
				d.Nack(false, true)
			} else {
				_, err1 = conn.Exec("delete from channel_product_snapshot where finance_code=?  and channel_id=?", modelMqInfo.FinanceCode, modelMqInfo.ChannelId)
				if err1 != nil {
					glog.Error("CheckELMProductDetail 删除本地product_third_id为数据失败：", modelMqInfo.FinanceCode)
					d.Nack(false, true)
				} else {
					glog.Info("CheckELMProductDetail 不存在商品，删除本地数据：", modelMqInfo.FinanceCode)
					d.Ack(false)
				}
			}
			_, err1 = conn.Exec("UPDATE datacenter.`store` SET do_status=do_status+1 WHERE finance_code=?", modelMqInfo.FinanceCode)
			if err1 != nil {
				glog.Error("CheckMtProductDetail 修改跑数据次数+1失败：", modelMqInfo.FinanceCode)
			}
			return "success", nil
		}
		if dataResponse.Code != 200 {
			d.Nack(false, true)
			glog.Error("CheckELMProductDetail 查询饿了么数据出错：", req, err, dataResponse)
			return "success", nil
		}
		glog.Info("CheckELMProductDetail 所有数据页数", dataResponse.Data.Total)
		var List []*et.ElmProductList
		//wg := new(sync.WaitGroup)
		wgdel := new(sync.WaitGroup)

		offsit := int64(-1)

		for {

			if LimitRate("GetElmProductList", 15, 1*time.Second, 1) {
				//wg.Add(1)
				//go func(page int32) {
				//	defer wg.Done()
				req1 := &et.ElmGetProductListRequest{
					ShopId:      modelMqInfo.ChannelStoreId,
					Page:        1,
					Pagesize:    100,
					AppChannel:  cast.ToInt32(modelMqInfo.AppChannel),
					SkuIdOffset: offsit,
				}
				//req1.Page = i
				externalGrpc = et.GetExternalClient()
				resp, err := externalGrpc.ELMPRODUCT.GetElmProductList(externalGrpc.Ctx, req1)
				if err != nil {
					glog.Info("CheckELMProductDetail 查询饿了么商品列表失败", err.Error(), req1)
					queryErrors++
					break
				}
				if len(resp.Data.List) > 0 {
					List = append(List, resp.Data.List...)
					offsit = int64(resp.Data.SkuIdOffset)
				}
				if resp.Data.Pages == 1 || len(resp.Data.List) == 0 {
					break
				}

				//}(i)

			} else {
				queryErrors++
				d.Nack(false, true)
				glog.Error("GetElmProductList 因为限流丢失了数据", modelMqInfo.FinanceCode)
				return "success", nil
			}
		}
		//wg.Wait()
		channelG := make(chan bool, 3)
		glog.Info("拉取到的饿了么所有商品数据：", modelMqInfo.FinanceCode, kit.JsonEncode(List))
		for _, x := range List {
			var conn = services.NewDbConn()
			productId := -1
			_, err := conn.SQL("SELECT b.product_id FROM channel_product_snapshot a INNER JOIN channel_sku b ON a.product_id=b.product_id AND a.channel_id=b.channel_id WHERE a.finance_code=? AND b.id=? AND a.channel_id=? ", modelMqInfo.FinanceCode, cast.ToInt64(x.CustomSkuId), enum.ChannelElmId).Get(&productId)
			if err != nil {
				queryErrors++
				glog.Error("CheckELMProductDetail 查询是否存在数据出错：", modelMqInfo.FinanceCode, x.CustomSkuId, err.Error())
				d.Nack(false, true)
				return "success", nil
				break
			}
			glog.Info("饿了么周翔标记1", productId)
			if productId > 0 {
				_, err1 := conn.Exec("update channel_product_snapshot set product_third_id=product_id where finance_code=? and product_id=? and product_third_id='' and channel_id=?", modelMqInfo.FinanceCode, productId, enum.ChannelElmId)
				if err1 != nil {
					queryErrors++
					glog.Error("CheckELMProductDetail 修改数据出错：", modelMqInfo.FinanceCode, x.CustomSkuId, err.Error())
					d.Nack(false, true)
					return "success", nil
					break
				}
			} else {
				params := et.UpdateElmShopSkuPriceRequest{
					ShopId:      modelMqInfo.ChannelStoreId,
					CustomSkuId: cast.ToString(x.CustomSkuId),
					AppChannel:  cast.ToInt32(modelMqInfo.AppChannel)}

				if LimitRate("DeleteElmShopSku", 15, 1*time.Second, 1) {
					channelG <- true
					wgdel.Add(1)
					glog.Info("饿了么周翔标记2", params)
					go func(parin et.UpdateElmShopSkuPriceRequest, name string) {
						defer func() {
							<-channelG
							wgdel.Done()
						}()
						externalGrpc = et.GetExternalClient()
						skuDelete, err := externalGrpc.ELMPRODUCT.DeleteElmShopSku(context.Background(), &parin)
						glog.Info("CheckELMProductDetail 删除饿了么商品： ", " 入参： ", kit.JsonEncode(parin), name, " 返回：", kit.JsonEncode(skuDelete))
						if err != nil {
							glog.Error("CheckELMProductDetail 删除饿了么商品异常： ", kit.JsonEncode(parin), " err: ", err.Error())
						}
					}(params, x.Name)

				} else {
					glog.Info("CheckELMProductDetail 删除饿了么商品限流导致未删除数据： ", " 入参： ", kit.JsonEncode(params), x.Name)
				}
			}

		}
		wgdel.Wait()
		glog.Info("CheckMtProductDetail 最终错误数：", queryErrors, " "+modelMqInfo.FinanceCode)
		if queryErrors == 0 {
			//删除本地所有第三方ID为空的数据
			var conn = services.NewDbConn()
			_, err1 := conn.Exec("delete from channel_product_snapshot where finance_code=? and product_third_id='' and channel_id=?", modelMqInfo.FinanceCode, enum.ChannelElmId)
			if err1 != nil {
				glog.Error("CheckELMProductDetail 删除本地product_third_id为数据失败：", modelMqInfo.FinanceCode)
				d.Nack(false, true)
			} else {
				//上架改下架
				_, err1 := conn.Exec("UPDATE channel_store_product csp  LEFT JOIN channel_product_snapshot cp ON csp.snapshot_id = cp.ID  SET csp.up_down_state = 0  WHERE cp.ID IS NULL AND csp.finance_code = ? and csp.up_down_state=1 AND csp.channel_id = ?", modelMqInfo.FinanceCode, enum.ChannelElmId)
				if err1 != nil {
					glog.Error("CheckELMProductDetail 删除本地product_third_id为数据失败：", modelMqInfo.FinanceCode)
					d.Nack(false, true)
				} else {

					_, err1 = conn.Exec("UPDATE datacenter.`store` SET do_status=do_status+1 WHERE finance_code=?", modelMqInfo.FinanceCode)
					if err1 != nil {
						glog.Error("CheckMtProductDetail 修改跑数据次数+1失败：", modelMqInfo.FinanceCode)
					}
					d.Ack(false)
				}
			}
		} else {
			glog.Info("CheckMtProductDetail 有错误没执行：", queryErrors, " "+modelMqInfo.FinanceCode)
			d.Ack(false)
		}

		return "success", nil
	})
}

// 删除没有绑定第三方ID的本地上架表数据
func DelProductDetail() {
	utils.ConsumeNew("Store-product-check-del", "Store-product-check-del", "product", func(d amqp.Delivery, channelGAll <-chan bool) (response string, err error) {
		//return "fail", err
		defer func() {
			<-channelGAll
		}()

		request := string(d.Body)
		var modelMqInfo ChannelStore
		if err := json.Unmarshal([]byte(request), &modelMqInfo); err != nil {
			glog.Error("Store-product-check-del 出错", err.Error(), string(request))
			d.Ack(false)
			return "success", nil
		}

		//删除本地所有第三方ID为空的数据
		var conn = services.NewDbConn()
		_, err1 := conn.Exec("delete from channel_product_snapshot where finance_code=?  and channel_id=?", modelMqInfo.FinanceCode, modelMqInfo.ChannelId)
		if err1 != nil {
			glog.Error("DelProductDetail 删除本地数据失败：", modelMqInfo.FinanceCode)
			d.Nack(false, true)
		} else {
			_, err1 = conn.Exec("delete from channel_store_product where finance_code=?  and channel_id=?", modelMqInfo.FinanceCode, modelMqInfo.ChannelId)
			if err1 != nil {
				glog.Error("DelProductDetail 删除本地product_third_id为数据失败：", modelMqInfo.FinanceCode)
				d.Nack(false, true)
			} else {
				glog.Info("DelProductDetail 不存在商品，删除本地数据：", modelMqInfo.FinanceCode)
				d.Ack(false)
			}
		}

		_, err = conn.Exec("UPDATE datacenter.`store` SET do_status=do_status+1 WHERE finance_code=?", modelMqInfo.FinanceCode)
		if err != nil {
			glog.Error("CheckMtProductDetail 修改跑数据次数+1失败：", modelMqInfo.FinanceCode, err.Error())
		}
		return "success", nil
	})
}

// 此处查询商品的快照信息
type MtSku struct {
	SkuId string `json:"sku_id"`
}

// 限流，并且重试10次
func LimitRate(key string, limit int, duration time.Duration, nowCount int) bool {

	client := services.KeepAliveRedisConn()
	// 使用 INCR 增加请求计数
	count, err := client.Incr(key).Result()
	if err != nil {
		return false
	}

	// 如果是第一次记录计数，设置过期时间
	if count == 1 {
		if err := client.Expire(key, duration).Err(); err != nil {
			return false
		}
	}

	// 检查是否超过限制
	if count > int64(limit) {
		if nowCount < 10 {
			// 睡眠 1 秒
			time.Sleep(1 * time.Second)
			return LimitRate(key, limit, duration, nowCount+1)
		} else {
			return false
		}
	}

	return true
}
