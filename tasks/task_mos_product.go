package tasks

import (
	"_/models"
	"_/services"
	"context"
	"encoding/json"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
	"strings"
	"time"
)

var (
	syncOmsProduct = "task:productcenter:syncomsproduct:key"
)

func initTaskOmsProduct(c *cron.Cron) {

	c.AddFunc("@every 5s", func() {
		taskCron(syncOmsProduct, SyncOmsProductToLocal, time.Hour*1)
	})

}

func SyncOmsProductToLocal() {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("SyncOmsProductToLocal捕获异常重启：", err)
		}
	}()
	var task = services.GetFirstUnFinishedTaskList(-1, services.SyncOmsProductTaskContent)

	logDatas := make([]*models.LogData, 0)

	if task != nil {
		// 更新任务状态-进行中
		updateModel := models.TaskList{
			Id:         task.Id,
			TaskStatus: 2,
		}
		services.UpdateTaskListInfo(updateModel)

		glog.Info("SyncOmsProductToLocal入参：", kit.JsonEncode(task))
		// 需要操作的数据
		operationFile := task.OperationFileUrl
		R1SkuS := make([]*models.R1Sku, 0)

		err := json.Unmarshal([]byte(operationFile), &R1SkuS)
		if err != nil {
			glog.Info("SyncOmsProductToLocal反序列化异常：", err.Error())

			// 更新任务状态-进行中
			updateModel := models.TaskList{
				Id:         task.Id,
				TaskStatus: 3,
				TaskDetail: err.Error(),
			}
			services.UpdateTaskListInfo(updateModel)
			return
		}

		//获取orgCode的配置:需要处理的组织下同步的R1商品信息
		getString := config.GetString("org.code.product.should.dispose")
		split := strings.Split(getString, ",")

		for i := range R1SkuS {

			sku := R1SkuS[i]

			var flag bool
			for i := range split {
				s := split[i]
				if s == sku.OrgCode {
					flag = true
					break
				}
			}
			// 没有在配置的组织下面的商品不需要同步到阿闻
			if !flag {
				//log_data.Msg = "不要处理的组织商品"
				continue
			}

			// 保存处理错误的商品数据
			var log_data = &models.LogData{Data: sku, Msg: "successful"}
			logDatas = append(logDatas, log_data)

			s := new(services.Product)
			// 插入本地数据库
			data, err := s.FormatOmsData(sku)
			if err != nil {
				glog.Error("SaveProduct,FormatOmsData格式化数据异常", err.Error())
				log_data.Msg = "FormatOmsData格式化数据异常" + err.Error()
				continue
			}

			//查询商品的货号有没有
			skuThird, err := s.FindThird(sku)
			if err != nil {
				glog.Error("查询oms货号异常：", err.Error())
				log_data.Msg = "查询oms货号异常：" + err.Error()
				continue
			}

			if skuThird.Id > 0 { // 跟新商品信息
				// 货号存在 查询是否是oms的商品
				product, err := s.FindOmsProduct(skuThird.ProductId)
				if err != nil {
					glog.Error(" 查询是否是oms的商品异常：", err.Error())
					log_data.Msg = " 查询是否是oms的商品异常：" + err.Error()
					continue
				}
				if product.FromOms == 1 { // 是oms商品更新
					resp, err := s.UpdateProductFromOms(context.Background(), sku, product)
					glog.Info("更新oms商品信息：", kit.JsonEncode(resp), " err: ", err)
					if err != nil {
						glog.Error("UpdateProductFromOms,FormatOmsData格式化数据异常", err.Error())
						log_data.Msg = " 更新oms商品信息异常：" + err.Error()
						continue
					}

					// 商品下架全部渠道
					err = s.DownOmsProduct(sku, product)
					if err != nil {
						glog.Error("商品下架全部渠道", err.Error())
						log_data.Msg = " 商品下架全部渠道" + err.Error()
						continue
					}

				} else {
					//不是oms的商品 不能创建也无法更新
					log_data.Msg = " 不是oms的商品,不能创建也无法更新："
					continue
				}

			} else {
				// 不存在插入商品信息
				response, err := s.NewProductFromOms(context.Background(), data)
				glog.Info("SaveProduct:", kit.JsonEncode(response), " sku ", kit.JsonEncode(sku), " err:", err)
				if err != nil {
					glog.Error("查询本地商品落地更新异常记录", err.Error())
					log_data.Msg = " 查询本地商品落地更新异常记录" + err.Error()
					continue
				}
			}

		}

		// 更新任务状态-已完成
		updateModel.TaskStatus = 3
		updateModel.ContextData = kit.JsonEncode(logDatas)

		services.UpdateTaskListInfo(updateModel)

	}

}
