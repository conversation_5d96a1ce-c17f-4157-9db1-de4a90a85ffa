package tasks

import (
	"_/models"
	"_/services"
	"testing"

	"github.com/maybgit/glog"
)

func TestSyncZlProductnewChild(t *testing.T) {
	type args struct {
		list           []models.SyncZlProductRecordExtend
		channelShopKey map[string]string
	}

	var data []models.SyncZlProductRecordExtend
	session := services.NewDbConn()
	if err := session.SQL("select r.*,s.finance_code,t.product_id from dc_product.sync_zl_product_record r " +
		"inner join datacenter.store s on s.zilong_id = r.zilong_id and r.id in (2276175)" +
		"left join sku_third t on t.third_sku_id = r.item_code;").
		Find(&data); err != nil {
		glog.Info("SyncZlProductnew 查询数据失败", err.Error())
	}

	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			name: "TestSyncZlProductnewChild",
			args: args{
				list:           data,
				channelShopKey: services.GetWarehouseShop(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SyncZlProductnewChild(tt.args.list, tt.args.channelShopKey)
		})
	}
}

func TestSyncZlProductnew(t *testing.T) {
	SyncZlProductnew()
}
