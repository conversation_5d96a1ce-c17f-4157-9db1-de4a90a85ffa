package tasks

import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/et"
	"_/proto/pc"
	"_/services"
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

func initElmTask(c *cron.Cron) {
	// 每天1点开始自动上架饿了么门店仓商品
	c.AddFunc("0 1 * * *", AutoShelvesElmZL)

	//每天0点开始自动上饿了么前置仓商品
	c.AddFunc("0 0 * * *", AutoShelvesElmQz)
}

// 自动上架--饿了么门店仓
func AutoShelvesElmZL() {
	defer kit.CatchPanic()
	glog.Info("AutoShelvesElmZL-获取锁")
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesElmZL"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	glog.Info("AutoShelvesElmZL-获取到锁-任务开始")

	var product services.Product
	warehouses, err := product.GezZLWarehouseList(services.ChannelElmId)
	if err != nil {
		glog.Error("医疗互联网门店仓商品关系查询异常", err.Error())
		return
	}

	var codeList []string
	for _, v := range warehouses {
		codeList = append(codeList, v.ShopId)
	}

	var service services.Product
	clientDac := dac.GetDataCenterClient()
	defer clientDac.Close()

	var codeMap = make(map[string]string)
	if out, err := clientDac.RPC.QueryStoresChannelId(clientDac.Ctx, &dac.StoreRelationUserRequest{
		FinanceCode: codeList,
		Psize:       int32(len(codeList)),
		ChannelId:   services.ChannelElmId,
	}); err != nil {
		glog.Errorf("获取饿了么id失败; err: %s", err.Error())
		return
	} else {
		if out.Code == 200 && len(out.Data) > 0 {
			for _, v := range out.Data {
				codeMap[v.FinanceCode] = v.ChannelStoreId
			}
		}
	}
	for _, v := range warehouses {
		appPoiCode := codeMap[v.ShopId]
		if len(appPoiCode) <= 0 {
			glog.Error("无饿了么id; 门店：", v.ShopId)
			continue
		}
		lists := service.GetProductSimpleInfo(v.ShopId, services.ChannelElmId, v.WarehouseId, v.WarehouseCode)

		for i, _ := range lists {
			lists[i].FinanceCode = v.ShopId
		}

		if len(lists) > 0 {
			//每次处理数据100条
			l := 100
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(lists) < l {
					l = len(lists)
				}
				_list := lists[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					itemToAutoElm(_list, services.ChannelElmId, appPoiCode, 3)
				}()
				lists = lists[l:]
				if len(lists) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)
		}
		glog.Info(fmt.Sprintf("该饿了么门店仓门店：%s已执行完成", v.ShopId))
	}

	glog.Info("AutoShelvesElmZL任务结束")
}

// 自动上架--饿了么前置仓
func AutoShelvesElmQz() {
	defer kit.CatchPanic()
	glog.Info("AutoShelvesElmQz-获取锁")
	redis := services.GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	taskLock := "productcenter:task:AutoShelvesElmQz"
	//任务已在执行
	if !redis.SetNX(taskLock, time.Now().Unix(), 10*time.Hour).Val() {
		return
	}
	defer redis.Del(taskLock)
	glog.Info("AutoShelvesElmQz-获取锁成功-任务开始")
	//client := services.GetDispatchClient()
	//defer client.Close()
	//// 查询前置仓门店
	//res, err := client.RPC.GetWarehouseRelation(context.Background(), &dc.Empty{})
	//if err != nil {
	//	glog.Error("查询GetWarehouseRelation失败，err:", err)
	//	return
	//}

	var product services.Product
	warehouses, err := product.GezQZWarehouseList(services.ChannelElmId)
	if err != nil {
		glog.Error("医疗互联网门店仓商品关系查询异常", err.Error())
		return
	}
	var codeList []string
	var idList []int32
	idForCode := make(map[int32][]string)
	codeForId := make(map[string][]int32)
	for _, v := range warehouses {
		codeList = append(codeList, v.ShopId)
		if _, ok := idForCode[int32(v.WarehouseId)]; !ok {
			idList = append(idList, int32(v.WarehouseId))
		}
		codeForId[v.ShopId] = append(codeForId[v.ShopId], int32(v.WarehouseId))
		idForCode[int32(v.WarehouseId)] = append(idForCode[int32(v.WarehouseId)], v.ShopId)
	}

	var service services.Product
	clientDac := dac.GetDataCenterClient()
	defer clientDac.Close()

	var codeMap = make(map[string]string)
	if out, err := clientDac.RPC.QueryStoresChannelId(clientDac.Ctx, &dac.StoreRelationUserRequest{
		FinanceCode: codeList,
		Psize:       int32(len(codeList)),
		ChannelId:   services.ChannelElmId,
	}); err != nil {
		glog.Errorf("获取饿了么id失败; err: %s", err.Error())
		return
	} else {
		if out.Code == 200 && len(out.Data) > 0 {
			for _, v := range out.Data {
				codeMap[v.FinanceCode] = v.ChannelStoreId
			}
		}
	}

	// 查询前置仓商品和价格
	a8List := service.GetProductQzInfo(services.ChannelElmId, idList)
	var idForA8 = make(map[int][]models.QzcPriceSync)
	for _, v := range a8List {
		idForA8[v.WarehouseId] = append(idForA8[v.WarehouseId], v)
	}
	var priceMap = make(map[string][]models.PriceSync)
	for code, ids := range codeForId {
		//a8List := service.GetProductQzInfo(2, id)
		appPoiCode := codeMap[code]
		if len(appPoiCode) <= 0 {
			glog.Error("无饿了么id; 门店：", code)
			continue
		}
		for _, id := range ids {
			if len(idForA8[int(id)]) == 0 {
				continue
			}
			priceList := service.GetProductQzCodeInfo(code, services.ChannelElmId, int(id), idForA8[int(id)])
			priceMap[code] = priceList
		}

	}
	for code, list := range priceMap {
		if len(list) > 0 {
			//每次处理数据100条
			l := 100
			//写协程
			wg := sync.WaitGroup{}
			var gNum = runtime.NumCPU()
			channelG := make(chan bool, gNum)
			for {
				channelG <- true
				wg.Add(1)
				if len(list) < l {
					l = len(list)
				}
				_list := list[:l]

				go func() {
					defer func() {
						<-channelG
						wg.Done()
					}()
					itemToAutoElm(_list, services.ChannelElmId, codeMap[code], 4)
				}()
				list = list[l:]
				if len(list) == 0 {
					break
				}
			}
			//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
			wg.Wait()
			close(channelG)
		}
		glog.Info(fmt.Sprintf("该饿了么前置门店：%s已执行完成", code))
	}
	glog.Info("AutoShelvesElmQz任务结束")
}

// 逐条处理
// todo
func itemToAutoElm(list []models.PriceSync, channelId int32, appPoiCode string, warehouseCategory int32) {
	var service services.Product
	engine := services.NewDbConn()

	var productIds []int
	for _, v := range list {
		productIds = append(productIds, v.ProductId)
	}
	logPrefix := fmt.Sprintf("自动上架到饿了么,入参:门店编码为:%s,饿了么门店id为:%s,渠道为:%d,仓库分类为:%d==========", list[0].FinanceCode, appPoiCode, channelId, warehouseCategory)
	glog.Info(logPrefix, "入参为", kit.JsonEncode(list))
	// 查询商品对应快照
	var snapShotList []models.ChannelProductSnapshot
	financeCode := list[0].FinanceCode
	err := engine.Where("channel_id = ?", channelId).
		And("finance_code = ?", financeCode).
		In("product_id", productIds).
		Find(&snapShotList)
	if err != nil {
		glog.Error(logPrefix, "查询饿了么快照信息失败, ", err.Error())
		return
	}

	// 有快照的商品sku_id
	var snapShotMap = make(map[int32]models.ChannelProductSnapshot)
	for _, v := range snapShotList {
		snapShotMap[v.ProductId] = v
	}

	appChannel, err := services.GetAppChannelByFinanceCode(financeCode)
	if err != nil {
		glog.Error(logPrefix, "services.GetAppChannelByFinanceCode发生错误:", err.Error())
		return
	}
	if appChannel == 0 {
		return
	}

	//v6.5.8 排除雀巢马氏等配置自动上架
	p := new(services.Product)
	agencyConfig, err := p.GetAgencyConfig(context.Background(), &empty.Empty{})
	if err != nil {
		glog.Error(logPrefix, "获取代运营配置异常:", err.Error())
		return
	}

	var upInfos map[int]*services.AwenProductUpInfo

	if channelId != services.ChannelDigitalHealth && (warehouseCategory == 3 || warehouseCategory == 4) {
		productIds1 := make([]int, len(list))
		for i, priceSync := range list {
			productIds1[i] = priceSync.ProductId
		}
		if upInfos, err = services.QueryAwenProductUpInfoByIds(productIds1, int(warehouseCategory)); err != nil {
			glog.Error(logPrefix, "ItemToAuto QueryAwenProductUpInfoByIds 出错：", err.Error())
			return
		}
	}

	session := engine.NewSession()
	defer session.Close()
	var clientElm *services.MtProductClientGlb
	productMap := make(map[int]int) //作用是一个商品只执行一次，如果有重复的商品，只执行一次
	for _, v := range list {
		if _, ok := productMap[v.ProductId]; ok {
			continue
		}
		productMap[v.ProductId] = 1
		if v.Price == 0 {
			continue
		}

		code := v.FinanceCode

		var flag bool
		for i := range agencyConfig.ConfigData {
			if appChannel == agencyConfig.ConfigData[i] {
				flag = true
			}
		}

		if !flag {
			product, err := p.IsUpProduct(context.Background(), appChannel, 0)
			if err != nil {
				glog.Error(logPrefix, "获取代运营商品异常：", err.Error(), " financeCode:", code, ",productId:", v.ProductId)
				continue
			}
			if _, ok := product[v.Sku]; !ok {
				glog.Error(fmt.Sprintf("%s该商品禁止上架，如需上架请联系总部运营:financeCode%s:sku%d: product%d", logPrefix, v.FinanceCode, v.Sku, v.ProductId))
				continue
			}
		}

		if upInfo, has := upInfos[cast.ToInt(v.Sku)]; has {

			////药品不允许上架
			//if upInfo.IsDrugs == 1 {
			//	glog.Error(fmt.Sprintf("药品不允许上架:financeCode%s:sku%d: product%d", v.FinanceCode, v.Sku, v.ProductId))
			//	continue
			//}

			if err := upInfo.Check(int(warehouseCategory), v.FinanceCode, cast.ToInt(channelId), ""); err != nil {
				glog.Error(fmt.Sprintf("%s不可销不允许上架:financeCode%s:sku%d: product%d", logPrefix, v.FinanceCode, v.Sku, v.ProductId))
				continue
			}

		}

		session.Begin()
		productId := int32(v.ProductId)
		ctx := context.WithValue(context.Background(), "user_info", &models.LoginUserInfo{
			UserNo: "AutoShelf",
		})

		resultBool, snap, stock, err := service.BatchOnTheShelf(v.FinanceCode, productId, channelId, 1, session, ctx, 1, v.Price)
		if !resultBool {
			session.Rollback()
			glog.Errorf("%s------饿了么商品自动上架失败, 门店: %s, 商品: %d, err: %s", logPrefix, v.FinanceCode, productId, err)
			continue
		}

		// 解析快照信息
		var sku et.UpdateElmShopSkuRequest
		var newSnap pc.ChannelProductRequest
		// todo  改成  上架返回快照
		err = json.Unmarshal([]byte(snap.JsonData), &newSnap)
		if err != nil {
			session.Rollback()
			glog.Errorf("%s,饿了么自动上架失败, 快照解析失败, 门店: %s, 商品id: %d,错误为%s", logPrefix, v.FinanceCode, v.ProductId, err.Error())
			continue
		}

		var c services.Product

		sku.ShopId = appPoiCode
		sku.LeftNum = stock
		sku.SalePrice = int32(v.Price)
		sku.CustomSkuId = cast.ToString(newSnap.SkuInfo[0].SkuId)
		newSnap.FinanceCode = v.FinanceCode
		if newSnap.SkuInfo[0].BarCode == "" {
			newSnap.SkuInfo[0].BarCode = cast.ToString(newSnap.SkuInfo[0].SkuId)
		}
		sku.Status = 1

		resPro := new(et.ElmGetProductListResponse)
		clientElm = services.GetMtGlobalProductClient()
		elmInfoReq := &et.ElmGetProductListRequest{
			ShopId:          appPoiCode,
			Page:            1,
			Pagesize:        1,
			Upc:             newSnap.SkuInfo[0].BarCode,
			IncludeCateInfo: 1,
			AppChannel:      appChannel,
		}
		resPro, err = clientElm.ELMPRODUCT.GetElmProductList(context.Background(), elmInfoReq)
		glog.Info(logPrefix, "获取饿了么商品信息，入参：", kit.JsonEncode(elmInfoReq), "数据返回：", kit.JsonEncode(resPro), "，错误：", kit.JsonEncode(err))
		if err != nil {
			session.Rollback()
			glog.Errorf("%s------饿了么自动上架失败，查询饿了么商品失败,门店id: %s , product: %d;失败原因1：%s", logPrefix, v.FinanceCode, v.ProductId, err.Error())
			continue
		}
		if resPro.Code != 200 {
			session.Rollback()
			glog.Errorf("%s----------饿了么自动上架失败，查询饿了么商品失败,门店id: %s , product: %d;", logPrefix, v.FinanceCode, v.ProductId)
			continue
		}

		//当饿了么存在此商品 且存在快照
		syncError := "初始值"
		if resPro.Data != nil && len(resPro.Data.List) > 0 {
			// 饿了么已存在该upc, 但sku不符
			if resPro.Data.List[0].CustomSkuId != cast.ToString(newSnap.SkuInfo[0].SkuId) {
				session.Rollback()
				glog.Errorf("%s----------饿了么自动上架失败，UPC已存在无法更新,门店id: %s , product: %d", logPrefix, v.FinanceCode, v.ProductId)
				continue
			}
			if cast.ToInt32(resPro.Data.List[0].Status) == 0 {
				sku.SkuId = cast.ToString(newSnap.SkuInfo[0].SkuId)
				sku.UpdateField = "market_price,stock,status"
				sku.AppChannel = appChannel

				res, err := clientElm.ELMPRODUCT.UpdateElmShopSku(context.Background(), &sku)
				glog.Info(logPrefix, " 4444饿了么创建/更新商品返回数据为： ", kit.JsonEncode(res), " 入参：", kit.JsonEncode(sku), " error: ", kit.JsonEncode(err))

				if err != nil {
					session.Rollback()
					glog.Errorf("%s----------饿了么商品自动上架失败，门店id: %s , product: %d;失败原因1：%s", logPrefix, v.FinanceCode, v.ProductId, err.Error())

					//clientElm.Close()
					continue
				}
				syncError = res.Error
				if res.Code != 200 {
					glog.Errorf("%s----------饿了么商品自动上架失败，门店id: %s, product: %d ;失败原因2：%s", logPrefix, v.FinanceCode, v.ProductId, res.Error)
					session.Rollback()
					//clientElm.Close()
					go func(productId int, financeCode, syncError string) {
						time.Sleep(1 * time.Second)
						services.UpdateProductThirdIdBySpu(enum.UpdateProductThirdIdFrom19, productId, services.ChannelElmId, financeCode, syncError)
					}(int(v.ProductId), v.FinanceCode, syncError)
					continue
				}
			}
		} else {
			//饿了么不存在此商品或者不存在快照,且商品没有第三方id则不调用接口
			if snap.ProductThirdId != "" {
				session.Rollback()
				go func(productId int, financeCode, syncError string) {
					services.UpdateProductThirdIdBySpu(enum.UpdateProductThirdIdFrom21, productId, services.ChannelElmId, financeCode, syncError)
				}(int(v.ProductId), v.FinanceCode, "skuid已上架渠道，但商品条码在阿闻与渠道不一致，请注意")
				glog.Errorf("%s---------饿了么商品自动上架失败，门店id: %s , product: %d;失败原因1：%s", logPrefix, v.FinanceCode, v.ProductId, "skuid已上架渠道，但商品条码在阿闻与渠道不一致，请注意")
				continue
			}

			sku.AppChannel = appChannel
			sku.Upc = newSnap.SkuInfo[0].BarCode
			res, err := c.UpdateElmProduct(&newSnap, sku)
			if err != nil {
				session.Rollback()
				glog.Errorf("%s----------饿了么商品自动上架失败，门店id: %s , product: %d;失败原因1：%s", logPrefix, v.FinanceCode, v.ProductId, err.Error())
				continue
			}
			syncError = res.SyncError

			if res.Code != 200 {
				session.Rollback()
				go func(productId int, financeCode, syncError string) {
					time.Sleep(1 * time.Second)
					if syncError != "初始值" {
						services.UpdateProductThirdIdBySpu(enum.UpdateProductThirdIdFrom20, productId, services.ChannelElmId, financeCode, syncError)
					}
				}(int(v.ProductId), v.FinanceCode, syncError)
				glog.Errorf("%s----------饿了么商品自动上架失败，门店id: %s, product: %d ;失败原因2：%s", logPrefix, v.FinanceCode, v.ProductId, res.Message)
				continue
			}
		}
		err = session.Commit()
		if err != nil {
			session.Rollback()
			glog.Errorf("%s----------饿了么商品自动上架失败, 提交事务失败, 门店: %s, 商品: %d err: %s", logPrefix, v.FinanceCode, v.ProductId, err.Error())
		} else {
			glog.Infof("%s----------饿了么商品自动上架成功, 门店: %s, 商品: %d", logPrefix, v.FinanceCode, v.ProductId)
			// 记录日志信息
			go new(services.Product).SaveChannelProductLogDetail(cast.ToInt(channelId), v.ProductId, enum.RecordTypeAutoUp,
				code, "AutoShelf", "AutoShelf")
		}
		go func(productId int, financeCode, syncError string) {
			time.Sleep(1 * time.Second)
			if syncError != "初始值" {
				services.UpdateProductThirdIdBySpu(enum.UpdateProductThirdIdFrom17, productId, services.ChannelElmId, financeCode, syncError)
			}
		}(int(v.ProductId), v.FinanceCode, syncError)
	}
	time.Sleep(100 * time.Millisecond)
}
