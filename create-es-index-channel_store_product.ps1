$esUrl = "http://es-cn-m7r1uawz10006espz.public.elasticsearch.aliyuncs.com:9200"
$pair = "elastic:dk3aOf6U"
$encodedCredentials = [System.Convert]::ToBase64String([System.Text.Encoding]::ASCII.GetBytes($pair))
$auth = @{ Authorization = "Basic $encodedCredentials" }

$dt = Get-Date
$indexName = "channel_store_product_"+($dt.ToString("yyyyMMdd"))
$indexAlias = "channel_store_product"

$body = "{
    `"mappings`": {
        `"properties`": {
            `"product`": {
                `"properties`": {
                    `"name`": {
                        `"type`": `"text`",
                        `"analyzer`": `"aliws`"
                    },
                    `"channel_category_name`": {
                        `"type`": `"text`",
                        `"analyzer`": `"aliws`"
                    },
                    `"selling_point`": {
                        `"type`": `"text`",
                        `"analyzer`": `"aliws`"
                    }
                }
            },
            `"sku_info`": {
                `"properties`": {
                    `"skuv`": {
                        `"properties`": {
                            `"spec_value_value`": {
                                `"type`": `"text`",
                                `"analyzer`": `"aliws`"
                            }
                        }
                    }
                }
            },
            `"tags`": {
                `"type`": `"text`",
                `"analyzer`": `"aliws`"
            }
        }
    }
}"

# 删除索引
# Invoke-WebRequest -Uri "$esUrl/$indexName" -Method DELETE -Headers $auth | Select-Object Content -ExpandProperty Content

# 清除索引数据
# Invoke-WebRequest -Uri "$esUrl/channel_store_product_20210105/_delete_by_query" -Method POST -ContentType "application/json;charset=UTF-8" -Headers $auth -Body "{`"query`":{`"match_all`":{}}}" | Select-Object Content -ExpandProperty Content

# 创建索引
Invoke-WebRequest -Uri "$esUrl/$indexName" `
    -Method "PUT" `
    -ContentType "application/json;charset=UTF-8" `
    -Headers $auth `
    -Body $body | Select-Object Content -ExpandProperty Content

# 重建索引复制数据
# Invoke-WebRequest -Uri "$esUrl/_reindex?wait_for_completion=false" `
#     -Method "POST" `
#     -ContentType "application/json" `
#     -Body "{`"source`":{`"index`":`"channel_store_product_20210105`"},`"dest`":{`"index`":`"$indexName`"}}" | Select-Object Content -ExpandProperty Content

# 设置别名
Invoke-WebRequest -Uri "$esUrl/_aliases" `
    -Headers $auth `
    -Method "POST" `
    -ContentType "application/json" `
    -Body "{`"actions`":[{`"add`":{`"index`":`"$indexName`",`"alias`":`"$indexAlias`"}}]}" | Select-Object Content -ExpandProperty Content

# 移除别名
# Invoke-WebRequest -Uri "$esUrl/_aliases" `
#     -Headers $auth `
#     -Method "POST" `
#     -ContentType "application/json" `
#     -Body "{`"actions`":[{`"remove`":{`"index`":`"channel_store_product_20210105`",`"alias`":`"channel_store_product`"}}]}" | Select-Object Content -ExpandProperty Content

# 设置别名并移除索引（原子操作）
# Invoke-WebRequest -Uri "$esUrl/_aliases" `
#     -Method "POST" `
#     -ContentType "application/json" `
#     -Body "{`"actions`":[{`"add`":{`"index`":`"$indexName`",`"alias`":`"$indexAlias`"}},{`"remove_index`":{`"index`":`"channel_store_product_20210105102843`"}}]}" | Select-Object Content -ExpandProperty Content
