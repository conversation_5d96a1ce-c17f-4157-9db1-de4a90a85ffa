package services

import (
	"_/models"
	"testing"

	kit "github.com/tricobbler/rp-kit"
)

func TestChannelProductPriceSync_BuildPrice(t *testing.T) {
	type fields struct {
		ChannelId           int
		ProductId           string
		FinanceCode         string
		WarehouseId         int
		WarehouseCategory   int
		ChannelFinanaceCode string
		ProductSkuId        string
		ProductThirdSkuId   string
		ProductSyncPrice    int
		SkuGroup            []*models.ChannelSkuGroup
		JsonData            string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		//financeCode: "CX0011",
		//				channelId:   1,
		//				category:    3,
		//				productId:   1023603,
		//				price:       0,
		{name: "组合商品价格", fields: fields{
			ChannelId:           1,
			ProductId:           "1033575",
			FinanceCode:         "CX0011",
			WarehouseId:         223,
			WarehouseCategory:   3,
			ChannelFinanaceCode: "CX0011",
			ProductSkuId:        "",
			ProductThirdSkuId:   "",
			ProductSyncPrice:    0,
			SkuGroup:            nil,
			JsonData:            "",
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &ChannelProductPriceSync{
				ChannelId:           tt.fields.ChannelId,
				ProductId:           tt.fields.ProductId,
				FinanceCode:         tt.fields.FinanceCode,
				WarehouseId:         tt.fields.WarehouseId,
				WarehouseCategory:   tt.fields.WarehouseCategory,
				ChannelFinanaceCode: tt.fields.ChannelFinanaceCode,
				ProductSkuId:        tt.fields.ProductSkuId,
				ProductThirdSkuId:   tt.fields.ProductThirdSkuId,
				ProductSyncPrice:    tt.fields.ProductSyncPrice,
				SkuGroup:            tt.fields.SkuGroup,
				JsonData:            tt.fields.JsonData,
			}
			if err := this.BuildPrice(); (err != nil) != tt.wantErr {
				t.Errorf("BuildPrice() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChannelProductPriceSync_getZilongPrice(t *testing.T) {
	type fields struct {
		ChannelId           int
		ProductId           string
		FinanceCode         string
		WarehouseId         int
		WarehouseCategory   int
		ChannelFinanaceCode string
		ProductSkuId        string
		ProductThirdSkuId   string
		ProductSyncPrice    int
		SkuGroup            []*models.ChannelSkuGroup
		JsonData            string
		oldJsonData         string
	}
	tests := []struct {
		name      string
		fields    fields
		wantPrice int
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			fields: fields{
				ChannelId:           1,
				ProductId:           "1033575",
				FinanceCode:         "CX0011",
				WarehouseId:         223,
				WarehouseCategory:   3,
				ChannelFinanaceCode: "CX0011",
				ProductSkuId:        "",
				ProductThirdSkuId:   "",
				ProductSyncPrice:    0,
				SkuGroup:            nil,
				JsonData:            "",
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			channelProductPriceSync := &ChannelProductPriceSync{
				ChannelId:           tt.fields.ChannelId,
				ProductId:           tt.fields.ProductId,
				FinanceCode:         tt.fields.FinanceCode,
				WarehouseId:         tt.fields.WarehouseId,
				WarehouseCategory:   tt.fields.WarehouseCategory,
				ChannelFinanaceCode: tt.fields.ChannelFinanaceCode,
				ProductSkuId:        tt.fields.ProductSkuId,
				ProductThirdSkuId:   tt.fields.ProductThirdSkuId,
				ProductSyncPrice:    tt.fields.ProductSyncPrice,
				SkuGroup:            tt.fields.SkuGroup,
				JsonData:            tt.fields.JsonData,
				oldJsonData:         tt.fields.oldJsonData,
			}
			_, err := channelProductPriceSync.getZilongPrice()
			t.Log(kit.JsonEncode(err))
			if (err != nil) != tt.wantErr {
				t.Errorf("getZilongPrice() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
