package services

import (
	"_/models"
	"_/pkg/code"
	"_/pool"
	"_/proto/dac"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

var icGrpcPool, ExGrpcPool pool.Pool

func init() {
	go func() {
		inventoryUrl := config.GetString("grpc.inventory-center")
		//inventoryUrl = "**********:11007"
		//inventoryUrl = "127.0.0.1:11007"
		icGrpcPool, _ = pool.New([]string{inventoryUrl}, pool.DefaultOptions)
	}()
	go func() {
		externalUrl := config.GetString("grpc.external")
		//externalUrl = "**********:11031"
		ExGrpcPool, _ = pool.New([]string{externalUrl}, pool.DefaultOptions)
	}()
}

//发送websocket消息
func SendWsMessage(message *dac.MessageCreateRequest) error {
	client := GetDataCenterClient()
	defer client.Conn.Close()

	if grpcRes, err := client.RPC.MessageCreate(client.Ctx, message); err != nil {
		return err
	} else if grpcRes.Code != 200 {
		return errors.New(grpcRes.Message)
	}

	params := &dac.MessageSendRequest{
		IsUser:   1,
		ObjectId: message.MemberMain,
		Msg:      message.Content,
	}
	if len(message.MemberMain) == 0 {
		params.IsUser = 0
		params.ObjectId = message.ShopId
	}

	if grpcRes, err := client.RPC.MessageSend(client.Ctx, params); err != nil {
		return err
	} else if grpcRes.Code != 200 {
		return errors.New(grpcRes.Message)
	}

	return nil
}

func GetFinanceCodeByStoresChannelId(appPoiCodes []string, channelId int32) (re map[string]string, err error) {
	client := GetDataCenterClient()
	defer client.Close()

	if res, err := client.RPC.GetFinanceCodeByStoresChannelId(client.Ctx, &dac.StoreRelationUserRequest{
		ChannelStoreId: appPoiCodes,
		ChannelId:      channelId,
	}); err != nil {
		glog.Error("调用GetFinanceCodeByStoresChannelId失败，", err)
	} else if res.Code != 200 {
		err = errors.New(res.Message)
		glog.Error("调用GetFinanceCodeByStoresChannelId失败，", err)
	} else {
		for _, v := range res.Data {
			if _, ok := re[v.ChannelStoreId]; !ok {
				re[v.ChannelStoreId] = v.FinanceCode
			}
		}
	}

	return
}

//通过用户获取渠道门店id
func GetAppPoiCodeByUser(userNo string, channelId int32) (appPoiCode []string) {
	client := GetDataCenterClient()
	defer client.Close()

	if out, err := client.RPC.QueryStoresChannelId(client.Ctx, &dac.StoreRelationUserRequest{
		UserNo:    []string{userNo},
		Psize:     2000,
		ChannelId: channelId,
	}); err != nil {
		glog.Error(err)
	} else {
		if out.Code != 200 {
			glog.Errorf(out.Message)
		}
		for _, v := range out.Data {
			if len(v.ChannelStoreId) > 0 {
				appPoiCode = append(appPoiCode, v.ChannelStoreId)
			}
		}
	}

	return
}

//所有查询库存调用此接口，不再单独去查
//先统一在这边查询商品信息，后面再改到从外面传进来 todo 周翔整理
func GetStockInfoBySkuCode(source int32, skuCodeInfo []*ic.SkuCodeInfo, channel_id int32, args ...interface{}) (map[string]int32, error) {
	//glog.Info("GetStockInfoBySkuCode参数信息：", kit.JsonEncode(skuCodeInfo), " args: ", kit.JsonEncode(args), " channel_id: ", channel_id)
	//clientIc := ic.GetInventoryServiceClient()
	//defer clientIc.Close()

	db := NewDbConn()

	if source == 0 {
		source = 2
	}

	//SKUID集合
	skuids := make([]string, 0)
	//是组合商品的SKUID
	skuids_group := make([]string, 0)
	//取商品的基本信息，不同渠道之间是不可以改的，所以去除重复的数据
	skumap := make(map[string]string)
	//是组合商品的SKUID去重
	groupmp := make(map[int]int)
	//查询商品数据
	product_list := make([]models.Product, 0)
	//查询分组商品详情
	product_group_list := make([]models.SkuGroupStock, 0)

	for _, x := range skuCodeInfo {

		_, ok := skumap[x.Sku]
		if !ok {
			skuids = append(skuids, x.Sku)
			skumap[x.Sku] = "1"
		}

	}

	//查询出所有的商品(用b.id映射到商品ID上，其实是SKUID)
	err := db.SQL("SELECT product_type,group_type,b.id  FROM `product` a INNER JOIN `sku` b ON a.id=b.product_id where is_del=0 and  b.id in (" + strings.Join(skuids, ",") + ")").Find(&product_list)
	if err != nil {
		glog.Error(utils.RunFuncName()+"查询商品出错，", err.Error())
		return map[string]int32{}, errors.New("查询商品出错" + err.Error())
	}
	if len(product_list) > 0 {
		for _, x := range product_list {
			_, ok := groupmp[x.Id]
			if !ok && x.ProductType == 3 {
				//不重复的组合商品集合
				skuids_group = append(skuids_group, strconv.Itoa(x.Id))
				groupmp[x.Id] = 1
			}
		}
	}
	if len(skuids_group) > 0 {

		//查询出所有的组合商品
		err = db.SQL("SELECT group_sku_id,`count`,b.product_type,a.sku_id FROM`sku_group` a INNER JOIN `product` b ON a.group_product_id=b.id WHERE a.sku_id IN (" + strings.Join(skuids_group, ",") + ")").Find(&product_group_list)
		if err != nil {
			glog.Error(utils.RunFuncName()+"查询组合商品出错，", err.Error())
			return map[string]int32{}, errors.New("查询组合商品出错" + err.Error())
		}
	}
	//SKUID对应的商品详细
	sku_product_mp := make(map[string]models.Product)
	sku_group_product_mp := make(map[string][]*ic.ChildRen)

	for _, x := range product_list {
		sku_product_mp[strconv.Itoa(x.Id)] = x
		//如果是组合商品，把组合商品也对应上
		if x.ProductType == 3 {
			list_gropu := make([]*ic.ChildRen, 0)
			//循环组合商品
			for _, f := range product_group_list {

				if f.SkuId == x.Id {

					children := ic.ChildRen{}
					children.SkuId = int32(f.GroupSkuId)
					children.RuleNum = int32(f.Count)
					if f.ProductType == 2 {
						//是否为虚拟 0:不是 1：是虚拟
						children.IsVirtual = 1
					} else {
						//是否为虚拟 0:不是 1：是虚拟
						children.IsVirtual = 0
					}
					list_gropu = append(list_gropu, &children)
				}
			}
			sku_group_product_mp[strconv.Itoa(x.Id)] = list_gropu
		}
	}

	//把对应关系都装进MAP里面方便后面组合参数

	var stockinforequest []*ic.ProductsInfo
	for _, _sku := range skuCodeInfo {
		stockinfo := &ic.ProductsInfo{}
		if sku_product_mp[_sku.Sku].ProductType == 3 {
			//组合商品
			stockinfo.Type = 1
		} else {
			//非组合商品
			stockinfo.Type = 2
		}
		stockinfo.SkuId = int32(sku_product_mp[_sku.Sku].Id)

		stockinfo.IsAllVirtual = 0
		//是否包含实物或者自己本身就是实物
		if (sku_product_mp[_sku.Sku].GroupType == 2 && sku_product_mp[_sku.Sku].ProductType == 3) || sku_product_mp[_sku.Sku].ProductType == 2 {
			stockinfo.IsAllVirtual = 1
		}
		stockinfo.Stock = 0
		stockinfo.ChildRen = sku_group_product_mp[_sku.Sku]

		stockinfo.FinanceCode = []string{_sku.FinanceCode}
		stockinforequest = append(stockinforequest, stockinfo)
	}

	//1要拉取子龙库存
	is_need_pull := 1
	if len(args) > 0 {
		if args[0] == 1 {
			//0不用拉子龙库存
			is_need_pull = 0
		}
	}
	stockParams := &ic.GetStockInfoRequest{
		ProductsInfo: stockinforequest,
		Source:       source,
		IsNeedPull:   int32(is_need_pull),
	}

	//v6.5.1 统一渠道设置返回查询的仓库数据
	product := new(Product)
	vo := pc.GetFinanceCodeWarehouseRelationVO{
		FinanceCode: skuCodeInfo[0].FinanceCode,
		ChannelId:   channel_id,
	}
	resp, err := product.GetFinanceCodeWarehouseRelation(context.Background(), &vo)
	if err != nil {
		glog.Error("同一渠道设置返回查询的仓库数据:", err.Error())
		return nil, err
	}
	stockParams.Stockwarehouse = resp.WarehouseIds // 设置仓库查询

	conn, _ := icGrpcPool.Get()
	defer conn.Close()
	clientIc := ic.NewInventoryServiceClient(conn.Value())
	ctx, cf := context.WithTimeout(context.Background(), time.Second*60)
	defer cf()

	//glog.Info(utils.RunFuncName()+"库存查询参数：", stockinforequest)
	//glog.Info("stockParams: ", kit.JsonEncode(stockParams))
	//  库存查询的接口
	if out, err := clientIc.GetStockInfo(ctx, stockParams); err != nil {
		glog.Error(utils.RunFuncName()+"调用GetStockInfo查询库存失败，", err)
		return map[string]int32{}, err
	} else if out.Code != 200 {
		glog.Info(utils.RunFuncName()+"调用GetStockInfo查询库存失败，", out.Message)
		return map[string]int32{}, errors.New(out.Message)
	} else {
		if len(args) >= 2 { // 断言参数向上传递，刷新表has_stock使用
			response, ok := args[1].(*ic.GetStockInfoResponse)
			if ok {
				response.GoodsInfo = out.GoodsInfo
				response.GoodsInWarehouse = out.GoodsInWarehouse
			}
		}
		retValue := map[string]int32{}
		//glog.Info(utils.RunFuncName()+" 调用GetStockInfo查询库存返回：", kit.JsonEncode(out))
		for _, v := range out.GoodsInfo.ProductsInfo {
			if v.Stock < 0 {
				retValue[v.FinanceCode[0]+":"+strconv.Itoa(int(v.SkuId))] = 0
			} else {
				retValue[v.FinanceCode[0]+":"+strconv.Itoa(int(v.SkuId))] = v.Stock
			}
		}
		return retValue, nil
	}
}

//通过财务编码获取渠道门店id
//param financeCode 门店财务编码
//param channelId 渠道id
func GetAppPoiCodeByFinanceCode(financeCode []string, channelId int32) (appPoiCodeMap map[string]string) {
	client := GetDataCenterClient()
	defer client.Close()

	appPoiCodeMap = make(map[string]string, 0)
	if out, err := client.RPC.QueryStoresChannelId(client.Ctx, &dac.StoreRelationUserRequest{
		FinanceCode: financeCode,
		Psize:       int32(len(financeCode)),
		ChannelId:   channelId,
	}); err != nil {
		glog.Error(err)
	} else {
		if out.Code == 200 && len(out.Data) > 0 {
			for _, v := range out.Data {
				if len(v.ChannelStoreId) > 0 {
					if _, ok := appPoiCodeMap[v.ChannelStoreId]; !ok {
						appPoiCodeMap[v.ChannelStoreId] = v.FinanceCode
					}
				}
			}
		}
	}
	return
}

//获取库存结果
func GetSkuStock(stockMap map[string]int32, appPoiCode, skuId string) int32 {
	return stockMap[appPoiCode+":"+skuId]
}

// 将处理失败的商品信息导入excel上传至七牛云
func ExportProductErr(errList [][]string) (string, error) {
	defer func() {
		if r := recover(); r != nil {
			glog.Error("ExportProductErr-panic", ",调用方法:", r, kit.RunFuncName(2), ",请求参数：", errList)
		}
	}()
	f := excelize.NewFile()
	for i, list := range errList {
		var index string
		for j, e := range list {
			if j < 26 {
				index = string(rune(65 + j))
			} else {
				n := j - 26
				index = string(rune(65+n/26)) + string(rune(65+n))
			}
			f.SetCellValue("Sheet1", index+strconv.Itoa(i+1), e)
		}
	}

	f.Save()
	file, err := f.WriteToBuffer()
	if err != nil {
		return "", err
	}

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	fileName := cast.ToString(time.Now().UnixNano()) + "_商品错误导出.xlsx"
	fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)
	io.Copy(fileWriter, file)
	path := config.GetString("file-upload-url") + "/fss/up"
	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()
	// 上传文件
	resp, _ := http.Post(path, contentType, bodyBuffer)
	defer resp.Body.Close()

	resp_body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	var result uploadResult
	err = json.Unmarshal(resp_body, &result)
	if err != nil {
		return "", err
	}
	if len(result.Url) == 0 {
		return "", errors.New(result.Err)
	}
	return result.Url, nil
}

var (
	storeAppChannelRedisKey = "datacenter:store:app-channel:"
)

//GetAppChannelByFinanceCode
//根据门店财务编码financeCode获取门店的appChannel
//1:首先从缓存中获取
//2:如果缓存中没有数据则从数据库中查询数据并更新到缓存之后返回
//饿了么代运营版本添加
func GetAppChannelByFinanceCode(financeCode string) (int32, error) {
	if len(financeCode) <= 0 {
		glog.Info("GetAppChannelByFinanceCode param error", "，财务编码", financeCode, "，调用函数：", kit.RunFuncName(2))
		return 0, nil
	}
	redisConn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}
	key := storeAppChannelRedisKey + financeCode
	appChannel := redisConn.Get(key).Val()
	//如果没有数据 去datacenter查询db获取 data-center查询数据之后会更新数据到缓存
	if len(appChannel) <= 0 {
		dacClient := dac.GetDataCenterClient()
		requestParam := &dac.GetAppChannelRequest{FinanceCode: financeCode}
		grpcRes, err := dacClient.RPC.GetAppChannelByFinanceCode(context.Background(), requestParam)
		if err != nil {
			glog.Error("GetAppChannelByFinanceCode rpc err", err, "，财务编码：", financeCode, "，调用函数：", kit.RunFuncName(2))
			return 0, err
		}
		if grpcRes.Code != 200 {
			glog.Info("GetAppChannelByFinanceCode rpc fail", "财务编码", financeCode, "，调用函数：", kit.RunFuncName(2))
			return 0, nil
		}
		return grpcRes.AppChannel, nil
	}
	return cast.ToInt32(appChannel), nil
}

// 获取包含京东到家信息的 所有店铺主体ID列表
func GetStoreMasterIDList() (storeMasterIdList []int32, retCode int) {
	retCode = code.Success
	client := GetDataCenterClient()
	defer client.Conn.Close()

	req := dac.GetStoreMasterListRequest{
		InfoLevel: 99,
	}

	resp, err := client.RPC.GetStoreMasterList(context.Background(), &req)
	if err != nil || resp.Common.Code != dac.RetCode_SUCCESS {
		retCode = code.ErrorCommon
		return
	}

	for _, v := range resp.Data {
		if v.JddjAppId == "" || v.JddjAppSecret == "" || v.JddjAppMerchantId == "" {
			continue
		}
		storeMasterIdList = append(storeMasterIdList, v.Id)
	}
	return
}

// 加载仓库关系缓存数据
func LoadWarehouseRelationCache(financeCode string) []models.Warehouse {
	var list []models.Warehouse

	engin := NewDbConn()
	if err := engin.SQL(`select w.* from dc_dispatch.warehouse w left join dc_dispatch.warehouse_relation_shop wrs on w.id = wrs.warehouse_id
	      where wrs.shop_id = ?`, financeCode).Find(&list); err != nil {
		glog.Errorf("LoadWarehouseRelationCache 查询店铺绑定所有仓库异常:%+v %s", err, financeCode)
		return nil
	}
	return list
}
