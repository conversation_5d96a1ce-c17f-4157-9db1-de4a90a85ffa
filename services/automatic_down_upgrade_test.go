package services

import (
	"_/models"
	"reflect"
	"testing"
)

func TestProduct_GetAutoDownProduct(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		channel_id int
		financeOne string
		tid        int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*models.UpProduct
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "TEST"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := p.GetAutoDownProduct(1, "CX0011", 1)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAutoDownProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("GetAutoDownProduct() got = %v, want %v", got, tt.want)
			}
		})
	}
}
