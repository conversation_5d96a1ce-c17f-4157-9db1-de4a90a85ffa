package services

import (
	"_/models"
	"_/proto/dac"
	"_/proto/dc"
	"_/proto/et"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	kit "github.com/tricobbler/rp-kit"

	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/tricobbler/mqgo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

///////////////////////////////   v5.0版本需要移除的方法列表

///////////////////////////////////////  上下架

// 饿了么渠道--批量上架/下架【MQ异步】
// 已废弃
func (c *Product) BatchOnTheShelfToElm(ctx context.Context, in *pc.BatchOnTheShelfToMTRequest) (*pc.BatchOnTheShelfToMTResponse, error) {
	out := new(pc.BatchOnTheShelfToMTResponse)
	out.Code = 200

	if in.ChannelId != ChannelElmId {
		out.Message = "渠道id不为 饿了么"
		return out, nil
	}

	/*
		redis := GetRedisConn()
		if !redis.SetNX(fmt.Sprintf("%s%v", channelProductUpLockPrefix, in.ChannelId), 1, 300*time.Second).Val() {
			out.Message = "有任务正在进行中，请稍后再试"
			return out,nil
		}
	*/

	clientData := GetDataCenterClient()
	defer clientData.Close()

	var params dac.GetHospitalListByUserNoRequest
	params.UserNo = in.UserNo
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo.IsGeneralAccount {
		params.IsLogo = 1
	} else {
		params.IsLogo = 0
	}

	var finance_code_arr []string
	//判断是全部门店
	if in.IsAll == 1 {
		if len(in.UserNo) <= 0 {
			out.Message = "用户编码不能为空"
			out.Code = 400
			return out, nil
		}

		params.ChannelId = in.ChannelId
		out_result, err := clientData.RPC.GetHospitalListByUserNo(clientData.Ctx, &params)
		if err != nil {
			out.Message = "请求GetHospitalListByUserNo失败"
			out.Error = err.Error()
			out.Code = 400
			return out, nil
		}
		for _, v := range out_result.Data {
			finance_code_arr = append(finance_code_arr, v.StructOuterCode)
		}
	} else {
		if len(in.FinanceCodeList) > 0 {
			finance_code_arr = strings.Split(in.FinanceCodeList, ",")
		} else {
			out.Message = "财务编码或标识门店不能为空"
			out.Code = 400
			return out, nil
		}
	}

	session := NewDbConn().NewSession()
	defer session.Close()

	var productIdSlice []int32
	// 如果产品id为空，则查更新所有的，如果不为空，则更新所选的产品id
	if len(in.ProductId) > 0 {
		for _, v := range strings.Split(in.ProductId, ",") {
			productIdSlice = append(productIdSlice, cast.ToInt32(v))
		}
	} else {
		var list []models.ChannelProduct
		session.Where("channel_id = ?", in.ChannelId).Find(&list)
		for _, k := range list {
			productIdSlice = append(productIdSlice, cast.ToInt32(k.Id))
		}
	}

	var callType int32
	if params.IsLogo == 1 {
		//总帐号
		callType = 1
	} else {
		//单帐号
		callType = 0
	}

	gmdr := pc.GetMtProductDataRequest{
		ProductId:   productIdSlice,
		ChannelId:   in.ChannelId,
		FinanceCode: finance_code_arr,
		Type:        callType,
		//IsAsync:     isAsync,
	}

	mtProductData, err := c.GetMtProductData(ctx, &gmdr)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	} else if mtProductData.Code != 200 || len(mtProductData.Data) == 0 {
		out.Message = mtProductData.Message
		return out, nil
	}

	//解析参数
	foodData := []*et.RetailInfo{}
	err = json.Unmarshal([]byte(mtProductData.Data), &foodData)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}
	if len(foodData) >= 0 && len(foodData[0].Skus) >= 0 && cast.ToInt32(foodData[0].Skus[0].WeightForUnit) <= 0 {
		out.Message = "重量不能为不空！"
		out.Code = 400
		return out, nil
	}
	if len(foodData) >= 0 && len(foodData[0].Skus) >= 0 && foodData[0].Skus[0].Upc == "" {
		out.Message = "条码不能为空！"
		out.Code = 400
		return out, nil
	}
	if len(foodData) >= 0 && len(foodData[0].Skus) >= 0 && cast.ToFloat32(foodData[0].Skus[0].Price) <= 0 {
		out.Message = "金额不能为空！"
		out.Code = 400
		return out, nil
	}
	foodDataJson, err := json.Marshal(foodData)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}

	//通过门店财务编码查询渠道门店id
	appPoiCodeMap := GetAppPoiCodeByFinanceCode(finance_code_arr, in.ChannelId)
	appPoiCodeSlice := []string{}
	for k := range appPoiCodeMap {
		if len(k) > 0 {
			appPoiCodeSlice = append(appPoiCodeSlice, k)
		}
	}

	storesData := map[string]string{}
	for appPoiCode, financeCode := range appPoiCodeMap {
		if _, ok := storesData[financeCode]; !ok {
			storesData[financeCode] = appPoiCode
		}
	}

	//推送到MQ
	bt, _ := json.Marshal(map[string]interface{}{
		"channelId":   in.ChannelId,
		"storesData":  storesData,
		"productData": string(foodDataJson),
		"userNo":      in.UserNo,
		"operateType": in.OperateType,
	})
	m := mqgo.SyncMqInfo{
		Exchange: DatacenterExchange,
		Queue:    ChannelProductUpQueueElm,
		RouteKey: ChannelProductUpQueueElm,
		Request:  string(bt),
	}
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}

	out.Code = 200
	return out, nil
}

// 饿了么--单个门店上架单个商品
// 已废弃
func (c *Product) SingleOnTheShelfToElm(ctx context.Context, in *pc.BatchOnTheShelfToMTRequest) (*pc.BatchToMTResponse, error) {
	glog.Info(" SingleOnTheShelfToElm 请求参数=", in)
	out := &pc.BatchToMTResponse{
		Code: 400,
	}

	// 获取门店信息
	var financeCode = in.FinanceCode
	if len(financeCode) == 0 {
		out.Message = "未传递门店代码信息"
		return out, nil
	}

	// 获取商品代码
	var productId = in.ProductId
	if len(productId) == 0 {
		out.Message = "未传递商品代码信息"
		return out, nil
	}

	// 查询门店对应的仓库
	client := GetDispatchClient()
	defer client.Close()
	res, err := client.RPC.GetWarehouseInfoByFanceCodes(client.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{financeCode}})
	if err != nil {
		glog.Error(err)
		out.Message = "调用GetWarehouseInfoByFanceCodes失败"
		return out, err
	}
	if len(res.Data) == 0 {
		out.Message = fmt.Sprintf("未查询到门店%s的仓库信息", financeCode)
		return out, nil
	}
	warehouseInfo := res.Data[0]

	// 查询门店渠道id
	appoicodeMap := GetAppPoiCodeByFinanceCode([]string{financeCode}, ChannelElmId)
	if len(appoicodeMap) == 0 {
		out.Message = "未找到渠道门店id"
		return out, nil
	}

	// 同步商品到饿了么
	for k, v := range appoicodeMap {
		err = UploadFinanceCodeProductIdToElm(v, k, in.ProductId, warehouseInfo)
		if err != nil {
			out.Message = err.Error()
			return out, nil
		}
	}
	out.Code = 200
	return out, nil
}

// 上架单个门店的单个商品到饿了么
// 已废弃
func UploadFinanceCodeProductIdToElm(financeCode, appoicode, productId string, warehouseInfo *dc.WarehouseList) error {
	// 数据库链接
	Engine := NewDbConn()
	// 商品街第三方sku信息
	var channelSkuThrid models.ChannelSkuThird
	var channelProductSnapshot models.ChannelProductSnapshot
	productSnapshot, err := Engine.SQL("select * from channel_product_snapshot where product_id=? and finance_code=? and channel_id=?", productId, financeCode, ChannelElmId).Get(&channelProductSnapshot)
	if err != nil {
		glog.Error(err)
		return errors.New("查询商品快照信息出错")
	}
	if productSnapshot == false {
		return errors.New("饿了么没有这个商品,请先编辑商品再上架")
	}
	// 解析sku信息
	var channelProductRequest pc.ChannelProductRequest
	err = json.Unmarshal([]byte(channelProductSnapshot.JsonData), &channelProductRequest)
	if err != nil {
		glog.Error(err)
		return errors.New("解析sku信息出错")
	}
	skuInfo := channelProductRequest.SkuInfo[0]
	switch warehouseInfo.Category {
	case 3: // 门店仓
		if skuInfo.StorePrice == 0 {
			return errors.New("门店仓价格为必填项")
		}
		// 查询子龙货号
		has, err := Engine.SQL("select * from channel_sku_third where product_id =  " + productId + " and erp_id = 4").Get(&channelSkuThrid)
		if err != nil {
			glog.Error(err)
			return errors.New("查询子龙货号信息失败")
		}
		if !has {
			return errors.New(productId + " 查询不到子龙货号信息，无法上架")
		} else {
			if channelSkuThrid.ThirdSkuId == "" {
				return errors.New(productId + " 商品子龙货号为空，无法上架")
			}
		}
	case 4: //前置仓
		if skuInfo.PreposePrice == 0 {
			return errors.New("前置仓价格为必填项")
		}
		has, err := Engine.SQL("select * from channel_sku_third where product_id =  " + productId + " and erp_id = 2").Get(&channelSkuThrid)
		if err != nil {
			glog.Error(err)
			return errors.New("查询A8货号信息失败")
		}
		if !has {
			return errors.New(productId + " 查询不到A8货号信息，无法上架")
		} else {
			if channelSkuThrid.ThirdSkuId == "" {
				return errors.New(productId + " 商品A8货号为空，无法上架")
			}
		}
	default:
		return errors.New(financeCode + " 门店仓库类型不正确")
	}

	// 校验商品库存信息
	stock, _ := GetStockInfoBySkuCodeAndShopId(ChannelElmId, channelSkuThrid.SkuId, financeCode)
	if stock <= 0 {
		return errors.New("库存为0，无法上架")
	}

	if warehouseInfo.Category == 3 {
		//已废弃
		price := UpdateZlPrice(financeCode, skuInfo.SkuId, ChannelElmId)
		if price > 0 {
			//channelProductRequest.SkuInfo[0].StorePrice = price
			_, err = Engine.Where("snapshot_id = ?", channelProductSnapshot.Id).Update(&models.ChannelStoreProduct{MarketPrice: int(price)})
			if err != nil {
				glog.Error("更新饿了么门店上架价格失败, err: ", err.Error())
			}
		}
		//jsonData, _ := json.Marshal(channelProductRequest)
		//channelProductSnapshot.JsonData = string(jsonData)
		//_, err = Engine.Id(channelProductSnapshot.Id).Update(&channelProductSnapshot)
		//if err != nil {
		//	glog.Error("更新饿了么门店快照价格失败, err: ", err.Error())
		//}
	} else if warehouseInfo.Category == 4 {
		price := UpdateQzPrice(financeCode, warehouseInfo.Id, skuInfo.SkuId, ChannelElmId)
		if price > 0 {
			_, err = Engine.Where("snapshot_id = ?", channelProductSnapshot.Id).Update(&models.ChannelStoreProduct{MarketPrice: int(price)})
			if err != nil {
				glog.Error("更新饿了么门店上架价格失败, err: ", err.Error())
			}
		}
	}

	appChannel, err := GetAppChannelByFinanceCode(financeCode)
	if err != nil {
		return err
	}
	if appChannel == 0 {
		err = errors.New("获取appChannel值错误")
		return err
	}

	// 同步到饿了么平台
	clientMt := GetMtProductClient()
	defer clientMt.Close()

	elmOnlineResponse, err := clientMt.ELMPRODUCT.OnlineElmShopSku(clientMt.Ctx, &et.UpdateElmShopSkuPriceRequest{
		ShopId:      appoicode,
		AppChannel:  appChannel,
		CustomSkuId: strconv.Itoa(int(skuInfo.SkuId)),
	})

	if err != nil {
		glog.Error(err)
		return errors.New("饿了么接口失败")
	}

	if elmOnlineResponse.Code != 200 {
		glog.Warning(elmOnlineResponse.Error)
		return errors.New("饿了么接口失败,失败原因:" + elmOnlineResponse.Error)
	}
	// 查询商品在门店的渠道信息
	var channelStoreProduct models.ChannelStoreProduct
	_, err = Engine.SQL("select * from channel_store_product where channel_id=? and finance_code=? and sku_id=?", ChannelElmId, financeCode, skuInfo.SkuId).Get(&channelStoreProduct)
	if err != nil {
		glog.Error(err)
		return errors.New("查询饿了么上架信息失败, err: " + err.Error())
	} else if channelStoreProduct.Id > 0 {
		// 更新上架状态
		channelStoreProduct.UpDownState = 1
		Engine.ID(channelStoreProduct.Id).Cols("up_down_state").Update(&channelStoreProduct)
	} else {
		var snap pc.ChannelProductRequest

		if err = json.Unmarshal([]byte(channelProductSnapshot.JsonData), &snap); err != nil {
			glog.Error(err)
			return errors.New("查询饿了么上架信息失败, err: " + err.Error())
		}
		channelStoreProduct.ChannelId = 3
		channelStoreProduct.FinanceCode = financeCode
		channelStoreProduct.ProductId = cast.ToInt(productId)
		channelStoreProduct.ChannelCategoryId = int(snap.Product.ChannelCategoryId)
		channelStoreProduct.ChannelCategoryName = snap.Product.ChannelCategoryName
		channelStoreProduct.IsRecommend = 0
		channelStoreProduct.UpDownState = 1
		channelStoreProduct.SnapshotId = channelProductSnapshot.Id
		channelStoreProduct.Name = snap.Product.Name
		channelStoreProduct.SkuId = int(snap.SkuInfo[0].SkuId)
		channelStoreProduct.MarketPrice = int(snap.SkuInfo[0].MarketPrice)
		Engine.Insert(&channelStoreProduct)
	}

	return nil
}

// 美团渠道--批量上架
func (c *Product) BatchOnTheShelfToMT(ctx context.Context, in *pc.BatchOnTheShelfToMTRequest) (*pc.BatchOnTheShelfToMTResponse, error) {
	out := new(pc.BatchOnTheShelfToMTResponse)
	out.Code = 200

	if in.ChannelId != ChannelMtId {
		out.Message = "渠道id不为 美团"
		return out, nil
	}
	var productIdSlice []int32
	// 如果产品id为空，则查更新所有的，如果不为空，则更新所选的产品id
	if len(in.ProductId) > 0 {
		for _, v := range strings.Split(in.ProductId, ",") {
			productIdSlice = append(productIdSlice, cast.ToInt32(v))
		}
	}

	clientData := GetDataCenterClient()
	defer clientData.Close()

	var finance_code_arr []string
	//判断是全部门店
	if in.IsAll == 1 {
		if len(in.UserNo) <= 0 {
			out.Message = "用户编码不能为空"
			out.Code = 400
			return out, nil
		}
		client := GetDataCenterClient()
		defer client.Close()

		var params dac.GetHospitalListByUserNoRequest
		params.UserNo = in.UserNo
		//用户校验
		userInfo := loadLoginUserInfo(ctx)
		if userInfo.IsGeneralAccount {
			params.IsLogo = 1
		} else {
			params.IsLogo = 0
		}
		params.ChannelId = in.ChannelId
		params.Category = in.Category
		out_result, err := client.RPC.GetHospitalListByUserNo(client.Ctx, &params)
		if err != nil {
			out.Message = "请求GetHospitalListByUserNo失败"
			out.Error = err.Error()
			out.Code = 400
			return out, nil
		}
		for _, v := range out_result.Data {
			finance_code_arr = append(finance_code_arr, v.StructOuterCode)
		}
	} else {
		if len(in.FinanceCodeList) > 0 {
			finance_code_arr = strings.Split(in.FinanceCodeList, ",")
		} else {
			out.Message = "财务编码或标识门店不能为空"
			out.Code = 400
			return out, nil
		}
	}

	session := NewDbConn().NewSession()
	defer session.Close()
	//通过门店财务编码查询渠道门店id
	appPoiCodeMap := GetAppPoiCodeByFinanceCode(finance_code_arr, 2)
	appPoiCodeSlice := []string{}
	for k := range appPoiCodeMap {
		if len(k) > 0 {
			appPoiCodeSlice = append(appPoiCodeSlice, k)
		}
	}

	var financeCodeList []string
	storesData := map[string]string{}
	for appPoiCode, financeCode := range appPoiCodeMap {
		if _, ok := storesData[financeCode]; !ok {
			storesData[financeCode] = appPoiCode
			financeCodeList = append(financeCodeList, financeCode)
		}
	}
	//任务进行中加锁
	redis := GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	rk := fmt.Sprintf("%s%s", ChannelProductUpLockPrefix, "mt")
	redis.Set(rk, 1, 300*time.Second)
	defer redis.Del(rk)
	//异步执行任务

	go func() {
		clientMt := GetMtProductClient()
		defer clientMt.Close()
		var p Product
		//查询新任务的ID
		params := &pc.GetTaskListRequest{
			Sort:        "createTimeAsc",
			TaskStatus:  1,
			ChannelId:   2,
			TaskContent: 4, //批量上架任务ID
			Status:      1,
			Page:        1,
			PageSize:    1,
			CreateId:    in.UserNo,
		}
		result, err := p.GetTaskList(ctx, params)
		if err != nil {
			glog.Error(err)
		}
		//更新任务状态为进行中
		updateModel := models.TaskList{
			TaskStatus:     2,
			TaskDetail:     "",
			ResulteFileUrl: "",
			ModifyTime:     time.Now(),
		}
		_, err = session.ID(result.TaskList[0].Id).Update(updateModel)
		if err != nil {
			glog.Info("美团批量同步：更新任务状态错误", err.Error())
		}
		clientDis := GetDispatchClient()
		defer clientDis.Close()

		resList := make([][]string, 0)
		for financeCode, appPoiCode := range storesData {
			if len(appPoiCode) == 0 {
				continue
			}
			mtData, err := p.GetMtProductData(ctx, &pc.GetMtProductDataRequest{
				ProductId:   productIdSlice,
				ChannelId:   2,
				FinanceCode: []string{financeCode},
				Type:        1, //单门店多商品
				Category:    in.Category,
			})
			if err != nil {
				glog.Error(err)
				row := []string{financeCode, "", err.Error()}
				resList = append(resList, row)
				continue
			}
			if mtData.Code != 200 {
				glog.Error(errors.New(mtData.Message))
				continue
			}
			if len(mtData.Data) == 0 {
				continue
			}

			foodData := []*et.RetailBatchinitdata{}
			if json.Unmarshal([]byte(mtData.Data), &foodData) != nil {
				glog.Error(err)
				row := []string{financeCode, "", err.Error()}
				resList = append(resList, row)
				continue
			}
			successID := make([]int32, 0)
			for _, v := range foodData {
				successID = append(successID, cast.ToInt32(v.AppFoodCode))
			}
			inSet := utils.NewSet(productIdSlice...)
			successSet := utils.NewSet(successID...)
			failSet := inSet.Minus(successSet)
			for _, v := range failSet.List() {
				row := []string{financeCode, cast.ToString(v), "商品快照不存在，请先编辑商品"}
				resList = append(resList, row)
			}
			//排除库存为空的商品
			realFoodData := []*et.RetailBatchinitdata{}
			for _, v := range foodData {
				for kk, sku := range v.Skus {
					stock, _ := GetStockInfoBySkuCodeAndShopId(2, cast.ToInt32(sku.SkuId), financeCode)
					if stock == 0 {
						row := []string{financeCode, cast.ToString(v.AppFoodCode), "库存为0，上架失败"}
						resList = append(resList, row)
						goto I
					}
					v.Skus[kk].Stock = cast.ToString(stock)
				}
				realFoodData = append(realFoodData, v)
			I:
			}

			if len(realFoodData) == 0 {
				continue
			}
			//排除美团分类为空的商品
			SuccessID_category := make([]int32, 0)
			cleanFoodData := []*et.RetailBatchinitdata{}
			for _, v := range realFoodData {
				if v.Skus[0].Stock != "0" {
					SuccessID_category = append(SuccessID_category, cast.ToInt32(v.AppFoodCode))
				}
				if v.TagId == 0 {
					row := []string{financeCode, cast.ToString(v.AppFoodCode), "美团分类为0，上架失败"}
					resList = append(resList, row)
				} else {
					cleanFoodData = append(cleanFoodData, v)
				}
			}
			//排除货号为空的商品
			warehouseMap := map[string]int32{}
			//warehouse, err := clientDis.RPC.GetWarehouseInfoByFanceCodes(clientDis.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{financeCode}})
			//if err != nil {
			//	glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
			//}
			product := new(Product)
			resp, err := product.GetChannelWarehouses([]string{financeCode}, ChannelMtId)
			if err != nil {
				glog.Error(utils.RunFuncName()+"GetChannelWarehouses，", err)
			}
			for _, v := range resp {
				warehouseMap[v.ShopId] = int32(v.Category)
			}
			noThirdID := make([]int32, 0)
			for kk := range cleanFoodData {
				var skuIdList []int32
				for _, sku := range cleanFoodData[kk].Skus {
					skuIdList = append(skuIdList, cast.ToInt32(sku.SkuId))
				}
				// 查询sku对应的erp_id
				var out = &pc.SkuThirdResponse{}
				out, err = p.QueryChannelSkuThird(context.Background(), &pc.OneofIdRequest{
					ChannelId: 2, Id: &pc.OneofIdRequest_SkuId{SkuId: &pc.ArrayIntValue{Value: skuIdList}},
				})
				if err != nil {
					row := []string{financeCode, cast.ToString(cleanFoodData[kk].AppFoodCode), err.Error()}
					resList = append(resList, row)
					continue
				}
				if warehouseMap[financeCode] == 3 {
					count := 0
					for _, v := range out.Details {
						if v.ErpId == 4 {
							count++
							if v.ThirdSkuId == "" {
								row := []string{financeCode, cast.ToString(cleanFoodData[kk].AppFoodCode), "子龙货号为空，上架失败"}
								resList = append(resList, row)
								noThirdID = append(noThirdID, cast.ToInt32(cleanFoodData[kk].AppFoodCode))
							}
						}
					}
					if count == 0 {
						row := []string{financeCode, cast.ToString(cleanFoodData[kk].AppFoodCode), "子龙货号为空，上架失败"}
						resList = append(resList, row)
						noThirdID = append(noThirdID, cast.ToInt32(cleanFoodData[kk].AppFoodCode))
					}
				}
				if warehouseMap[financeCode] == 4 || warehouseMap[financeCode] == 5 {
					count := 0
					for _, v := range out.Details {
						if v.ErpId == 2 {
							count++
							if v.ThirdSkuId == "" {
								row := []string{financeCode, cast.ToString(cleanFoodData[kk].AppFoodCode), "A8货号为空，上架失败"}
								resList = append(resList, row)
								noThirdID = append(noThirdID, cast.ToInt32(cleanFoodData[kk].AppFoodCode))
							}
						}
					}
					if count == 0 {
						row := []string{financeCode, cast.ToString(cleanFoodData[kk].AppFoodCode), "A8货号为空，上架失败"}
						resList = append(resList, row)
						noThirdID = append(noThirdID, cast.ToInt32(cleanFoodData[kk].AppFoodCode))
					}
				}
			}
			nothirdSet := utils.NewSet(noThirdID...)
			SuccessID_categorySet := utils.NewSet(SuccessID_category...)
			SuccessID_third_Set := SuccessID_categorySet.Minus(nothirdSet)
			SuccessID_third := SuccessID_third_Set.List()

			rsreq := &et.RetailSellStatusRequest{}
			appfcSlice := make([]*et.AppFoodCode, 0)
			rsreq.AppPoiCode = appPoiCode
			rsreq.SellStatus = 0
			for _, v := range SuccessID_third {
				ele := &et.AppFoodCode{
					AppFoodCode: cast.ToString(v),
				}
				appfcSlice = append(appfcSlice, ele)
			}
			rsreq.FoodData = appfcSlice
			rsreq.StoreMasterId = in.StoreMasterId

			res, err := clientMt.RPC.RetailSellStatus(context.Background(), rsreq)
			glog.Info("上架-6666上下架到美团返回数据", kit.JsonEncode(res), ",入参：", kit.JsonEncode(rsreq), "错误err：", kit.JsonEncode(err))

			if err != nil {
				row := []string{financeCode, "", err.Error()}
				resList = append(resList, row)
				glog.Error(err)
				continue
			} else if res.Code != 200 {
				glog.Error(errors.New("美团接口失败，失败原因：" + res.Error.Msg))
				row := []string{financeCode, "具体商品ID请查看错误信息", res.Error.Msg}
				resList = append(resList, row)
				continue
			}
			insertData := &pc.NewChannelStoreProductRequest{
				Info: []*pc.ChannelStoreProduct{},
			}
			for _, v := range SuccessID_third {
				insertData.Info = append(insertData.Info, &pc.ChannelStoreProduct{
					ChannelId:   2,
					FinanceCode: financeCode,
					ProductId:   v,
					UpDownState: 1,
				})
			}

			//更新快照
			p.UpdateSnapShotNew(ctx, &pc.UpdateSnapShotRequest{
				ChannelId:   2,
				ProductId:   SuccessID_third,
				FinanceCode: []string{financeCode},
			})
			//门店上架成功商品写入数据库
			if _, err := p.NewChannelStoreProduct(ctx, insertData); err != nil {
				glog.Error(err)
				row := []string{financeCode, "", err.Error()}
				resList = append(resList, row)
				continue
			}
		}
		//错误信息以excel形式上传至七牛云
		excelUrl := ""
		if len(resList) > 0 {
			headRow := append([]string{}, "财务编码", "平台商品ID", "失败原因")
			errList := append([][]string{}, headRow)
			errList = append(errList, resList...)
			excelUrl, err = ExportProductErr(errList)
			if err != nil {
				glog.Error("错误信息上传失败; err: " + err.Error())
			}
		}
		//更新任务状态为已完成
		updateModel = models.TaskList{
			TaskStatus:     3,
			TaskDetail:     "",
			ResulteFileUrl: excelUrl,
			ModifyTime:     time.Now(),
		}
		if len(resList) == 0 {
			updateModel.TaskDetail = "成功"
		} else {
			updateModel.TaskDetail = "失败商品:"
		}
		_, err = session.ID(result.TaskList[0].Id).Update(updateModel)
		if err != nil {
			glog.Info("更新任务状态错误", err.Error())
		}
	}()

	//价格同步--美团
	go func(storesData map[string]string) {
		disclient := GetDispatchClient()
		defer disclient.Close()
		var financeCodeSlice []string
		for k, _ := range storesData {
			financeCodeSlice = append(financeCodeSlice, k)
		}
		//处理门店仓的价格，判断仓库属性
		//res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(context.Background(), &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: financeCodeSlice})
		//if err != nil {
		//	glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
		//}

		product := new(Product)
		resp, err := product.GetChannelWarehouses(financeCodeSlice, ChannelMtId)
		if err != nil {
			glog.Error(utils.RunFuncName()+"GetChannelWarehouses，", err)
		}
		warehouseMap := make(map[string]models.ChannelWarehouse, 0)
		//结果赋值
		for _, datum := range resp {
			warehouseMap[datum.ShopId] = datum
		}
		for financeCode, _ := range storesData {
			for _, v := range productIdSlice {
				if value, ok := warehouseMap[financeCode]; ok && value.Category == 3 {
					var sku_third models.SkuThird
					engine.Where("erp_id=4").And("product_id=?", v).Get(&sku_third)
					if sku_third.Id > 0 {
						UpdateZlPrice(financeCode, sku_third.SkuId, ChannelMtId)
						//价格同步--美团
						//var params models.PriceSync
						//params.FinanceCode = financeCode
						//params.Sku = cast.ToInt(sku_third.SkuId)
						//if sku_third.Id > 0 {
						//	var param pc.GetProductPriceByBJRequest
						//	param.StructCode = append(param.StructCode, params.FinanceCode)
						//	param.ProductCode = append(param.ProductCode, sku_third.ThirdSkuId)
						//	out, err := c.GetProductPriceByLocalBJ(context.Background(), &param)
						//	if err != nil {
						//		glog.Error("价格同步err:", err)
						//		continue
						//	}
						//	params.ZlProductid = sku_third.ThirdSkuId
						//	params.ProductId = int(sku_third.ProductId)
						//	if len(out.Data) > 0 {
						//		params.Price = cast.ToInt(out.Data[0].Product_Info[0].Sell_Price)
						//		c.MTPriceEx(params)
						//	}
					}
				} else if value.Category == 4 {
					var sku_third models.SkuThird
					engine.Where("erp_id=2").And("product_id=?", v).Get(&sku_third)
					if sku_third.Id > 0 {
						UpdateQzPrice(financeCode, int32(value.WarehouseId), sku_third.SkuId, ChannelMtId)
					}
				}
			}
		}
	}(storesData)
	out.Code = 200
	return out, nil
}

// 阿闻渠道--批量上架
// 已废弃
func (c *Product) BatchOnTheShelfToAW(ctx context.Context, in *pc.BatchOnTheShelfToAWRequest) (*pc.BatchOnTheShelfToAWResponse, error) {
	glog.Info("BatchOnTheShelfToAW请求参数=", in)
	out := new(pc.BatchOnTheShelfToAWResponse)
	if in.ChannelId <= 0 { //默认阿闻渠道
		out.Message = "渠道id不能为空"
		out.Code = 400
		return out, nil
	}
	var financeCodeArr []string
	//判断是全部门店
	if in.IsAll == 1 {
		if len(in.UserNo) <= 0 {
			out.Message = "用户编码不能为空"
			out.Code = 400
			return out, nil
		}
		client := GetDataCenterClient()
		defer client.Conn.Close()
		defer client.Cf()
		var params dac.GetHospitalListByUserNoRequest
		params.UserNo = in.UserNo
		//用户校验
		userInfo := loadLoginUserInfo(ctx)
		if userInfo.IsGeneralAccount {
			params.IsLogo = 1
		} else {
			params.IsLogo = 0
		}
		params.Category = in.Category
		params.ChannelId = in.ChannelId
		outResult, err := client.RPC.GetHospitalListByUserNo(client.Ctx, &params)
		if err != nil {
			out.Message = "获取用户权限内的门店医院列表失败"
			out.Error = err.Error()
			out.Code = 400
			return out, nil
		}
		for _, v := range outResult.Data {
			financeCodeArr = append(financeCodeArr, v.StructOuterCode)
		}
	} else {
		if len(in.FinanceCode) > 0 {
			financeCodeArr = strings.Split(in.FinanceCode, ",")
		} else {
			out.Message = "财务编码或标识门店不能为空"
			out.Code = 400
			return out, nil
		}
	}
	var productIdArr []string
	//如果产品id不传则默认是所有的
	if len(in.ProductId) > 0 {
		productIdArr = strings.Split(in.ProductId, ",")
	}

	session := NewDbConn().NewSession()
	defer session.Close()
	for _, v := range financeCodeArr {
		if len(in.ProductId) == 0 {
			//查询该渠道下的财务编码下的所有商品
			var list []models.ChannelStoreProduct
			session.Where("channel_id=?", in.ChannelId).And("finance_code=?", v).Find(&list)
			for _, k := range list {
				productIdArr = append(productIdArr, strconv.Itoa(k.ProductId))
			}
		}
		for _, k := range productIdArr {
			k_p, _ := strconv.Atoi(k)
			resultBool, _, _, err := c.BatchOnTheShelf(v, int32(k_p), in.ChannelId, in.UpDownState, session, ctx)
			if !resultBool {
				session.Rollback()
				out.Code = 400
				//out.Message = "提交批量上架---公共使用方法失败"
				out.Message = err.Error()
				out.Error = err.Error()
				return out, nil
			}
		}
	}
	err := session.Commit()
	if err != nil {
		session.Rollback()
		out.Code = 400
		out.Message = err.Error()
		return out, nil
	}

	out.Code = 200
	return out, nil
}

// 批量上下架---公共使用方法 目前正在使用
func (c *Product) BatchOnTheShelf(financeCode string, productId, channelId, UpDownState int32, session *xorm.Session, ctx context.Context, args ...interface{}) (Bool bool, snap *pc.ChannelProductSnapshot, stock int32, err error) {
	params := fmt.Sprintf("finance_code=%sproduct_id=%dchannel_id=%dUpDownState=%d", financeCode, productId, channelId, UpDownState)
	glog.Info("BatchOnTheShelf,请求参数", params)
	var errs error

	if len(financeCode) <= 0 || productId <= 0 || channelId <= 0 {
		return false, nil, 0, errors.New("参数不能为空")
	}

	resp, err := c.GetChannelWarehouses([]string{financeCode}, channelId)
	if err != nil {
		return false, nil, 0, err
	}
	if len(resp) <= 0 && channelId == ChannelAwenId {
		data, err := c.GetChannelWarehouses([]string{financeCode}, ChannelAwenPickUpId)
		if err != nil {
			return false, nil, 0, err
		}
		resp = append(resp, data...)
	}
	glog.Info("BatchOnTheShelf,获取仓库类型：", kit.JsonEncode(resp), " finance_code: ", financeCode, " channel_id:", channelId, " productId: ", productId)

	warehouseType := 0
	for _, v := range resp {
		if v.Category == 5 {
			warehouseType = 4
		} else {
			warehouseType = v.Category
			break
		}
	}

	// 仓库库存信息 //判断库存是否满足
	wareStock := new(ic.GetStockInfoResponse)
	//根据不同的渠道处理库存逻辑
	if UpDownState == 1 {
		_redis := GetRedisConn()
		if kit.EnvCanCron() {
			defer _redis.Close()
		}

		//判断库存是否足够
		var channelSkuThirds []models.ChannelSkuThird
		session.SQL("SELECT id,product_id,sku_id,third_spu_id,third_sku_id,erp_id,channel_id,third_spu_sku_id,is_use FROM dc_product.channel_sku_third WHERE channel_id = ? AND product_id = ?", channelId, productId).Find(&channelSkuThirds)

		zlId := cast.ToInt32(_redis.HGet("store:relation:dctozl", financeCode).Val())
		if zlId == 0 {
			return false, nil, 0, errors.New("查询不到门店对应子龙id，" + financeCode)
		}

		if len(channelSkuThirds) == 0 {
			return false, nil, 0, errors.New("查询不到商品子龙或A8货号，" + cast.ToString(productId))
		}

		skuId := "0"
		var skuCodeInfos []*ic.SkuCodeInfo
		for _, _sku := range channelSkuThirds {
			skuId = cast.ToString(_sku.SkuId)
			skuCodeInfo := &ic.SkuCodeInfo{
				FinanceCode: financeCode,
				Sku:         skuId,
				Spu:         _sku.ThirdSpuId,
				ThirdSkuid:  _sku.ThirdSkuId,
				ZlId:        zlId,
				ErpId:       _sku.ErpId,
			}
			skuCodeInfos = append(skuCodeInfos, skuCodeInfo)
		}

		//互联网医院不校验库存
		if channelId != ChannelDigitalHealth {
			if len(args) > 0 {
				//查询本地redis
				//查询子龙的库存
				stockMap, _ := GetStockInfoBySkuCode(0, skuCodeInfos, channelId, args[0], wareStock)
				stock = GetSkuStock(stockMap, financeCode, skuId)
				//如果子龙库存=0，则跳过
				if stock <= 0 {
					return false, nil, 0, errors.New("库存为0，无法上架")
				}

			} else {
				//查询北京
				//查询子龙的库存
				stockMap, _ := GetStockInfoBySkuCode(0, skuCodeInfos, channelId, 0, wareStock)
				stock = GetSkuStock(stockMap, financeCode, skuId)
				//如果子龙库存=0，则跳过
				if stock <= 0 {
					return false, nil, 0, errors.New("库存为0，无法上架")
				}
			}
		}
	}

	var model models.ChannelStoreProduct
	isEmpty, _ := session.Where("channel_id=?", channelId).And("finance_code=?", financeCode).And("product_id=?", productId).Get(&model)
	glog.Infof("BatchOnTheShelf,ChannelStoreProduct查詢結果：%v,%v,%v,%#v,%v", channelId, financeCode, productId, model, isEmpty)

	if !isEmpty {
		//如果为空，则新增一条上架数据
		if UpDownState == 1 { //如果是上架，新增快照，和对应关系
			in := &pc.ChannelProductSnapshotRequest{
				FinanceCode: financeCode,
				ChannelId:   channelId,
				ProductId:   []int32{productId},
			}
			result, err := c.QueryChannelProductSnapshot(ctx, in)
			if err != nil {
				glog.Error(utils.RunFuncName()+"，调用QueryChannelProductSnapshot失败，", err)
				return false, nil, 0, errors.New("根据sku、财务编码、spu查询商品信息以及库存失败")
			}
			if len(result.Details) > 0 {
				snap = result.Details[0]
				//处理更新渠道商品分类和推荐字段无法更新到小程序问题
				var newSnap pc.ChannelProductRequest
				err = json.Unmarshal([]byte(snap.JsonData), &newSnap)
				if err != nil {
					glog.Error(utils.RunFuncName()+"，json解析失败，", err, "，json：", snap.JsonData)
					return false, nil, 0, err
				}
				switch warehouseType {
				case 3, 1: // 电商仓取门店价格
					newSnap.SkuInfo[0].MarketPrice = newSnap.SkuInfo[0].StorePrice
					//model.MarketPrice = int(newSnap.SkuInfo[0].StorePrice)
				case 4:
					newSnap.SkuInfo[0].MarketPrice = newSnap.SkuInfo[0].PreposePrice
					model.MarketPrice = int(newSnap.SkuInfo[0].PreposePrice)
				default:
					newSnap.SkuInfo[0].MarketPrice = newSnap.SkuInfo[0].StorePrice
					//model.MarketPrice = int(newSnap.SkuInfo[0].StorePrice)
				}

				//自动上架任务，电商仓取门店价格
				if len(args) > 0 && (warehouseType == 3 || warehouseType == 1) {
					//给价格
					newSnap.SkuInfo[0].MarketPrice = cast.ToInt32(args[1])
					newSnap.SkuInfo[0].StorePrice = cast.ToInt32(args[1])
					model.MarketPrice = cast.ToInt(args[1])
				} else if len(args) > 0 && warehouseType == 4 {
					newSnap.SkuInfo[0].MarketPrice = cast.ToInt32(args[1])
					newSnap.SkuInfo[0].PreposePrice = cast.ToInt32(args[1])
					model.MarketPrice = cast.ToInt(args[1])
				}

				NewData, err := json.Marshal(newSnap)
				if err != nil {
					glog.Error(err)
					return false, nil, 0, err
				}
				//todo 事务更新数据，后续需要提供一个公共的方法处理
				var snapShot models.ChannelProductSnapshot
				//如果快照id=0，则新插入一条
				if snap.Id <= 0 {
					//插入数据库
					snap.CreateDate = time.Now().Format("2006-01-02 15:04:05")
					sql := "INSERT INTO `channel_product_snapshot` (`channel_id`,`user_no`,`finance_code`,`product_id`,`json_data`,`create_date`) VALUES (?, ?, ?, ?, ?, ?)"
					r, err := session.Exec(sql, snap.ChannelId, snap.UserNo, snap.FinanceCode, snap.ProductId, NewData, snap.CreateDate)
					if err != nil {
						glog.Error("快照信息插入失败=", err)
						return false, nil, 0, err
					}
					result, err := r.LastInsertId()
					if err != nil {
						glog.Error("获取插入的快照信息失败=", err)
						return false, nil, 0, err
					}
					snapShot.Id = int(result)
					snap.Id = int32(result)
				} else {
					//如果已存在，则编辑
					_, err := NewDbConn().SQL("SELECT id,channel_id,user_no,finance_code,product_id,json_data,create_date FROM dc_product.channel_product_snapshot WHERE finance_code = ? AND product_id = ? AND channel_id = ?", snap.FinanceCode, snap.ProductId, channelId).Get(&snapShot)
					if err != nil {
						glog.Error("查询不到已存在的快照信息=", err)
						return false, nil, 0, err
					}
					//自动上架任务
					if len(args) > 0 {
						_, err = session.Id(snap.Id).Update(&models.ChannelProductSnapshot{
							JsonData: string(NewData),
						})
						if err != nil {
							glog.Error("更新快照价格失败：", err)
							return false, nil, 0, err
						}
					}
				}
				snap.JsonData = string(NewData)

				model.ChannelId = int(channelId)
				model.FinanceCode = financeCode
				model.ProductId = int(productId)
				model.UpDownState = 1
				model.CreateDate = time.Now()
				model.UpdateDate = time.Now()
				model.SnapshotId = snapShot.Id
				model.IsRecommend = int(newSnap.Product.IsRecommend)
				model.ChannelCategoryId = int(newSnap.Product.ChannelCategoryId)
				model.ChannelCategoryName = newSnap.Product.ChannelCategoryName
				model.Name = newSnap.Product.Name
				model.SkuId = int(newSnap.SkuInfo[0].SkuId)
				model.MarketPrice = int(newSnap.SkuInfo[0].MarketPrice)

				isWeightOk, err := JudgeChannelStoreProductWeight(newSnap)
				if !isWeightOk {
					return false, nil, 0, err
				}

				//实物商品的价格为0或者5000的不让上
				product := models.Product{}
				session.SQL("select * from product where id = ? ", productId).Get(&product)
				if product.ProductType == 1 {
					if newSnap.SkuInfo[0].MarketPrice == 0 || newSnap.SkuInfo[0].MarketPrice == 500000 {
						err = errors.New("实物商品的价格必须不能为0或者5000")
						return false, nil, 0, err
					}
				}

				if newSnap.SkuInfo[0].MarketPrice <= 0 {
					err = errors.New("商品价格必须大于0")
					return false, nil, 0, err
				}

				_, err = session.Insert(&model)
				if err != nil {
					errs = errors.New("新增渠道门店表失败")
					return false, nil, 0, err
				}
				//更新渠道门店商品表中的几个商品字段
				updateChannelStoreProductFromSnapshot(model, session)
			} else {
				glog.Info("BatchOnTheShelf请求参数", params, "查询结果为空")
				errs = errors.New("查询渠道门店表失败")
				return false, nil, 0, errs
			}
		}
	} else {
		model.UpDownState = int(UpDownState)
		if UpDownState == 1 {
			//处理更新渠道商品分类和推荐字段无法更新到小程序问题
			var newSnap pc.ChannelProductRequest
			var snapShot models.ChannelProductSnapshot
			isemp, _ := session.Where("channel_id=?", channelId).And("finance_code=?", financeCode).And("product_id=?", productId).Get(&snapShot)
			if !isemp {
				//如果不存在，则新增一条快照
				in := &pc.ChannelProductSnapshotRequest{
					FinanceCode: financeCode,
					ChannelId:   channelId,
					ProductId:   []int32{productId},
				}
				result, err := c.QueryChannelProductSnapshot(ctx, in)
				if err != nil {
					errs = errors.New("根据sku、财务编码、spu查询商品信息以及库存失败")
					return false, nil, 0, errs
				}
				snap = result.Details[0]
				// 根据门店属性更新快照中的market_price

				var tmpSnap pc.ChannelProductRequest
				err = json.Unmarshal([]byte(snap.JsonData), &tmpSnap)
				if err != nil {
					glog.Error("NewChannelStoreProductTask方法查询快照信息失败,", err)
					return false, nil, 0, err
				}

				//自动上架任务
				if len(args) > 0 && warehouseType == 3 {
					//给价格
					tmpSnap.SkuInfo[0].MarketPrice = cast.ToInt32(args[1])
					tmpSnap.SkuInfo[0].StorePrice = cast.ToInt32(args[1])
					model.MarketPrice = cast.ToInt(args[1])
				}

				switch warehouseType {
				case 3:
					tmpSnap.SkuInfo[0].MarketPrice = tmpSnap.SkuInfo[0].StorePrice
				case 4:
					tmpSnap.SkuInfo[0].MarketPrice = tmpSnap.SkuInfo[0].PreposePrice
				default:
					tmpSnap.SkuInfo[0].MarketPrice = tmpSnap.SkuInfo[0].StorePrice
				}
				NewJsonData, err := json.Marshal(tmpSnap)
				if err != nil {
					glog.Error(err)
					return false, nil, 0, err
				}
				//插入数据库
				snap.CreateDate = time.Now().Format("2006-01-02 15:04:05")
				sql := "INSERT INTO `channel_product_snapshot` (`channel_id`,`user_no`,`finance_code`,`product_id`,`json_data`,`create_date`) VALUES (?, ?, ?, ?, ?, ?)"
				r, err := session.Exec(sql, snap.ChannelId, snap.UserNo, snap.FinanceCode, snap.ProductId, NewJsonData, snap.CreateDate)
				if err != nil {
					glog.Error("快照信息插入失败=", err)
				}
				result_int64, err := r.LastInsertId()
				if err != nil {
					glog.Error("获取插入的快照信息失败=", err)
				}
				snapShot.Id = int(result_int64)
				model.SnapshotId = snapShot.Id
				model.IsRecommend = int(tmpSnap.Product.IsRecommend)
				model.ChannelCategoryId = int(tmpSnap.Product.ChannelCategoryId)
				snapShot.JsonData = string(NewJsonData)
				snap.JsonData = string(NewJsonData)
			} else {
				if snapShot.Id == 0 {
					glog.Error("不存在该快照信息，快照id：", model.SnapshotId, err)
					return false, nil, 0, err
				}
				err = json.Unmarshal([]byte(snapShot.JsonData), &newSnap)
				if err != nil {
					glog.Error("BatchOnTheShelf方法查询快照信息失败,", err)
					return false, nil, 0, err
				}
				//自动上架任务
				if len(args) > 0 && warehouseType == 3 {
					//给价格
					newSnap.SkuInfo[0].MarketPrice = cast.ToInt32(args[1])
					newSnap.SkuInfo[0].StorePrice = cast.ToInt32(args[1])
					model.MarketPrice = cast.ToInt(args[1])
				} else if len(args) > 0 && warehouseType == 4 {
					newSnap.SkuInfo[0].MarketPrice = cast.ToInt32(args[1])
					newSnap.SkuInfo[0].PreposePrice = cast.ToInt32(args[1])
					model.MarketPrice = cast.ToInt(args[1])
				}
				switch warehouseType {
				case 3:
					newSnap.SkuInfo[0].MarketPrice = newSnap.SkuInfo[0].StorePrice
					//model.MarketPrice = int(newSnap.SkuInfo[0].StorePrice)
				case 4:
					newSnap.SkuInfo[0].MarketPrice = newSnap.SkuInfo[0].PreposePrice
					model.MarketPrice = int(newSnap.SkuInfo[0].PreposePrice)
				default:
					newSnap.SkuInfo[0].MarketPrice = newSnap.SkuInfo[0].StorePrice
					//model.MarketPrice = int(newSnap.SkuInfo[0].StorePrice)
				}
				model.SnapshotId = snapShot.Id
				model.IsRecommend = int(newSnap.Product.IsRecommend)
				model.ChannelCategoryId = int(newSnap.Product.ChannelCategoryId)

				isWeightOk, err := JudgeChannelStoreProductWeight(newSnap)
				if !isWeightOk {
					return false, nil, 0, err
				}

				if newSnap.SkuInfo[0].MarketPrice <= 0 {
					err = errors.New("商品价格必须大于0")
					return false, nil, 0, err
				}
				if len(args) > 0 {
					NewData, err := json.Marshal(newSnap)
					if err != nil {
						glog.Error(err)
						return false, nil, 0, err
					}
					_, err = session.Id(snapShot.Id).Update(&models.ChannelProductSnapshot{
						JsonData: string(NewData),
					})
					if err != nil {
						glog.Error("更新快照价格失败：", err)
						return false, nil, 0, err
					}
					//修复程序挂掉重启
					if snap == nil {
						snap = new(pc.ChannelProductSnapshot)
					}
					snap.Id = int32(snapShot.Id)
					snap.FinanceCode = snapShot.FinanceCode
					snap.ProductId = snapShot.ProductId
					snap.ChannelId = int32(snapShot.ChannelId)
					snap.UserNo = snapShot.UserNo
					snap.JsonData = string(NewData)
					snap.ProductThirdId = snapShot.ProductThirdId
				}
			}
		}

		glog.Info("更新上下架状态参数：", model)
		_, err := session.ID(model.Id).Cols("up_down_state, is_recommend, channel_category_id, snapshot_id,market_price").Update(&model)
		if err != nil {
			glog.Error("更新上下架状态失败：", err)
			return false, nil, 0, err
		}

		//更新渠道门店商品表中的几个商品字段
		updateChannelStoreProductFromSnapshot(model, session)
	}
	err = InsertOrUpdateProductHasStock(model, wareStock)
	if err != nil {
		return false, nil, 0, err
	}
	return true, snap, stock, nil
}

// 已废弃
func (c *Product) JddjOnTheShelfMulti(ctx context.Context, in *pc.BatchOnTheShelfToJddjRequest) (*pc.BatchOnTheShelfToJddjResponse, error) {
	out := &pc.BatchOnTheShelfToJddjResponse{
		Code: 200,
	}
	financeCodeSlice := strings.Split(in.FinanceCodeList, ",")

	redis1 := GetRedisConn()
	if kit.EnvCanCron() {
		defer redis1.Close()
	}

	pip := redis1.Pipeline()
	defer pip.Close()

	msg_type := ""
	if in.UpDownState == 1 {
		msg_type = "上架"
	} else if in.UpDownState == 0 {
		msg_type = "下架"
	}

	//批量获取子龙编码
	zilongMap := map[string]string{}
	err := func() error {
		for _, v := range financeCodeSlice {
			pip.HGet("store:relation:dctozl", v)
		}
		cmders, _ := pip.Exec()
		for _, c := range cmders {
			zilongMap[cast.ToString(c.Args()[2])] = c.(*redis.StringCmd).Val()
		}

		return nil
	}()
	if err != nil {
		return nil, status.Error(codes.Internal, "获取门店子龙编码失败")
	}
	glog.Info("zilongMap: ", zilongMap)

	//批量获取京东到家编码
	jddjMap := map[string]string{}
	err = func() error {
		for _, v := range financeCodeSlice {
			pip.HGet("store:relation:dctojddj", v)
		}
		cmders, _ := pip.Exec()
		for _, c := range cmders {
			jddjMap[cast.ToString(c.Args()[2])] = c.(*redis.StringCmd).Val()
		}
		return nil
	}()
	if err != nil {
		return nil, status.Error(codes.Internal, "获取门店京东到家编码失败")
	}
	glog.Info("jddjMap: ", jddjMap)

	//批量获取门店所属仓库类型
	warehouseMap := map[string]int32{}
	err = func() error {
		client := GetDispatchClient()
		defer client.Close()

		res, err := client.RPC.GetWarehouseInfoByFanceCodes(client.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: financeCodeSlice})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
			return err
		}
		for _, v := range res.Data {
			warehouseMap[v.Code] = v.Category
		}

		return nil
	}()
	if err != nil {
		return nil, status.Error(codes.Internal, "获取门店仓库类型失败")
	}
	glog.Info("warehouseMap: ", warehouseMap)

	for _, financeCode := range financeCodeSlice {
		in.FinanceCode = financeCode
		ctx = context.WithValue(context.Background(), "zilongId", zilongMap[financeCode])
		ctx = context.WithValue(ctx, "jddjId", jddjMap[financeCode])
		ctx = context.WithValue(ctx, "storeCategory", warehouseMap[financeCode])
		glog.Info("执行京东商品"+msg_type+"，参数:", ctx)
		res1, err := c.SingleOnTheShelfToJddj(ctx, in)
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用SingleOnTheShelfToJddj失败，", err)
			out.Message = "京东商品" + msg_type + "失败" + err.Error()
			continue
		}
		if res1.Code != 200 {
			glog.Error(utils.RunFuncName()+"调用SingleOnTheShelfToJddj失败，", res1.Message)
			out.Message = "京东商品" + msg_type + "失败" + res1.Message
			continue
		}

		glog.Info(utils.RunFuncName()+"，京东到家门店"+msg_type+"成功，", in.FinanceCode)
		res2, err := c.JddjBatchOnTheShelf(ctx, in)
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用JddjBatchOnTheShelf失败，", err)
			out.Message = "京东到家门店" + msg_type + "失败" + err.Error()
			continue
		}
		if res2.Code != 200 {
			glog.Error(utils.RunFuncName()+"调用JddjBatchOnTheShelf失败，", res2.Message)
			out.Message += "京东到家门店" + msg_type + "失败" + res2.Message
			continue
		}
		glog.Info(utils.RunFuncName()+"，门店"+msg_type+"成功，", in.FinanceCode)
	}
	return out, nil
}

// 已废弃
func (c *Product) JddjBatchOnTheShelf(ctx context.Context, in *pc.BatchOnTheShelfToJddjRequest) (*pc.BatchOnTheShelfToJddjResponse, error) {
	glog.Info("JddjBatchOnTheShelf请求参数=", in)
	out := new(pc.BatchOnTheShelfToJddjResponse)
	out.Code = 200

	session := NewDbConn().NewSession()
	defer session.Close()

	session.Begin()
	glog.Info("开启事务，调用BatchOnTheShelf公共方法...")
	result_bool, _, _, err := c.BatchOnTheShelf(in.FinanceCode, cast.ToInt32(in.ProductId), in.ChannelId, in.UpDownState, session, ctx)
	if !result_bool {
		session.Rollback()
		out.Code = 400
		if err != nil {
			out.Message = err.Error() //"上架失败"
			out.Error = err.Error()
		}
		return out, nil
	}
	err = session.Commit()
	if err != nil {
		session.Rollback()
		out.Code = 400
		out.Message = "提交事务失败"
		out.Error = err.Error()
		return out, nil
	} else {
		//价格同步--京东
		//根据财务编码获取仓库类型
		disclient := GetDispatchClient()
		defer disclient.Close()
		resWarehouse, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{in.FinanceCode}})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
			out.Message = "查询仓库类型失败: " + err.Error()
			return out, nil
		}
		warehouseMap := make(map[string]*dc.WarehouseList, len(resWarehouse.Data))
		for _, v := range resWarehouse.Data {
			warehouseMap[v.Code] = v
		}
		if value, ok := warehouseMap[in.FinanceCode]; ok && value.Category == 3 {
			//var params models.PriceSync
			//params.FinanceCode = in.FinanceCode
			//params.ProductId = cast.ToInt(in.ProductId)
			var sku_third models.SkuThird
			engine.Where("erp_id=4").And("product_id=?", in.ProductId).Get(&sku_third)
			if sku_third.Id > 0 {
				UpdateZlPrice(in.FinanceCode, sku_third.SkuId, ChannelJddjId)
				//var param pc.GetProductPriceByBJRequest
				//param.StructCode = append(param.StructCode, params.FinanceCode)
				//param.ProductCode = append(param.ProductCode, sku_third.ThirdSkuId)
				//out1, err := c.GetProductPriceByLocalBJ(context.Background(), &param)
				//if err != nil {
				//	glog.Error("价格同步err:", err)
				//}
				//if len(out1.Data) > 0 {
				//	params.ZlProductid = sku_third.ThirdSkuId
				//	params.Price = cast.ToInt(out1.Data[0].Product_Info[0].Sell_Price)
				//	c.JDPriceEx(params)
				//}
			}
		} else if value.Category == 4 {
			var sku_third models.SkuThird
			engine.Where("erp_id=2").And("product_id=?", in.ProductId).Get(&sku_third)
			if sku_third.Id > 0 {
				UpdateQzPrice(in.FinanceCode, value.Id, sku_third.SkuId, ChannelJddjId)
			}
		}
		glog.Info("提交事务成功...")
		return out, nil
	}

}

// 阿闻渠道--单个上架
// 已废弃
func (c *Product) BatchOnTheShelfSingeToAW(ctx context.Context, in *pc.BatchOnTheShelfToAWRequest) (*pc.BatchOnTheShelfToAWResponse, error) {
	glog.Info("BatchOnTheShelfSingeToAW请求参数=", in)
	out := new(pc.BatchOnTheShelfToAWResponse)
	out.Code = 200
	session := NewDbConn().NewSession()
	defer session.Close()
	session.Begin()

	//1：上架验证是否存在货号
	if in.UpDownState == 1 {
		storeCategory := 0
		financeCodes := strings.Split(in.FinanceCode, ",")
		//单门店
		if len(financeCodes) == 1 {
			//根据财务编码获取仓库类型
			disclient := GetDispatchClient()
			defer disclient.Close()
			res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{in.FinanceCode}})
			if err != nil {
				glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
			}
			if len(res.Data) > 0 {
				storeCategory = int(res.Data[0].Category)
			}
		} else {
			if md, ok := metadata.FromIncomingContext(ctx); ok && len(md.Get("category")) > 0 {
				category := md.Get("category")[0]
				mapl := make(map[string]int32)
				err := json.Unmarshal([]byte(category), &mapl)
				if err != nil {
					glog.Error("反序列化失败 err:", err)
					storeCategory = 0
				} else {
					storeCategory = cast.ToInt(mapl["category"])
				}
			}
		}
		glog.Info(in.FinanceCode+"，门店仓库类型：", storeCategory)

		//3-门店仓上架需要有子龙货号;//4-前置仓上架需要有A8货号
		if storeCategory == 3 || storeCategory == 4 {
			//如果是3-门店仓，4-前置仓
			wareName := "子龙"
			erp_id := 4
			if storeCategory == 4 {
				wareName = "A8"
				erp_id = 2
			}

			var thirdSkuId string
			has, err := session.SQL("select third_sku_id from channel_sku_third where product_id =  " + in.ProductId + " and erp_id = " + strconv.Itoa(erp_id) + "  and  channel_id=" + cast.ToString(in.ChannelId)).Get(&thirdSkuId)
			if err != nil {
				session.Rollback()
				glog.Error(err)
				out.Message = in.ProductId + err.Error()
				out.Code = 400
				return out, err
			}

			if !has {
				session.Rollback()
				out.Message = in.ProductId + "查询不到" + wareName + "货号信息，无法上架"
				out.Code = 400
				return out, nil
			} else {
				if thirdSkuId == "" {
					session.Rollback()
					out.Message = in.ProductId + "商品" + wareName + "货号为空，无法上架"
					out.Code = 400
					return out, nil
				}
			}
		} else {
			//其他仓库类型
			out.Message = in.FinanceCode + "，门店仓库类型不正确"
			out.Code = 400
			return out, nil
		}
	}

	//2：上下架操作
	result_bool, _, _, err := c.BatchOnTheShelf(in.FinanceCode, cast.ToInt32(in.ProductId), in.ChannelId, in.UpDownState, session, ctx)
	if err != nil {
		out.Code = 400
		out.Message = "上下架失败，err:" + err.Error() //"上下架失败"
		out.Error = err.Error()
		return out, nil
	}
	if !result_bool {
		session.Rollback()
		out.Code = 400
		if err != nil {
			out.Message = err.Error() //"上下架失败"
			out.Error = err.Error()
		}
		return out, nil
	}
	err = session.Commit()
	if err != nil {
		session.Rollback()
		out.Code = 400
		out.Message = "提交事务失败"
		out.Error = err.Error()
		return out, nil
	}

	//3：如果上架完成，同步价格
	if in.UpDownState == 1 {
		//根据财务编码获取仓库类型
		disclient := GetDispatchClient()
		defer disclient.Close()
		res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{in.FinanceCode}})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
		}
		warehouseMap := make(map[string]int32, len(res.Data))
		for _, v := range res.Data {
			if v.Category == 3 {
				warehouseMap[v.Code] = v.Category
			}
		}
		if len(warehouseMap) > 0 {
			var model models.SkuThird
			engine.Where("erp_id=4").And("product_id=?", in.ProductId).Get(&model)
			if model.Id > 0 {
				var params pc.UpdatePriceToChannelRequest
				params.FinanceCode = in.FinanceCode
				params.ChannelId = in.ChannelId
				params.ProductCode = model.ThirdSkuId
				params.ProductId = cast.ToInt32(in.ProductId)
				params.SkuId = model.SkuId
				glog.Info("上架价格同步，请求参数：", params)
				c.UpdatePriceToChannel(ctx, &params)
			}
		}
	}
	return out, nil
}

// 批量上架，顺序执行，并把错误返回出去
// 已废弃
func (c *Product) BatchJddjOnTheShelfMulti(ctx context.Context, in *pc.BatchOnTheShelfToJddjRequest) (*pc.BatchOnTheShelfToJddjResponse, error) {
	out := &pc.BatchOnTheShelfToJddjResponse{
		Code: 200,
	}
	financeCodeSlice := strings.Split(in.FinanceCodeList, ",")

	redis1 := GetRedisConn()
	if kit.EnvCanCron() {
		defer redis1.Close()
	}

	pip := redis1.Pipeline()
	defer pip.Close()

	//批量获取子龙编码
	zilongMap := map[string]string{}
	err := func() error {
		for _, v := range financeCodeSlice {
			pip.HGet("store:relation:dctozl", v)
		}
		cmders, _ := pip.Exec()
		for _, c := range cmders {
			zilongMap[cast.ToString(c.Args()[2])] = c.(*redis.StringCmd).Val()
		}

		return nil
	}()
	if err != nil {
		return nil, status.Error(codes.Internal, "获取门店子龙编码失败")
	}
	glog.Info("zilongMap: ", zilongMap)

	//批量获取京东到家编码
	jddjMap := map[string]string{}
	err = func() error {
		for _, v := range financeCodeSlice {
			pip.HGet("store:relation:dctojddj", v)
		}
		cmders, _ := pip.Exec()
		for _, c := range cmders {
			jddjMap[cast.ToString(c.Args()[2])] = c.(*redis.StringCmd).Val()
		}
		return nil
	}()
	if err != nil {
		return nil, status.Error(codes.Internal, "获取门店京东到家编码失败")
	}
	glog.Info("jddjMap: ", jddjMap)

	//批量获取门店所属仓库类型
	warehouseMap := map[string]int32{}
	err = func() error {
		client := GetDispatchClient()
		defer client.Close()

		res, err := client.RPC.GetWarehouseInfoByFanceCodes(client.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: financeCodeSlice})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
			return err
		}
		for _, v := range res.Data {
			warehouseMap[v.Code] = v.Category
		}

		return nil
	}()
	if err != nil {
		return nil, status.Error(codes.Internal, "获取门店仓库类型失败")
	}
	glog.Info("warehouseMap: ", warehouseMap)

	if len(financeCodeSlice) == 1 {

		for _, financeCode := range financeCodeSlice {
			in.FinanceCode = financeCode
			ctx = context.WithValue(context.Background(), "zilongId", zilongMap[financeCode])
			ctx = context.WithValue(ctx, "jddjId", jddjMap[financeCode])
			ctx = context.WithValue(ctx, "storeCategory", warehouseMap[financeCode])
			glog.Info("执行京东商品上架，参数:", ctx)
			res1, err := c.SingleOnTheShelfToJddj(ctx, in)
			if err != nil {
				glog.Error(utils.RunFuncName()+"调用SingleOnTheShelfToJddj失败，", err)
				out.Message = "京东商品上架失败" + err.Error()
				out.Code = 400
				return out, nil
			}
			if res1.Code != 200 {
				glog.Error(utils.RunFuncName()+"调用SingleOnTheShelfToJddj失败，", res1.Message)
				out.Message = "京东商品上架失败" + res1.Message
				out.Code = 400
				return out, nil
			}

			glog.Info(utils.RunFuncName()+"，京东到家门店上架成功，", in.FinanceCode)
			res2, err := c.JddjBatchOnTheShelf(ctx, in)
			if err != nil {
				glog.Error(utils.RunFuncName()+"调用JddjBatchOnTheShelf失败，", err)
				out.Message = "京东到家门店上架失败" + err.Error()
				out.Code = 400
				return out, nil
			}
			if res2.Code != 200 {
				glog.Error(utils.RunFuncName()+"调用JddjBatchOnTheShelf失败，", res2.Message)
				out.Message += "京东到家门店上架失败" + res2.Message
				out.Code = 400
				return out, nil
			}
			glog.Info(utils.RunFuncName()+"，门店上架成功，", in.FinanceCode)
		}
	}
	return out, nil
}

// 京东 TODO DONE:京东到家渠道上下架/同步库存(单个商品)
// 已废弃
func (c *Product) SingleOnTheShelfToJddj(ctx context.Context, in *pc.BatchOnTheShelfToJddjRequest) (*pc.BatchOnTheShelfToJddjResponse, error) {
	glog.Info("SingleOnTheShelfToJddj请求参数=", in)
	out := new(pc.BatchOnTheShelfToJddjResponse)
	out.Code = 200

	Engine := NewDbConn()

	if in.UpDownState == 1 {
		storeCategory := cast.ToInt32(ctx.Value("storeCategory"))
		glog.Info(in.FinanceCode+"，门店仓库类型：", storeCategory)
		switch storeCategory {
		case 3: //门店仓上架需要有子龙货号
			var thirdSkuId string
			has, err := Engine.SQL("select third_sku_id from channel_sku_third where product_id =  " + in.ProductId + " and erp_id = 4").Get(&thirdSkuId)
			if err != nil {
				glog.Error(err)
				return nil, err
			}
			if !has {
				out.Message = in.ProductId + "查询不到子龙货号信息，无法上架"
				out.Code = 400
				return out, nil
			} else {
				if thirdSkuId == "" {
					out.Message = in.ProductId + "商品子龙货号为空，无法上架"
					out.Code = 400
					return out, nil
				}
			}
		case 4: //前置仓上架需要有A8货号
			var thirdSkuId string
			has, err := Engine.SQL("select third_sku_id from channel_sku_third where product_id =  " + in.ProductId + " and erp_id = 2").Get(&thirdSkuId)
			if err != nil {
				glog.Error(err)
				return nil, err
			}
			if !has {
				out.Message = in.ProductId + "查询不到A8货号信息，无法上架"
				out.Code = 400
				return out, nil
			} else {
				if thirdSkuId == "" {
					out.Message = in.ProductId + "商品A8货号为空，无法上架"
					out.Code = 400
					return out, nil
				}
			}
		default:
			return nil, errors.New(in.FinanceCode + "，门店仓库类型不正确")
		}
	}
	//查询sku信息
	var (
		p        Product
		skuId    string
		skuCodes []*ic.SkuCodeInfo
	)
	if res, err := p.QueryChannelSkuThird(ctx, &pc.OneofIdRequest{
		ChannelId: 4, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{cast.ToInt32(in.ProductId)}}},
	}); err != nil {
		glog.Error("查询sku信息失败：" + err.Error())
		out.Code = 400
		out.Message = err.Error()
		return out, err
	} else if res.Code != 200 {
		glog.Error("查询sku信息失败：" + res.Message)
		out.Code = 400
		out.Message = res.Message
		return out, err
	} else {
		//查询子龙id
		zilongId := cast.ToInt32(ctx.Value("zilongId"))
		if zilongId == 0 {
			err = errors.New("查询redis门店子龙id为空，" + in.FinanceCode)
			glog.Error(err)
			return nil, err
		}
		glog.Info(in.FinanceCode+"，子龙ID: ", zilongId)

		for _, v := range res.Details {
			skuId = cast.ToString(v.SkuId)
			skuCodes = append(skuCodes, &ic.SkuCodeInfo{
				FinanceCode: in.FinanceCode,
				ZlId:        zilongId,
				Sku:         skuId,
				ThirdSkuid:  v.ThirdSkuId,
				ErpId:       v.ErpId,
			})
		}
	}
	glog.Info("skuCodes:", skuCodes)
	//查询库存
	stockMap, _ := GetStockInfoBySkuCode(0, skuCodes, in.ChannelId)
	stock := GetSkuStock(stockMap, in.FinanceCode, skuId)
	glog.Infof("%v，%v，库存stock: %v", in.FinanceCode, skuId, stock)

	if in.UpDownState == 1 && stock == 0 {
		out.Code = 400
		out.Message = "商品" + cast.ToString(in.ProductId) + "库存为0，无法上架"
		return out, nil
	}

	//根据门店属性同步价格到京东，先取快照中的价格，如果没有快照，则取channel-sku表中的价格
	var jsonData string
	syncPriceReq := new(pc.SyncJddjPriceRequest)
	syncPriceReq.FinanceCode = in.FinanceCode
	syncPriceReq.OutSkuId = cast.ToInt32(skuId)
	snapHas, err := Engine.Table("channel_product_snapshot").Select("`json_data`").Where("product_id=? and channel_id = 4 and finance_code = ?", in.ProductId, in.FinanceCode).Get(&jsonData)
	if err != nil {
		glog.Error("查询对应快照失败", err)
		out.Code = 400
		out.Message = "查询对应快照失败:" + in.ProductId
		return out, err
	} else if snapHas == true {
		glog.Info(in.ProductId, "有快照，取快照中的价格", jsonData)
		var channelProductReq pc.ChannelProductRequest
		err = json.Unmarshal([]byte(jsonData), &channelProductReq)
		if err != nil {
			glog.Error(err)
		} else {
			glog.Info("快照中前置仓价", channelProductReq.SkuInfo[0].PreposePrice, " 快照中门店仓价格", channelProductReq.SkuInfo[0].StorePrice)
			syncPriceReq.StorePrice = channelProductReq.SkuInfo[0].StorePrice
			syncPriceReq.PreposePrice = channelProductReq.SkuInfo[0].PreposePrice
		}
	} else if snapHas != true {
		glog.Info(in.ProductId, "无快照，取channel-sku表中的价格")
		var preposePrice, storePrice int32
		_, err := Engine.Table("channel_sku").Select("`prepose_price`").Where("product_id=? and channel_id = ? ", in.ProductId, in.ChannelId).Get(&preposePrice)
		if err != nil {
			glog.Errorf("查询前置仓价格失败")
			out.Code = 400
			out.Message = "查询前置仓价格失败:" + in.ProductId
			return out, err
		}
		_, err = Engine.Table("channel_sku").Select("`store_price`").Where("product_id=? and channel_id = ? ", in.ProductId, in.ChannelId).Get(&storePrice)
		if err != nil {
			glog.Errorf("查询门店仓价格失败")
			out.Code = 400
			out.Message = "查询门店仓价格失败:" + in.ProductId
			return out, err
		}
		syncPriceReq.PreposePrice = preposePrice
		syncPriceReq.StorePrice = storePrice
	}
	resp, err := c.SyncJddjPrice(ctx, syncPriceReq)
	if err != nil {
		glog.Errorf("调用同步京东价格失败")
		out.Code = 400
		out.Message = "同步京东价格失败:" + in.ProductId
		return out, err
	} else if resp.Code != 200 {
		glog.Errorf("同步京东价格失败")
		out.Code = 400
		out.Message = "同步京东价格失败:" + in.ProductId
		return out, nil
	}

	var subData et.JddjStockVendibility
	if has, err := Engine.Table("channel_sku").Select("`id`").Where("product_id=?", in.ProductId).Get(&skuId); err != nil {
		glog.Errorf("上架状态同步京东到家失败")
		out.Code = 400
		out.Message = "查询对应SkuID失败:" + in.ProductId
		return out, err
	} else if has != true {
		glog.Errorf("上架状态同步京东到家失败")
		out.Code = 400
		out.Message = "找不到" + in.ProductId + "对应skuId"
		return out, nil
	} else {
		subData.OutSkuId = skuId
	}

	jddjFinanceCode := cast.ToString(ctx.Value("jddjId"))
	syncData := &et.BatchUpdateVendibilityRequest{
		StationNo: jddjFinanceCode,
		UserPin:   "xrp",
	}
	glog.Info("财务编码为:", in.FinanceCode)
	glog.Info("准备同步到京东，京东门店编号为:", syncData.StationNo)
	if in.UpDownState == 0 {
		glog.Info("同步下架状态到京东到家...")
		subData.DoSale = false
	} else if in.UpDownState == 1 {
		glog.Info("同步上架状态到京东到家...")
		subData.DoSale = true
	}
	syncData.StockVendibilityList = []*et.JddjStockVendibility{&subData}

	client := et.GetExternalClient()
	defer client.Close()

	glog.Info("调用京东到家的BatchUpdateVendibility接口（同步门店可售状态），参数为:", syncData)
	//todo tp jd
	//已废弃
	res, err := client.JddjProduct.BatchUpdateVendibility(client.Ctx, syncData)
	if err != nil {
		glog.Info("上架状态同步京东到家失败: " + err.Error())
		out.Code = 400
		out.Message = "上架状态同步京东到家失败"
		return out, err
	} else if res.RetCode != "0" {
		glog.Errorf("上架状态同步京东到家失败:" + res.RetMsg)
		out.Code = 400
		out.Message = "上架状态同步京东到家失败:" + res.RetMsg
		return out, nil
	} else {
		glog.Info("res.RetMsg:", res.RetMsg)
	}

	//构造请求
	updateStockReq := &et.UpdateStockRequest{
		StationNo: jddjFinanceCode,
		UserPin:   "xrp",
		SkuStockList: []*et.SkuStockList{
			{
				OutSkuId: skuId,
				StockQty: stock,
			},
		},
	}
	glog.Info("同步京东门店库存...")
	//todo tp jd
	//已废弃
	stockRes, err := client.JddjProduct.UpdateStock(client.Ctx, updateStockReq)
	if err != nil {
		glog.Info("京东门店同步库存失败: " + err.Error())
		out.Code = 400
		out.Message = "京东门店同步库存失败"
		return out, err
	} else if strings.ContainsAny(stockRes.RetMsg, "失败") {
		glog.Info(stockRes)
		out.Code = 400
		out.Message = "京东门店同步库存失败" + stockRes.RetMsg
		return out, nil
	} else {
		glog.Info("同步京东库存成功，返回:", stockRes)
	}

	//价格同步--京东
	//根据财务编码获取仓库类型
	disclient := GetDispatchClient()
	defer disclient.Close()
	resWarehouse, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{in.FinanceCode}})
	if err != nil {
		glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
	}
	warehouseMap := make(map[string]*dc.WarehouseList, len(resWarehouse.Data))
	for _, v := range resWarehouse.Data {
		warehouseMap[v.Code] = v
	}
	if value, ok := warehouseMap[in.FinanceCode]; ok && value.Category == 3 {
		//var params models.PriceSync
		//params.FinanceCode = in.FinanceCode
		//params.ProductId = cast.ToInt(in.ProductId)
		var sku_third models.SkuThird
		engine.Where("erp_id=4").And("product_id=?", in.ProductId).Get(&sku_third)
		if sku_third.Id > 0 {
			UpdateZlPrice(in.FinanceCode, sku_third.SkuId, ChannelJddjId)
			//var param pc.GetProductPriceByBJRequest
			//param.StructCode = append(param.StructCode, params.FinanceCode)
			//param.ProductCode = append(param.ProductCode, sku_third.ThirdSkuId)
			//out1, err := c.GetProductPriceByLocalBJ(context.Background(), &param)
			//if err != nil {
			//	glog.Error("价格同步err:", err)
			//}
			//if len(out1.Data) > 0 {
			//	params.ZlProductid = sku_third.ThirdSkuId
			//	params.Price = cast.ToInt(out1.Data[0].Product_Info[0].Sell_Price)
			//	c.JDPriceEx(params)
			//}
		}
	} else if value.Category == 4 {
		var sku_third models.SkuThird
		engine.Where("erp_id=2").And("product_id=?", in.ProductId).Get(&sku_third)
		if sku_third.Id > 0 {
			UpdateQzPrice(in.FinanceCode, value.Id, sku_third.SkuId, ChannelJddjId)
		}
	}
	return out, nil
}

// 已废弃
func (c *Product) UpdatePriceCommonBatchV5(ctx context.Context, FinanceCode []string, in *pc.ChannelProductRequest) {
	if len(FinanceCode) > 0 {
		disclient := GetDispatchClient()
		defer disclient.Close()
		res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: FinanceCode})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
		}
		storeWarehouseMap := make(map[string]int32)
		preposeWarehouseMap := make(map[string]int32)
		for _, v := range res.Data {
			if v.Category == 3 {
				storeWarehouseMap[v.Code] = v.Category
			} else if v.Category == 4 {
				preposeWarehouseMap[v.Code] = v.Id
			}
		}

		bt, err := json.Marshal(in)
		if err != nil {
			glog.Error(err)
		}
		//写入商品快照
		snap := pc.ChannelProductSnapshot{
			ChannelId: cast.ToInt32(in.Product.ChannelId),
			ProductId: in.Product.Id,
			UserNo:    "priceSync",
			JsonData:  string(bt),
		}

		glog.Info("priceSync排查价格为0的数据,参数为：", snap.JsonData, "ProductId:", in.Product.Id, "ChannelId：", in.Product.ChannelId)
		var params pc.GetProductPriceByBJRequest
		for k, _ := range storeWarehouseMap {
			params.StructCode = append(params.StructCode, k)
		}
		//copy(params.StructCode, FinanceCode)

		var preposeParams pc.GetPreposePriceRequest
		for k, v := range preposeWarehouseMap {
			var prepose pc.PreposeWarehouseIdWithCode
			prepose.Id = v
			prepose.Code = k
			preposeParams.StoreCode = append(preposeParams.StoreCode, &prepose)
		}

		// 货号对应的product_id
		var idWithThirdSkuZlMap = make(map[int32]string)
		var idWithThirdSkuQzMap = make(map[int32]string)
		if in.Product.ProductType != 3 {
			for _, info := range in.SkuInfo[0].SkuThird {
				if info.ErpId == 4 {
					idWithThirdSkuZlMap[info.ProductId] = info.ThirdSkuId
					params.ProductCode = append(params.ProductCode, info.ThirdSkuId)
				} else if info.ErpId == 2 {
					idWithThirdSkuQzMap[info.ProductId] = info.ThirdSkuId
					preposeParams.ProductCode = append(preposeParams.ProductCode, info.ThirdSkuId)
				}
			}
		} else {
			var proList []int32
			for _, v := range in.SkuInfo[0].SkuGroup {
				proList = append(proList, v.GroupProductId)
			}
			var sku []models.SkuThird
			err := engine.In("product_id", proList).Find(&sku)
			if err != nil {
				glog.Error("查询第三方货号失败: ", err.Error())
				return
			}
			for _, v := range sku {
				if v.ErpId == 4 {
					idWithThirdSkuZlMap[v.ProductId] = v.ThirdSkuId
					params.ProductCode = append(params.ProductCode, v.ThirdSkuId)
				} else if v.ErpId == 2 {
					idWithThirdSkuQzMap[v.ProductId] = v.ThirdSkuId
					preposeParams.ProductCode = append(preposeParams.ProductCode, v.ThirdSkuId)
				}
			}
		}
		//优化sql查询
		var channel_store_product_list []models.ChannelStoreProduct
		err = engine.Where("channel_id=?", in.Product.ChannelId).In("finance_code", FinanceCode).And("product_id=?", in.Product.Id).Find(&channel_store_product_list)
		channel_store_product_map := make(map[string]models.ChannelStoreProduct, len(channel_store_product_list))
		for _, v := range channel_store_product_list {
			channel_store_product_map[v.FinanceCode] = v
		}
		// 门店仓取价格
		if len(params.StructCode) > 0 && len(params.ProductCode) > 0 {
			res, err := c.GetStoreWarehousePrice(ctx, &params)
			if err != nil {
				glog.Error("批量请求北京失败，err:", err)
				return
			}
			str_map := make(map[string]string, 0)
			for _, v := range res.Data {
				for _, s := range v.Struct_Code {
					for _, info := range v.Product_Info {
						//taxRate, _ := decimal.NewFromString(info.Sell_Price)
						//kk := taxRate.Mul(decimal.NewFromInt(100))
						//newstring := cast.ToString(kk)
						str_map[s+":"+info.Product_Code] = info.Sell_Price
					}
				}
			}
			Product_Id := in.Product.Id
			var model_json_list []models.PriceSync
			engine.Where("product_id=?", Product_Id).In("finance_code", FinanceCode).Find(&model_json_list)
			model_json_map := make(map[string]models.PriceSync, len(model_json_list))
			for _, v := range model_json_list {
				model_json_map[v.FinanceCode] = v
			}
			var sku models.SkuThird
			engine.Where("product_id=?", in.Product.Id).And("erp_id=4").Get(&sku)
			if sku.Id <= 0 {
				glog.Info("UpdatePriceCommon查询不到skuid,无法处理,请求参数为", in)
				return
			}

			for _, v := range params.StructCode {
				if in.Product.ProductType != 3 {
					for _, pro := range params.ProductCode {
						if value, ok := str_map[v+":"+pro]; ok {
							//修改channel_store_product价格数据
							if res, ok := channel_store_product_map[v]; ok {
								res.MarketPrice = cast.ToInt(value)
								engine.Where("id=?", res.Id).Cols("market_price").Update(&res)
							}
							in.SkuInfo[0].MarketPrice = cast.ToInt32(value)
							in.SkuInfo[0].StorePrice = cast.ToInt32(value)
							bt, err := json.Marshal(in)
							if err != nil {
								glog.Error(err)
							}
							//写入商品快照
							snap = pc.ChannelProductSnapshot{
								ChannelId: cast.ToInt32(in.Product.ChannelId),
								ProductId: in.Product.Id,
								UserNo:    "priceSync",
								JsonData:  string(bt),
							}
							glog.Info("priceSync排查价格为0的数据,参数为：", snap.JsonData, "ProductId:", in.Product.Id, "ChannelId：", in.Product.ChannelId)
							c.NewChannelProductSnapshot(ctx, &snap)
							if _, ok := storeWarehouseMap[v]; ok {
								//第三方处理渠道商品价格
								var model_json models.PriceSync
								if res, ok := model_json_map[v]; !ok {
									skuint := int32(0)
									for _, v := range in.SkuInfo[0].SkuThird {
										if v.ErpId == 4 {
											skuint = v.SkuId
										}
									}
									if skuint == 0 {
										if sku.Id > 0 {
											skuint = sku.SkuId
										} else {
											glog.Info("UpdatePriceCommon查询不到skuid,无法处理,请求参数为", in)
											return
										}
									}
									model_json.Sku = int(skuint)
									model_json.Price = cast.ToInt(value)
									model_json.ProductId = cast.ToInt(Product_Id)
									model_json.FinanceCode = v
								} else {
									model_json = res
								}
								//价格同步，仓库属性判断
								if storeWarehouseMap[model_json.FinanceCode] == 3 {
									//数据推送过去
									if cast.ToInt32(in.Product.ChannelId) == 1 {
										c.EditProductPrice(model_json, 1)
									} else
									//美团渠道
									if cast.ToInt32(in.Product.ChannelId) == 2 { //美团渠道
										c.MTPriceEx(model_json)
									} else if cast.ToInt32(in.Product.ChannelId) == 3 { //饿了么渠道
										c.ELMPriceEx(model_json)
									} else if cast.ToInt32(in.Product.ChannelId) == 4 { //京东渠道
										// 废弃
										c.JDPriceEx(model_json)
									}
								}
							}
						} else {
							c.NewChannelProductSnapshot(ctx, &snap)
						}
						UpdateGroupProductPrice(v, cast.ToInt32(in.Product.ChannelId), 3, in.Product.Id, in.SkuInfo[0].StorePrice, nil, cast.ToInt32(in.SkuInfo[0].SkuId))
					}
				} else {
					// 组合商品取价
					var floatPrice float64
					for _, sku := range in.SkuInfo[0].SkuGroup {
						if value, ok := str_map[v+":"+idWithThirdSkuZlMap[sku.GroupProductId]]; ok {
							sku.MarketPrice = cast.ToInt32(value)
						} else {
							var snapshotGroup = models.ChannelProductSnapshot{}
							_, err = engine.Where("channel_id=?", in.Product.ChannelId).
								And("finance_code = ?", v).
								And("product_id = ?", sku.GroupProductId).
								Get(&snapshotGroup)
							if err != nil {
								glog.Error("查询子商品快照信息失败: ", err)
							} else if snapshotGroup.Id > 0 {
								var snapData pc.ChannelProductRequest
								err = json.Unmarshal([]byte(snapshotGroup.JsonData), &snapData)
								if err == nil {
									sku.MarketPrice = snapData.SkuInfo[0].StorePrice
								}
							} else {
								_, err = engine.Select("store_price").Table("channel_sku").
									Where("channel_id=?", in.Product.ChannelId).
									And("product_id = ?", sku.GroupProductId).
									Get(&sku.MarketPrice)
							}
						}
						if sku.DiscountType == 1 {
							//in.SkuInfo[0].MarketPrice = in.SkuInfo[0].MarketPrice + (int32(math.Floor((float64(sku.MarketPrice)*(float64(sku.DiscountValue)/100) + 0.5))) * sku.Count)
							floatPrice = floatPrice + ((float64(sku.MarketPrice) * (float64(sku.DiscountValue) / 100)) * float64(sku.Count))
						} else {
							in.SkuInfo[0].MarketPrice = in.SkuInfo[0].MarketPrice + (sku.DiscountValue * sku.Count)
						}
					}
					if floatPrice > 0 {
						in.SkuInfo[0].MarketPrice = int32(math.Floor(floatPrice + 0.5))
					}
					// 更新上架表价格
					if res, ok := channel_store_product_map[v]; ok {
						res.MarketPrice = cast.ToInt(in.SkuInfo[0].MarketPrice)
						engine.Where("id=?", res.Id).Cols("market_price").Update(&res)
					}
					in.SkuInfo[0].StorePrice = in.SkuInfo[0].MarketPrice
					bt, err := json.Marshal(in)
					if err != nil {
						glog.Error(err)
					}
					//更新快照价格
					snap = pc.ChannelProductSnapshot{
						ChannelId:   cast.ToInt32(in.Product.ChannelId),
						ProductId:   in.Product.Id,
						JsonData:    string(bt),
						FinanceCode: v,
					}
					glog.Info("组合商品,参数为：", snap.JsonData, "ProductId:", in.Product.Id, "ChannelId：", in.Product.ChannelId)
					c.NewChannelProductSnapshot(ctx, &snap)
				}
			}
		}
		// 前置仓取价格
		if len(preposeParams.StoreCode) > 0 && len(preposeParams.ProductCode) > 0 {
			preposeRes, err := c.GetPreposeWarehousePrice(ctx, &preposeParams)
			if err != nil {
				glog.Error("批量取前置仓价格失败: ", err.Error())
				return
			}
			//if preposeRes.Code != 200 {
			//	glog.Error("无前置仓价格")
			//	return
			//}
			var preposeProWithPrice = make(map[string]int32)
			for _, v := range preposeRes.Data {
				preposeProWithPrice[v.FinanceCode+":"+v.ProductCode] = v.Price
			}
			for _, store := range preposeParams.StoreCode {
				// 非组合商品更新价格
				if in.Product.ProductType != 3 {
					if price, ok := preposeProWithPrice[store.Code+":"+idWithThirdSkuQzMap[in.Product.Id]]; ok {
						in.SkuInfo[0].PreposePrice = price
						in.SkuInfo[0].MarketPrice = price
						if res, ok := channel_store_product_map[store.Code]; ok {
							res.MarketPrice = cast.ToInt(in.SkuInfo[0].MarketPrice)
							engine.Where("id=?", res.Id).Cols("market_price").Update(&res)
						}
						//第三方处理渠道商品价格
						var model_json models.PriceSync
						skuint := int32(0)
						for _, v := range in.SkuInfo[0].SkuThird {
							if v.ErpId == 2 {
								skuint = v.SkuId
							}
						}
						if skuint == 0 {
							if in.SkuInfo[0].SkuId > 0 {
								skuint = in.SkuInfo[0].SkuId
							} else {
								glog.Info("UpdatePriceCommon查询不到skuid,无法处理,请求参数为", in)
								return
							}
						}
						model_json.Sku = int(skuint)
						model_json.Price = cast.ToInt(price)
						model_json.ProductId = cast.ToInt(in.Product.Id)
						model_json.FinanceCode = store.Code

						//阿闻
						if cast.ToInt32(in.Product.ChannelId) == 1 {
							c.EditProductPrice(model_json, 1)
						} else if cast.ToInt32(in.Product.ChannelId) == 2 { //美团渠道
							c.MTPriceEx(model_json)
						} else if cast.ToInt32(in.Product.ChannelId) == 3 { //饿了么渠道
							c.ELMPriceEx(model_json)
						} else if cast.ToInt32(in.Product.ChannelId) == 4 { //京东渠道
							// 废弃
							c.JDPriceEx(model_json)
						}
					}
					UpdateGroupProductPrice(store.Code, cast.ToInt32(in.Product.ChannelId), 4, in.Product.Id, in.SkuInfo[0].PreposePrice,
						nil, cast.ToInt32(in.SkuInfo[0].SkuId))
				} else {
					// 组合商品取价
					var floatPrice float64
					for _, sku := range in.SkuInfo[0].SkuGroup {
						// 使用前置仓表价格
						if price, ok := preposeProWithPrice[store.Code+":"+idWithThirdSkuQzMap[sku.GroupProductId]]; ok {
							sku.MarketPrice = price
						} else {
							// 使用子商品快照价格
							var snapshotGroup = models.ChannelProductSnapshot{}
							_, err = engine.Where("channel_id=?", in.Product.ChannelId).
								And("finance_code = ?", store.Code).
								And("product_id = ?", sku.GroupProductId).
								Get(&snapshotGroup)
							if err != nil {
								glog.Error("查询子商品快照信息失败: ", err)
							} else if snapshotGroup.Id > 0 {
								var snapData pc.ChannelProductRequest
								err = json.Unmarshal([]byte(snapshotGroup.JsonData), &snapData)
								if err == nil {
									sku.MarketPrice = snapData.SkuInfo[0].PreposePrice
								}
							} else {
								_, err = engine.Select("prepose_price").Table("channel_sku").
									Where("channel_id=?", in.Product.ChannelId).
									And("product_id = ?", sku.GroupProductId).
									Get(&sku.MarketPrice)
							}
						}
						if sku.DiscountType == 1 {
							floatPrice = floatPrice + ((float64(sku.MarketPrice) * (float64(sku.DiscountValue) / 100)) * float64(sku.Count))
						} else {
							in.SkuInfo[0].MarketPrice = in.SkuInfo[0].MarketPrice + (sku.DiscountValue * sku.Count)
						}
					}
					if floatPrice > 0 {
						in.SkuInfo[0].MarketPrice = int32(math.Floor(floatPrice + 0.5))
					}
					// 更新上架表价格
					if res, ok := channel_store_product_map[store.Code]; ok {
						res.MarketPrice = cast.ToInt(in.SkuInfo[0].MarketPrice)
						engine.Where("id=?", res.Id).Cols("market_price").Update(&res)
					}
					in.SkuInfo[0].PreposePrice = in.SkuInfo[0].MarketPrice
					bt, err := json.Marshal(in)
					if err != nil {
						glog.Error(err)
					}
					//更新快照价格
					snap = pc.ChannelProductSnapshot{
						ChannelId:   cast.ToInt32(in.Product.ChannelId),
						ProductId:   in.Product.Id,
						JsonData:    string(bt),
						FinanceCode: store.Code,
					}
					glog.Info("组合商品,参数为：", snap.JsonData, "ProductId:", in.Product.Id, "ChannelId：", in.Product.ChannelId)
					c.NewChannelProductSnapshot(ctx, &snap)
				}
			}
		}
	}
}
