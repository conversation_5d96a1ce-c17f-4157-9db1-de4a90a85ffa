package services

import (
	"_/models"
	"_/proto/pc"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/techoner/gophp"
)

//同步商品
func (c *Product) SyncGoods(ctx context.Context, in *pc.ChannelStoreProduct) (*pc.BaseResponse, error) {
	glog.Info("开始执行电商虚拟商品同步!")
	out := &pc.BaseResponse{
		Code: 400,
	}
	imgDomain := "https://oss.upetmart.com/www/shop/store/goods/1/"

	mpValue := make(map[string]int32)

	var arrSpecValue []models.UpetSpecValue
	specValueAll := make(map[int]models.UpetSpecValue)
	err := UpetNewDbConn().Table("upet_spec_value").Find(&arrSpecValue)
	if err != nil {
		glog.Error("查询电商数据库失败：", err)
		out.Message = "查询电商数据库失败：" + err.Error()
		return out, err
	}
	for _, v := range arrSpecValue {
		specValueAll[v.SpValueId] = v
	}

	var arrCategory []models.Category
	mapCategory := make(map[int32]models.Category)
	if err := NewDbConn().Find(&arrCategory); err != nil {
		glog.Error("查询电商数据库失败：", err)
		out.Message = "查询电商数据库失败：" + err.Error()
		return out, err
	}
	for _, v := range arrCategory {
		mapCategory[v.Id] = v
	}

	//.Where("goods_commonid=100094")
	var goodsCommon []models.UpetGoodsCommon
	// and goods_commonid=100094
	if err := UpetNewDbConn().Table("upet_goods_common").Where("is_virtual=1").Find(&goodsCommon); err != nil {
		glog.Error("查询电商数据库失败：", err)
		out.Message = "查询电商数据库失败：" + err.Error()
		return out, err
	} else {
		var goods []models.UpetGoods
		//.Where("goods_commonid=100094")
		if err := UpetNewDbConn().Table("upet_goods").Where("goods_id < 1000000").Find(&goods); err != nil {
			glog.Error("查询电商数据库失败：", err)
			out.Message = "查询电商数据库失败：" + err.Error()
			return out, err
		}
		for _, v := range goodsCommon {
			in := &pc.ProductRequest{
				Product: &pc.Product{
					Id:            v.GoodsCommonid,
					CategoryId:    v.GcId,
					BrandId:       v.BrandId,
					Name:          v.GoodsName,
					Pic:           imgDomain + v.GoodsImage,
					SellingPoint:  v.GoodsJingle,
					Video:         v.GoodsVideo,
					ContentMobile: v.MobileBody,
					ContentPc:     v.GoodsBody,
					IsUse:         1,
					TermType:      1,
					UseRange:      "1,2,3",
					ChannelId:     "5",
				},
			}
			if v.IsVirtual == 1 {
				in.Product.ProductType = 2
			} else {
				in.Product.ProductType = 1
			}

			for _, g := range goods {
				if g.GoodsCommonid != v.GoodsCommonid {
					continue
				}

				//建立品牌关联
				if has, err := NewDbConn().Table("type_brand").Where("type_id=? and brand_id=?", v.TypeId, v.BrandId).Exist(); err != nil {
					glog.Error("查询商品数据库失败：", err)
					out.Message = "查询商品数据库失败：" + err.Error()
					return out, err
				} else if !has {
					_, err = NewDbConn().Insert(&models.TypeBrand{TypeId: v.TypeId, BrandId: v.BrandId})
					if err != nil {
						glog.Error("插入品牌关联失败：", err)
						out.Message = "插入品牌关联失败：" + err.Error()
						return out, err
					}
				}

				if g.VirtualIndate > 0 {
					in.Product.TermValue = g.VirtualIndate
				} else {
					in.Product.TermValue = int32(time.Now().Add(time.Hour * 99999).Unix())
				}

				skuInfo := &pc.SkuInfo{
					MarketPrice: int32(g.GoodsMarketprice * 100),
					RetailPrice: int32(g.GoodsMarketprice * 100),
					ProductId:   g.GoodsCommonid,
					SkuId:       g.GoodsId,
					BarCode:     cast.ToString(g.GoodsId),
				}

				if skuInfo.BarCode == "" {
					skuInfo.BarCode = fmt.Sprintf("%d", time.Now().UnixNano())
				}

				skuInfo.SkuThird = append(skuInfo.SkuThird, &pc.SkuThird{
					ProductId: g.GoodsCommonid,
					SkuId:     g.GoodsId,
					//ThirdSpuId: strconv.Itoa(int(g.GoodsCommonid)),
					ThirdSkuId: strconv.Itoa(int(g.GoodsId)),
					ErpId:      4,
				})

				skuInfo.SkuThird = append(skuInfo.SkuThird, &pc.SkuThird{
					ProductId: g.GoodsCommonid,
					SkuId:     g.GoodsId,
					//ThirdSpuId: strconv.Itoa(int(g.GoodsCommonid)),
					ThirdSkuId: strconv.Itoa(int(g.GoodsId)),
					ErpId:      2,
				})

				spec, _ := gophp.Unserialize([]byte(g.SpecName))
				specValue, _ := gophp.Unserialize([]byte(g.GoodsSpec))
				//fmt.Println(spec, specValue)
				if spec != nil && specValue != nil {
					for k, _ := range spec.(map[string]interface{}) {
						for k2, k2v := range specValue.(map[string]interface{}) {
							ki, _ := strconv.Atoi(k)
							k2i, _ := strconv.Atoi(k2)
							if specValueAll[k2i].SpId == ki {
								if specValueAll[k2i].SpValueName == k2v {
									skuInfo.Skuv = append(skuInfo.Skuv, &pc.SkuValue{SpecId: int32(ki), SpecValueId: int32(k2i)})
								} else {
									if id, ok := mpValue[k2v.(string)]; ok {
										skuInfo.Skuv = append(skuInfo.Skuv, &pc.SkuValue{SpecId: int32(ki), SpecValueId: id})
									} else {
										model := models.SpecValue{
											SpecId: int32(ki),
											Value:  k2v.(string),
										}
										_, err = NewDbConn().Insert(&model)
										if err != nil {
											glog.Error("规格值插入数据库失败：", err)
											out.Message = "规格值插入数据库失败：" + err.Error()
											return out, nil
										}

										mpValue[model.Value] = model.Id

										skuInfo.Skuv = append(skuInfo.Skuv, &pc.SkuValue{SpecId: int32(ki), SpecValueId: model.Id, Pic: in.Product.Pic})
									}
								}
							}
						}
					}
				} else {
					skuInfo.Skuv = append(skuInfo.Skuv, &pc.SkuValue{SpecId: 1, SpecValueId: -1})
				}
				in.SkuInfo = append(in.SkuInfo, skuInfo)
			}

			funAttr := func(vname, vvalue string) {
				var tempAttr []models.Attr
				var attr models.Attr

				if err := NewDbConn().Where("`name`=?", vname).Find(&tempAttr); err != nil {
					glog.Error(err)
					return
				} else if len(tempAttr) > 0 {
					attr = tempAttr[0]
				} else {
					attr.Name = vname
					attr.IsShow = 1
					if _, err := NewDbConn().Insert(&attr); err != nil {
						glog.Error(err)
						return
					}
				}

				var tempAttrValue []models.AttrValue
				var attrValue models.AttrValue
				if err := NewDbConn().Where("attr_id=? AND `value`=?", attr.Id, vvalue).Find(&tempAttrValue); err != nil {
					glog.Error(err)
					return
				} else if len(tempAttrValue) > 0 {
					attrValue = tempAttrValue[0]
				} else {
					attrValue.Value = vvalue
					attrValue.AttrId = attr.Id
					if _, err := NewDbConn().Insert(&attrValue); err != nil {
						glog.Error(err)
						return
					}
				}

				if attrValue.Id > 0 {
					if v, ok := mapCategory[in.Product.CategoryId]; ok {
						if has, err := NewDbConn().Table("type_attr").Where("type_id=? and attr_id=?", v.TypeId, attrValue.AttrId).Exist(); err != nil {
							glog.Error(err)
							return
						} else if !has {
							NewDbConn().Insert(&models.TypeAttr{TypeId: v.TypeId, AttrId: attrValue.AttrId})
						}
					}
					in.ProductAttr = append(in.ProductAttr, &pc.ProductAttr{AttrId: attrValue.AttrId, AttrValueId: attrValue.Id})
				}
			}

			//商品属性
			goodsAttr, _ := gophp.Unserialize([]byte(v.GoodsAttr))
			if goodsAttr != nil {
				for _, v := range goodsAttr.(map[string]interface{}) {
					var vname, vvalue string
					vc := v.(map[string]interface{})
					for k2, v2 := range vc {
						if k2 == "name" {
							vname = vc[k2].(string)
						} else {
							if v2 != nil {
								vvalue = v2.(string)
							}
						}

					}
					//fmt.Println(k, vname, vvalue)
					funAttr(vname, vvalue)
				}
				//fmt.Println(goodsAttr)
			}

			//商品自定义属性
			cv, _ := gophp.Unserialize([]byte(v.GoodsCustom))

			if cv != nil {
				for _, v := range cv.(map[string]interface{}) {
					//fmt.Println(k,v)
					if v.(map[string]interface{})["value"] != nil {
						//ki, _ := strconv.Atoi(k)
						vname := v.(map[string]interface{})["name"].(string)
						vvalue := v.(map[string]interface{})["value"].(string)
						funAttr(vname, vvalue)
						//fmt.Println(k, vname, vvalue)
					} else {
						continue
					}
				}
			}
			// b, _ := json.Marshal(in)
			// println(string(b))
			var sis []*pc.SkuInfo
			for _, v := range in.SkuInfo {
				sis = append(sis, v)
			}

			name := in.Product.Name
			for i := 0; i < len(sis); i++ {
				in.SkuInfo = sis[i : i+1]
				in.SkuInfo[0].SkuThird[0].ThirdSkuId += strconv.Itoa(int(time.Now().Unix()))
				in.SkuInfo[0].SkuThird[1].ThirdSkuId += strconv.Itoa(int(time.Now().Unix()))
				var skv string
				for _, v := range in.SkuInfo[0].Skuv {
					skv += v.SpecValueValue + " "
				}
				//fmt.Println(in.Product.Id, in.SkuInfo[0].SkuId, in.SkuInfo[0].SkuThird[0].ThirdSkuId)
				in.Product.Name = name + " " + skv
				if res, err := c.NewProduct(context.Background(), in); err != nil {
					glog.Error("新建商品失败：", err)
					out.Message = "新建商品失败：" + err.Error()
					return out, err
				} else {
					b, _ := json.Marshal(res)
					println(string(b))
					if res.Code != 200 {
						glog.Error("新建商品失败：", res.Message)
						out.Message = "新建商品失败：" + res.Message
						return out, err
					}
				}
				// println(in.Product.Name, in.Product.Id)
			}
			// break
		}
	}
	out.Code = 200
	return out, nil
}
