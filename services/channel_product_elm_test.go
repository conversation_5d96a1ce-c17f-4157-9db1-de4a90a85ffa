package services

import (
	"_/proto/pc"
	"context"
	"reflect"
	"testing"
)

func TestProduct_UpdateElmCategoryList(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.CategoryListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "更新饿了么类目列表",
			args: args{
				ctx: context.Background(),
				in:  &pc.CategoryListRequest{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.UpdateElmCategoryList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateElmCategoryList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("UpdateElmCategoryList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetElmCategoryList(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.CategoryListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.ElmCategoryListResponse
		wantErr bool
	}{
		{
			name: "查询饿了么类目列表",
			args: args{
				ctx: context.Background(),
				in:  &pc.CategoryListRequest{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.GetElmCategoryList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetElmCategoryList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetElmCategoryList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
