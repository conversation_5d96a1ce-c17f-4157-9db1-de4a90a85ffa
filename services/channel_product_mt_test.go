package services

import (
	"_/proto/pc"
	"context"
	"reflect"
	"testing"
)

func TestProduct_BatchToMT(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.BatchToMTRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BatchBaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := c.BatchToMT(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchToMT() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchToMT() got = %v, want %v", got, tt.want)
			}
		})
	}
}
