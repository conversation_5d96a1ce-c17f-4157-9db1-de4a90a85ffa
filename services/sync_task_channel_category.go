package services

import (
	"_/models"
	"_/proto/dac"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/maybgit/glog"
	"github.com/ppkg/distributed-worker/dto"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 门店分类同步任务
func DealAsyncChannelCategory(taskContent int32) func() bool {
	return func() bool {
		//查询新任务的ID

		params := &pc.GetTaskListRequest{
			Sort:        "createTimeAsc",
			TaskStatus:  1,
			ChannelId:   -1,          //-1 查询全部的
			TaskContent: taskContent, // 查询task_content in (67)
			Status:      1,
			Page:        1,
			PageSize:    1,
		}

		//获取需要执行的任务，只取一条数据
		pd := new(Product)
		result, err := pd.GetTaskList(context.Background(), params)
		if err != nil {
			glog.Errorf("DealAsyncChannelCategory-定时查询新任务异常,%+v", err)
			return false
		}
		//无任务的时候，直接返回
		if len(result.TaskList) == 0 {
			return false
		}
		glog.Info("DealAsyncChannelCategory 定时任务执行")
		for _, task := range result.TaskList {
			myCtx := SyncChannelCategoryContext{
				TaskId:    task.Id,
				ChannelId: task.ChannelId,
			}
			err = handleSyncChannelCategory(&myCtx, task)
			if err != nil {
				glog.Errorf("DealAsyncChannelCategory-门店异步切仓处理异常 %v", err)
				myCtx.SystemError = err.Error()
			}

			updateModel := models.TaskList{
				TaskStatus:     3,
				TaskDetail:     myCtx.SystemError,
				ResulteFileUrl: "",
				ModifyTime:     time.Now(),
			}
			// 如果有调度任务则设置为进行中
			if myCtx.ScheduleSize > 0 {
				updateModel.TaskStatus = 2
				updateModel.TaskDetail = "--"
				updateModel.ContextData = kit.JsonEncode(myCtx)
			}
			/*	if updateModel.TaskStatus == 3 && myCtx.SystemError == "" {
					updateModel.TaskDetail = fmt.Sprintf("同步成功%d个门店，同步失败%d个门店", len(myCtx.SuccessList), len(myCtx.FailList))
				}

				// 当任务完成时导出excel文档
				if updateModel.TaskStatus == 3 {
					updateModel.ResulteFileUrl = exportSyncChannelCategoryError(&myCtx)
					if myCtx.SystemError != "" {
						updateModel.SuccessNum = 0
						updateModel.FailNum = int32(myCtx.Size)
					} else {
						updateModel.SuccessNum = int32(len(myCtx.SuccessList))
						updateModel.FailNum = int32(len(myCtx.FailList))
					}
				}*/

			_, err = engine.Where("id=?", task.Id).Update(updateModel)
			if err != nil {
				glog.Errorf("DealAsyncChannelCategory-更新任务状态异常,%+v", err)
			}
		}
		return true
	}
}

func exportSyncChannelCategoryError(taskId int32, list []*pc.SyncChannelCategoryScheduleCallbackData) string {
	if len(list) == 0 {
		return ""
	}
	var excelRow [][]string
	// excel文件头
	excelRow = append(excelRow, []string{"门店财务编码", "门店名称", "门店是否同步成功", "分类名称", "分类操作失败原因", "失败的商品SKUID", "商品失败原因"})
	for _, v := range list {
		excelRow = append(excelRow, []string{v.FinanceCode, v.ShopName, v.Message, v.CategoryName, v.CategoryPostErr, v.SkuId, v.SkuPostErr})
	}

	if len(excelRow) <= 1 {
		return ""
	}
	url, err := ExportProductErr(excelRow)
	if err != nil {
		glog.Errorf("DealAsyncChannelCategory-导出Excel文件失败,taskId:%d,%+v", taskId, err)
	}
	return url
}

//分发任务
func handleSyncChannelCategory(myCtx *SyncChannelCategoryContext, task *pc.TaskList) error {
	glog.Infof("DealAsyncChannelCategory 开始执行任务:%s", kit.JsonEncode(task))
	myCtx.UserInfo = new(models.LoginUserInfo)
	err := json.Unmarshal([]byte(task.RequestHeader), myCtx.UserInfo)
	if err != nil {
		glog.Errorf("DealAsyncChannelCategory 解析当前用户登录信息失败:%+v,taskId:%d", err, task.Id)
		return fmt.Errorf("解析当前用户登录信息失败,%+v", err)
	}
	ctx := context.WithValue(context.Background(), "user_info", myCtx.UserInfo)

	storeList, financeCodes, err := parseSyncChannelCategoryStoreExcel(ctx, task.OperationFileUrl)
	if err != nil {
		glog.Errorf("DealAsyncChannelCategory 导入Excel文件内容异常:%+v,taskId:%d", err, task.Id)
		return err
	}
	//查询storemaster以及第三方门店id
	//GetFinanceCodeByStoresChannelId
	//GetStoreMasterIdByFinanceCode
	dcClient := dac.GetDataCenterClient()
	storeMasterResRequest := new(dac.GetStoreMasterIdByFinanceCodeRequest)
	storeMasterResRequest.FinanceCode = financeCodes
	storeMasterRes, err := dcClient.RPC.GetStoreMasterIdByFinanceCode(dcClient.Ctx, storeMasterResRequest)
	if err != nil {
		return errors.New("查询门店appChannel出错:" + err.Error())
	}
	storeMasterMap := make(map[string]int32)
	for _, v := range storeMasterRes.StoreMasterList {
		storeMasterMap[v.FinanceCode] = v.StoreMasterId
	}

	AppPoiCodeRequest := new(dac.StoreRelationUserRequest)
	AppPoiCodeRequest.ChannelId = myCtx.ChannelId
	AppPoiCodeRequest.FinanceCode = financeCodes
	AppPoiCodeRes, err := dcClient.RPC.GetFinanceCodeByStoresChannelId(dcClient.Ctx, AppPoiCodeRequest)
	if err != nil {
		return errors.New("查询门店appChannel出错:" + err.Error())
	}
	AppPoiCodeMap := make(map[string]string)
	for _, v := range AppPoiCodeRes.Data {
		AppPoiCodeMap[v.FinanceCode] = v.ChannelStoreId
	}

	// 去重处理
	storeList = filterSyncChannelCategoryStore(storeList, storeMasterMap, AppPoiCodeMap)

	myCtx.Size = len(storeList)

	// 限制处理门店数量
	if len(storeList) > 3000 {
		return errors.New("只支持一次性最大导入3000家门店，超出部分不做处理")
	}
	myCtx.SuccessList = storeList
	//一个门店一个调度任务
	// 提交调度job
	err = submitSyncChannelCategoryScheduleJob(myCtx, task)
	if err != nil {
		return err
	}

	return nil
}

// 门店去重
func filterSyncChannelCategoryStore(storeList []SyncChannelCategoryInfo, storeMasterMap map[string]int32, AppPoiCodeMap map[string]string) []SyncChannelCategoryInfo {
	myMap := make(map[string]SyncChannelCategoryInfo)
	for _, v := range storeList {
		if storeMasterId, ok := storeMasterMap[v.FinanceCode]; ok {
			v.StoreMasterId = storeMasterId
		}
		if appPoiCodeMap, ok := AppPoiCodeMap[v.FinanceCode]; ok {
			v.AppPoiCode = appPoiCodeMap
		}
		myMap[v.FinanceCode] = v

	}
	list := make([]SyncChannelCategoryInfo, 0, len(myMap))
	for _, v := range myMap {
		list = append(list, v)
	}
	return list
}

// 提交调度job
func submitSyncChannelCategoryScheduleJob(ctx *SyncChannelCategoryContext, task *pc.TaskList) error {
	name := fmt.Sprintf("门店:%s同步分类-%s", ctx.FinanceCode, time.Now().Format("200601021504"))
	req := dto.AsyncJobRequest{
		Name:  name,
		Label: "门店分类同步",
		Meta: map[string]string{
			"taskId": cast.ToString(ctx.TaskId),
		},
		Type:     "syncChannelCategory",
		IsNotify: true,
		PluginSet: []string{
			"syncChannelCategory",
		},
		TaskInputList:          []string{},
		TaskExceptionOperation: 1,
	}
	type ExtendData struct {
		TodoList     []string `json:"todo_list"`
		ConflictList []string `json:"conflict_list"`
	}
	extendData := new(ExtendData)
	if task.ExtendedData != "" {
		_ = json.Unmarshal([]byte(task.ExtendedData), extendData)
	}
	conlictMap := make(map[string]struct{})
	if len(extendData.ConflictList) > 0 {
		for _, v := range extendData.ConflictList {
			conlictMap[v] = struct{}{}
		}
	}
	for _, v := range ctx.SuccessList {
		item := SyncChannelCategoryInputItem{
			FinanceCode:   v.FinanceCode,
			ShopName:      v.ShopName,
			AppPoiCode:    v.AppPoiCode,
			StoreMasterId: v.StoreMasterId,
			ChannelId:     ctx.ChannelId,
			UserInfo:      ctx.UserInfo,
			IsConflict:    0,
		}
		if _, ok := conlictMap[v.FinanceCode]; ok {
			item.IsConflict = 1
		}
		req.TaskInputList = append(req.TaskInputList, kit.JsonEncode(item))
	}

	ctx.ScheduleSize = len(req.TaskInputList)
	// 如果没有符合条件数据则直接return
	if ctx.ScheduleSize == 0 {
		return nil
	}
	glog.Errorf("DealAsyncChannelCategory 请求分布式调度,taskId:%d,请求参数:%s", ctx.TaskId, kit.JsonEncode(req))
	jobId, err := workerCtx.AsyncSubmitJob(req)
	if err != nil {
		glog.Errorf("DealAsyncChannelCategory 请求分布式调度异常,taskId:%d,请求参数:%s,%+v", ctx.TaskId, kit.JsonEncode(req), err)
		return fmt.Errorf("请求分布式调度异常,%+v", err)
	}

	ctx.ScheduleId = int(jobId)
	return nil
}

// 解析门店配置Excel文档
func parseSyncChannelCategoryStoreExcel(ctx context.Context, url string) ([]SyncChannelCategoryInfo, []string, error) {
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return nil, nil, errors.New("用户不存在")
	}

	// 下载excel
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("parseSyncChannelCategoryStoreExcel-构建下载Excel文档(%s)请求异常,%+v", url, err)
	}

	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		return nil, nil, fmt.Errorf("parseSyncChannelCategoryStoreExcel-执行下载Excel文档(%s)请求异常,%+v", url, err)
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		return nil, nil, fmt.Errorf("parseSyncChannelCategoryStoreExcel-打开Excel文档(%s)异常,%+v", url, err)
	}

	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows := f.GetRows(sheetName)

	list := make([]SyncChannelCategoryInfo, 0, len(rows))
	var financeCodes []string
	for i, item := range rows {
		if i == 0 {
			continue
		}
		financeCode := strings.TrimSpace(item[0])
		list = append(list, SyncChannelCategoryInfo{
			FinanceCode: financeCode,
			ShopName:    strings.TrimSpace(item[1]),
		})
		financeCodes = append(financeCodes, financeCode)
	}
	return list, financeCodes, nil
}

// 门店信息
type SyncChannelCategoryInfo struct {
	FinanceCode string `json:"finance_code"`
	//第三方门店id
	AppPoiCode string `json:"app_poi_code"`
	//门店渠道
	StoreMasterId int32 `json:"store_master_id"`
	//是否和其他任务冲突 1是 0 否
	IsConflict    int32  `json:"is_conflict"`
	ShopName      string `json:"shop_name"`
	CategoryName  string `json:"category_name"`
	CategoryError string `json:"category_error"`
	SkuId         string `json:"sku_id"`
	SkuError      string `json:"sku_error"`
	// 如果有错误信息则放在这里
	Message string `json:"message"`
}

type SyncChannelCategoryInputItem struct {
	// 门店财务编码
	FinanceCode string `json:"finance_code"`
	ShopName    string `json:"shop_name"`
	// 渠道，1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;
	ChannelId int32 `json:"channel_id"`
	// 用户信息
	UserInfo *models.LoginUserInfo `json:"user_info"`
	//第三方门店id
	AppPoiCode string `json:"app_poi_code"`
	//门店渠道
	StoreMasterId int32 `json:"store_master_id"`
	//是否和其他任务冲突 1是 0 否
	IsConflict int32 `json:"is_conflict"`
}

type SyncChannelCategoryContext struct {
	// 任务ID
	TaskId int32 `json:"task_id"`
	// 渠道，1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;
	ChannelId   int32  `json:"channel_id"`
	Size        int    `json:"size"`
	FinanceCode string `json:"finance_code"`
	// 有多少个任务被调度
	ScheduleSize int `json:"schedule_size"`
	// 调度器返回的ID
	ScheduleId int `json:"schedule_id"`
	// 处理失败门店
	FailList []SyncChannelCategoryInfo `json:"fail_list"`
	// 处理成功门店
	SuccessList []SyncChannelCategoryInfo `json:"success_list"`
	// 系统错误
	SystemError string `json:"system_error"`
	// 用户登录信息
	UserInfo *models.LoginUserInfo `json:"user_info"`
}
