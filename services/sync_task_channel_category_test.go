package services

import (
	"_/proto/pc"
	"reflect"
	"testing"
)

func TestDealAsyncChannelCategory(t *testing.T) {
	type args struct {
		taskContent int32
	}
	tests := []struct {
		name string
		args args
		want func() bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				taskContent: 31,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := DealAsyncChannelCategory(tt.args.taskContent); !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("DealAsyncChannelCategory() = %v, want %v", got, tt.want)
			}
		})
	}
}

/*
func Test_sssDealAsyncChannelCategory(t *testing.T) {
	type args struct {
		taskContent int32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				taskContent: 31,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := sssDealAsyncChannelCategory(tt.args.taskContent); got != tt.want {
				//t.Errorf("sssDealAsyncChannelCategory() = %v, want %v", got, tt.want)
			}
		})
	}
}*/

func Test_submitSyncChannelCategoryScheduleJob(t *testing.T) {
	type args struct {
		ctx  *SyncChannelCategoryContext
		task *pc.TaskList
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: &SyncChannelCategoryContext{
					SuccessList: []SyncChannelCategoryInfo{{
						FinanceCode: "CX0005",
					},
						{
							FinanceCode: "CX0013",
						},
					},
				},
				task: &pc.TaskList{
					Id:           183941,
					ExtendedData: `{"todo_list":["CX0013"],"conflict_list":["CX0005"]}`,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := submitSyncChannelCategoryScheduleJob(tt.args.ctx, tt.args.task); (err != nil) != tt.wantErr {
				t.Errorf("submitSyncChannelCategoryScheduleJob() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
