package services

import (
	"_/proto/models"
	"_/proto/pc"
	"context"
	"fmt"
	"sync"

	"_/proto/es"

	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
)

//查询电商商品数据
func loadUpetGoodsData() []models.UpetGoodsExt {
	var goods []models.UpetGoodsExt
	err := engine.SQL("SELECT upet_goods.goods_id, upet_goods.is_virtual, upet_goods.region_id, upet_goods.goods_name, upet_goods.spec_name, upet_goods.goods_spec, upet_goods.goods_image, upet_goods.goods_jingle, upet_goods.gc_id, upet_goods.goods_commonid, upet_goods.goods_price, upet_goods.member_price_1, upet_goods.goods_salenum, upet_goods.goods_promotion_type, upet_goods.goods_promotion_price, CONCAT( upet_goods_class.gc_name, '>', upet_goods_class_1.gc_name, '>', upet_goods_class_2.gc_name ) AS channel_category_name, REPLACE ( REPLACE ( CONCAT( species, ',', varieties, ',', sex, ',', shape, ',', age, ',', special_stage, ',', is_sterilization, ',', content_type, ',', `status` ), '不限,', '' ), ',不限', '' ) AS tags,goods_type FROM upet_goods INNER JOIN upet_goods_class ON upet_goods_class.gc_id = upet_goods.gc_id INNER JOIN upet_goods_class upet_goods_class_1 ON upet_goods_class_1.gc_id = upet_goods.gc_id_1 INNER JOIN upet_goods_class upet_goods_class_2 ON upet_goods_class_2.gc_id = upet_goods.gc_id_2 LEFT JOIN product_tag ON product_tag.sku_id = upet_goods.goods_id AND product_tag.product_type IN ( 1, 2 ) WHERE upet_goods.goods_state = 1 AND upet_goods.store_id=1 ").Find(&goods)
	if err != nil {
		glog.Error(err)
	}
	return goods
}

//导入渠道门店商品到es
func importChannelStoreProduct(indexName string) error {
	// client := es.NewEsClient("http://es-cn-m7r1uawz10006espz.public.elasticsearch.aliyuncs.com:9200", "elastic", "dk3aOf6U")

	var wg sync.WaitGroup
	ch := make(chan int, 3)
	for i := 1; i <= 999; i++ {
		println(i)
		wg.Add(1)
		ch <- i
		go func(pageIndex int) {
			defer func() {
				wg.Done()
				<-ch
			}()
			p := &Product{}
			products, err := p.GetChannelProductEsBaseData(context.Background(), &pc.ChannelProductEsBaseDataRequest{PageIndex: int32(pageIndex), PageSize: 5000})
			if err != nil {
				glog.Error(err)
			}

			println(len(products.Details))
			client := es.NewEsClient()
			// client := es.NewEsClient("http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200", "elastic", "PRO2GUm1ZIw4AFNitdz")
			bulkRequest := client.Bulk()
			for _, p := range products.Details {
				if p.UpDownState == 1 {
					m := es.FormatChannelProductRequestEs(p)
					// if strings.Contains(m.Product.Name, "猫") {
					// 	m.Tags = "猫"
					// } else if strings.Contains(m.Product.Name, "犬") {
					// 	m.Tags = "犬"
					// }
					bulkRequest.Add(elastic.NewBulkIndexRequest().Index(indexName).Id(fmt.Sprintf("%s-1-%d", m.FinanceCode, p.SkuId)).Doc(m))
				} else {
					bulkRequest.Add(elastic.NewBulkDeleteRequest().Index(indexName).Id(fmt.Sprintf("%s-1-%d", p.FinanceCode, p.SkuId)))
				}
			}
			bulkRequest.Do(context.Background())
		}(i)
	}
	wg.Wait()
	return nil
}

//导入电商商品到es
func importUpetGoods(indexName string) error {
	// client := es.NewEsClient("http://es-cn-m7r1uawz10006espz.public.elasticsearch.aliyuncs.com:9200", "elastic", "dk3aOf6U")
	// client := es.NewEsClient()
	client := es.NewEsClient("http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200", "elastic", "PRO2GUm1ZIw4AFNitdz")

	bulkRequest := client.Bulk()

	goods := loadUpetGoodsData()
	// println(len(goods))
	// return nil
	for _, p := range goods {
		m := es.FormatUpetGoodsToChannelProductRequestEs(p)
		// if strings.Contains(m.Product.Name, "猫") {
		// 	m.Tags = "猫"
		// } else if strings.Contains(m.Product.Name, "犬") {
		// 	m.Tags = "犬"
		// }
		bulkRequest = bulkRequest.Add(elastic.NewBulkIndexRequest().Index(indexName).Id(fmt.Sprintf("%s-5-%d", m.FinanceCode, p.GoodsId)).Doc(m))
	}
	_, err := bulkRequest.Do(context.Background())
	return err
}
