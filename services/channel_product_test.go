package services

import (
	"_/proto/pc"
	"context"
	"reflect"
	"testing"
)

//	func TestProduct_GetProductIdBySkuId(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.GetProductIdBySkuIdRequest
//		}
//		tests := []struct {
//			name    string
//			c       *ChannelProduct
//			args    args
//			want    *pc.GetProductIdBySkuIdResponse
//			wantErr bool
//		}{
//			{
//				name: "T1",
//				c:    &ChannelProduct{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.GetProductIdBySkuIdRequest{
//						SkuId: []int32{1000002001, 1000002001},
//					},
//				},
//				wantErr: false,
//			},
//			{
//				name: "T2",
//				c:    &ChannelProduct{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.GetProductIdBySkuIdRequest{
//						SkuId: []int32{1000028001},
//					},
//				},
//				wantErr: false,
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.GetProductIdBySkuId(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("Product.CopyProductToChannelProduct() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				t.Log(got.Data)
//			})
//		}
//	}
//
//	func TestA(t *testing.T) {
//		redisClient := GetRedisConn()
//		if kit.EnvCanCron() {
//			defer redisClient.Close()
//		}
//		//redis上锁，全部认领同时只能有一个任务在执行
//		result := redisClient.SetNX(copyAllLockKey, 1, time.Hour*6)
//
//		bt, _ := json.Marshal(result)
//
//		glog.Info(string(bt))
//
//		t.Error()
//
// }
//
//	func TestProduct_NewChannelCategory(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.Category
//		}
//		tests := []struct {
//			name string
//			p    *Product
//			args args
//			want *pc.BaseResponse
//		}{
//			//{
//			//	name: "新增一级分类",
//			//	p:    &Product{},
//			//	want: &pc.BaseResponse{
//			//		Code: 200,
//			//	},
//			//	args: args{
//			//		ctx: nil,
//			//		in: &pc.Category{
//			//			Id:        0,
//			//			Name:      "测试一级分类",
//			//			ParentId:  0,
//			//			ChannelId: 0,
//			//			Sort:      0,
//			//		},
//			//	},
//			//},
//			//{
//			//	name: "新增二级分类",
//			//	p:    &Product{},
//			//	want: &pc.BaseResponse{
//			//		Code: 200,
//			//	},
//			//	args: args{
//			//		ctx: nil,
//			//		in: &pc.Category{
//			//			Id:       0,
//			//			Name:     "测试二级分类",
//			//			ParentId: 1371,
//			//		},
//			//	},
//			//},
//			{
//				name: "修改二级分类",
//				p:    &Product{},
//				want: &pc.BaseResponse{
//					Code: 200,
//				},
//				args: args{
//					ctx: nil,
//					in: &pc.Category{
//						Id:        1373,
//						Name:      "dy二级分类3",
//						ParentId:  0,
//						ChannelId: 2,
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.p.NewChannelCategory(tt.args.ctx, tt.args.in)
//				if err != nil {
//					glog.Error(err)
//					return
//				}
//				if got.Code != tt.want.Code {
//					t.Errorf(got.Message)
//					return
//				}
//
//			})
//		}
//	}
//
// func testa(session *xorm.Session) {
//
//		var mode = models.Erp{}
//		session.Id(4).Get(mode)
//		session.Id(8)
//		time.Sleep(time.Second * 5)
//		session.Cols("name").Update(&mode)
//	}
//
//	func TestProduct_SyncChannelCategory(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.SyncChannelCategoryRequest
//		}
//		tests := []struct {
//			name string
//			p    *Product
//			args args
//			want *pc.BaseResponse
//		}{
//			//{
//			//	name: "同步渠道分类t1",
//			//	p:    &Product{},
//			//	args: args{
//			//		ctx: nil,
//			//		in:  &pc.SyncChannelCategoryRequest{ChannelStoreId: "4889_2701013"},
//			//	},
//			//},
//			{
//				name: "同步渠道分类t2",
//				p:    &Product{},
//				args: args{
//					ctx: nil,
//					in:  &pc.SyncChannelCategoryRequest{ChannelStoreId: ""},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//
//				session := NewDbConn().NewSession()
//				defer session.Close()
//
//				go func(session *xorm.Session) {
//					var mode = models.Erp{}
//					session.Id(3)
//					session.Get(&mode) //第一个查询条件有
//
//					session.Id(8)                      //再赋值条件
//					time.Sleep(time.Second * 5)        //等5秒后等外面的session执行get后，这个条件就没了
//					session.Cols("name").Update(&mode) //打印出来的sql没有条件了
//				}(session)
//
//				time.Sleep(time.Second * 2)
//				session.Id(3)
//				var mode1 = models.Erp{}
//				//执行get会把session携程里面的seeion的条件置空
//				session.Get(&mode1)
//
//				time.Sleep(time.Second * 6)
//
//				got, err := tt.p.SyncChannelCategory(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Error(err)
//					return
//				}
//				if got.Code != 200 {
//					t.Errorf("Product.DelProduct() = %v", got)
//				}
//				t.Log(got)
//			})
//		}
//	}
//
// //func TestProduct_GetSyncCateToChannelData(t *testing.T) {
// //	type args struct {
// //		ctx context.Context
// //		in  *pc.GetSyncCateToChannelDataRequest
// //	}
// //	tests := []struct {
// //		name string
// //		p    *Product
// //		args args
// //		want *pc.BaseStringResponse
// //	}{
// //		{
// //			name: "获取同步到渠道的分类数据t1",
// //			p:    &Product{},
// //			args: args{
// //				ctx: nil,
// //				in:  &pc.GetSyncCateToChannelDataRequest{CategoryId: 1373},
// //			},
// //		},
// //	}
// //	for _, tt := range tests {
// //		t.Run(tt.name, func(t *testing.T) {
// //			got, err := tt.p.GetSyncCateToChannelData(tt.args.ctx, tt.args.in)
// //			if err != nil {
// //				t.Error(err)
// //				return
// //			}
// //			if got.Code != 200 {
// //				t.Errorf(got.Message)
// //			}
// //			t.Log(got)
// //		})
// //	}
// //}
// //
// //func TestProduct_RollbackChannelCategory(t *testing.T) {
// //	type args struct {
// //		ctx context.Context
// //		in  *pc.GetSyncCateToChannelDataRequest
// //	}
// //	tests := []struct {
// //		name string
// //		p    *Product
// //		args args
// //		want *pc.BaseStringResponse
// //	}{
// //		{
// //			name: "回滚分类数据t1",
// //			p:    &Product{},
// //			args: args{
// //				ctx: nil,
// //				in:  &pc.GetSyncCateToChannelDataRequest{CategoryId: 1373},
// //			},
// //		},
// //	}
// //	for _, tt := range tests {
// //		t.Run(tt.name, func(t *testing.T) {
// //			got, err := tt.p.RollbackChannelCategory(tt.args.ctx, tt.args.in)
// //			if err != nil {
// //				t.Error(err)
// //				return
// //			}
// //			if got.Code != 200 {
// //				t.Errorf(got.Message)
// //			}
// //		})
// //	}
// //}
//
//	func TestProduct_QueryChannelProductDetail(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.OneofIdRequest
//		}
//		tests := []struct {
//			name string
//			c    *Product
//			args args
//		}{
//			{
//				name: "查询渠道商品详情信息",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.OneofIdRequest{
//						Id:        &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{1000000, 1000001}}},
//						ChannelId: 2,
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryChannelProductDetail(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Fatal(err)
//				}
//				if got.Code != 200 {
//					t.Fatalf(got.Message)
//				}
//				if len(got.Details) != len(tt.args.in.GetProductId().Value) {
//					t.Fatalf("失败")
//				}
//
//				t.Logf("%#v\n", got.Details)
//			})
//		}
//	}
//
//	func TestProduct_QueryChannelSku(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.OneofIdRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.SkuValueResponse
//			wantErr bool
//		}{
//			{
//				name: "根据商品ID查询SKU规格详情(系统默认)（渠道）",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.OneofIdRequest{
//						Id:        &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{1031494, 1031480}}},
//						ChannelId: 2,
//					},
//				},
//				wantErr: false,
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryChannelSku(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("Product.QueryChannelSkuValue() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if len(got.Details) == 0 {
//					t.Errorf("Product.QueryChannelSkuValue() = %v, want %v", got, tt.want)
//				}
//				for _, v := range got.Details {
//					t.Logf("%#v", v)
//				}
//			})
//		}
//	}
//
//	func TestProduct_UpdateSnapShot(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.UpdateSnapShotRequest
//		}
//		tests := []struct {
//			name    string
//			p       *Product
//			args    args
//			want    *pc.BaseResponse
//			wantErr bool
//		}{
//			{
//				name: "更新快照t1",
//				p:    &Product{},
//				want: &pc.BaseResponse{
//					Code: 200,
//				},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.UpdateSnapShotRequest{
//						ChannelId:   2,
//						FinanceCode: []string{"CX0004"},
//						ProductId:   []int32{1000156},
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.p.UpdateSnapShot(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Error(err)
//					return
//				}
//				if got.Code != 200 {
//					t.Errorf(got.Message)
//					return
//				}
//				for _, v := range got.Details {
//					t.Logf("%#v", v)
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryChannelSkuValue(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.OneofIdRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.SkuValueResponse
//			wantErr bool
//		}{
//			{
//				name: "根据商品ID查询SKU规格详情(系统默认)（渠道）",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.OneofIdRequest{
//						Id:        &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{100010}}},
//						ChannelId: 1,
//					},
//				},
//				wantErr: false,
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryChannelSkuValue(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("Product.QueryChannelSkuValue() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if len(got.Details) == 0 {
//					t.Errorf("Product.QueryChannelSkuValue() = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryChannelSkuThird(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.OneofIdRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.SkuThirdResponse
//			wantErr bool
//		}{
//			{
//				name: "根据商品ID查询第三方货号信息（渠道）",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.OneofIdRequest{
//						Id:        &pc.OneofIdRequest_SkuId{SkuId: &pc.ArrayIntValue{Value: []int32{1052762001}}},
//						ChannelId: 1,
//					},
//				},
//				wantErr: false,
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryChannelSkuThird(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("Product.QueryChannelSkuThird() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if len(got.Details) == 0 {
//					t.Errorf("Product.QueryChannelSkuThird() = %v, want %v", got, tt.want)
//				}
//				for _, v := range got.Details {
//					t.Logf("%#v", v)
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryChannelStoreProduct(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelStoreProductRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.ChannelStoreProductResponse
//			wantErr bool
//		}{
//			{
//				name: "查询渠道门店商品（渠道）",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.ChannelStoreProductRequest{
//						ChannelId:   9,
//						FinanceCode: []string{"CX0004"},
//						SkuId:       []int32{1033114001, 1033082001, 1030178001, 1030183001, 1030300001, 1030302001},
//						UpDownState: -1,
//					},
//				},
//			},
//		}
//		kit.IsDebug = true
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryChannelStoreProduct(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Errorf("Product.QueryChannelStoreProduct() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if len(got.Details) == 0 {
//					t.Errorf("Product.QueryChannelStoreProduct() = %v, want %v", got, tt.want)
//					return
//				}
//				t.Logf("%#v", got.Details[0])
//			})
//		}
//	}
//
//	func TestProduct_GetChannelProductSnapshotRequest(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.GetChannelProductSnapshotRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.ChannelProductDetailResponse
//			wantErr bool
//		}{
//			{
//				name: "获取分类商品在指定门店上架快照信息",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.GetChannelProductSnapshotRequest{
//						ChannelId:   1,
//						FinanceCode: "CX0004",
//						UpDownState: 1,
//						//CategoryId:  0,
//						PageSize:  5,
//						PageIndex: 1,
//						ProductId: []int32{},
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.GetChannelProductSnapshot(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Errorf("Product.QueryChannelStoreProduct() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				//1019414
//				if len(got.Details) == 0 {
//					t.Errorf("Product.QueryChannelStoreProduct() = %v, want %v", got, tt.want)
//				}
//				for _, v := range got.Details {
//					t.Logf("%#v", v.Product)
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryChannelStoreProductGroupByFinanceCodeCount(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelProductGroupByFinanceCodeCountRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.ChannelStoreProductResponse
//			wantErr bool
//		}{
//			{
//				name: "根据商品id分组查询权限范围内的渠道门店商品上架门店的数量（渠道）",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.ChannelProductGroupByFinanceCodeCountRequest{
//						ChannelId: 1,
//						//FinanceCode: []string{"AA0051", "123"},
//						//ProductId:   []int32{1000095, 1},
//					},
//				},
//				wantErr: false,
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryChannelStoreProductGroupByFinanceCodeCount(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("Product.QueryChannelStoreProductGroupByFinanceCodeCount() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("Product.QueryChannelStoreProductGroupByFinanceCodeCount() = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryChannelCategory(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.CategoryRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.ChannelStoreProductResponse
//			wantErr bool
//		}{
//			{
//				name: "获取渠道商品分类列表",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.CategoryRequest{
//						Where: &pc.Category{
//							ChannelId: 4,
//							//ParentId:  0,
//							Id: 188,
//						},
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryChannelCategory(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Error(err)
//					return
//				}
//				if got.Code != 200 {
//					t.Errorf(got.Message)
//				}
//				for _, v := range got.Details {
//					t.Logf("%#v", v)
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryStoreRecommendProduct(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelStoreProductRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.ChannelStoreProductResponse
//			wantErr bool
//		}{
//			{
//				name: "查询门店推荐商品",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.ChannelStoreProductRequest{
//						ChannelId:   1,
//						FinanceCode: []string{"CX0010"},
//						PageSize:    5,
//						PageIndex:   1,
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryStoreRecommendProduct(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Error(err)
//					return
//				}
//
//				for _, v := range got.Data {
//					t.Log(v.FinanceCode)
//					t.Log(v.Details)
//					break
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryStoreLatestProduct(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelStoreProductRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.ChannelStoreProductResponse
//			wantErr bool
//		}{
//			{
//				name: "查询门店推荐商品",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.ChannelStoreProductRequest{
//						ChannelId:   1,
//						FinanceCode: []string{"CX0004"},
//						PageSize:    5,
//						PageIndex:   1,
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryStoreLatestProduct(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Error(err)
//					return
//				}
//
//				for _, v := range got.Data {
//					t.Log(v.FinanceCode)
//					t.Log(v.Details)
//					break
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryChannelProduct(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.QueryProductRequest
//		}
//		tests := []struct {
//			name    string
//			p       *Product
//			args    args
//			want    *pc.ProductResponse
//			wantErr bool
//		}{
//			{
//				name: "t1",
//				p:    &Product{},
//				args: args{
//					in: &pc.QueryProductRequest{
//						PageIndex:   1,
//						PageSize:    10,
//						CategoryId:  -1,
//						UpDownState: 1,
//						FinanceCode: "",
//						ChannelId:   1,
//					},
//				},
//				wantErr: false,
//				want: &pc.ProductResponse{
//					Code: 200,
//				},
//			},
//			// {
//			// 	name: "t2",
//			// 	p:    &Product{},
//			// 	args: args{
//			// 		in: &pc.QueryProductRequest{
//			// 			PageIndex:  1,
//			// 			PageSize:   1,
//			// 			CategoryId: 1076,
//			// 		},
//			// 	},
//			// 	wantErr: false,
//			// 	want: &pc.ProductResponse{
//			// 		Code:       200,
//			// 		TotalCount: 0,
//			// 	},
//			// },
//		}
//		for _, tt := range tests {
//			tt := tt
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.p.QueryChannelProduct(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Error(err)
//					return
//				}
//				if got.Code != 200 {
//					t.Errorf(got.Message)
//					return
//				}
//				t.Log("total: ", got.TotalCount)
//				for _, v := range got.Details {
//					t.Logf("%#v", v)
//				}
//			})
//		}
//	}
//
//	func TestProduct_NewChannelStoreProduct(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.NewChannelStoreProductRequest
//		}
//		tests := []struct {
//			name    string
//			p       *Product
//			args    args
//			want    *pc.ProductResponse
//			wantErr bool
//		}{
//			{
//				name: "t1",
//				p:    &Product{},
//				args: args{
//					in: &pc.NewChannelStoreProductRequest{
//						Info: []*pc.ChannelStoreProduct{
//							{
//								ChannelId:   1,
//								FinanceCode: "RP0009",
//								ProductId:   1000001,
//								UpDownState: 1,
//							},
//							{
//								ChannelId:   1,
//								FinanceCode: "CX0013",
//								ProductId:   1000013,
//								UpDownState: 1,
//							},
//							{
//								ChannelId:   1,
//								FinanceCode: "CX0013",
//								ProductId:   1000012,
//								UpDownState: 1,
//							},
//						},
//					},
//				},
//			},
//			// {
//			// 	name: "t2",
//			// 	p:    &Product{},
//			// 	args: args{
//			// 		in: &pc.QueryProductRequest{
//			// 			PageIndex:  1,
//			// 			PageSize:   1,
//			// 			CategoryId: 1076,
//			// 		},
//			// 	},
//			// 	wantErr: false,
//			// 	want: &pc.ProductResponse{
//			// 		Code:       200,
//			// 		TotalCount: 0,
//			// 	},
//			// },
//		}
//		for _, tt := range tests {
//			tt := tt
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.p.NewChannelStoreProduct(tt.args.ctx, tt.args.in)
//				if err != nil {
//					t.Error(err)
//					return
//				}
//				if got.Code != 200 {
//					t.Errorf(got.Message)
//					return
//				}
//				t.Logf("%#v", got.Details)
//			})
//		}
//	}
//
// func TestProduct_NewChannelProductSnapshot(t *testing.T) {
//
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelProductSnapshot
//		}
//		tests := []struct {
//			name    string
//			p       *Product
//			args    args
//			want    *pc.BaseResponse
//			wantErr bool
//		}{
//
//			{
//				name: "创建快照",
//				p:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.ChannelProductSnapshot{
//						ProductId:   10000499,
//						ChannelId:   1,
//						JsonData:    "sddd",
//						FinanceCode: "cx0004",
//					},
//				},
//				want:    &pc.BaseResponse{},
//				wantErr: false,
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				tt.args.ctx = context.WithValue(tt.args.ctx, "user_info", &models.LoginUserInfo{})
//				got, err := tt.p.NewChannelProductSnapshot(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("NewChannelProductSnapshot() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("NewChannelProductSnapshot() got = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryStoreRecommendProductOnstatus(t *testing.T) {
//		type fields struct {
//			categoryNames []string
//		}
//		type args struct {
//			ctx context.Context
//			in  *pc.QueryStoreRecommendProductOnstatusRequest
//		}
//
//		var params = pc.QueryStoreRecommendProductOnstatusRequest{
//			ChannelId: 1,
//			ProductId: "1000001",
//		}
//		tests := []struct {
//			name    string
//			fields  fields
//			args    args
//			want    *pc.QueryStoreRecommendProductOnstatusResponse
//			wantErr bool
//		}{
//
//			{
//				name: "创建快照",
//				args: args{
//					ctx: context.Background(),
//					in:  &params,
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &Product{
//					categoryNames: tt.fields.categoryNames,
//				}
//				got, err := c.QueryStoreRecommendProductOnstatus(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("QueryStoreRecommendProductOnstatus() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("QueryStoreRecommendProductOnstatus() got = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestProduct_DealChannelProductUpsale(t *testing.T) {
//		type fields struct {
//			categoryNames []string
//		}
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelProductUnsaleRequest
//		}
//
//		ctx := context.WithValue(context.Background(), "userinfo", "{\"FinancialCode\":\"1111\"}")
//		var params = pc.ChannelProductUnsaleRequest{
//			ChannelId:       1,
//			ProductIds:      []string{"1000074"},
//			FinanceCodeList: "RP0231",
//			Category:        4,
//		}
//
//		tests := []struct {
//			name    string
//			fields  fields
//			args    args
//			want    *pc.BaseResponse
//			wantErr bool
//		}{
//
//			{
//				name: "创建快照",
//				args: args{
//					ctx: ctx,
//					in:  &params,
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &Product{
//					categoryNames: tt.fields.categoryNames,
//				}
//				got, err := c.DealChannelProductUpsale(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("DealChannelProductUpsale() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("DealChannelProductUpsale() got = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryChannelProductUp(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.QueryProductRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.ChannelProductResponse
//			wantErr bool
//		}{
//			{
//				name: "渠道已经上架商品",
//				c:    &Product{},
//				args: args{ctx: context.Background(), in: &pc.QueryProductRequest{
//					ChannelId:   1,
//					FinanceCode: "AA0234,AN0016,AA0090,RP0227,CX0234,CX0030,AA0051,AW0001,RP0209,RP0178,AN0033,CX0115,RP0152,CX0028,RP0127,RP0123,RP0244,RP0126,RP0161,YC0063,RP0027,RP0001,RP0180,CX0061,CX0029,CX0027,AA0073,AW0004,AW0005,AW0003,AW0002,RP0312,RP0038,RP0143,RP0120,RP0083,RP0235,CX0149,CX0059,CX0060,RP0117,RP0104,RP0130,RP0299,CX0172,CX0254,CX0104,CX0117,CX0092,CX0189,CX0159,CX0184,CX0116,ML0074,ML0072,CX0018,CX0003,CX0055,CX0001,CX0002,CX0037,YC0096,CX0290,CX0256,CX0258,CX0257,CX0263,CX0264,CX0271,CX0304,CX0019,CX0034,CX0044,CX0043,YC0057,CX0052,CX0054,CX0074,CX0056,YC0077,CX0165,AA0176,ML0032,AA0128,ML0036,AA0136,AA0113,CX0118,CX0148,AN0002,AA0018,YC0080,CX0103,AA0058,CX0108,CX0147,YC0010,CX0150,CX0152,CX0109,AN0008,AN0009,AN0010,AN0011,AN0012,AN0013,CX0119,AN0014,CX0163,CX0161,CX0123,CX0260,CX0110,NJ0006,NJ0005,CX0126,YC0119,YC0090,ML0030,ML0037,ML0040,ML0035,ML0042,ML0053,ML0023,ML0011,ML0009,ML0027,ML0010,ML0056,ML0046,ML0052,ML0043,ML0012,ML0075,ML0077,ML0078,ML0044,ML0014,ML0013,ML0017,ML0016,ML0041,ML0045,ML0055,ML0059,ML0060,ML0071,ML0061,ML0067,ML0063,ML0068,ML0065,ML0064,ML0066,ML0062,ML0018,ML0039,ML0069,ML0070,ML0021,ML0020,YC0054,ML0048,MLM051,ML0058,CX0004,AN0005,AN0006,AN0004,AN0028,YC0111,AN0003,AN0001,YC0009,YC0012,CX0020,AN0017,AN0018,AN0019,YC0089,CX0191,NJ0001,NJ0002,NJ0004,NJ0003,CX0121,YC0118,YC0087,CX0200,CX0261,CX0246,CX0245,AN0022,AN0031,AN0023,YC0092,YC0120,YC0122,YC0125,CX0295,CX0173,YC0123,NJ0011,AN0025,CX0306,CX0309,YC0021,AN0030,YC0147,YC0128,YC0144,ML0019,YC0016,CX0021,AN0029,CX0017,YC0051,CX0160,YC0072,YC0079,CX0035,CX0070,CX0084,CX0093,CX0073,RP0048,RP0046,RP0045,RP0066,RP0050,RP0042,RP0057,RP0061,RP0034,RP0064,RP0071,RP0076,RP0074,RP0020,RP0072,RP0068,RP0075,RP0079,RP0080,RP0085,RP0084,RP0026,RP0091,RP0015,RP0095,RP0023,RP0103,RP0081,RP0073,RP0260,RP0259,RP0078,RP0264,RP0047,RP0115,RP0116,RP0133,RP0261,RP0263,RP0124,RP0262,RP0141,RP0144,RP0177,RP0137,RP0086,RP0148,RP0184,RP0265,RP0205,RP0266,RP0194,RP0237,RP0236,RP0197,RP0208,RP0210,RP0238,RP0223,RP0268,RP0271,RP0225,RP0269,RP0270,RP0226,AA0245,RP0274,RP0282,RP0284,AA0022,AA0004,AA0010,AA0003,CX0266,AA0008,CX0139,AA0217,AA0014,AA0206,AA0225,AA0229,AA0021,YC0112,AA0067,AA0012,AA0066,AA0017,AA0032,AA0192,AA0179,AA0181,AA0155,AA0218,AA0219,AA0220,CX0244,AA0036,AA0053,AA0055,AA0087,AA0056,AA0068,AA0033,CX0291,YC0114,CX0223,CX0240,CX0224,AA0202,AA0205,AA0190,AA0144,AA0222,AA0153,AA0195,AA0216,AA0244,AA0242,AA0241,AA0237,AA0238,AA0240,AA0211,AA0254,CX0292,AA0121,CX0294,AA0250,CX0280,CX0281,CX0285,YC0101,CX0249,AA0231,AA0248,CX0302,CX0303,CX0311,CX0312,CX0313,CX0314,YC0141,YC0142,YC0148,CX0318,AB0034,AA0116,AB0033,CX0175,AB0074,AB0078,CX0176,CX0177,CX0179,AB0003,CX0180,CX0181,AB0002,CX0182,CX0183,AB0013,AB0007,AB0011,AB0016,CX0133,AB0019,AB0027,CX0130,AB0029,YC0047,AB0031,AB0099,AB0030,AB0037,AB0050,AB0057,AB0056,AB0060,AB0066,AB0071,AB0046,AB0021,AB0022,RP0055,RP0017,AB0035,RP0019,RP0032,RP0041,RP0054,RP0065,RP0018,RP0110,RP0119,RP0139,RP0135,RP0147,RP0138,RP0136,RP0113,RP0118,RP0125,RP0140,RP0146,RP0154,RP0150,RP0153,RP0175,RP0172,RP0168,RP0174,RP0173,RP0187,RP0179,RP0189,RP0188,RP0191,RP0215,RP0246,RP0247,RP0296,RP0306,CX0129,AA0057,YC0086,AA0210,CX0209,CX0196,CX0197,CX0205,CX0207,CX0206,CX0227,AA0209,AA0223,AA0236,YC0135,CX0274,YC0124,CX0267,YC0156,YC0151,YC0152,AB0072,YC0084,AA0103,AA0104,RP0022,RP0029,RP0067,RP0069,RP0070,RP0089,RP0077,RP0087,RP0097,RP0101,RP0096,RP0082,RP0109,RP0114,RP0112,RP0128,RP0121,RP0165,RP0158,RP0159,RP0164,RP0157,RP0169,RP0171,RP0186,RP0182,RP0243,RP0245,RP0248,RP0249,RP0239,RP0240,AB0008,AB0006,AB0020,AB0028,AB3002,AB0014,AB0047,AB0075,AA0109,CX0077,CX0086,CX0136,CX0137,CX0138,CX0134,CX0101,CX0111,CX0187,CX0006,CX0008,RP0098,RP0099,RP0100,RP0107,RP0106,RP0131,RP0156,RP0142,RP0149,RP0183,RP0181,RP0204,RP0203,YC0068,RP0190,RP0193,RP0200,RP0202,RP0214,RP0211,CX0210,RP0241,AB0083,RP0207,CX0174,RP0256,RP0272,RP0257,RP0279,AB0064,RP0281,RP0278,RP0285,RP0294,RP0298,YC0005,CX0232,CX0005,AA0024,AB0065,CX0262,CX0237,CX0238,CX0239,YC0097,AB0055,AA0114,AA0226,AB0036,AB0038,CX0305,CX0276,AB0068,YC0127,RP0252,CX0300,RP0250,RP0251,NWT005,CX0157,NWT003,NWT004,CX0156,CX0154,CX0155,RP0280,CX0025,AA0061,CX0041,CX0051,CX0047,AA0101,RP0300,AA0100,RP0283,RP0287,AA0082,AA0193,AB0052,AB0082,AB0069,AB0067,AB0077,AB0079,AB0039,AB0054,AB0063,AB0080,AB0076,AB0084,AA0070,AA0040,YC0060,AA0064,AA0083,AA0095,AA0075,AA0098,AA0191,CX0164,AA0108,AA0111,AA0112,CX0046,CX0298,AA0092,AA0124,AA0139,AA0138,AA0099,AA0233,AA0127,AA0097,AA0142,AA0081,AA0149,AA0052,AA0086,AA0050,AA0041,CX0144,CX0143,RP0132,CX0221,CX0145,AA0146,CX0141,CX0142,CX0140,YC0083,AB0044,AB0061,AB0048,YC0099,RP0213,AB0041,AB0040,AB0042,AA0140,AA0079,AA0132,AA0063,AA0182,AA0077,AA0028,AA0115,ML0076,CX0222,AA0134,AA0029,AA0102,AA0227,CX0087,CX0282,AA0091,AA0230,AB0015,YC0039,RP0185,CX0265,AB0070,NWT001,AB0053,AA0007,AB0073,CX0231,NJ0008,CX0269,AA0062,AN0026,CX0098,CX0229,CX0007,CX0212,CX0096,AB0051,CX0078,AB0045,ML0050,NJ0009,CX0049,YC0065,AB0001,AB0058,AA0133,AA0020,YC0013,RP0108,ML0033,AA0027,RP0111,RP0196,CX0283,CX0024,YC0014,YC0037,YC0041,YC0045,AA0026,AA0035,AA0015,AA0030,AA0016,AA0060,CX0011,KD0001,CX0014,CX0015,CX0016,CX0022,CX0023,CX0032,AA0043,AA0042,AA0044,AA0045,AA0046,AA0048,AA0069,AA0085,YC0064,AA0125,CX0071,CX0072,CX0075,CX0076,AA0141,NJ0010,CX0045,CX0094,CX0097,AA0158,CX0102,CX0090,AA0180,CX0099,CX0113,CX0114,CX0124,CX0125,CX0131,CX0132,CX0135,CX0146,AA0173,AA0178,AA0177,AA0183,AA0184,AA0185,AA0196,AA0197,AA0198,YC0098,CX0153,CX0158,CX0166,CX0167,CX0178,CX0185,AA0203,AA0204,AA0212,AA0213,AA0214,CX0186,CX0192,CX0193,CX0194,CX0195,CX0203,AA0221,YC0113,CX0208,AA0239,CX0236,CX0243,CX0211,CX0228,AB0009,AB0012,AB0017,AB0018,AB0023,AB0062,AB0049,AB0059,NWT002,CX0270,NJ0013,CX0279,CX0284,CX0286,AN0027,CX0293,CX0299,AA0251,AB0081,CX0307,CX0308,YC0153,RP0286,CX0322,ML0015,ML0026,ML0051,ML0073,RP0049,RP0088,RP0102,RP0198,RP0201,RP0212,RP0221,RP0222,RP0232,RP0255,RP0258,RP0267,RP0273,RP0276,RP0277,RP0292,RP0304,RP0313,YC0160,AA0009,AA0025,RP0039,AB0032,AA0150,AA0006,RP0151,AA0074,RP0052,RP0033,AA0189,AA0107,RP0216,RP0037,AA0089,RP0014,RP0253,RP0192,RP0044,RP0220,RP0028,RP0031,RP0162,AN0020,AA0049,CX0079,AA0031,CX0241,AB0010,CX0105,CX0080,AA0039,RP0224,RP0035,RP0167,AA0187,AA0071,RP0217,RP0170,RP0230,RP0228,AA0152,RP0231,RP0043,AA0131,AA0105,RP0010,RP0006,RP0195,RP0060,YC0034,RP0234,RP0233,YC0157,RP0160,RP0003,RP0005,RP0145,RP0093,CX0026,RP0090,RP0122,RP0105,CX0259,RP0058,RP0021,AN0034,RP0025,YC0042,ML0022,ML0038,YC0001,ML0031,YC0023,ML0034,YC0050,YC0048,YC0049,ML0057,YC0061,YC0002,YC0093,YC0071,ML0028,ML0047,CX0009,YC0003,ML0029,ML0025,ML0024,CX0040,AA0171,RP0059,AB0005,AA0023,CX0242,AB0024,CX0296,RP0063,AA0076,RP0092,RP0094,YC0043,AB0025,RP0163,RP0040,RP0218,RP0206,RP0129,AA0208,AA0243,YC0126,AA0001,AA0137,AA0088,RP0199,AA0118,RP0219,RP0062,AA0013,RP0166,RP0176,RP0036,RP0254,AA0186,ML0049,RP0009,RP0013,RP0008,YC0019,RP0002,RP0011,RP0229,YC0109,AA0135,RP0053,RP0004,RP0007,RP0012,RP0030,RP0155,CX0013,CX0010",
//					UpDownState: 1,
//					PageIndex:   1,
//					PageSize:    5,
//				}},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.QueryChannelProductUp(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("Product.QueryChannelProductUp() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("Product.QueryChannelProductUp() = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func Test_updateChannelStoreProductFromSnapshot(t *testing.T) {
//		type args struct {
//			m models.ChannelStoreProduct
//		}
//		tests := []struct {
//			name string
//			args args
//			want string
//		}{
//			{
//				name: "解析快照更新渠道门店商品表",
//				args: args{m: models.ChannelStoreProduct{Id: 0, ChannelId: 1, FinanceCode: "RP0227", ProductId: 100012}},
//				want: "",
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				if got := updateChannelStoreProductFromSnapshot(tt.args.m, nil); got != tt.want {
//					t.Errorf("updateChannelStoreProductFromSnapshot() = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func Test_copyGjProductToChannelProduct(t *testing.T) {
//		type args struct {
//			ctx       context.Context
//			productID []int32
//			channelID []int32
//		}
//		tests := []struct {
//			name    string
//			args    args
//			want    [][]string
//			wantErr bool
//		}{
//
//			{},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := copyGjProductToChannelProduct(context.Background(), []int32{1020522, 1020521}, []int32{4}, "claim", true)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("copyGjProductToChannelProduct() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				fmt.Println(got)
//				//if got != tt.want {
//				//	t.Errorf("copyGjProductToChannelProduct() got = %v, want %v", got, tt.want)
//				//}
//			})
//		}
//	}
//
//	func TestProduct_CopyGjProductToChannelProduct(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.CopyGjProductToChannelProductRequest
//		}
//		tests := []struct {
//			name    string
//			args    args
//			want    *pc.BaseResponse
//			wantErr bool
//		}{
//			{
//				name: "从管家商品库认领商品到渠道商品库（批量认领）",
//				args: args{
//					ctx: context.Background(),
//					in: &pc.CopyGjProductToChannelProductRequest{
//						ProductId:    []int32{1032747},
//						ChannelId:    []int32{1, 4},
//						UpdateFields: "a8,zilong",
//					},
//				},
//				wantErr: false,
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &Product{}
//				got, err := c.CopyGjProductToChannelProduct(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("CopyGjProductToChannelProduct() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				t.Log(got)
//			})
//		}
//		time.Sleep(5 * time.Second)
//	}
//
//	func TestGetNameByPID1(t *testing.T) {
//		type args struct {
//			engine *xorm.Engine
//			pid    []int32
//		}
//		engine := NewDbConn()
//		tests := []struct {
//			name string
//			args args
//			want map[int32]string
//		}{
//			{
//				name: "111",
//				args: args{
//					engine: engine,
//					pid:    []int32{1018188, 1018189},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				if got := GetNameByPID(tt.args.engine, tt.args.pid); !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("GetNameByPID() = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestGetSkuIDByPID(t *testing.T) {
//		type args struct {
//			engine *xorm.Engine
//			pid    []int32
//		}
//		engine := NewDbConn()
//		tests := []struct {
//			name string
//			args args
//			want map[int32]int32
//		}{
//			{
//				name: "111",
//				args: args{
//					engine: engine,
//					pid:    []int32{1018188, 1018189},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				if got := GetSkuIDByPID(tt.args.engine, tt.args.pid); !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("GetSkuIDByPID() = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestProduct_GetChannelProductSnapshotBySpuOrSku(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.GetChannelProductSnapshotBySpuOrSkuRequest
//		}
//		tests := []struct {
//			name    string
//			args    args
//			want    *pc.ChannelProductDetailResponse
//			wantErr bool
//		}{
//			{
//				name: "根据spu或者sku获取商品信息",
//				args: args{
//					ctx: context.Background(),
//					in: &pc.GetChannelProductSnapshotBySpuOrSkuRequest{
//						ChannelId:   1,
//						FinanceCode: "RP0158",
//						SkuId:       []int32{1426317409},
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &Product{}
//				got, err := c.GetChannelProductSnapshotBySpuOrSku(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("GetChannelProductSnapshotBySpuOrSku() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				//if !reflect.DeepEqual(got, tt.want) {
//				//	t.Errorf("GetChannelProductSnapshotBySpuOrSku() got = %v, want %v", got, tt.want)
//				//}
//				for _, v := range got.Details {
//					t.Log(v)
//				}
//			})
//		}
//	}
//
//	func TestProduct_GetChannelProductEsBaseData(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelProductEsBaseDataRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.ChannelProductEsBaseDataResponse
//			wantErr bool
//		}{
//			{
//				name: "查询同步到ES的商品信息",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.ChannelProductEsBaseDataRequest{
//						PageIndex:  1,
//						PageSize:   102000,
//						UpdateTime: "2000-12-17 18:55:37",
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				if got, err := tt.c.GetChannelProductEsBaseData(tt.args.ctx, tt.args.in); err != nil {
//					glog.Error(err)
//				} else {
//					// fmt.Println(len(got.Details))
//					fmt.Println(len(got.Details))
//				}
//
//			})
//		}
//	}
//
//	func TestProduct_ChannelProductCategoryElmSync(t *testing.T) {
//		var category CategorySync
//		category.BuildSyncCategoryToThirdParams(20, 0, 3, 11, 1, "测试分类", []string{}, []string{"32267735205-1"})
//		category.SyncCategoryToThird()
//	}
//
//	func TestProduct_ChannelProductCategoryMtSync(t *testing.T) {
//		var category CategorySync
//		category.BuildSyncCategoryToThirdParams(20, 0, 2, 11, 1, "测试分类", []string{}, []string{"GIP3500"})
//		category.SyncCategoryToThird()
//	}
//
//	func TestProduct_ChannelProductTaskCategorySync(t *testing.T) {
//		// 提取参数
//		var params = `{"SyncType":3,"ChannelId":2,"CategoryId":32,"CategoryName":"精细洗浴","ParentId":29,"Sort":14,"FinanceCode":null,"ChannelStoreCode":null,"ThirdCategoryList":null,"Result":null}`
//		var syncCategoryParams CategorySync
//		err := json.Unmarshal([]byte(params), &syncCategoryParams)
//		if err != nil {
//			t.Error(err)
//		} else {
//			// 同步至第三方
//			syncCategoryParams.SyncCategoryToThird()
//			fmt.Println("完成...")
//		}
//	}
//
// // 渠道商品上架
// func TestProduct_ChannelProductUp(t *testing.T) {
//
//		// 美团-构造参数
//		var request = new(pc.UpDownChannelProductRequest)
//		request.ChannelId = 1
//		request.FinanceCode = []string{"CX0010"}
//		request.ProductId = []string{"1000250"}
//		request.UserNo = "admin"
//
//		// 调用接口
//		var product = new(Product)
//		res, err := product.UpChannelProduct(context.Background(), request)
//		if err != nil {
//			t.Error(err)
//		} else {
//			if res.Code != 200 {
//				t.Error(res.Message)
//			}
//		}
//	}
//
//	func TestProduct_GetStock(t *testing.T) {
//		// 校验商品库存信息
//		stock, _ := GetStockInfoBySkuCodeAndShopId(1, 1000216001, "CX0004")
//		if stock == 0 {
//			t.Error("库存为0")
//		}
//	}
//
// // 渠道商品下架
//
//	func TestProduct_ChannelProductDown(t *testing.T) {
//		// 美团-构造参数
//		var request = new(pc.UpDownChannelProductRequest)
//		request.ChannelId = 1
//		request.FinanceCode = []string{"CX0004"}
//		request.ProductId = []string{"1000184"}
//		request.UserNo = "admin"
//		// 调用接口
//		var product = new(Product)
//		res, err := product.DownChannelProduct(context.Background(), request)
//		if err != nil {
//			t.Error(err)
//		} else {
//			if res.Code != 200 {
//				t.Error(res.Message)
//			}
//		}
//	}
//
// //渠道商品上架的批量Test
//
//	func TestProduct_ChannelProductDownBatch(t *testing.T) {
//		// 美团-构造参数
//		var request = new(pc.UpDownChannelProductRequest)
//		request.ChannelId = 2
//		request.FinanceCode = []string{"CX0004"}
//
//		request.UserNo = "admin"
//		// 调用接口
//		var product = new(Product)
//
//		SetupDB()
//		var ints []string
//		engine.SQL("select id from dc_product.channel_product cps where channel_id =2;").Find(&ints)
//
//		start := time.Now()
//		//for _, v := range ints {
//		//	request.ProductId = []string{cast.ToString(v)}
//		//	res, err := product.BatchDownChannelProduct(context.Background(), request)
//		//if err != nil {
//		//	t.Error(err)
//		//} else {
//		//	if res.Code != 200 {
//		//		t.Error(res.Message)
//		//	}
//		//}
//		//fmt.Println("ProductId :", request.ProductId, kit.JsonEncode(res), err)
//
//		//}
//
//		request.ProductId = ints
//		res, err := product.BatchUpChannelProduct(context.Background(), request)
//		fmt.Println("ProductId :", request.ProductId, kit.JsonEncode(res), err)
//
//		t1 := time.Now()
//		elapsed := t1.Sub(start)
//		fmt.Println("elapsed:", elapsed)
//	}
//
// // 同步价格
//
//	func TestProduct_SyncPrie(t *testing.T) {
//		var productSyncPrice ChannelProductPriceSync
//		productSyncPrice.ProductId = "1000217"
//		productSyncPrice.ChannelId = 1
//		// RP0158
//		productSyncPrice.FinanceCode = "CX0010"
//		// err := productSyncPrice.SyncPrice()
//		err := productSyncPrice.checkParams()
//		if err != nil {
//			t.Error(err)
//		}
//		err = productSyncPrice.BuildPrice()
//		if err != nil {
//			t.Error(err)
//		} else {
//			t.Log(productSyncPrice.ProductSyncPrice)
//		}
//	}
//
//	func TestChannelProduct_GroupProductStockNotice(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.StockNoticeRequest
//		}
//		tests := []struct {
//			name    string
//			c       *Product
//			args    args
//			want    *pc.BaseResponse
//			wantErr bool
//		}{
//			{
//				name: "组合商品库存通知",
//				c:    &Product{},
//				args: args{
//					ctx: context.Background(),
//					in: &pc.StockNoticeRequest{
//						StockInfo: []*pc.StockStore{
//							{
//								FinanceCode: "AA001",
//								Skus: []*pc.StoreSku{
//									{
//										SkuId: 1000,
//										Stock: 1,
//									},
//								},
//							},
//						},
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				got, err := tt.c.GroupProductStockNotice(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("ChannelProduct.GroupProductStockNotice() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("ChannelProduct.GroupProductStockNotice() = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestProduct_SyncGoodsQzcPrice(t *testing.T) {
//		type fields struct {
//			categoryNames []string
//		}
//		type args struct {
//			ctx context.Context
//			in  *pc.PreposePriceInfo
//		}
//		tests := []struct {
//			name    string
//			fields  fields
//			args    args
//			want    *pc.BaseResponse
//			wantErr bool
//		}{
//			// TODO: Add test cases.
//			{name: "前置仓价格强制更新"},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &Product{
//					categoryNames: tt.fields.categoryNames,
//				}
//				app := pc.PreposePriceUpInfo{}
//				app.FinanceCode = "RP0151"
//				app.ChannelIds = "1"
//				got, err := c.SyncGoodsQzcPrice(context.Background(), &app)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("Product.SyncGoodsQzcPrice() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("Product.SyncGoodsQzcPrice() = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryChannelSkuGrouop(t *testing.T) {
//		type fields struct {
//			categoryNames []string
//		}
//		type args struct {
//			ctx context.Context
//			in  *pc.OneofIdRequest
//		}
//		tests := []struct {
//			name    string
//			fields  fields
//			args    args
//			want    *pc.SkuGroupResponse
//			wantErr bool
//		}{
//			// TODO: Add test cases.
//			{
//				name: "",
//				args: args{
//					ctx: context.TODO(),
//					in: &pc.OneofIdRequest{
//						ChannelId: 1,
//						Id: &pc.OneofIdRequest_ProductId{
//							ProductId: &pc.ArrayIntValue{Value: []int32{1031452}},
//						},
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &Product{
//					categoryNames: tt.fields.categoryNames,
//				}
//				got, err := c.QueryChannelSkuGrouop(tt.args.ctx, tt.args.in)
//				fmt.Println(got, err)
//			})
//		}
//	}
//
//	func TestProduct_QueryQzcPriceList(t *testing.T) {
//		type fields struct {
//			categoryNames []string
//			oldSnap       string
//		}
//		type args struct {
//			ctx context.Context
//			in  *pc.A8PriceInfoRequest
//		}
//		tests := []struct {
//			name    string
//			fields  fields
//			args    args
//			want    *pc.A8PriceInfoResponse
//			wantErr bool
//		}{
//			// TODO: Add test cases.
//			{
//				name: "test",
//				args: args{
//					ctx: context.Background(),
//					in: &pc.A8PriceInfoRequest{
//						WarehouseId:   10,
//						WarehouseName: "黄兴",
//						ThirdSkuId:    "0",
//						PageIndex:     1,
//						PageSize:      10,
//					}},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &Product{
//					categoryNames: tt.fields.categoryNames,
//					oldSnap:       tt.fields.oldSnap,
//				}
//				got, err := c.QueryQzcPriceList(tt.args.ctx, tt.args.in)
//				fmt.Println(got)
//				fmt.Println(err)
//			})
//		}
//	}
//
//	func TestChannelProduct_List(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelProductListReq
//		}
//		tests := []struct {
//			name    string
//			args    args
//			wantOut *pc.ChannelProductListRes
//			wantErr bool
//		}{
//			{
//				name: "",
//				args: args{in: &pc.ChannelProductListReq{
//					PageIndex: 1,
//					PageSize:  10,
//					Where:     "1033114001",
//					WhereType: "sku_id",
//					//CategoryId:  201540471,
//					BrandId: 0,
//					//ProductType: 1,
//					ChannelId: 1,
//					//UpDownState: 1,
//					FinanceCode: "CX0013",
//				}},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &ChannelProduct{}
//				gotOut, err := c.List(tt.args.ctx, tt.args.in)
//				t.Log(gotOut)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("List() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//			})
//		}
//	}
//
//	func TestChannelProduct_Count(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelProductCountReq
//		}
//		tests := []struct {
//			name    string
//			args    args
//			wantOut *pc.ChannelProductCountRes
//			wantErr bool
//		}{
//			{
//				args: args{in: &pc.ChannelProductCountReq{
//					ChannelId:   1,
//					FinanceCode: "CX0010",
//					CategoryId:  201540420,
//				}},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &ChannelProduct{}
//				gotOut, err := c.Count(tt.args.ctx, tt.args.in)
//				t.Log(gotOut, err)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("Count() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//			})
//		}
//	}
//
//	func TestProduct_QueryQzcOperationRecord(t *testing.T) {
//		type fields struct {
//			categoryNames []string
//			oldSnap       string
//		}
//		type args struct {
//			ctx context.Context
//			in  *pc.QueryA8PriceRecordRequest
//		}
//		tests := []struct {
//			name    string
//			fields  fields
//			args    args
//			wantOut *pc.QueryA8PriceRecordResponse
//			wantErr bool
//		}{
//			{
//				name: "",
//				args: args{in: &pc.QueryA8PriceRecordRequest{
//					PageIndex: 1,
//					PageSize:  10,
//					StartTime: "",
//					EndTime:   "",
//					Promoter:  1,
//					Warehouse: "021SHHXD",
//					Sku:       "",
//				}},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &Product{
//					categoryNames: tt.fields.categoryNames,
//					oldSnap:       tt.fields.oldSnap,
//				}
//				gotOut, err := c.QueryQzcOperationRecord(tt.args.ctx, tt.args.in)
//
//				t.Log(kit.JsonEncode(gotOut), err)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("QueryQzcOperationRecord() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//			})
//		}
//	}
//
//	func TestExportA8Price(t *testing.T) {
//		type args struct {
//			in *pc.A8PriceInfoRequest
//		}
//		tests := []struct {
//			name      string
//			args      args
//			wantUrl   string
//			wantCount int32
//			wantErr   bool
//		}{
//			{
//				args: args{in: &pc.A8PriceInfoRequest{
//					WarehouseName: "",
//					WarehouseId:   0,
//					ThirdSkuId:    "",
//				}},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				gotUrl, gotCount, err := ExportA8Price(tt.args.in)
//				t.Log(gotUrl, gotCount, err)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("ExportA8Price() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//			})
//		}
//	}
//
//	func TestImportA8Price(t *testing.T) {
//		type args struct {
//			task *pc.TaskList
//		}
//		tests := []struct {
//			name        string
//			args        args
//			wantRsUrl   string
//			wantSuccess int32
//			wantFail    int32
//			wantErr     bool
//		}{
//			{
//				args: args{task: &pc.TaskList{
//					OperationFileUrl: "http://file.vetscloud.com/7f2abd11887f66ae7065123e59e7c8ad.xlsx",
//				}},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				gotRsUrl, gotSuccess, gotFail, err := ImportA8Price(tt.args.task)
//				t.Log(gotRsUrl, gotSuccess, gotFail, err)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("ImportA8Price() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//			})
//		}
//	}
//
//	func TestChannelProduct_ExceptionList(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ChannelProductListReq
//		}
//		tests := []struct {
//			name    string
//			args    args
//			wantOut *pc.ChannelProductListRes
//			wantErr bool
//		}{
//			// TODO: Add test cases.
//			{name: "查询异常商品列表"},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &ChannelProduct{}
//				app := pc.ChannelProductListReq{}
//				app.ChannelId = 4
//				app.PageSize = 10
//				app.PageIndex = 1
//				app.WhereType = "third_spu_sku_id"
//				app.Where = ""
//				app.ProductType = 1
//				gotOut, err := c.ExceptionList(context.Background(), &app)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("ExceptionList() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(gotOut, tt.wantOut) {
//					t.Errorf("ExceptionList() gotOut = %v, want %v", gotOut, tt.wantOut)
//				}
//			})
//		}
//	}
//
//	func TestChannelProduct_PriceStore(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ProductPriceStoreReq
//		}
//		tests := []struct {
//			name    string
//			args    args
//			wantOut *pc.ProductPriceStoreRes
//			wantErr bool
//		}{
//			{
//				args: args{in: &pc.ProductPriceStoreReq{
//					ChannelId:   1,
//					FinanceCode: []string{"CX0013", "RP0158", "RP0243", "AA0150", "AA0006", "AB0032"},
//					SkuId:       1020753001,
//					Price:       350,
//					SearchType:  1,
//					//Search:      "安安上海心",
//					PageIndex: 1,
//					PageSize:  10,
//				}},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &ChannelProduct{}
//				gotOut, err := c.PriceStore(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("PriceStore() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(gotOut, tt.wantOut) {
//					t.Errorf("PriceStore() gotOut = %v, want %v", gotOut, tt.wantOut)
//				}
//			})
//		}
//	}
//
//	func TestChannelProduct_StoreProductUpDistinct(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.StoreProductUpDistinctReq
//		}
//		tests := []struct {
//			name    string
//			args    args
//			wantOut *pc.StoreProductUpDistinctRes
//			wantErr bool
//		}{
//			{
//				name: "",
//				args: args{
//					ctx: context.Background(),
//					in: &pc.StoreProductUpDistinctReq{
//						ChannelId: 1,
//						PageIndex: 1,
//						PageSize:  10,
//						//ProductId: []int32{1012198},
//						//SkuId:     []int32{1012198001},
//						FinanceCode: []string{"CX0011"},
//					},
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &ChannelProduct{}
//				gotOut, err := c.StoreProductUpDistinct(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("StoreProductUpDistinct() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(gotOut, tt.wantOut) {
//					t.Errorf("StoreProductUpDistinct() gotOut = %v, want %v", gotOut, tt.wantOut)
//				}
//			})
//		}
//	}
//
//	func TestChannelProduct_CountByPrice(t *testing.T) {
//		type args struct {
//			ctx context.Context
//			in  *pc.ProductCountByPriceReq
//		}
//		tests := []struct {
//			name    string
//			args    args
//			wantOut *pc.ProductCountByPriceRes
//			wantErr bool
//		}{
//			{
//				name: "",
//				args: args{in: &pc.ProductCountByPriceReq{
//					ChannelId:   1,
//					FinanceCode: []string{"CX0013", "RP0158", "RP0243", "AA0150", "AA0150", "AA0006", "AB0032"},
//					SkuId:       []int32{1012198001, 1023486001, 1020753001},
//				}},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				c := &ChannelProduct{}
//				gotOut, err := c.CountByPrice(tt.args.ctx, tt.args.in)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("CountByPrice() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(gotOut, tt.wantOut) {
//					t.Errorf("CountByPrice() gotOut = %v, want %v", gotOut, tt.wantOut)
//				}
//			})
//		}
//	}
func TestProduct_GetEshopProductSnapshotBySpuOrSku(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}

	type args struct {
		ctx context.Context
		in  *pc.GetChannelProductSnapshotBySpuOrSkuRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ChannelProductDetailResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "a",
			fields: fields{oldSnap: "", categoryNames: make([]string, 0)},
			args: args{
				ctx: context.Background(),
				in: &pc.GetChannelProductSnapshotBySpuOrSkuRequest{
					ChannelId:   2,
					ProductId:   []int32{89},
					FinanceCode: "", //
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.GetEshopProductSnapshotBySpuOrSku(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEshopProductSnapshotBySpuOrSku() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("GetEshopProductSnapshotBySpuOrSku() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}
