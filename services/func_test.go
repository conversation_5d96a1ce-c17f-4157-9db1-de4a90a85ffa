package services

import (
	"_/proto/ic"
	"reflect"
	"testing"
)

func TestGetStockInfoBySkuCode(t *testing.T) {
	type args struct {
		skuCodeInfo []*ic.SkuCodeInfo
		args        []interface{}
	}

	tests := []struct {
		name    string
		args    args
		want    map[string]int32
		wantErr bool
	}{
		{
			name: "GetStockInfoBySkuCode",
			args: args{
				skuCodeInfo: []*ic.SkuCodeInfo{
					{
						FinanceCode: "CX0004",
						Sku:         "1000216001",
						ThirdSkuid:  "4717439",
						ZlId:        8597,
						ErpId:       2,
					},
					//{
					//	FinanceCode: "CX0011",
					//	Sku:         "1044911",
					//	ThirdSkuid:  "C1029XX170",
					//	ZlId:        8597,
					//	ErpId:       4,
					//},
					//{
					//	FinanceCode: "CX0011",
					//	Sku:         "1000113001",
					//	ThirdSkuid:  "C1029XX170",
					//	ZlId:        8597,
					//	ErpId:       4,
					//},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetStockInfoBySkuCode(0, tt.args.skuCodeInfo, 0, tt.args.args...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStockInfoBySkuCode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetStockInfoBySkuCode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetAppChannelByFinanceCode(t *testing.T) {
	type args struct {
		financeCode string
	}
	tests := []struct {
		name    string
		args    args
		want    int32
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "获取美团门店",
			args: args{
				financeCode: "CX0000064",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetAppChannelByFinanceCode(tt.args.financeCode)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAppChannelByFinanceCode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetAppChannelByFinanceCode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestExportProductErr(t *testing.T) {
	type args struct {
		errList [][]string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				errList: [][]string{[]string{"1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "2"},
					[]string{"1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "1", "2", "2"},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ExportProductErr(tt.args.errList)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExportProductErr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ExportProductErr() got = %v, want %v", got, tt.want)
			}
		})
	}
}
