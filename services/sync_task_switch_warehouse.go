package services

import (
	"_/enum"
	"_/models"
	"_/proto/dc"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/maybgit/glog"
	"github.com/ppkg/distributed-worker/dto"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 门店切仓任务
func DealAsyncTaskSwitchWarehouse(taskContent int32) func() bool {
	return func() bool {
		//查询新任务的ID
		params := &pc.GetTaskListRequest{
			Sort:        "createTimeAsc",
			TaskStatus:  1,
			ChannelId:   -1,          //-1 查询全部的
			TaskContent: taskContent, // 查询task_content in (67)
			Status:      1,
			Page:        1,
			PageSize:    1,
		}

		//获取需要执行的任务，只取一条数据
		pd := new(Product)
		result, err := pd.GetTaskList(context.Background(), params)
		if err != nil {
			glog.Errorf("DealAsyncTaskSwitchWarehouse 定时查询新任务异常,%+v", err)
			return false
		}
		//无任务的时候，直接返回
		if len(result.TaskList) == 0 {
			return false
		}

		for _, task := range result.TaskList {

			myCtx := SwitchWarehouseContext{
				TaskId:     task.Id,
				ChannelId:  task.ChannelId,
				CreateIp:   task.CreateIp,
				IpLocation: task.IpLocation,
			}

			_, err = engine.Where("id=?", task.Id).Update(models.TaskList{TaskStatus: 2})
			if err != nil {
				glog.Errorf("DealAsyncTaskSwitchWarehouse 更新任务状态异常,%+v", err)
			}

			err = handleSwitchWarehouse(&myCtx, task)
			if err != nil {
				glog.Errorf("DealAsyncTaskSwitchWarehouse 门店异步切仓处理异常 %v", err)
				myCtx.SystemError = err.Error()
			}

			updateModel := models.TaskList{
				TaskStatus:     3,
				TaskDetail:     myCtx.SystemError,
				ResulteFileUrl: "",
				ModifyTime:     time.Now(),
			}
			// 如果有调度任务则设置为进行中
			if myCtx.ScheduleSize > 0 {
				updateModel.TaskStatus = 2
				updateModel.ContextData = kit.JsonEncode(myCtx)
			}
			if updateModel.TaskStatus == 3 && myCtx.SystemError == "" {
				updateModel.TaskDetail = fmt.Sprintf("关联成功%d个门店，关联失败%d个门店", len(myCtx.SuccessList), len(myCtx.FailList))
			}

			// 当任务完成时导出excel文档
			if updateModel.TaskStatus == 3 {
				updateModel.ResulteFileUrl = exportSwitchWarehouseError(&myCtx)
				if myCtx.SystemError != "" {
					updateModel.SuccessNum = 0
					updateModel.FailNum = int32(myCtx.Size)
				} else {
					updateModel.SuccessNum = int32(len(myCtx.SuccessList))
					updateModel.FailNum = int32(len(myCtx.FailList))
				}
			}
			if updateModel.TaskDetail == "" {
				updateModel.TaskDetail = fmt.Sprintf("关联成功%d个门店，关联失败%d个门店", len(myCtx.SuccessList), len(myCtx.FailList))
			}

			_, err = engine.Where("id=?", task.Id).Update(updateModel)
			if err != nil {
				glog.Errorf("DealAsyncTaskSwitchWarehouse 更新任务状态异常,%+v", err)
			}
		}
		return true
	}
}

func exportSwitchWarehouseError(myCtx *SwitchWarehouseContext) string {
	if len(myCtx.FailList) == 0 && len(myCtx.SuccessList) == 0 {
		return ""
	}
	var excelRow [][]string
	// excel文件头
	excelRow = append(excelRow, []string{"门店财务编码", "门店名称", "门店是否关联仓库成功", "失败的商品SKUID", "商品失败原因"})
	list := make([]StoreInfo, 0, len(myCtx.SuccessList)+len(myCtx.FailList))
	list = append(list, myCtx.FailList...)
	list = append(list, myCtx.SuccessList...)
	for _, v := range list {
		if v.Message != "" {
			excelRow = append(excelRow, []string{v.FinanceCode, v.Name, v.Message, "--", "--"})
			continue
		}
		for _, item := range v.FailProductList {
			excelRow = append(excelRow, []string{v.FinanceCode, v.Name, "--", cast.ToString(item.SkuId), item.Message})
		}
	}
	if len(excelRow) <= 1 {
		return ""
	}
	url, err := ExportProductErr(excelRow)
	if err != nil {
		glog.Errorf("DealAsyncTaskSwitchWarehouse 导出Excel文件失败,taskId:%d,%+v", myCtx.TaskId, err)
	}
	return url
}

func handleSwitchWarehouse(myCtx *SwitchWarehouseContext, task *pc.TaskList) error {
	glog.Infof("DealAsyncTaskSwitchWarehouse 开始执行任务:%s", kit.JsonEncode(task))
	myCtx.UserInfo = new(models.LoginUserInfo)
	err := json.Unmarshal([]byte(task.RequestHeader), myCtx.UserInfo)
	if err != nil {
		glog.Errorf("DealAsyncTaskSwitchWarehouse 解析当前用户登录信息失败:%+v,taskId:%d", err, task.Id)
		return fmt.Errorf("解析当前用户登录信息失败,%+v", err)
	}
	ctx := context.WithValue(context.Background(), "user_info", myCtx.UserInfo)

	temp := strings.Split(task.OperationFileUrl, ";")
	if len(temp) != 2 {
		return fmt.Errorf("参数OperationFileUrl(%s)不合法", task.OperationFileUrl)
	}
	myCtx.TargetCategory = cast.ToInt32(temp[0])
	storeList, err := parseStoreExcel(ctx, temp[1])
	if err != nil {
		glog.Errorf("DealAsyncTaskSwitchWarehouse 导入Excel文件内容异常:%+v,taskId:%d", err, task.Id)
		return err
	}
	// 去重处理
	storeList = filterStoreRepeat(storeList)

	myCtx.Size = len(storeList)

	// 限制处理门店数量
	if len(storeList) > 50 {
		return errors.New("导入错误，每次最多只能导入50个门店")
	}

	client := dc.GetDcDispatchClient()
	defer client.Close()

	req := &dc.ShopBindInfoByShopIdRequest{
		ChannelId: task.ChannelId,
	}
	for _, v := range storeList {
		req.ShopId = append(req.ShopId, v.FinanceCode)
	}
	resp, err := client.RPC.ShopBindInfoListByShopId(ctx, req)
	if err != nil {
		glog.Errorf("DealAsyncTaskSwitchWarehouse 根据财务编码查询仓库绑定关系异常:%+v,请求参数:%s,taskId:%d", err, kit.JsonEncode(req), task.Id)
		return fmt.Errorf("根据财务编码查询仓库绑定关系异常,%+v", err)
	}
	glog.Infof("DealAsyncTaskSwitchWarehouse 根据财务编码查询仓库绑定关系结果:%s,请求参数:%s,taskId:%d", kit.JsonEncode(resp), kit.JsonEncode(req), myCtx.TaskId)

	if resp.Code != http.StatusOK {
		glog.Errorf("DealAsyncTaskSwitchWarehouse 根据财务编码查询仓库绑定关系失败,请求参数:%s,taskId:%d,返回结果:%s", kit.JsonEncode(req), task.Id, kit.JsonEncode(resp))
		return fmt.Errorf("根据财务编码查询仓库绑定关系失败,code:%d,message:%s,err:%s", resp.Code, resp.Message, resp.Error)
	}

	// 初始化原来绑定仓库信息
	bindingMap := make(map[string]*dc.ShopBindInfo, len(resp.Info))
	for _, item := range resp.Info {
		bindingMap[item.ShopId] = item
	}
	for _, v := range storeList {
		bindingInfo, ok := bindingMap[v.FinanceCode]
		if ok {
			v.SourceWarehouse = WarehouseInfo{
				Id:       int(bindingInfo.WarehouseId),
				Code:     bindingInfo.Code,
				Name:     bindingInfo.WarehouseName,
				Category: int(bindingInfo.Category),
			}
		}
		myCtx.SuccessList = append(myCtx.SuccessList, v)
	}

	// 预选满足条件切仓的门店
	predicateSwitchWarehouseStore(myCtx)

	// 持久化切仓绑定关系
	err = saveWarehouseRelation(myCtx)
	if err != nil {
		return err
	}

	// 任务状态变更为进行中
	updateModel := models.TaskList{
		TaskStatus:  2,
		ModifyTime:  time.Now(),
		ContextData: kit.JsonEncode(myCtx),
	}
	_, err = engine.Where("id=?", task.Id).Update(updateModel)
	if err != nil {
		glog.Errorf("DealAsyncTaskSwitchWarehouse 更新任务状态异常,%+v", err)
	}

	// 异步记录切仓日志
	go asyncLogSwitchWarehouse(myCtx)

	// 互联网医院渠道跳过
	if myCtx.ChannelId == int32(enum.ChannelDigitalHealth) {
		return nil
	}

	// 提交调度job
	err = submitScheduleJob(myCtx)
	if err != nil {
		return err
	}
	return nil
}

// 异步记录切仓日志
func asyncLogSwitchWarehouse(ctx *SwitchWarehouseContext) {
	total := len(ctx.SuccessList) + len(ctx.FailList)
	if total == 0 {
		return
	}

	list := make([]models.SwitchWarehouseLog, 0, total)

	for _, v := range ctx.SuccessList {
		content := fmt.Sprintf(`关联成功：从“关联%s”修改为“关联%s”`, enum.WarehouseCategoryMap[v.SourceWarehouse.Category], enum.WarehouseCategoryMap[v.TargetWarehouse.Category])
		if v.SourceWarehouse.Code == "" {
			content = fmt.Sprintf(`关联成功：修改为“关联%s”`, enum.WarehouseCategoryMap[v.TargetWarehouse.Category])
		}
		list = append(list, models.SwitchWarehouseLog{
			ChannelId:            ctx.ChannelId,
			Action:               1,
			FinanceCode:          v.FinanceCode,
			ShopName:             v.Name,
			SrcWarehouseCode:     v.SourceWarehouse.Code,
			SrcWarehouseName:     v.SourceWarehouse.Name,
			SrcWarehouseCategory: int32(v.SourceWarehouse.Category),
			DstWarehouseCode:     v.TargetWarehouse.Code,
			DstWarehouseName:     v.TargetWarehouse.Name,
			DstWarehouseCategory: int32(v.TargetWarehouse.Category),
			Content:              content,
			CreateId:             ctx.UserInfo.UserNo,
			CreateTime:           time.Now().Format("2006-01-02 15:04:05"),
			CreateName:           ctx.UserInfo.UserName,
			CreateMobile:         ctx.UserInfo.Mobile,
			CreateIp:             ctx.CreateIp,
			IpLocation:           ctx.IpLocation,
		})
	}
	for _, v := range ctx.FailList {
		list = append(list, models.SwitchWarehouseLog{
			ChannelId:            ctx.ChannelId,
			Action:               1,
			FinanceCode:          v.FinanceCode,
			ShopName:             v.Name,
			SrcWarehouseCode:     v.SourceWarehouse.Code,
			SrcWarehouseName:     v.SourceWarehouse.Name,
			SrcWarehouseCategory: int32(v.SourceWarehouse.Category),
			Content:              v.Message,
			CreateId:             ctx.UserInfo.UserNo,
			CreateTime:           time.Now().Format("2006-01-02 15:04:05"),
			CreateName:           ctx.UserInfo.UserName,
			CreateMobile:         ctx.UserInfo.Mobile,
			CreateIp:             ctx.CreateIp,
			IpLocation:           ctx.IpLocation,
		})
	}

	_, err := engine.Insert(list)
	if err != nil {
		glog.Errorf("asyncLogSwitchWarehouse 异步记录切仓日志异常:%+v", err)
	}
}

// 门店去重
func filterStoreRepeat(storeList []StoreInfo) []StoreInfo {
	myMap := make(map[string]StoreInfo)
	for _, v := range storeList {
		myMap[v.FinanceCode] = v
	}
	list := make([]StoreInfo, 0, len(myMap))
	for _, v := range myMap {
		list = append(list, v)
	}
	return list
}

// 保存切仓绑定关系
func saveWarehouseRelation(myCtx *SwitchWarehouseContext) error {
	client := dc.GetDcDispatchClient()
	defer client.Close()
	// 持久化切仓映射关系
	saveReq := &dc.StoreWarehouseRelationShopRequest{
		ChannelId: myCtx.ChannelId,
	}
	for _, v := range myCtx.SuccessList {
		saveReq.Wrs = append(saveReq.Wrs, &dc.WarehouseRelationShop{
			ShopId:        v.FinanceCode,
			ShopName:      v.Name,
			WarehouseId:   int32(v.TargetWarehouse.Id),
			WarehouseName: v.TargetWarehouse.Name,
			Category:      int32(v.TargetWarehouse.Category),
			Code:          v.TargetWarehouse.Code,
		})
	}
	if len(saveReq.Wrs) > 0 {
		saveResp, err := client.RPC.StoreWarehouseRelationShop(client.Ctx, saveReq)
		if err != nil {
			glog.Errorf("Product/saveWarehouseRelation 请求持久化仓库绑定关系异常,请求参数:%s,taskId:%d,%+v", kit.JsonEncode(saveReq), myCtx.TaskId, err)
			return fmt.Errorf("请求持久化仓库绑定关系异常,%+v", err)
		}
		glog.Infof("Product/saveWarehouseRelation 持久化仓库绑定关系结果:%s,请求参数:%s,taskId:%d", kit.JsonEncode(saveResp), kit.JsonEncode(saveReq), myCtx.TaskId)
		// 把处理失败的数据加入到上下文中
		pushContextError(myCtx, saveResp.ShopIds, fmt.Sprintf("持久化仓库绑定关系失败:%s,%s", saveResp.Message, saveResp.Error))
	}
	return nil
}

func pushContextError(ctx *SwitchWarehouseContext, financeCodes []string, message string) {
	if len(financeCodes) == 0 {
		return
	}

	successMap := make(map[string]StoreInfo, len(ctx.SuccessList))
	for _, v := range ctx.SuccessList {
		successMap[v.FinanceCode] = v
	}

	for _, financeCode := range financeCodes {
		storeInfo, ok := successMap[financeCode]
		if !ok {
			continue
		}
		storeInfo.Message = message
		ctx.FailList = append(ctx.FailList, storeInfo)
		delete(successMap, financeCode)
	}

	var successList []StoreInfo
	for _, v := range successMap {
		successList = append(successList, v)
	}
	ctx.SuccessList = successList
}

// 提交调度job
func submitScheduleJob(ctx *SwitchWarehouseContext) error {
	name := fmt.Sprintf("%d家门店切到%s-%s", ctx.Size, enum.WarehouseCategoryMap[int(ctx.TargetCategory)], time.Now().Format("200601021504"))
	req := dto.AsyncJobRequest{
		Name:  name,
		Label: "门店切仓",
		Meta: map[string]string{
			"taskId": cast.ToString(ctx.TaskId),
		},
		Type:     "switchWarehouse",
		IsNotify: true,
		PluginSet: []string{
			"switchWarehouseOffshelfProduct",
			"switchWarehouseSyncProduct",
			"switchWarehouseOnshelfProduct",
			"switchWarehouseSyncStock",
		},
		TaskInputList:          []string{},
		TaskExceptionOperation: 1,
	}
	channelStores := make([]string, 0, len(ctx.SuccessList))
	channelId := ctx.ChannelId
	if channelId == int32(enum.ChannelAwenId) || channelId == int32(enum.ChannelAwenPickUpId) {
		channelId = 100
	}
	for _, v := range ctx.SuccessList {
		if v.IsSkip {
			glog.Infof("DealAsyncTaskSwitchWarehouse 仓库绑定成功,人工确认跳过切仓处理 %s", kit.JsonEncode(v))
			continue
		}
		req.TaskInputList = append(req.TaskInputList, kit.JsonEncode(SwitchWarehouseInputItem{
			FinanceCode:             v.FinanceCode,
			ChannelId:               ctx.ChannelId,
			TargetWarehouseId:       int32(v.TargetWarehouse.Id),
			TargetWarehouseCode:     v.TargetWarehouse.Code,
			TargetWarehouseCategory: int32(v.TargetWarehouse.Category),
			SourceWarehouseId:       int32(v.SourceWarehouse.Id),
			SourceWarehouseCode:     v.SourceWarehouse.Code,
			SourceWarehouseCategory: int32(v.SourceWarehouse.Category),
			UserInfo:                ctx.UserInfo,
		}))
		channelStores = append(channelStores, fmt.Sprintf("%s:%d", v.FinanceCode, channelId))
	}
	req.Meta["channelStores"] = strings.Join(channelStores, ",")

	ctx.ScheduleSize = len(req.TaskInputList)
	// 如果没有符合条件数据则直接return
	if ctx.ScheduleSize == 0 {
		return nil
	}

	jobId, err := workerCtx.AsyncSubmitJob(req)
	if err != nil {
		glog.Errorf("DealAsyncTaskSwitchWarehouse 请求分布式调度异常,taskId:%d,请求参数:%s,%+v", ctx.TaskId, kit.JsonEncode(req), err)
		return fmt.Errorf("请求分布式调度异常,%+v", err)
	}

	ctx.ScheduleId = int(jobId)
	return nil
}

// 预选切仓门店,判断是否满足切仓条件
func predicateSwitchWarehouseStore(ctx *SwitchWarehouseContext) {
	redisConn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}
	var successList []StoreInfo

	//先一次性查出所有要切换的目标仓库
	var warehouseIds []int
	var shops []string
	for _, v := range ctx.SuccessList {
		warehouseIds = append(warehouseIds, v.TargetWarehouse.Id)
		shops = append(shops, v.FinanceCode)
	}
	var list []*models.Warehouse
	//engine.In("id", warehouseIds).Find(&list)
	engine.Find(&list)

	var listshop []*models.WarehouseRelationShop
	engine.In("shop_id", shops).Find(&listshop)

	var warehosueMap = make(map[int]*models.Warehouse)
	for _, v := range list {
		warehosueMap[v.Id] = v
	}

	var ShopChannelIdMap = make(map[string]int)
	for _, v := range listshop {
		ShopChannelIdMap[v.ShopId+"|"+cast.ToString(v.ChannelId)] = v.WarehouseId
	}

	//如果是渠道1或者10，查询出相互对应的渠道绑定的仓库，看和现在导入的是否一致，如果不一致，报错

	for i, v := range ctx.SuccessList {
		glog.Infof("DealAsyncTaskSwitchWarehouse 正在处理第%d家门店:%s,%s", i+1, v.Name, v.FinanceCode)

		var targetWarehouse *models.Warehouse
		if _, ok := warehosueMap[v.TargetWarehouse.Id]; ok {
			myWarhouse := warehosueMap[v.TargetWarehouse.Id]
			if warehosueMap[v.TargetWarehouse.Id].Category == int(ctx.TargetCategory) {
				targetWarehouse = myWarhouse
			}
		}

		if targetWarehouse == nil {
			v.Message = fmt.Sprintf("关联失败：未找到仓库ID为：%d,类型为%d 的仓库", v.TargetWarehouse.Id, ctx.TargetCategory)
			ctx.FailList = append(ctx.FailList, v)
			continue
		}

		if targetWarehouse.Status == 0 {
			v.Message = fmt.Sprintf("关联失败：仓库%s(%s)处于禁用状态，无法关联", enum.WarehouseCategoryMap[int(ctx.TargetCategory)], targetWarehouse.Code)
			ctx.FailList = append(ctx.FailList, v)
			continue
		}

		switch ctx.ChannelId {
		case 10:

			if _, ok := ShopChannelIdMap[v.FinanceCode+"|1"]; ok {

				//阿闻外卖渠道的仓库
				awenWarehouse := warehosueMap[ShopChannelIdMap[v.FinanceCode+"|1"]]
				if awenWarehouse.Category == 3 && ShopChannelIdMap[v.FinanceCode+"|1"] != targetWarehouse.Id {
					v.Message = "关联失败：外卖渠道和当前绑定仓库不一致"
					ctx.FailList = append(ctx.FailList, v)
					continue
				}

				if awenWarehouse.Category == 4 {

					if targetWarehouse.Category == 4 && targetWarehouse.Id != awenWarehouse.Id {
						v.Message = "关联失败：自提绑定前置仓，必须和阿闻外卖是同一个仓"
						ctx.FailList = append(ctx.FailList, v)
						continue
					}
					if targetWarehouse.Category != 5 && targetWarehouse.Category != 4 {
						v.Message = "关联失败：外卖是前置仓，自提只可以是前置虚拟仓或者和外卖绑同一个仓"
						ctx.FailList = append(ctx.FailList, v)
						continue
					}

				}

			} else {
				v.Message = "关联失败：外卖渠道未绑定仓库"
				ctx.FailList = append(ctx.FailList, v)
				continue
			}
		case 2:
			// 判断有没有绑定美团
			if !isBindStore(v.FinanceCode, "store:relation:dctomt") {
				v.Message = "关联失败：门店未绑定美团店铺"
				ctx.FailList = append(ctx.FailList, v)
				continue
			}
		case 3:
			// 判断有没有绑定饿了么
			if !isBindStore(v.FinanceCode, "store:relation:dctoele") {
				v.Message = "关联失败：门店未绑定饿了么店铺"
				ctx.FailList = append(ctx.FailList, v)
				continue
			}
		case 4:
			// 判断有没有绑定京东
			if !isBindStore(v.FinanceCode, "store:relation:dctojddj") {
				v.Message = "关联失败：门店未绑定京东店铺"
				ctx.FailList = append(ctx.FailList, v)
				continue
			}
		}

		v.TargetWarehouse = WarehouseInfo{
			Id:       targetWarehouse.Id,
			Thirdid:  targetWarehouse.Thirdid,
			Code:     targetWarehouse.Code,
			Name:     targetWarehouse.Name,
			Level:    targetWarehouse.Level,
			Category: targetWarehouse.Category,
		}

		successList = append(successList, v)
	}
	ctx.SuccessList = successList

}

// 是否绑定美团门店
func isBindStore(financeCode string, key string) bool {
	redisConn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}
	appPoiCode := redisConn.HGet(key, financeCode).Val()
	return appPoiCode != ""
}

// 解析门店配置Excel文档
func parseStoreExcel(ctx context.Context, url string) ([]StoreInfo, error) {
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return nil, errors.New("用户不存在")
	}

	// 下载excel
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("构建下载Excel文档(%s)请求异常,%+v", url, err)
	}

	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		return nil, fmt.Errorf("执行下载Excel文档(%s)请求异常,%+v", url, err)
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文档(%s)异常,%+v", url, err)
	}

	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows := f.GetRows(sheetName)

	list := make([]StoreInfo, 0, len(rows))
	isSkipHeader := true
	for _, item := range rows {
		if strings.TrimSpace(item[0]) == "门店财务编码" {
			isSkipHeader = false
			continue
		}
		if isSkipHeader {
			continue
		}
		//如果财务编码或者仓库ID是空的就跳过
		if len(item) < 4 || strings.TrimSpace(item[0]) == "" || strings.TrimSpace(item[3]) == "" {
			continue
		}
		//如果仓库ID不是数字类型
		_, err := strconv.Atoi(item[3])
		if err != nil {
			continue
		}
		data := StoreInfo{
			FinanceCode:     strings.TrimSpace(item[0]),
			Name:            strings.TrimSpace(item[1]),
			TargetWarehouse: WarehouseInfo{Id: cast.ToInt(item[3])},
		}
		if len(item) > 2 && strings.TrimSpace(item[2]) != "是" {
			data.IsSkip = true
		}
		list = append(list, data)
	}
	glog.Infof("DealAsyncTaskSwitchWarehouse 读取Excel数据结果:%s", kit.JsonEncode(list))
	return list, nil
}

// 门店信息
type StoreInfo struct {
	FinanceCode     string        `json:"finance_code"`
	Name            string        `json:"name"`
	TargetWarehouse WarehouseInfo `json:"target_warehouse"`
	SourceWarehouse WarehouseInfo `json:"source_warehouse"`
	// 处理失败的商品
	FailProductList []ProductInfo `json:"fail_product_list"`
	// 如果有错误信息则放在这里
	Message string `json:"message"`
	// 是否跳过
	IsSkip bool `json:"is_skip"`
}

type SwitchWarehouseContext struct {
	// 任务ID
	TaskId int32 `json:"task_id"`
	// 切换目标仓库类型，3:门店仓 4：前置仓，5：虚拟仓
	TargetCategory int32 `json:"target_category"`
	// 渠道，1.阿闻到家 阿闻渠道-外卖;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.互联网医疗 8阿闻渠道-自提;
	ChannelId int32 `json:"channel_id"`
	Size      int   `json:"size"`
	// 有多少个任务被调度
	ScheduleSize int `json:"schedule_size"`
	// 调度器返回的ID
	ScheduleId int `json:"schedule_id"`
	// 处理失败门店
	FailList []StoreInfo `json:"fail_list"`
	// 处理成功门店
	SuccessList []StoreInfo `json:"success_list"`
	// 系统错误
	SystemError string `json:"system_error"`
	// 用户登录信息
	UserInfo *models.LoginUserInfo `json:"user_info"`
	//创建人IP
	CreateIp string `json:"create_ip"`
	// ip位置
	IpLocation string `json:"ip_location"`
}

type WarehouseInfo struct {
	Id       int    `json:"id"`
	Thirdid  string `json:"thirdid"`
	Code     string `json:"code"`
	Name     string `json:"name"`
	Level    int    `json:"level"`
	Category int    `json:"category"`
}

// 商品信息
type ProductInfo struct {
	Id    int64
	SkuId int64
	Name  string
	// 商品类别（1-实物商品，2-虚拟商品，3-组合商品）
	Type    int32
	Message string
}

type SwitchWarehouseInputItem struct {
	// 门店财务编码
	FinanceCode string `json:"finance_code"`
	// 渠道，1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;
	ChannelId int32 `json:"channel_id"`
	// 目标仓库ID
	TargetWarehouseId       int32  `json:"target_warehouse_id"`
	TargetWarehouseCode     string `json:"target_warehouse_code"`
	TargetWarehouseCategory int32  `json:"target_warehouse_category"`
	// 源仓库ID
	SourceWarehouseId       int32  `json:"source_warehouse_id"`
	SourceWarehouseCode     string `json:"source_warehouse_code"`
	SourceWarehouseCategory int32  `json:"source_warehouse_category"`
	// 用户信息
	UserInfo *models.LoginUserInfo `json:"user_info"`
}
