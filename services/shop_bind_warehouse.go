package services

import (
	"_/models"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/spf13/cast"

	//"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/limitedlee/microservice/example/proto"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
)

func (c *Product) ShopBindWarehouse(ctx context.Context, in *pc.BindShopWarehouse) (*pc.BindShopWarehouseResponse, error) {
	glog.Info("门店绑定前置虚拟仓: ", in)
	out := &pc.BindShopWarehouseResponse{Code: 400}
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		out.Error = "用户不存在"
		return out, nil
	}

	// 下载excel
	req, err := http.NewRequest("POST", in.QiniuUrl, nil)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = fmt.Sprintf("文件打开失败，请上传正确格式的文件！%s", err.Error())
		return out, nil
	}

	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, _ := f.GetRows(sheetName)

	// excel为空
	if len(rows) <= 1 {
		out.Message = "请导入门店信息"
		return out, nil
	}

	var shopMap = make(map[string]int32)
	isSkipHeader := true
	for _, row := range rows {
		if row == nil {
			isSkipHeader = false
			continue
		}
		if strings.TrimSpace(row[0]) == "门店财务编码" {
			isSkipHeader = false
			continue
		}
		if isSkipHeader {
			continue
		}
		if len(row) < 2 || strings.TrimSpace(row[0]) == "" {
			continue
		}

		shopMap[row[0]]++
	}
	if len(shopMap) > 50 {
		out.Message = "导入错误，每次最多只能导入50个门店"
		return out, nil
	}
	session := NewDbConn().NewSession()
	defer session.Close()
	res, err := CommonShopBindWarehouse(session, &pc.BindShopWarehouse{
		ChannelId:  in.ChannelId,
		QiniuUrl:   in.QiniuUrl,
		Ip:         in.Ip,
		IpLocation: in.IpLocation,
		BindType:   in.BindType,
	}, userInfo, int32(len(shopMap)))
	if err != nil {
		out.Message = res.Message
		return out, nil
	}

	out.Message = "批量门店绑定前置虚拟仓任务进行中..."
	out.Code = 200
	return out, err
}

//EditShopBindWarehouse 门店仓库修改
func (c *Product) EditShopBindWarehouse(ctx context.Context, in *pc.ShopBindingWarehouseReq) (out *pc.BaseResponse, e error) {
	out = &pc.BaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("EditShopBindWarehouse 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return
	}

	//测试数据
	//userInfo := &models.LoginUserInfo{
	//	UserName: "test",
	//	UserNo:   "userno",
	//	Mobile:   "15118811943",
	//}

	if len(in.Data) == 0 {
		out.Message = "无提交修改记录"
		return
	}
	session := NewDbConn().NewSession()
	defer session.Close()

	session.Begin()
	//需要切仓的数据
	for _, v := range in.Data {
		var newData = make([]*pc.BindData, 0)
		var warehouse = models.Warehouse{}
		newData = append(newData, v)
		if res, err := session.SQL(`select * from dc_dispatch.warehouse where id = ?;`, v.WarehouseId).Get(&warehouse); err != nil {
			out.Message = err.Error()
			return
		} else if !res {
			session.Rollback()
			out.Message = "查询无仓库信息"
			return
		}

		//1.先转成excel上传到七牛云
		Url, err := ImportShopBindWarehouse(newData, v.ChannelId)
		if err != nil {
			session.Rollback()
			out.Message = fmt.Sprintf("上传文件异常:%s", err.Error())
			return
		}
		//2.添加task_list任务记录
		if res, err := CommonShopBindWarehouse(session, &pc.BindShopWarehouse{
			ChannelId:  v.ChannelId,
			QiniuUrl:   Url,
			Ip:         in.Ip,
			IpLocation: in.IpLocation,
			BindType:   cast.ToInt32(warehouse.Category),
		}, userInfo, 1); err != nil {
			session.Rollback()
			out.Message = res.Message
			return
		}
	}
	session.Commit()
	out.Message = "门店绑定仓库任务进行中..."
	out.Code = 200
	return
}

//CommonShopBindWarehouse 门店切库创建任务
func CommonShopBindWarehouse(session *xorm.Session, in *pc.BindShopWarehouse, userInfo *models.LoginUserInfo, shopNum int32) (out *proto.BaseResponse, err error) {
	userInfoJson, err := json.Marshal(userInfo)
	if err != nil {
		out.Message = fmt.Sprintf("序列化用户信息异常:%+v", err)
		return out, err
	}

	tasklist := models.TaskList{}
	tasklist.ChannelId = in.ChannelId
	tasklist.CreateId = userInfo.UserNo
	tasklist.ModifyId = userInfo.UserNo
	tasklist.ModifyTime = time.Now()
	tasklist.CreateTime = time.Now()
	tasklist.TaskContent = 67
	tasklist.TaskStatus = 1
	tasklist.Status = 1
	tasklist.Category = in.BindType

	switch in.BindType {
	case 1:
		tasklist.OperationFileUrl = "1;" + in.QiniuUrl
	case 3:
		tasklist.OperationFileUrl = "3;" + in.QiniuUrl
	case 4:
		tasklist.OperationFileUrl = "4;" + in.QiniuUrl
	case 5:
		tasklist.OperationFileUrl = "5;" + in.QiniuUrl
	default:
		tasklist.OperationFileUrl = in.QiniuUrl
	}

	tasklist.RequestHeader = string(userInfoJson)
	tasklist.CreateMobile = userInfo.Mobile
	tasklist.CreateName = userInfo.UserName
	tasklist.CreateId = userInfo.UserNo
	tasklist.CreateIp = in.Ip
	tasklist.IpLocation = in.IpLocation
	tasklist.ShopNum = shopNum

	var extendedData []string
	switch in.ChannelId {
	case 1:
		extendedData = append(extendedData, "阿闻外卖渠道任务")
	case 2:
		extendedData = append(extendedData, "美团渠道任务")
	case 3:
		extendedData = append(extendedData, "饿了么渠道任务")
	case 4:
		extendedData = append(extendedData, "京东到家渠道任务")
	case 9:
		extendedData = append(extendedData, "互联网医院渠道任务")
	case 10:
		extendedData = append(extendedData, "阿闻自提渠道任务")
	}

	switch in.BindType {
	case 1:
		extendedData = append(extendedData, "切电商仓")
	case 3:
		extendedData = append(extendedData, "切门店仓")
	case 4:
		extendedData = append(extendedData, "切前置仓")
	case 5:
		extendedData = append(extendedData, "切前置虚拟仓")
	}

	tasklist.ExtendedData = strings.Join(extendedData, "-")

	// 保存任务信息
	_, err = session.Insert(&tasklist)
	if err != nil {
		out.Message = "保存批量门店绑定前置虚拟仓任务信息失败"
		return out, err
	}

	return out, nil
}

//ImportShopBindWarehouse 门店修改生成excel上传文件
func ImportShopBindWarehouse(data []*pc.BindData, ChannelId int32) (rsUrl string, err error) {
	file := excelize.NewFile()
	defer file.Close()

	writer, err := file.NewStreamWriter("Sheet1")
	if err != nil {
		return
	}
	writer.MergeCell("A1", "F3")
	_ = writer.SetRow("A1", []interface{}{
		"功能说明：\n1.针对“是否跑商品数据”这个字段，只能填“是”或“否”，不要填其他文案。\n2.如果填“是”，会对该门店的商品数据进行初始化处理（原有商品下架、原有库存清0、新门店商品同步上架、新门店商品库存同步）；如果填“否”，则不会对该门店的商品数据进行初始化处理",
	})
	_ = writer.SetRow("A4", []interface{}{
		"门店财务编码", "门店名称", "是否跑商品数据", "仓库ID",
	})

	for i, v := range data {
		var text = "否"
		if v.RunType == 1 {
			text = "是"
		}
		_ = writer.SetRow(fmt.Sprintf("A%d", i+5), []interface{}{v.ShopId, v.ShopName, text, v.WarehouseId})
	}
	if err = writer.Flush(); err != nil {
		return "", errors.New("flush文件错误：" + err.Error())
	}
	rsUrl, err = utils.UploadExcelToQiNiu(file, fmt.Sprintf("切仓导入%d%v.xlsx", ChannelId, time.Now().UnixNano()))
	if err != nil {
		return "", errors.New("上传文件错误：" + err.Error())
	}

	return
}
