package services

import (
	"_/models"
	"_/proto/pc"
	"context"
	"encoding/json"
	"testing"

	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

func Test_updateGroupHasStock(t *testing.T) {
	// 初始化数据库连接
	SetupDB()
	engine.ShowSQL(true)
	orderEngine.ShowSQL(true)
	redisHandle = GetRedisConn()

	args := []string{
		// app_poi_code不带#，即是财务编码
		`{"app_poi_code":"CX0011","food_data":[{"app_food_code":"1020749","skus":[{"sku_id":"1020749001","stock":"2"}]},{"app_food_code":"1020750","skus":[{"sku_id":"1020750001","stock":"5"}]}]}`,
		// app_poi_code带#，即是仓库ID
		`{"app_poi_code":"#1515","food_data":[{"app_food_code":"1020749","skus":[{"sku_id":"1020749001","stock":"3"}]},{"app_food_code":"1020750","skus":[{"sku_id":"1020750001","stock":"4"}]}]}`,
		// 自己发送mq消息不处理，直接return
		`{"app_poi_code":"CX0004","sender":"updateStock","food_data":[{"app_food_code":"1031408","skus":[{"sku_id":"1031408099","stock":"5"}]}]}`,
		// skuId为组合商品不处理第三方渠道组合商品库存推送
		`{"app_poi_code":"CX0004","food_data":[{"app_food_code":"1031408","skus":[{"sku_id":"1031408099","stock":"5"}]}]}`,
	}
	for _, req := range args {
		_, err := func(request string) (string, error) {
			var err error

			glog.Info("接收到库存消息：" + request)
			var req models.StoreStock
			if err := json.Unmarshal([]byte(request), &req); err != nil {
				glog.Error("接收到库存消息推送错误的：" + request + err.Error())
				return "", err
			}

			// MQ消息发送人等于自己则不处理，直接return
			if req.Sender == UpdateStockSender {
				return "", nil
			}

			// 处理阿闻渠道库存更新
			err1 := updateStockOnAwen(req.Food_data, req.App_poi_code)
			if err1 != nil {
				glog.Errorf("更新阿闻渠道库存异常,req:%s,err:%+v", kit.JsonEncode(req), err1)
				err = err1
			}
			// 处理美团、饿了么、京东到家等第三渠道组合商品库存更新
			err2 := updateGroupStockOnThirdChannel(req.Food_data, req.App_poi_code)
			if err2 != nil {
				glog.Errorf("更新美团、饿了么、京东到家等第三渠道库存异常,req:%s,err:%+v", kit.JsonEncode(req), err2)
				err = err2
			}

			return "", err
		}(req)
		if err != nil {
			t.Error(err)
		}
	}

}

func Test_updateHasStock(t *testing.T) {
	engine = NewDbConn()
	// engine.ShowSQL(true)
	type args struct {
		id          int32
		stock       int32
		financeCode string
	}
	tests := []struct {
		name    string
		args    args
		want    int
		wantErr bool
	}{
		{
			name: "库存通知",
			args: args{
				id:          1,
				stock:       0,
				financeCode: "#11",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//got, err := updateHasStock(tt.args.id, tt.args.stock, tt.args.financeCode)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("updateHasStock() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if got != tt.want {
			//	t.Errorf("updateHasStock() = %v, want %v", got, tt.want)
			//}
		})
	}
}

func TestProduct_GetHasStockSkuIDs(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.HasStockSkuIDsRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.HasStockSkuIDsResponse
		wantErr bool
	}{
		{
			name: "根据传入的SKUID，过滤掉没有库存的返回",
			c:    &Product{},
			args: args{
				ctx: context.Background(),
				in: &pc.HasStockSkuIDsRequest{
					FinanceCode: "RP0228",
					SkuId:       []int32{105303, 108389, 113016, 107292, 1019533001, 105297, 114153, 107311, 111968, 106047054, 106610, 104778, 113160, 104781, 111035, 107463, 113373, 113143, 105215083, 109615, 105215182, 107318, 106147, 106778, 104963, 113146, 113139, 112207, 111397, 113154, 107584, 111971, 108410, 107989, 106047055, 111693, 1000001001, 109601, 108395, 109157, 108412, 107033, 105713, 1019714001, 108749, 105215042, 1019120001, 108397, 105215198, 109259, 106047003, 105708, 107920, 113190, 100386, 112635, 112399, 104525, 111991, 1019152001, 112473, 112608, 114157, 104773, 1008364001, 109202, 1019713001, 1001118001, 105215203, 1000850001, 105215184, 104675, 112474, 106734, 107575, 1000082002, 104984, 106047056, 1016816001, 104965, 112046, 108750, 105215084, 112402, 1018311001, 105061, 1008376001, 113194, 108997, 1000249001, 1016910001, 110021, 101461, 107784, 100562, 104772, 114179, 105215202, 109548, 104780},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetHasStockSkuIDs(tt.args.ctx, tt.args.in)
			if err != nil {
				t.Error(err)
			} else {
				t.Error(len(got.SkuId), len(tt.args.in.SkuId))
			}
		})
	}
}

func TestAdd_Product_Has_Stock(t *testing.T) {
	type args struct {
		skuId       int32
		codes       []string
		warehouseId int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "测试插入"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.skuId = 1000741001
			tt.args.warehouseId = 1284
			tt.args.codes = append(tt.args.codes, "RP0067")
			tt.args.codes = append(tt.args.codes, "YC0090")
			if err := Add_Product_Has_Stock(tt.args.skuId, tt.args.codes, tt.args.warehouseId); (err != nil) != tt.wantErr {
				t.Errorf("Add_Product_Has_Stock() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_updateHasStock1(t *testing.T) {
	type args struct {
		id          int32
		stock       int32
		financeCode string
	}
	tests := []struct {
		name    string
		args    args
		want    int
		want1   int
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "测试修改has_stock"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.id = 1020750001
			tt.args.stock = 0
			tt.args.financeCode = "RP0158"
			GetRedisConn()
			NewDbConn()
			got, got1, err := updateHasStock(tt.args.id, tt.args.stock, tt.args.financeCode)
			if (err != nil) != tt.wantErr {
				t.Errorf("updateHasStock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("updateHasStock() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("updateHasStock() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestConsumeStock(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ConsumeStock()
		})
	}
}
