package services

import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/et"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	kit "github.com/tricobbler/rp-kit"

	"github.com/go-xorm/xorm"
	"github.com/spf13/cast"

	"github.com/maybgit/glog"
)

func DealAsyncTaskListAll() bool { //初始化db
	db := NewDbConn()

	//拼装参数
	params := &pc.GetTaskListRequest{
		Sort:        "createTimeAsc",
		TaskStatus:  1,
		ChannelId:   -1,  //-1 查询全部的
		TaskContent: 101, //101 查询task_content in (1,2,3,11,13,35,36,37,38)
		Status:      1,
		Page:        1,
		PageSize:    1,
	}

	//获取需要执行的任务，只取一条数据
	pd := new(Product)
	result, err := pd.GetTaskList(context.Background(), params)
	if err != nil {
		glog.Error(err)
		return false
	}
	//无任务的时候，直接返回
	if len(result.TaskList) == 0 {
		return false
	}

	gNum := runtime.NumCPU()
	glog.Info("gNum数量:", gNum)
	channelG := make(chan bool, gNum)
	wg := new(sync.WaitGroup)
	listId := int32(0)
	for _, task := range result.TaskList {
		listId = task.Id
		glog.Info("任务id:" + string(strconv.Itoa(int(task.Id))))
		channelG <- true
		wg.Add(1)
		task := task
		go func() {
			defer func() {
				if r := recover(); r != nil {
					glog.Error("DealAsyncTaskListAll-定时任务异常 ", r)
				}
			}()
			defer func() {
				glog.Info("wg.done run ..")
				<-channelG
				wg.Done()
			}()
			updateModel := models.TaskList{
				Id:         task.Id,
				TaskStatus: 2,
			}
			////更新任务状态-进行中
			//UpdateTaskListInfo(updateModel)
			TaskContent := task.TaskContent
			switch TaskContent {
			case 1: //todo 商品批量新建 excel导入商品到商品库
				glog.Info("商品批量新建 excel导入商品到商品执行。。。", task.Id)
				var taskRes pc.ImportExcelProductResponse
				if task.ChannelId == 0 { // 实物商品
					res, err := pd.ImportProduct(task.OperationFileUrl, 0)
					if err != nil {
						glog.Error(err)
						//return false
					}
					taskRes = *res
				} else if task.ChannelId == 1 {
					res, err := pd.ImportProduct(task.OperationFileUrl, 4)
					if err != nil {
						glog.Error(err)
						//return false
					}
					taskRes = *res
				}

				//更新任务列表信息
				//数据拼装
				taskDetailjson, err := json.Marshal(&taskRes)
				if err != nil {
					glog.Error(err)
					//	return false
				}

				updateModel.TaskStatus = 3
				updateModel.TaskDetail = string(taskDetailjson)
				updateModel.ResulteFileUrl = taskRes.QiniuUrl
				updateModel.SuccessNum = taskRes.SuccessNum
				updateModel.FailNum = taskRes.FailNum
				UpdateTaskListInfo(updateModel)

				glog.Info("商品批量新建 excel导入商品到商品结束。。。", task.Id)
				break
			case 2: //todo 批量更新
				param := make(map[string]interface{})
				json.Unmarshal([]byte(task.OperationFileUrl), &param)
				glog.Info("批量更新商品：", task.OperationFileUrl)
				//商品批量更新
				taskRes, err := pd.ImportProduct(param["url"].(string), int(param["update_type"].(float64)))

				//log.Fatal(taskRes)
				if err != nil {
					glog.Error(err)
					//return false
				}
				//更新任务列表信息
				//数据拼装
				taskDetailjson, err := json.Marshal(taskRes)
				if err != nil {
					glog.Error(err)
					//return false
				}
				glog.Info("批量更新商品结果：", string(taskDetailjson))
				updateModel.TaskStatus = 3
				updateModel.TaskDetail = string(taskDetailjson)
				updateModel.ResulteFileUrl = taskRes.QiniuUrl
				updateModel.SuccessNum = taskRes.SuccessNum
				updateModel.FailNum = taskRes.FailNum
				UpdateTaskListInfo(updateModel)
				//if err != nil {
				//	glog.Info("批量更新商品结果：更新错误", string(taskDetailjson), err.Error())
				//	//return false
				//}
				glog.Info("批量更新商品结果：更新成功", string(taskDetailjson))
				break
				//todo 周翔整理方法，暂时没有发现有用到，注释掉测试后没问题删除
			//case 3: //todo 渠道--批量新建
			//	switch task.ChannelId {
			//	case 1: //阿闻渠道
			//		var params pc.BatchCreateToAWRequest
			//		//todo 处理业务逻辑
			//		err := json.Unmarshal([]byte(task.OperationFileUrl), &params)
			//		if err != nil {
			//			glog.Error("阿闻批量新建异步参数解析失败:", err.Error())
			//			//	return false
			//		}
			//
			//		var model *models.LoginUserInfo
			//		err = json.Unmarshal([]byte(task.RequestHeader), &model)
			//		if err != nil {
			//			glog.Error("阿闻渠道批量新建异步任务头部反序列化失败：", err.Error())
			//		}
			//		ctx := context.WithValue(context.Background(), "user_info", model)
			//		resultRes, err := pd.BatchCreateToAW(ctx, &params)
			//		if err != nil {
			//			glog.Error(err)
			//			//	return false
			//		}
			//		if resultRes.Code != 200 {
			//			glog.Error(err)
			//			//return false
			//		}
			//		//更新任务列表信息
			//		//数据拼装
			//		taskDetailjson, err := json.Marshal(resultRes)
			//		if err != nil {
			//			glog.Error(err)
			//			//	return false
			//		}
			//		updateModel := models.TaskList{
			//			TaskStatus:     3,
			//			TaskDetail:     string(taskDetailjson),
			//			ResulteFileUrl: resultRes.QiniuUrl,
			//			ModifyTime:     time.Now(),
			//		}
			//		//_, err = db.Id(result.TaskList[0].Id).Update(updateModel)
			//		updateModel.Id = task.Id
			//		sql := "update `dc_product`.`task_list` set `task_status` = ?,task_detail=?,resulte_file_url=?,modify_time=? where `id` = ?"
			//		_, err = db.Exec(sql, updateModel.TaskStatus, updateModel.TaskDetail, updateModel.ResulteFileUrl, updateModel.ModifyTime, updateModel.Id)
			//		//_, err = db.Update(updateModel)
			//		if err != nil {
			//			glog.Error(err.Error())
			//			//return false
			//		}
			//		break
			//		/*	case 2: //美团渠道
			//				break
			//			case 3: //饿了么渠道
			//				break
			//			case 4: //美团渠道
			//				break*/
			//	default:
			//		//如果以上都没有处理，则更新下最后创建时间
			//		updateTaskList(task.Id, db)
			//
			//	}
			//	break
			case 11:
				//todo 渠道--商品导出
				glog.Info("渠道--商品导出执行。。。", task.Id)
				req := new(pc.ChannelProductListReq)
				_ = json.Unmarshal([]byte(task.OperationFileUrl), req)
				url, num, _ := pd.ChannelProductExport(req)

				//updateModel := models.TaskList{
				//	TaskStatus:     3,
				//	TaskDetail:     "成功",
				//	ResulteFileUrl: url,
				//	ModifyTime:     time.Now(),
				//	SuccessNum: num,
				//}
				updateModel.TaskStatus = 3
				updateModel.TaskDetail = "成功"
				updateModel.ResulteFileUrl = url
				updateModel.SuccessNum = num
				UpdateTaskListInfo(updateModel)

				//sql := "update `dc_product`.`task_list` set `task_status` = ?,task_detail=?,resulte_file_url=?,success_num = ?,modify_time=now() where `id` = ?"
				//_, err = db.Exec(sql, updateModel.TaskStatus, updateModel.TaskDetail, updateModel.ResulteFileUrl,updateModel.SuccessNum, updateModel.Id)
				//if err != nil {
				//	glog.Error(err.Error())
				//	//return false
				//}
				glog.Info("渠道--商品导出结束。。。", task.Id)
				break
			case 20, 21:
				//todo 平台--商品导出
				glog.Info("平台--实物商品导出执行。。。", task.Id)

				req := new(pc.QueryProductRequest)
				_ = json.Unmarshal([]byte(task.OperationFileUrl), req)

				url, num, _ := pd.ExportProductInfo(req)

				updateModel.TaskStatus = 3
				updateModel.ResulteFileUrl = url
				updateModel.SuccessNum = num
				UpdateTaskListInfo(updateModel)
				glog.Info("平台--实物商品导出结束。。。", task.Id)
				break
			case 35: //互联网医院商品价格-批量导入商品价格
				glog.Info("互联网医院商品价格-批量导入商品价格 excel导入执行。。。", task.Id)
				////更新任务状态-进行中
				UpdateTaskListInfo(updateModel)
				var taskRes pc.ImportExcelProductResponse

				res, err := pd.ImportHospitalProductPrice(task.OperationFileUrl, task)
				if err != nil {
					glog.Error(err)
					//return false
				}
				taskRes = *res

				//更新任务列表信息
				//数据拼装
				taskDetailjson, err := json.Marshal(&taskRes)
				if err != nil {
					glog.Error(err)
					//	return false
				}

				updateModel.TaskStatus = 3
				updateModel.TaskDetail = string(taskDetailjson)
				//updateModel.ResulteFileUrl = taskRes.QiniuUrl
				updateModel.ResulteFileUrl = task.OperationFileUrl
				updateModel.SuccessNum = taskRes.SuccessNum
				updateModel.FailNum = taskRes.FailNum
				updateModel.ExtendedData = "互联网医院商品价格-导入"
				UpdateTaskListInfo(updateModel)

				glog.Info("互联网医院商品价格-批量导入商品价格 excel导入结束。。。", task.Id)
				break
			case 36:
				//todo 互联网医院商品价格--导出
				glog.Info("平台--互联网医院商品价格导出执行。。。", task.Id)
				////更新任务状态-进行中
				UpdateTaskListInfo(updateModel)
				url, num, _ := pd.ExportHospitalProductPrice(cast.ToInt32(task.OperationFileUrl))
				updateModel.TaskStatus = 3
				updateModel.ResulteFileUrl = url
				updateModel.SuccessNum = num
				UpdateTaskListInfo(updateModel)
				glog.Info("平台--互联网医院商品价格导出结束。。。", task.Id)
				break
			case 37: //仓库白名单导入
				glog.Info("仓库白名单-批量导入excel导入执行。。。", task.Id)
				////更新任务状态-进行中
				UpdateTaskListInfo(updateModel)
				var taskRes pc.ImportExcelProductResponse

				res, err := pd.ImportWarehouseWhite(task.OperationFileUrl, task)
				if err != nil {
					glog.Error(err)
					//return false
				}
				taskRes = *res

				//更新任务列表信息
				//数据拼装
				taskDetailjson, err := json.Marshal(&taskRes)
				if err != nil {
					glog.Error(err)
					//	return false
				}

				updateModel.TaskStatus = 3
				updateModel.TaskDetail = string(taskDetailjson)
				//updateModel.ResulteFileUrl = taskRes.QiniuUrl
				updateModel.ResulteFileUrl = task.OperationFileUrl
				updateModel.SuccessNum = taskRes.SuccessNum
				updateModel.FailNum = taskRes.FailNum
				updateModel.ExtendedData = "仓库白名单-导入"
				UpdateTaskListInfo(updateModel)

				glog.Info("互联网医院商品价格-批量导入商品价格 excel导入结束。。。", task.Id)
				break
			case 38:
				//todo 仓库白名单--导出
				glog.Info("仓库白名单导出执行。。。", task.Id)
				////更新任务状态-进行中
				UpdateTaskListInfo(updateModel)
				url, num, _ := pd.ExportWarehouseWhite(task.OperationFileUrl)
				updateModel.TaskStatus = 3
				updateModel.ResulteFileUrl = url
				updateModel.SuccessNum = num
				UpdateTaskListInfo(updateModel)
				glog.Info("仓库白名单导出结束。。。", task.Id)
				break
			default:
				//如果以上都没有处理，则更新下最后创建时间
				updateTaskList(task.Id, db)
			}
		}()
	}
	wg.Wait()
	close(channelG)
	//如果以上都没有处理，则更新下最后创建时间
	updateTaskList(listId, db)
	return true
}

func DealAsyncTaskListByChannel(channelId int32) func() bool { //初始化db
	return func() bool {
		// 获取一个新任务
		var task = GetFirstUnFinishedTaskList(int(channelId), 102)
		redisConn := GetRedisConn()
		if kit.EnvCanCron() {
			defer redisConn.Close()
		}
		if task == nil {
			return true
		}
		logPrefix := fmt.Sprintf("DealAsyncTaskListByChannel渠道id=%d,taskId=%d", channelId, task.Id)
		glog.Info(logPrefix, "task run ...", kit.JsonEncode(task))

		updateModel := models.TaskList{
			Id:         task.Id,
			TaskStatus: 2,
		}

		//更新任务状态-进行中
		UpdateTaskListInfo(updateModel)
		if task.TaskContent == 4 { // 批量上架
			glog.Info(logPrefix, "task.TaskContent == 4 run ...")

			var fileUrl, taskDetail string
			var successNum, failNum int32
			var channelProductUpParams ChannelProductUpDown
			err := json.Unmarshal([]byte(task.OperationFileUrl), &channelProductUpParams)
			if err != nil {
				glog.Error(logPrefix, err)
				taskDetail = "序列化参数出错"
			} else {
				channelProductUpParams.TaskId = int(task.Id)
				// 1.执行上架
				channelProductUpParams.UpType = enum.RecordTypeUpBatch
				channelProductUpParams.UpPorudct()
				// 2.导出错误
				fileUrl, taskDetail, successNum, failNum = channelProductUpParams.Export()
			}
			// 更新任务状态-结束
			updateModel.TaskStatus = 3
			updateModel.TaskDetail = taskDetail
			updateModel.ResulteFileUrl = fileUrl
			updateModel.SuccessNum = successNum
			updateModel.FailNum = failNum
			UpdateTaskListInfo(updateModel)
			glog.Info(logPrefix, "task.TaskContent == 4 finish ...")
		}
		if task.TaskContent == 18 { // 批量下架
			glog.Info(logPrefix, "task.TaskContent == 18 run ...")
			var fileUrl, taskDetail string
			var successNum, failNum int32
			var channelProductUpParams ChannelProductUpDown
			err := json.Unmarshal([]byte(task.OperationFileUrl), &channelProductUpParams)
			if err != nil {
				glog.Error(logPrefix, err)
				taskDetail = "序列化参数出错"
			} else {
				// 1.执行下架
				channelProductUpParams.DownType = enum.DownRecordTypeBatch // 批量下架标识
				channelProductUpParams.DownPorudct()
				// 2.导出错误
				fileUrl, taskDetail, successNum, failNum = channelProductUpParams.Export()
			}
			updateModel.TaskStatus = 3
			updateModel.TaskDetail = taskDetail
			updateModel.ResulteFileUrl = fileUrl
			updateModel.SuccessNum = successNum
			updateModel.FailNum = failNum
			// 更新任务状态-结束
			UpdateTaskListInfo(updateModel)
			glog.Info(logPrefix, "task.TaskContent == 18 finish  ...")
		}
		if task.TaskContent == 12 { // 批量更新
			glog.Info(logPrefix, "task.TaskContent == 12 run ...", kit.JsonEncode(task))
			var pd Product
			switch task.ChannelId {
			case 1: //阿闻渠道
				var product pc.ChannelProductRequest
				if len(task.OperationFileUrl) > 0 {
					err := json.Unmarshal([]byte(task.OperationFileUrl), &product)
					if err != nil {
						glog.Error(logPrefix, "阿闻渠道批量编辑更新反序列化报错，err:", err, "TaskList是：", task)
						break
					}
				}
				if product.Product.Id > 0 {
					tokenStr := task.RequestHeader
					var model *models.LoginUserInfo
					json.Unmarshal([]byte(tokenStr), &model)
					ctx := context.WithValue(context.Background(), "user_info", model)
					bbp, err := pd.BatchEditChannelProduct(ctx, &product)

					//更新任务列表信息
					//数据拼装
					taskDetailjson := ""
					if bbp.SuccessNum > 0 && bbp.FailNum > 0 {
						taskDetailjson = "部分成功"
					} else {
						taskDetailjson = "成功"
					}
					if len(bbp.Error) > 0 { //如果还有错误
						taskDetailjson = "失败:" + bbp.Error
						//当mysql事务产生死锁时 进行重试  更新状态
						if strings.Contains(bbp.Error, "Deadlock") {
							updateModel.TaskStatus = 1
							updateModel.TaskDetail = "需重试"
							updateModel.SuccessNum = bbp.SuccessNum
							updateModel.FailNum = bbp.FailNum

							UpdateTaskListInfo(updateModel)
							break
						}
					}

					updateModel.TaskStatus = 3
					updateModel.TaskDetail = taskDetailjson
					updateModel.ResulteFileUrl = bbp.QiniuUrl
					updateModel.SuccessNum = bbp.SuccessNum
					updateModel.FailNum = bbp.FailNum

					UpdateTaskListInfo(updateModel)
					//todo 封装方法
					//sql := "update `dc_product`.`task_list` set `task_status` = ?,task_detail=?,resulte_file_url=?,modify_time=now() where `id` = ?"
					//_, err = engine.Exec(sql, updateModel.TaskStatus, taskDetailjson, updateModel.ResulteFileUrl, updateModel.Id)
					if err != nil {
						glog.Error(logPrefix, "阿闻渠道批量更新商品报错："+err.Error())
					}
				}
				break
			case 2:
				//美团编辑商品批量任务--创建连接
				//clientMt := GetMtProductClient()
				//defer clientMt.Close()
				var product pc.ChannelProductRequest
				if len(task.OperationFileUrl) > 0 {
					err := json.Unmarshal([]byte(task.OperationFileUrl), &product)
					if err != nil {
						glog.Error(logPrefix, "美团渠道批量编辑更新反序列化报错，err:", err, "TaskList是：", task)
						break
					}
				}
				if product.Product.Id > 0 {
					tokenStr := task.RequestHeader
					var model *models.LoginUserInfo
					json.Unmarshal([]byte(tokenStr), &model)
					glog.Info(logPrefix, "用户信息为", kit.JsonEncode(model))
					ctx := context.WithValue(context.Background(), "user_info", model)

					bbp, err := pd.BatchEditChannelProduct(ctx, &product)
					glog.Info(logPrefix, "pd.BatchEditChannelProduct返回数据为", kit.JsonEncode(bbp), "|请求数据为", kit.JsonEncode(product), "|错误为", kit.JsonEncode(err))
					if err != nil {
						glog.Error(logPrefix, "调用BatchEditChannelProduct失败，err:", err)
					}
					Dataclient := GetDataCenterClient()
					defer Dataclient.Close()
					var params dac.GetHospitalListByUserNoRequest
					params.UserNo = model.UserNo
					params.ChannelId = ChannelMtId
					out_result, err := Dataclient.RPC.GetHospitalListByUserNo(Dataclient.Ctx, &params)
					glog.Info(logPrefix, "获取用户有权限的店铺列表数据为", kit.JsonEncode(out_result), "|请求数为", kit.JsonEncode(params), "|错误为", kit.JsonEncode(err))

					if err != nil {
						glog.Error(logPrefix, err)
						return false
					}
					var finance_code_arr []string
					for _, v := range out_result.Data {
						finance_code_arr = append(finance_code_arr, v.StructOuterCode)
					}
					//通过门店财务编码查询渠道门店id
					appPoiCodeMap := GetAppPoiCodeByFinanceCode(finance_code_arr, 2)
					glog.Info(logPrefix, "GetAppPoiCodeByFinanceCode请求数据为", kit.JsonEncode(finance_code_arr), "|返回数据为", kit.JsonEncode(appPoiCodeMap))
					appPoiCodeSlice := []string{}
					for k := range appPoiCodeMap {
						if len(k) > 0 {
							appPoiCodeSlice = append(appPoiCodeSlice, k)
						}
					}
					glog.Info(logPrefix, "渠道门店id为", kit.JsonEncode(appPoiCodeSlice))
					if len(appPoiCodeSlice) == 0 {
						return true
					}
					var p Product
					resList := make([][]string, 0)
					session := NewDbConn().NewSession()
					defer session.Close()
					for _, appPoiCodeEle := range appPoiCodeSlice {
						ok, err := session.SQL("select 1 from product a  where  a.product_type = 2 and a.id = ? ", product.Product.Id).Exist()
						if err != nil {
							glog.Error(logPrefix, "查询数据库异常", err.Error())
						}
						glog.Info(logPrefix, "查询商品是否存在,商品id为", product.Product.Id, "|ok值为", ok, "|错误信息为", kit.JsonEncode(err))
						if ok {
							continue // 跳过虚拟商品的同步mt
						}

						storeMasterId, err := GetAppChannelByFinanceCode(appPoiCodeMap[appPoiCodeEle])
						glog.Info(logPrefix, "GetAppChannelByFinanceCode获取第三方门店id,入参为", appPoiCodeMap[appPoiCodeEle], "|返回数据为", storeMasterId, "|错误为", kit.JsonEncode(err))
						if err != nil {
							glog.Error(logPrefix, "DealAsyncTaskListByChannel,", "GetAppChannelByFinanceCode failed,", appPoiCodeMap[appPoiCodeEle], err)
							continue
						}
						//storeMasterId := GetAppChannelByStoreId(appPoiCodeEle)

						gmdr := pc.GetMtProductDataRequest{
							ProductId:   []int32{product.Product.Id},
							ChannelId:   2,
							FinanceCode: []string{appPoiCodeMap[appPoiCodeEle]},
							Type:        1,
						}
						mtProductData, err := p.GetMtProductData(ctx, &gmdr)
						glog.Info(logPrefix, " p.GetMtProductData返回数据为", kit.JsonEncode(mtProductData), "|入参为", kit.JsonEncode(gmdr), "|错误为", kit.JsonEncode(err))
						if err != nil {
							row := []string{appPoiCodeMap[appPoiCodeEle], cast.ToString(product.Product.Id), err.Error()}
							resList = append(resList, row)
							glog.Error(logPrefix, err)
							continue
						} else if mtProductData.Code != 200 || len(mtProductData.Data) == 0 {
							row := []string{appPoiCodeMap[appPoiCodeEle], cast.ToString(product.Product.Id), "格式化美团商品失败"}
							resList = append(resList, row)
							continue
						}

						foodData := []*et.RetailBatchinitdata{}
						if json.Unmarshal([]byte(mtProductData.Data), &foodData) != nil {
							glog.Error(logPrefix, "解析美团商品信息失败: ", err)
							continue
						}
						//存储原来的上下架状态。key为商品ID，value为美团上下架状态
						statusMap := make(map[int32]int32)
						r := make([]MtMap, 0)
						session := NewDbConn().NewSession()
						defer session.Close()
						session.Table("channel_store_product").Where("finance_code = ?", appPoiCodeMap[appPoiCodeEle]).And("channel_id = 2").In("product_id", product.Product.Id).Find(&r)
						glog.Info(logPrefix, "上架表信息", kit.JsonEncode(r))
						for _, v := range r {
							statusMap[v.Product_id] = v.Up_down_state
						}

						//查询库存
						skuCodes := []*ic.SkuCodeInfo{}
						for _, sku := range foodData[0].Skus {
							skuCode := &ic.SkuCodeInfo{}
							skuCode.FinanceCode = appPoiCodeMap[appPoiCodeEle]
							skuCode.Sku = sku.SkuId
							skuCodes = append(skuCodes, skuCode)
						}
						stockMap, err := GetStockInfoBySkuCode(0, skuCodes, ChannelMtId, 1) //查询本地的库存
						glog.Info(logPrefix, "GetStockInfoBySkuCode返回数据为", kit.JsonEncode(stockMap), "|请求数据为", kit.JsonEncode(skuCodes), "|错误为", kit.JsonEncode(err))
						if err != nil {
							glog.Error(logPrefix, "GetStockInfoBySkuCode查询库存失败：", err)
							row := []string{appPoiCodeMap[appPoiCodeEle], cast.ToString(product.Product.Id), "查询库存失败"}
							resList = append(resList, row)
						}

						// 批量操作后，无法判断商品是否美团已存在，需一条一条处理。 速度慢
						for k := range foodData {
							OperateType := 0 //商品是否存在美团 -- 如果不确定的话，则更新库存为0
							//调用美团的商品信息
							clientMt := GetMtGlobalProductClient()
							//defer clientMt.Close()
							retailGetRequest := et.RetailGetRequest{}
							retailGetRequest.AppPoiCode = appPoiCodeEle
							retailGetRequest.StoreMasterId = storeMasterId
							retailGetRequest.AppFoodCode = foodData[k].AppFoodCode
							retailRes, retailErr := clientMt.RPC.RetailGet(clientMt.Ctx, &retailGetRequest)
							glog.Info(logPrefix, "获取美团信息为", kit.JsonEncode(retailRes), "|请求数据为", kit.JsonEncode(retailGetRequest), "|返回错误为", kit.JsonEncode(retailErr))
							if retailErr == nil {
								if retailRes.Code == 200 {
									OperateType = 2
								}
							}

							if statusMap[cast.ToInt32(foodData[k].AppFoodCode)] == 0 {
								foodData[k].IsSoldOut = 1
							} else {
								foodData[k].IsSoldOut = 0
							}
							if len(foodData[k].Skus) > 0 {
								//单sku处理方式
								foodDataSku := foodData[k].Skus
								mapkey := appPoiCodeMap[appPoiCodeEle] + ":" + foodDataSku[0].SkuId
								if OperateType != 2 {
									// 创建的时候需要构建价格
									getPrice, err1 := GetPrice(int(ChannelMtId), foodDataSku[0].SkuId, appPoiCodeMap[appPoiCodeEle])
									if err1 != nil {
										glog.Error(logPrefix, "获取商品价格失败", err1)
									} else {
										foodData[k].Skus[0].Price = cast.ToString(getPrice)
									}
								}
								if _, ok := stockMap[mapkey]; ok {
									if OperateType != 2 {
										foodData[k].Skus[0].Stock = cast.ToString(stockMap[mapkey])

									} else {
										foodData[k].Skus[0].Stock = ""
									}
								}
							}

							var retailBatchinitdataRequest = &et.RetailBatchinitdataRequest{
								AppPoiCode:    appPoiCodeEle,
								FoodData:      foodData,
								OperateType:   int32(OperateType),
								StoreMasterId: storeMasterId,
							}
							// 编辑也无需判断第三方商品id是否存在， 因为上面有查第三方商品是否存在， 存在则编辑，不存在则创建
							initRes, err := clientMt.RPC.RetailBatchinitdata(context.Background(), retailBatchinitdataRequest)
							glog.Info(logPrefix, "1111美团创建/更新商品返回数据为", kit.JsonEncode(initRes), "----------请求参数为", kit.JsonEncode(retailBatchinitdataRequest), "----------错误为", kit.JsonEncode(err))
							// v7.0.11 同步第三方商品ID回来
							MtProductThirdId(logPrefix+enum.UpdateProductThirdIdFrom6, initRes, foodData, appPoiCodeMap[appPoiCodeEle], retailBatchinitdataRequest.OperateType)
							if err != nil {
								glog.Error(logPrefix, "RetailBatchinitdata美团商品编辑失败: ", err)
								row := []string{appPoiCodeMap[appPoiCodeEle], cast.ToString(product.Product.Id), "美团商品编辑失败"}
								resList = append(resList, row)
							}
							if initRes.Code != 200 {
								row := []string{appPoiCodeMap[appPoiCodeEle], cast.ToString(product.Product.Id), initRes.Error.Msg}
								resList = append(resList, row)
							}

						}
					}
					////更新任务列表信息
					////数据拼装
					//taskDetailjson, err := json.Marshal(bbp)
					//if err != nil {
					//	glog.Error(err)
					//}

					total := len(appPoiCodeSlice)
					glog.Info(logPrefix, "美团批量更新,appPoiCodeSlice条数为", total)
					//数据拼装
					taskDetailjson := ""
					if bbp.SuccessNum > 0 && bbp.FailNum > 0 {
						taskDetailjson = "部分成功"
					} else {
						taskDetailjson = "成功"
					}
					if len(bbp.Error) > 0 { //如果还有错误
						taskDetailjson = "失败:" + bbp.Error
					}

					excelUrl := ""
					if len(resList) > 0 {
						headRow := append([]string{}, "财务编码", "平台商品ID", "失败原因")
						errList := append([][]string{}, headRow)
						errList = append(errList, resList...)
						excelUrl, err = ExportProductErr(errList)
						if err != nil {
							glog.Error(logPrefix, "错误信息上传失败; err: "+err.Error())
						}
					}
					failNum := len(resList)
					updateModel.TaskStatus = 3
					updateModel.TaskDetail = taskDetailjson
					updateModel.ResulteFileUrl = excelUrl
					updateModel.SuccessNum = cast.ToInt32(total - failNum)
					updateModel.FailNum = cast.ToInt32(failNum)
					UpdateTaskListInfo(updateModel)

					//sql := "update `dc_product`.`task_list` set `task_status` = ?,task_detail=?,resulte_file_url=?,modify_time=now() where `id` = ?"
					//_, err = engine.Exec(sql, updateModel.TaskStatus, taskDetailjson, updateModel.ResulteFileUrl, updateModel.Id)
					if err != nil {
						glog.Error(logPrefix, err.Error())
					}
				}
				break
			case 3:
				//饿了么编辑商品批量任务--创建连接

				var product pc.ChannelProductRequest
				if len(task.OperationFileUrl) > 0 {
					err := json.Unmarshal([]byte(task.OperationFileUrl), &product)
					if err != nil {
						updateModel.TaskStatus = 3
						updateModel.TaskDetail = "饿了么渠道批量编辑更新反序列化报错"
						UpdateTaskListInfo(updateModel)
						glog.Error(logPrefix, "饿了么渠道批量编辑更新反序列化报错，err:", err, "TaskList是：", task)
						break
					}
				}
				// todo 多门店多商品批量创建更新
				if product.Product.Id > 0 {
					tokenStr := task.RequestHeader
					var model *models.LoginUserInfo
					json.Unmarshal([]byte(tokenStr), &model)

					Dataclient := GetDataCenterClient()
					defer Dataclient.Close()
					var params dac.GetHospitalListByUserNoRequest
					params.UserNo = model.UserNo
					params.ChannelId = ChannelElmId

					//一个用户只能同步他权限内门店的商品
					out_result, err := Dataclient.RPC.GetHospitalListByUserNo(Dataclient.Ctx, &params)
					glog.Info(logPrefix, "Dataclient.RPC.GetHospitalListByUserNo请求数据为", kit.JsonEncode(params), "|数据返回为", kit.JsonEncode(out_result), "|错误为", kit.JsonEncode(err))
					if err != nil {
						glog.Error(logPrefix, "饿了么渠道批量编辑更新未查询到用户门店信息，查询参数", kit.JsonEncode(params), ",错误信息", err, "TaskListId是：", task.Id)
						updateModel.TaskStatus = 3
						updateModel.TaskDetail = "饿了么渠道批量编辑更新查询用户门店信息出错" + err.Error()
						UpdateTaskListInfo(updateModel)
						return true
					}
					if len(out_result.Data) == 0 {
						glog.Error(logPrefix, "饿了么渠道批量编辑更新未查询到用户门店信息，查询参数", kit.JsonEncode(params), "TaskListId是：", task.Id)
						updateModel.TaskStatus = 3
						updateModel.TaskDetail = "饿了么渠道批量编辑更新未查询到用户门店信息"
						UpdateTaskListInfo(updateModel)
						return true
					}

					var finance_code_arr []string
					for _, v := range out_result.Data {
						finance_code_arr = append(finance_code_arr, v.StructOuterCode)
					}

					//通过门店财务编码查询渠道门店id

					appPoiCodeMap := GetAppPoiCodeByFinanceCode(finance_code_arr, 3)
					glog.Info(logPrefix, "GetAppPoiCodeByFinanceCode请求数据为", kit.JsonEncode(finance_code_arr), "|数据返回为", kit.JsonEncode(appPoiCodeMap))
					appPoiCodeSlice := []string{}

					for k := range appPoiCodeMap {
						if len(k) > 0 {
							appPoiCodeSlice = append(appPoiCodeSlice, k)
						}
					}
					if len(appPoiCodeSlice) == 0 {
						//todo 打日志
						updateModel.TaskStatus = 3
						updateModel.TaskDetail = "可更新门店数为0"
						UpdateTaskListInfo(updateModel)
						return true
					}

					st := new(Product)

					resp, err := st.GetChannelWarehouses(finance_code_arr, ChannelElmId)
					glog.Info(logPrefix, "GetChannelWarehouses返回数据为", kit.JsonEncode(resp), "|err=", kit.JsonEncode(err))

					if err != nil {
						updateModel.TaskStatus = 3
						updateModel.TaskDetail = "调用GetChannelWarehouses失败"
						UpdateTaskListInfo(updateModel)
						glog.Error(logPrefix, utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
						return true
					}
					//仓库编号=>仓库类型的map

					//子龙门店仓 仓库编号=财务编码 而前置仓仓库编号并不是门店财务编码
					//GetWarehouseInfoByFanceCodes 返回的code实际上是门店仓与前置仓对应门店的财务编码  而非一定是仓库的编号，
					warehouseMap := make(map[string]int32, 0)
					for _, datum := range resp {
						warehouseMap[datum.ShopId] = int32(datum.Category)
					}
					ctx := context.WithValue(context.Background(), "user_info", model)
					resList := make([][]string, 0)
					var c Product

					if model.IsGeneralAccount {
						_, err = pd.BatchEditChannelProduct(ctx, &product)
						if err != nil {
							glog.Error(logPrefix, "调用BatchEditChannelProduct失败，err:", err)
						}
						productToSku := make(map[int32]int32)
						productId := product.Product.Id

						//图片转换
						var picList []string
						photoList := strings.Split(product.Product.Pic, ",")

						client := GetMtProductClient()
						defer client.Close()

						for _, picV := range photoList {
							if strings.TrimSpace(picV) == "" {
								continue
							}
							var pic et.UploadPictureRequest
							pic.Url = picV
							//商品图片的上传直接使用瑞鹏的appChannel就好，该方法仅仅是饿了么上传图片并返回他的图片地址，所以与应用无关
							pic.AppChannel = 1
							//todo 循环请求 饿了么 可使用协程优化
							res, err := client.ELMPRODUCT.UploadPicture(context.Background(), &pic)
							glog.Info(logPrefix, "client.ELMPRODUCT.UploadPicture请求数据为", kit.JsonEncode(pic), "|返回数据为", kit.JsonEncode(res), "|错误为", kit.JsonEncode(err))
							if err != nil {
								glog.Errorf(logPrefix, "饿了么商品图片上传失败，全部门店，productId：%d，ERR：%v, pic: %s", productId, err.Error(), picV)
								continue
							}
							if res.Code != 200 {
								glog.Errorf(logPrefix, "饿了么商品图片上传失败，全部门店，productId：%d，ERR：%v, pic: %s", productId, res.Error, picV)
								continue
							}
							picList = append(picList, res.Data)
						}
						if len(picList) == 0 {
							resList = append(resList, []string{"全部门店", cast.ToString(productId), "图片不可为空"})
							updateModel.TaskStatus = 3
							updateModel.TaskDetail = "图片不可为空"
							UpdateTaskListInfo(updateModel)
							return true
						}

						var skuThirdMap = make(map[int32]bool)
						for _, i := range product.SkuInfo[0].SkuThird {
							if i.ErpId == 2 {
								skuThirdMap[4] = true
							}
							if i.ErpId == 4 {
								skuThirdMap[3] = true
							}
						}

						for _, appPoiCodeEle := range appPoiCodeSlice {
							financeCode := appPoiCodeMap[appPoiCodeEle]

							if _, ok := skuThirdMap[warehouseMap[financeCode]]; !ok {
								if warehouseMap[financeCode] == 3 {
									resList = append(resList, []string{financeCode, cast.ToString(productId), "缺少子龙货号"})
								} else if warehouseMap[financeCode] == 4 {
									resList = append(resList, []string{financeCode, cast.ToString(productId), "缺少A8货号"})
								}
								continue
							}

							clientElm := GetMtGlobalProductClient()
							//取快照信息
							var snapShots *pc.ChannelProductSnapshot
							if res, err := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
								ChannelId:   3,
								ProductId:   []int32{productId},
								FinanceCode: financeCode,
							}); err != nil {
								glog.Error(logPrefix, err)
								continue
							} else if res.Code != 200 || len(res.Details) == 0 {
								b2, _ := json.Marshal(res)
								glog.Info(logPrefix, "b2 ", string(b2))
								continue
							} else {
								snapShots = res.Details[0]
							}
							glog.Info(logPrefix, "QueryChannelProductSnapshot获取快照：", kit.JsonEncode(snapShots), "财务编码为", financeCode)
							var newSnap pc.ChannelProductRequest
							if err := json.Unmarshal([]byte(snapShots.JsonData), &newSnap); err != nil {
								glog.Errorf(logPrefix, "反序列化报错：门店id: %s, product: %d, err: %s", financeCode, productId, err.Error())
								//clientElm.Close()
								continue
							}

							if len(newSnap.SkuInfo[0].BarCode) == 0 {
								newSnap.SkuInfo[0].BarCode = cast.ToString(newSnap.SkuInfo[0].SkuId)
							}

							//获取appChannel
							appChannel, err := GetAppChannelByFinanceCode(financeCode)
							if err != nil {
								glog.Errorf(logPrefix, "获取门店appChannel出错：门店id: %s, product: %d, err: %s", financeCode, productId, err.Error())
								continue
							}
							if appChannel == 0 {
								glog.Errorf(logPrefix, "获取门店appChannel值不对：门店id: %s, product: %d", financeCode, productId)
								continue
							}

							productToSku[productId] = newSnap.SkuInfo[0].SkuId
							var sku et.UpdateElmShopSkuRequest
							GetElmProductListReq := &et.ElmGetProductListRequest{
								ShopId:          appPoiCodeEle,
								Page:            1,
								Pagesize:        1,
								Upc:             cast.ToString(newSnap.SkuInfo[0].BarCode),
								IncludeCateInfo: 1,
								AppChannel:      appChannel,
							}
							resPro, err := clientElm.ELMPRODUCT.GetElmProductList(context.Background(), GetElmProductListReq)
							glog.Info(logPrefix, "clientElm.ELMPRODUCT.GetElmProductList请求数据为", kit.JsonEncode(GetElmProductListReq), "返回数据为", kit.JsonEncode(resPro), "|错误为", kit.JsonEncode(err))
							if err != nil {
								glog.Error(logPrefix, "查询饿了么商品失败, err: ", err.Error())
								resList = append(resList, []string{financeCode, cast.ToString(productId), "查询饿了么商品失败, err: " + err.Error()})
								//clientElm.Close()
								continue
							}
							if resPro.Code != 200 {
								glog.Error(logPrefix, "查询饿了么商品失败, err: ", resPro.Error)
								resList = append(resList, []string{financeCode, cast.ToString(productId), resPro.Error})
								//clientElm.Close()
								continue
							} else if resPro.Data != nil && len(resPro.Data.List) > 0 {
								if resPro.Data.List[0].CustomSkuId == cast.ToString(newSnap.SkuInfo[0].SkuId) {
									sku.SkuId = resPro.Data.List[0].CustomSkuId
								} else {
									resList = append(resList, []string{financeCode, cast.ToString(productId), "编辑失败, UPC已存在"})
									//clientElm.Close()
									continue
								}
							} else {
								//饿了么不存在此商品或者不存在快照,且商品没有第三方id则不调用接口
								if snapShots.ProductThirdId != "" {
									resList = append(resList, []string{financeCode, cast.ToString(productId), "编辑失败, kuid已上架渠道，但商品条码在阿闻与渠道不一致"})
									continue
								}
							}
							//clientElm.Close()

							sku.ShopId = appPoiCodeEle
							sku.Upc = newSnap.SkuInfo[0].BarCode
							// 图片
							for k, v := range picList {
								photo := et.SkuPhotos{}
								photo.IsMaster = 0
								if k == 0 {
									photo.IsMaster = 1
								}
								photo.Url = v
								sku.Photos = append(sku.Photos, &photo)
							}
							newSnap.FinanceCode = financeCode
							// 富文本
							//sku.Rtf = rtfStr
							sku.Desc = product.Product.ContentPc
							//应用渠道
							sku.AppChannel = appChannel
							res, err := c.UpdateElmProduct(&newSnap, sku)
							glog.Info(logPrefix, "c.UpdateElmProduct请求数据为", kit.JsonEncode(sku), "|返回数据为", kit.JsonEncode(res), "|返回错误为", kit.JsonEncode(err))
							if err != nil {
								resList = append(resList, []string{financeCode, cast.ToString(productId), "更新饿了么商品失败"})
								continue
							}
							if res.SyncError != "初始值" {
								UpdateProductThirdIdBySpu(logPrefix+enum.UpdateProductThirdIdFrom16, int(productId), ChannelElmId, financeCode, res.SyncError)
							}

							if res.Code != 200 {
								resList = append(resList, []string{financeCode, cast.ToString(productId), res.Message})
								continue
							}
						}
					} else {
						//非总账号只能更新价格
						for _, appPoiCodeEle := range appPoiCodeSlice {
							financeCode := appPoiCodeMap[appPoiCodeEle]
							SingleUpdatePriceToElmReq := &pc.ChannelProductRequest{
								FinanceCode: financeCode,
								Product:     product.Product,
								SkuInfo:     product.SkuInfo,
								ProductAttr: product.ProductAttr,
								Tags:        product.Tags,
							}
							out, err := c.SingleUpdatePriceToElm(ctx, SingleUpdatePriceToElmReq)
							glog.Info(logPrefix, "c.SingleUpdatePriceToElm请求数据为", kit.JsonEncode(SingleUpdatePriceToElmReq), "|数据返回为", kit.JsonEncode(out), "|错误为", kit.JsonEncode(err))
							if err != nil {
								resList = append(resList, []string{financeCode, cast.ToString(product.Product.Id), "更新饿了么价格失败, err: " + err.Error()})
								continue
							}
							if out.Code != 200 {
								resList = append(resList, []string{financeCode, cast.ToString(product.Product.Id), out.Message})
							}
						}
					}

					taskDetailjson := ""
					totalNum := len(appPoiCodeSlice)
					failNum := len(resList)
					successNum := totalNum - failNum
					if failNum == 0 {
						taskDetailjson = "成功"
					} else if failNum < totalNum {
						taskDetailjson = "部分成功"
					} else if failNum >= totalNum { //如果还有错误
						taskDetailjson = "失败"
					}
					excelUrl := ""
					if len(resList) > 0 {
						headRow := append([]string{}, "财务编码", "平台商品ID", "失败原因")
						errList := append([][]string{}, headRow)
						errList = append(errList, resList...)
						excelUrl, err = ExportProductErr(errList)
						if err != nil {
							glog.Error(logPrefix, "错误信息上传失败; err: "+err.Error())
						}
					}

					updateModel.TaskStatus = 3
					updateModel.TaskDetail = taskDetailjson
					updateModel.ResulteFileUrl = excelUrl
					updateModel.SuccessNum = cast.ToInt32(successNum)
					updateModel.FailNum = cast.ToInt32(failNum)
					UpdateTaskListInfo(updateModel)
				}
			case 4: //京东商品渠道批量编辑更新
				var product pc.ChannelProductRequest
				if len(task.OperationFileUrl) > 0 {
					err := json.Unmarshal([]byte(task.OperationFileUrl), &product)
					if err != nil {
						glog.Error("京东渠道批量编辑更新反序列化报错，err:", err, "TaskList是：", task)
						break
					}
				}
				if product.Product.Id > 0 {
					thirdIdReq := &pc.GetThirdIdByPidRequest{
						CategoryId: cast.ToString(product.Product.ChannelCategoryId),
						ChannelId:  "4",
					}
					thirdIdRes, err := pd.GetThirdIdByPid(context.Background(), thirdIdReq)
					if err != nil {
						glog.Error("根据product_id查询第三方分类id失败，err:", err)
					}

					Mtclient := et.GetExternalClient()
					defer Mtclient.Close()
					in := new(et.JddjUpdateGoodsListRequest)
					in.OutSkuId = cast.ToString(product.SkuInfo[0].SkuId)
					in.TraceId = cast.ToString(product.Product.Id) + "-" + cast.ToString(time.Now().Nanosecond())
					in.ShopCategories = []string{thirdIdRes.ThirdId}            //京东店内分类--店内分类
					in.CategoryId = 24479                                       //商品分类--商品分类
					in.SkuName = product.Product.Name                           //商品名称--商品名称
					in.Slogan = product.Product.SellingPoint                    //商品卖点--商品广告词
					for _, v := range strings.Split(product.Product.Pic, ",") { //商品图片--商品管理
						if v != "" {
							in.Images = append(in.Images, v)
						}
					}
					var params_excel [][]string
					int_err := 0
					var exl_str []string
					exl_str = append(exl_str, cast.ToString(product.Product.Id))
					in.SkuPrice = product.SkuInfo[0].StorePrice //门店仓价格--商品价格
					in.ProductDesc = product.Product.ContentPc
					in.Weight = cast.ToFloat32(product.SkuInfo[0].WeightForUnit)
					in.BrandId = int64(product.Product.BrandId)
					in.FixedStatus = 1

					var model *models.LoginUserInfo
					storeMasterId, err := GetAppChannelByFinanceCode(product.FinanceCode)
					if err != nil {
						glog.Error("京东商品渠道批量编辑更新 GetAppChannelByFinanceCode出错：门店id:", product.FinanceCode, err.Error())
						msg := "调用失败，商品信息同步到京东失败:" + err.Error()
						exl_str = append(exl_str, "调用GetAppChannelByFinanceCode接口失败，err:"+msg)
						int_err++
						params_excel = append(params_excel, exl_str)
					} else {
						in.StoreMasterId = storeMasterId

						glog.Info("调用JddjUpdateGoodsList接口,输入:", in)
						res, err := Mtclient.JddjProduct.JddjUpdateGoodsList(Mtclient.Ctx, in)
						if err != nil {
							msg := "调用失败，商品信息同步到京东失败:" + err.Error()
							glog.Error("调用JddjUpdateGoodsList接口失败，err:", msg)
							exl_str = append(exl_str, "调用JddjUpdateGoodsList接口失败，err:"+msg)
							int_err++
							params_excel = append(params_excel, exl_str)

						} else if strings.Contains(res.Message, "失败") {
							msg := "调用成功，商品信息同步失败:" + res.Message
							glog.Error("调用JddjUpdateGoodsList接口失败，调用成功，商品信息同步失败,err:", msg)
							exl_str = append(exl_str, "调用JddjUpdateGoodsList接口失败，调用成功，商品信息同步失败，err:"+msg)
							int_err++
							params_excel = append(params_excel, exl_str)
						} else {
							glog.Info("商品信息同步成功,调用公共方法，遍历所有门店，更新快照...")
						}
						tokenStr := task.RequestHeader
						err = json.Unmarshal([]byte(tokenStr), &model)
						if err != nil {
							glog.Error("反序列化失败,err:", err.Error())
							exl_str = append(exl_str, "反序列化失败 err:"+err.Error())
							int_err++
							params_excel = append(params_excel, exl_str)
						}

					}

					// glog.Info("调用JddjUpdateGoodsList接口,输入:", in)
					// res, err := Mtclient.JddjProduct.JddjUpdateGoodsList(Mtclient.Ctx, in)
					// if err != nil {
					// 	msg := "调用失败，商品信息同步到京东失败:" + err.Error()
					// 	glog.Error("调用JddjUpdateGoodsList接口失败，err:", msg)
					// 	exl_str = append(exl_str, "调用JddjUpdateGoodsList接口失败，err:"+msg)
					// 	int_err++
					// 	params_excel = append(params_excel, exl_str)

					// } else if strings.Contains(res.Message, "失败") {
					// 	msg := "调用成功，商品信息同步失败:" + res.Message
					// 	glog.Error("调用JddjUpdateGoodsList接口失败，调用成功，商品信息同步失败,err:", msg)
					// 	exl_str = append(exl_str, "调用JddjUpdateGoodsList接口失败，调用成功，商品信息同步失败，err:"+msg)
					// 	int_err++
					// 	params_excel = append(params_excel, exl_str)
					// } else {
					// 	glog.Info("商品信息同步成功,调用公共方法，遍历所有门店，更新快照...")
					// }
					// tokenStr := task.RequestHeader
					// var model *models.LoginUserInfo
					// err = json.Unmarshal([]byte(tokenStr), &model)
					// if err != nil {
					// 	glog.Error("反序列化失败,err:", err.Error())
					// 	exl_str = append(exl_str, "反序列化失败 err:"+err.Error())
					// 	int_err++
					// 	params_excel = append(params_excel, exl_str)
					// }

					//错误判断
					if int_err > 0 && len(params_excel) > 0 {
						var url string
						url, err = ExportProductErr(params_excel)
						bbp_Message := "失败"
						if err != nil {
							glog.Error("错误信息上传失败; err: " + err.Error())
							bbp_Message = "错误信息上传失败; err: " + err.Error()
						}
						//updateModel := models.TaskList{
						//	TaskStatus:     3,
						//	TaskDetail:     bbp_Message,
						//	ResulteFileUrl: url,
						//	ModifyTime:     time.Now(),
						//}
						updateModel.Id = task.Id
						updateModel.TaskStatus = 3
						updateModel.TaskDetail = bbp_Message
						updateModel.ResulteFileUrl = url
						UpdateTaskListInfo(updateModel)
						//sql := "update `dc_product`.`task_list` set `task_status` = ?,task_detail=?,resulte_file_url=?,modify_time=now() where `id` = ?"
						//_, err = engine.Exec(sql, updateModel.TaskStatus, updateModel.TaskDetail, updateModel.ResulteFileUrl, updateModel.Id)
						//if err != nil {
						//	glog.Error(err.Error())
						//}
						return true
					} else {
						ctx := context.WithValue(context.Background(), "user_info", model)
						bbp, err := pd.BatchEditChannelProduct(ctx, &product)
						if err != nil {
							glog.Error("调用BatchEditChannelProduct失败，err:", err)
						}
						//更新任务列表信息
						//数据拼装
						taskDetailjson := ""
						if bbp.SuccessNum > 0 && bbp.FailNum > 0 {
							taskDetailjson = "部分成功"
						} else {
							taskDetailjson = "成功"
						}
						//updateModel := models.TaskList{
						//	TaskStatus:     3,
						//	TaskDetail:     taskDetailjson,
						//	ResulteFileUrl: bbp.QiniuUrl,
						//	ModifyTime:     time.Now(),
						//}
						//updateModel.Id = task.Id
						updateModel.TaskStatus = 3
						updateModel.TaskDetail = taskDetailjson
						updateModel.ResulteFileUrl = bbp.QiniuUrl
						updateModel.SuccessNum = bbp.SuccessNum
						updateModel.FailNum = bbp.FailNum
						UpdateTaskListInfo(updateModel)
						//sql := "update `dc_product`.`task_list` set `task_status` = ?,task_detail=?,resulte_file_url=?,modify_time=now() where `id` = ?"
						//_, err = engine.Exec(sql, updateModel.TaskStatus, updateModel.TaskDetail, updateModel.ResulteFileUrl, updateModel.Id)
						//if err != nil {
						//	glog.Error(err.Error())
						//}
					}
				}
				break
			case 9: // 医疗互联网渠道
				var product pc.ChannelProductRequest
				if len(task.OperationFileUrl) > 0 {
					err := json.Unmarshal([]byte(task.OperationFileUrl), &product)
					if err != nil {
						glog.Error("阿闻渠道批量编辑更新反序列化报错，err:", err, "TaskList是：", task)
						break
					}
				}
				if product.Product.Id > 0 {
					tokenStr := task.RequestHeader
					var model *models.LoginUserInfo
					json.Unmarshal([]byte(tokenStr), &model)
					ctx := context.WithValue(context.Background(), "user_info", model)
					bbp, err := pd.BatchEditChannelProduct(ctx, &product)

					//更新任务列表信息
					//数据拼装
					taskDetailjson := ""
					if bbp.SuccessNum > 0 && bbp.FailNum > 0 {
						taskDetailjson = "部分成功"
					} else {
						taskDetailjson = "成功"
					}
					if len(bbp.Error) > 0 { //如果还有错误
						taskDetailjson = "失败:" + bbp.Error
						//当mysql事务产生死锁时 进行重试  更新状态
						if strings.Contains(bbp.Error, "Deadlock") {
							updateModel.TaskStatus = 1
							updateModel.TaskDetail = "需重试"
							updateModel.SuccessNum = bbp.SuccessNum
							updateModel.FailNum = bbp.FailNum

							UpdateTaskListInfo(updateModel)
							break
						}
					}

					updateModel.TaskStatus = 3
					updateModel.TaskDetail = taskDetailjson
					updateModel.ResulteFileUrl = bbp.QiniuUrl
					updateModel.SuccessNum = bbp.SuccessNum
					updateModel.FailNum = bbp.FailNum

					UpdateTaskListInfo(updateModel)
					//todo 封装方法
					//sql := "update `dc_product`.`task_list` set `task_status` = ?,task_detail=?,resulte_file_url=?,modify_time=now() where `id` = ?"
					//_, err = engine.Exec(sql, updateModel.TaskStatus, taskDetailjson, updateModel.ResulteFileUrl, updateModel.Id)
					if err != nil {
						glog.Error("阿闻渠道批量更新商品报错：" + err.Error())
					}
				}
				break
			default:
				//如果以上都没有处理，则更新下最后创建时间
				updateModel.TaskStatus = 3
				updateModel.TaskDetail = "渠道不支持"
				UpdateTaskListInfo(updateModel)
			}
			glog.Info("task.TaskContent == 12 finish ...")
		}
		if task.TaskContent == 42 {
			glog.Info("task.TaskContent == 24 run ...")
			var channelProductUpParams models.ChannelProductSpecialtyModel
			err := json.Unmarshal([]byte(task.OperationFileUrl), &channelProductUpParams)
			if err != nil {
				glog.Error(err)
			}

			p := Product{}
			//先查询这批快照出来
			//取快照信息
			ids := make([]int32, 0)
			total := len(channelProductUpParams.ProductIds)
			FailNum := 0
			//数据拼装
			taskDetailjson := ""

			//通过门店财务编码查询渠道门店id
			appPoiCodeMap := GetAppPoiCodeByFinanceCode(channelProductUpParams.FinanceCodes, 2)
			appPoiCodeSlice := []string{}
			for k := range appPoiCodeMap {
				if len(k) > 0 {
					appPoiCodeSlice = append(appPoiCodeSlice, k)
				}
			}
			if len(appPoiCodeSlice) == 0 {
				taskDetailjson = "失败:没有可用的渠道门店"
			}

			in := &pc.BatchToMTRequest{
				ProductId:       strings.Join(channelProductUpParams.ProductIds, ","),
				ChannelId:       2,
				IsAll:           0,
				UserNo:          task.CreateId,
				FinanceCode:     strings.Join(channelProductUpParams.FinanceCodes, ","),
				FinanceCodeList: strings.Join(channelProductUpParams.FinanceCodes, ","),
			}

			for _, x := range channelProductUpParams.ProductIds {
				ids = append(ids, cast.ToInt32(x))
			}
			var snapShots []*pc.ChannelProductSnapshot
			if res, err := p.QueryChannelProductSnapshot(context.Background(), &pc.ChannelProductSnapshotRequest{
				ChannelId:   in.ChannelId,
				ProductId:   ids,
				FinanceCode: in.FinanceCode,
			}); err != nil {
				glog.Error(err)

			} else if res.Code != 200 || len(res.Details) == 0 {
				b2, _ := json.Marshal(res)
				glog.Info("b2 ", string(b2))

			} else {
				snapShots = res.Details
			}
			tokenStr := task.RequestHeader
			var model *models.LoginUserInfo
			json.Unmarshal([]byte(tokenStr), &model)
			ctx := context.WithValue(context.Background(), "user_info", model)
			resList := make([][]string, 0)
			mapsjon := make(map[int32]*pc.ChannelProductRequest)
			for _, v := range snapShots {

				jsonData := v.JsonData
				channelProductReq := new(pc.ChannelProductRequest)
				err = json.Unmarshal([]byte(jsonData), channelProductReq)
				if err != nil {
					glog.Error("批量力荐", "，json解析失败，", err, "，json：", jsonData)
					continue
				}
				if channelProductUpParams.IsSpecialty != -1 {
					channelProductReq.Product.IsRecommend = cast.ToInt32(channelProductUpParams.IsSpecialty)
				}
				if channelProductUpParams.MinOrderCount != -1 {
					channelProductReq.SkuInfo[0].MinOrderCount = cast.ToInt32(channelProductUpParams.MinOrderCount)
				}
				mapsjon[channelProductReq.Product.Id] = channelProductReq

			}

			storeMasterId, err := GetAppChannelByFinanceCode(in.FinanceCode)
			if err != nil {
				glog.Error("批量力键失败,", "GetAppChannelByFinanceCode failed,", in.FinanceCode, err)
			}

			foodData1 := []*et.RetailBatchinitdata{}
			var retailBatchinitdataRequest = &et.RetailBatchinitdataRequest{
				AppPoiCode:    appPoiCodeSlice[0],
				FoodData:      foodData1,
				OperateType:   2,
				StoreMasterId: storeMasterId,
			}
			var allProductIds []int32 // 门店下面所有的批量的商品的商品数
			for _, V := range channelProductUpParams.ProductIds {

				if data, ok := mapsjon[cast.ToInt32(V)]; ok {

					if baseResponse, err := p.EditChannelProduct(ctx, data); err != nil {
						glog.Error(err)
						row := []string{in.FinanceCode, cast.ToString(V), "编辑商品报错:" + err.Error()}
						resList = append(resList, row)
						FailNum++

					} else if baseResponse.Code == 400 {
						row := []string{in.FinanceCode, cast.ToString(V), "编辑商品报错:" + baseResponse.Message}
						resList = append(resList, row)
						FailNum++
					}
					allProductIds = append(allProductIds, cast.ToInt32(V))

				} else {
					//快照记录错误
					row := []string{in.FinanceCode, cast.ToString(V), "快照不存在"}
					resList = append(resList, row)
					FailNum++
				}
			}

			//for _, id := range channelProductUpParams.ProductIds {
			//	allProductIds = append(allProductIds, cast.ToInt32(id))
			//}

			//获取美团商品信息 --批量查询
			gmdr := pc.GetMtProductDataRequest{
				ProductId:   allProductIds,
				ChannelId:   2,
				FinanceCode: []string{in.FinanceCode},
				Type:        1,
			}
			mtProductData, err := p.GetMtProductData(ctx, &gmdr)
			glog.Info("获取美团分类数据：", kit.JsonEncode(mtProductData), "err:", err)
			if err != nil {
				glog.Error("处理格式化美团商品信息异常", err)
				row := []string{in.FinanceCode, in.ProductId, "处理格式化美团商品信息异常:" + err.Error()}
				resList = append(resList, row)
				FailNum++
			} else if mtProductData.Code != 200 || len(mtProductData.Data) == 0 {
				row := []string{in.FinanceCode, in.ProductId, "处理格式化美团商品信息失败"}
				resList = append(resList, row)
				FailNum++
			}
			foodData := []*et.RetailBatchinitdata{}
			if json.Unmarshal([]byte(mtProductData.Data), &foodData) != nil {
				glog.Error("序列化美团商品信息失败", err)
				row := []string{in.FinanceCode, in.ProductId, "序列化美团商品信息失败"}
				resList = append(resList, row)
				FailNum++
			}

			idsmap := make(map[string]string)
			for k := range foodData {
				for i, _ := range foodData[k].Skus {
					//不更新价格和库存
					foodData[k].Skus[i].Price = ""
					foodData[k].Skus[i].Stock = ""
				}
				foodData[k].CategoryName = ""
				foodData[k].CategoryCode = ""
				idsmap[foodData[k].AppFoodCode] = ""
			}
			haveids := ""
			//统计出失败的个数
			for _, x := range allProductIds {

				if _, ok := idsmap[cast.ToString(x)]; !ok {
					row := []string{in.FinanceCode, cast.ToString(x), "没有查询到美团商品信息"}
					resList = append(resList, row)
					FailNum++
				} else {
					haveids += cast.ToString(x) + ","
				}
			}

			retailBatchinitdataRequest.FoodData = append(retailBatchinitdataRequest.FoodData, foodData...)

			//调用美团的商品信息
			clientMt := GetMtGlobalProductClient()
			glog.Info("2222美团创建/更新商品返回数据为,请求参数:", kit.JsonEncode(retailBatchinitdataRequest))
			initRes, err := clientMt.RPC.RetailBatchinitdata(context.Background(), retailBatchinitdataRequest)
			glog.Info("2222美团创建/更新商品返回数据为,返回结果:", kit.JsonEncode(initRes))

			if err != nil {
				FailNum += len(retailBatchinitdataRequest.FoodData)
				row := []string{in.FinanceCode, haveids, "调用美团接口报错" + err.Error()}
				resList = append(resList, row)
			}
			// v7.0.11 同步第三方商品ID回来(这里operateType传1,由于没有明确的去查第三方商品列表，这里没办法确定商品在第三方一定存在)
			MtProductThirdId(enum.UpdateProductThirdIdFrom7, initRes, foodData, in.FinanceCode, 1)

			if initRes.Code != 200 {

				FailNum += len(retailBatchinitdataRequest.FoodData)
				row := []string{in.FinanceCode, haveids, "调用美团接口报错" + initRes.Message}
				resList = append(resList, row)
			} else {
				for _, itemerr := range initRes.ErrorList {
					if itemerr.BlockFlag == 1 {
						row := []string{in.FinanceCode, itemerr.AppSpuCode, itemerr.Msg}
						resList = append(resList, row)
						FailNum++
					}

				}

			}

			excelUrl := ""
			if len(resList) > 0 {
				headRow := append([]string{}, "财务编码", "平台商品ID", "失败原因")
				errList := append([][]string{}, headRow)
				errList = append(errList, resList...)
				excelUrl, err = ExportProductErr(errList)
				if err != nil {
					glog.Error("错误信息上传失败; err: " + err.Error())
				}
			}
			failNum := len(resList)
			updateModel.TaskStatus = 3

			updateModel.ResulteFileUrl = excelUrl
			updateModel.SuccessNum = cast.ToInt32(total - failNum)
			updateModel.FailNum = cast.ToInt32(failNum)
			taskDetailjson = "部分成功"
			if updateModel.SuccessNum == 0 {
				taskDetailjson = "失败"
			}
			if updateModel.FailNum == 0 {
				taskDetailjson = "成功"
			}

			updateModel.TaskDetail = taskDetailjson
			UpdateTaskListInfo(updateModel)

		}
		return true
	}
}

// tasklistId, taskStatus int, taskDetail, resultFileUrl string,successNum,failNum int32
// 更新任务信息
func UpdateTaskListInfo(task models.TaskList) {
	//var task models.TaskList
	//task.TaskStatus = int32(taskStatus)
	//task.TaskDetail = taskDetail
	//task.ResulteFileUrl = resultFileUrl
	//task.ModifyTime = time.Now()
	var sql strings.Builder
	sql.WriteString("update task_list set modify_time=now() ")
	if task.TaskStatus > 0 {
		sql.WriteString(fmt.Sprintf(",task_status=%d ", task.TaskStatus))
	}
	if len(task.TaskDetail) > 0 {
		sql.WriteString(fmt.Sprintf(",task_detail='%s' ", task.TaskDetail))
	}
	if len(task.ResulteFileUrl) > 0 {
		sql.WriteString(fmt.Sprintf(",resulte_file_url='%s' ", task.ResulteFileUrl))
	}
	if task.SuccessNum > 0 {
		sql.WriteString(fmt.Sprintf(",success_num='%d' ", task.SuccessNum))
	}
	if task.FailNum > 0 {
		sql.WriteString(fmt.Sprintf(",fail_num='%d' ", task.FailNum))
	}
	if len(task.ContextData) > 0 {
		sql.WriteString(fmt.Sprintf(",context_data='%s' ", task.ContextData))
	}
	if len(task.ExtendedData) > 0 {
		sql.WriteString(fmt.Sprintf(",extended_data='%s' ", task.ExtendedData))
	}
	_, err := engine.Exec(fmt.Sprintf("%s where id=%d", sql.String(), task.Id))
	if err != nil {
		glog.Errorf("更新商品库任务异常，参数：%s，异常：%s", kit.JsonEncode(task), err.Error())
	}
}

// 批量认领任务
func DealAsyncTaskListByCopyChannel(taskContent int32) func() bool {
	return func() bool {
		db := NewDbConn()
		//查询新任务的ID
		params := &pc.GetTaskListRequest{
			Sort:        "createTimeAsc",
			TaskStatus:  1,
			ChannelId:   -1,          //-1 查询全部的
			TaskContent: taskContent, // 查询task_content in (5,6)
			Status:      1,
			Page:        1,
			PageSize:    1,
		}

		//获取需要执行的任务，只取一条数据
		pd := new(Product)
		result, err := pd.GetTaskList(context.Background(), params)
		if err != nil {
			glog.Error(err)
			return false
		}
		//无任务的时候，直接返回
		if len(result.TaskList) == 0 {
			return false
		}

		listId := int32(0)
		for _, list := range result.TaskList {
			listId = list.Id
			glog.Info("任务id:" + strconv.Itoa(int(list.Id)))
			list := list
			TaskContent := list.TaskContent
			copyProductRequest := pc.CopyGjProductToChannelProductRequest{}
			err = json.Unmarshal([]byte(list.OperationFileUrl), &copyProductRequest)
			if err != nil {
				glog.Error("批量认领任务解析失败: ", err)
				return false
			}
			switch TaskContent {
			case 5:
				CopyGjProductToChannelProductTask(copyProductRequest.ChannelId, copyProductRequest.ProductId, list.Id)
			case 6:
				var productIDs []int32
				//加载所有商品id
				session := NewDbConn().Table("gj_product").Select("id")
				if list.ChannelId == 0 {
					session.Where("product_type = 1").And("is_del = 0")
				} else if list.ChannelId == 1 {
					session.Where("product_type = 3").And("is_del = 0").And("use_range like '%2%' or use_range like '%3%'")
				} else if list.ChannelId == 5 { // 虚拟商品认领
					session.Where("product_type = 2").And("is_del = 0")
				}

				if err := session.Find(&productIDs); err != nil {
					glog.Error(err)
				}
				glog.Info("查询全部的商品信息", productIDs, len(productIDs))
				CopyGjProductToChannelProductTask(copyProductRequest.ChannelId, productIDs, list.Id)
			default:
				//如果以上都没有处理，则更新下最后创建时间
				updateTaskList(list.Id, db)
			}
		}
		//如果以上都没有处理，则更新下最后创建时间
		updateTaskList(listId, db)
		return true
	}
}

// 批量导入、导出前置仓价格
func DealAsyncTaskListByQzcPrice(taskContent int32) func() bool {
	return func() bool {
		db := NewDbConn()
		//查询新任务的ID
		params := &pc.GetTaskListRequest{
			Sort:        "createTimeAsc",
			TaskStatus:  1,
			ChannelId:   -1,          //-1 查询全部的
			TaskContent: taskContent, // 查询task_content in (5,6)
			Status:      1,
			Page:        1,
			PageSize:    1,
		}

		//获取需要执行的任务，只取一条数据
		pd := new(Product)
		result, err := pd.GetTaskList(context.Background(), params)
		if err != nil {
			glog.Error(err)
			return false
		}
		//无任务的时候，直接返回
		if len(result.TaskList) == 0 {
			return false
		}

		listId := int32(0)
		for _, list := range result.TaskList {
			listId = list.Id
			glog.Info("任务id:" + strconv.Itoa(int(list.Id)))

			update := models.TaskList{
				TaskStatus: 3,
				ModifyTime: time.Now(),
				TaskDetail: "成功",
			}

			switch list.TaskContent {
			case 19:
				url, success, fail, err := ImportA8Price(list)
				update.ResulteFileUrl = url

				var detail []string
				if success > 0 {
					detail = append(detail, fmt.Sprintf("导入成功%d个商品价格", success))
				}
				if fail > 0 {
					detail = append(detail, fmt.Sprintf("导入失败%d个商品价格", fail))
				}
				if err != nil {
					detail = append(detail, err.Error())
				}
				if len(detail) > 0 {
					update.TaskDetail = strings.Join(detail, "，")
				}
			case 41:
				req := new(pc.A8PriceInfoRequest)
				if err = json.Unmarshal([]byte(list.OperationFileUrl), req); err != nil {
					update.TaskDetail = err.Error()
				} else {
					if url, count, err := ExportA8Price(req); err != nil {
						update.TaskDetail = err.Error()
					} else {
						update.ResulteFileUrl = url
						update.TaskDetail = fmt.Sprintf("导出成功，共%d条数据", count)
					}
				}
			}

			if _, err := engine.Id(list.Id).Update(update); err != nil {
				glog.Info("DealAsyncTaskListByQzcPrice，任务：", kit.JsonEncode(list), "，更新结果出错：", err.Error())
			}

			return true
		}

		//如果以上都没有处理，则更新下最后创建时间
		updateTaskList(listId, db)
		return true
	}
}

func updateTaskList(tasklistId int32, db *xorm.Engine) {
	//如果以上都没有处理，则更新下最后创建时间
	sql := "update `dc_product`.`task_list` set modify_time=now() where `id` = ?"
	_, err := db.Exec(sql, tasklistId)
	if err != nil {
		glog.Error("该任务没有做任何代码逻辑处理，任务id：", tasklistId)
	}
}

// channelId, taskContent int, operationFileUrl, userNo,userName,userMobile,userIp string
// 保存一个新的任务
func InsertTaskList(task models.TaskList) (int, error) {
	// 保存到异步任务
	//var taskList models.TaskList
	//taskList.TaskContent = int32(taskContent)
	task.TaskStatus = 1
	//taskList.ChannelId = int32(channelId)
	task.Status = 1
	//taskList.OperationFileUrl = operationFileUrl
	//taskList.CreateId = userNo
	task.CreateTime = time.Now()
	task.ModifyTime = time.Now()
	//taskList.ModifyId = userNo
	//taskList.CreateName = userName
	//taskList.CreateMobile = userMobile
	//taskList.CreateIp = userIp
	// 保存到异步任务
	_, err := engine.Insert(&task)
	return int(task.Id), err
}

// 获取一个未完成的任务
// channelId 渠道id,-1 代表所有
// taskContent 任务类型
func GetFirstUnFinishedTaskList(channelId, taskContent int) *pc.TaskList {
	//拼装参数
	params := &pc.GetTaskListRequest{
		Sort:        "idAsc",
		TaskStatus:  1,
		ChannelId:   int32(channelId),   //-1 查询全部的
		TaskContent: int32(taskContent), //101 查询task_content in (1,2,3,11,13)
		Status:      1,
		Page:        1,
		PageSize:    1,
	}

	//获取需要执行的任务，只取一条数据
	pd := new(Product)
	result, err := pd.GetTaskList(context.Background(), params)
	if err != nil {
		glog.Error(err)
		return nil
	}
	//无任务的时候，直接返回
	if len(result.TaskList) == 0 {
		return nil
	}
	return result.TaskList[0]
}

// 获取用户对应任务的最后一条记录
func GetUserUnFinishedTask(userNo string, channelId, taskContent int32, operationFileUrl string) (task *models.TaskList, err error) {
	db := NewDbConn()
	_, err = db.Table("task_list").
		Where("create_id = ? and task_content = ? and task_status in (1,2) and channel_id = ? and status = 1 and operation_file_url = ?", userNo, taskContent, channelId, operationFileUrl).
		OrderBy("create_time desc").
		Get(task)
	if err != nil {
		glog.Error(userNo + "，获取用户对应任务的最后一条记录异常（GetUserUnFinishedTask）：" + err.Error())
		return task, err
	}

	return task, nil
}
