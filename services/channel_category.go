package services

import (
	"_/models"
	"_/pkg/code"
	"_/proto/dac"
	"_/proto/et"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/panjf2000/ants"
	kit "github.com/tricobbler/rp-kit"
	"math"
	"runtime"
	"strconv"
	"strings"
	syncWait "sync"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var AntsP *ants.Pool

func init() {
	AntsP, _ = ants.NewPool(10)
	//defer p.Release()
}

//---------------------  阿闻管家分类管理模块  --------------------------------------------------

//新增/编辑渠道类别
func (c *Product) NewChannelCategory(ctx context.Context, in *pc.Category) (*pc.NewChannelCategoryResponse, error) {
	defer func() {
		if err := recover(); err != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[NewChannelCategory PANIC RECOVER] %v %s\n", err, stack[:length])
		}
	}()
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return nil, status.Error(codes.Internal, "用户不存在")
	}
	//ctx = context.WithValue(ctx, "userInfo", userInfo)

	var err error

	//参数校验
	if err = checkNewChannelCategoryParam(in); err != nil {
		return nil, err
	}

	db := NewDbConn()
	out := &pc.NewChannelCategoryResponse{
		Code: 200,
	}

	model := new(models.ChannelCategory)

	if in.Id > 0 { //修改

		if has, err := db.ID(in.Id).Get(model); err != nil {
			err = GetDBError(err)
			glog.Error(err)
			return nil, status.Error(codes.Internal, err.Error())
		} else if !has {
			return nil, status.Error(codes.Internal, "未查询分类，id: "+cast.ToString(in.Id))
		}

		//调整了分类层级
		if model.ParentId > 0 && in.ParentId == 0 {
			return nil, status.Error(codes.Internal, "不支持二级分类修改为一级分类")
		}

		model.OriginalData = ""
		originalData, _ := json.Marshal(model)
		model.OriginalData = string(originalData)
		model.ParentId = in.ParentId
		model.Name = in.Name
		model.Sort = in.Sort
		model.CreateName = userInfo.UserName
		if _, err = db.ID(in.Id).Cols("name,parent_id,original_data,update_time,sort").Update(model); err != nil {
			err = GetDBError(err)
			glog.Error(err)
			return nil, status.Error(codes.Internal, err.Error())
		}
		out.CategoryId = in.Id
	} else { //新增
		// 当前最大排序值
		//channelCategory := new(models.ChannelCategory)
		//if _, err = db.Select("max(sort) sort").Get(channelCategory); err != nil {
		//	err = GetDBError(err)
		//	glog.Error(err)
		//	return nil, status.Error(codes.Internal, err.Error())
		//}
		model.Name = in.Name
		model.ParentId = in.ParentId
		//model.Sort = channelCategory.Sort + 1
		model.Sort = in.Sort
		model.ChannelId = in.ChannelId
		model.CreateName = userInfo.UserName
		if _, err = db.Insert(model); err != nil {
			err = GetDBError(err)
			glog.Error(err)
			return nil, status.Error(codes.Internal, err.Error())
		}
		out.CategoryId = model.Id
	}

	if out.Code == 200 {
		if in.Id > 0 {
			c.syncChannelCategoryToThird(model, 2, []string{}, []int{}, userInfo, in.IpAddr)
		} else {
			c.syncChannelCategoryToThird(model, 1, []string{}, []int{}, userInfo, in.IpAddr)
		}
	}
	return out, nil
}

//查询渠道类别
func (c *Product) QueryChannelCategory(ctx context.Context, in *pc.CategoryRequest) (*pc.CategoryResponse, error) {
	out := new(pc.CategoryResponse)
	out.Code = 200

	channelId := in.Where.ChannelId
	//京东到家用阿闻的分类
	if in.Where.ChannelId == ChannelJddjId {
		channelId = ChannelAwenId
	}

	//医疗互联网用阿闻的分类
	if in.Where.ChannelId == ChannelDigitalHealth {
		channelId = ChannelAwenId
	}

	session := NewDbConn().Table("channel_category").Alias("a").
		Join("left", "channel_category_thirdid b", "a.id=b.id and b.channel_id=?", in.Where.ChannelId).
		Where("a.channel_id=?", channelId)

	if in.Where.ParentId > 0 {
		session.And("a.parent_id = ?", in.Where.ParentId)
	}
	if len(in.Where.Name) > 0 {
		session.And("a.name = ?", in.Where.Name)
	}
	if in.Where.Id > 0 {
		session.And("a.id = ?", in.Where.Id)
	}

	//排除没有商品的分类
	if in.FilterEmpty && len(in.FinanceCode) > 0 && in.WarehouseId > 0 {
		var model []*models.ChannelCategory
		err := NewDbConn().Alias("a").Select("DISTINCT(a.id) id,a.parent_id").
			Join("inner", "channel_store_product b", "a.id = b.channel_category_id AND a.channel_id=b.channel_id").
			Join("left", "channel_store_product_has_stock c", "b.id = c.channel_store_product_id").
			Where("b.finance_code=? AND c.warehouse_id=? AND c.has_stock_up=1 AND a.channel_id=?", in.FinanceCode, in.WarehouseId, channelId).
			Find(&model)
		if err == nil && len(model) > 0 {
			idSlice := []int32{}
			for _, v := range model {
				idSlice = append(idSlice, v.ParentId, v.Id)
			}
			session.In("a.id", idSlice)
		}
	}

	countSession := *session
	count, _ := countSession.Count()
	out.TotalCount = int32(count)

	if out.TotalCount == 0 {
		return out, nil
	}

	sortBy := "ASC"
	if in.Where.ChannelId == ChannelElmId {
		sortBy = "DESC"
	}

	if err := session.Select("a.*,b.category_id third_category_id").GroupBy("a.id").OrderBy("a.parent_id ASC, a.sort " + sortBy).Find(&out.Details); err != nil {
		err = GetDBError(err)
		glog.Error(err)
		return nil, err
	}
	if in.Where.ChannelId == ChannelJddjId {
		var third_category_id string
		engine.SQL("select category_id from channel_category_thirdid where id = ?", in.Where.Id).Get(&third_category_id)
		out.Details[0].ThirdCategoryId = third_category_id
	}
	return out, nil
}

//排序渠道类别
func (c *Product) SortChannelCategory(ctx context.Context, in *pc.CategorySortRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return nil, status.Error(codes.Internal, "用户不存在")
	}
	ctx = context.WithValue(ctx, "userInfo", userInfo)

	//查询调整的分类
	var currentChannelCategory models.ChannelCategory
	has, err := engine.Id(in.Id).Get(&currentChannelCategory)
	if !has || err != nil {
		if err != nil {
			glog.Error(err)
		}
		out.Message = "分类不存在"
		return out, err
	}
	// 相关被调整的分类
	var relationChannelCategory models.ChannelCategory

	//上升
	if in.SortAction == "up" {
		has, err = engine.Where("parent_id=? AND channel_id=?", currentChannelCategory.ParentId, currentChannelCategory.ChannelId).And("sort<?", currentChannelCategory.Sort).OrderBy("sort desc").Get(&relationChannelCategory)
		if err != nil {
			glog.Error(err)

		}
	} else {
		has, err = engine.Where("parent_id=? AND channel_id=?", currentChannelCategory.ParentId, currentChannelCategory.ChannelId).And("sort>?", currentChannelCategory.Sort).OrderBy("sort asc").Get(&relationChannelCategory)
		if err != nil {
			glog.Error(err)
		}
	}

	// 查询到了可以调整的分类
	if has {
		// 开启事务
		var session = engine.NewSession()
		defer session.Close()
		session.Begin()
		// 更新
		session.ID(currentChannelCategory.Id).Update(&models.ChannelCategory{Sort: relationChannelCategory.Sort})
		session.ID(relationChannelCategory.Id).Update(&models.ChannelCategory{Sort: currentChannelCategory.Sort})
		//提交事务
		err := session.Commit()
		if err != nil {
			session.Rollback()
			glog.Error(err)
			out.Message = "分类排序失败"
			return out, err
		}
	} else {
		out.Message = "查询分类失败"
		return out, err
	}

	if in.Id > 0 {
		// var err error
		var storeMasterIdList []int32
		var retCode int
		if userInfo.FinancialCode != "" {
			storeMasterId, err := GetAppChannelByFinanceCode(userInfo.FinancialCode)
			if err != nil {
				glog.Error("SortChannelCategory,", "GetAppChannelByFinanceCode failed,", userInfo.FinancialCode, err)
				out.Code = 400
				return out, nil
			}
			storeMasterIdList = append(storeMasterIdList, storeMasterId)

		} else if userInfo.IsGeneralAccount { // 是总账号对所有店铺主体进行操作
			storeMasterIdList, retCode = GetStoreMasterIDList()
			if retCode != code.Success {
				glog.Error("SortChannelCategory,", "GetAllStoreMaster failed,")
				out.Code = 400
				return out, nil
			}
		}

		// 更新京东的排序
		var jdCategoryId int
		engine.SQL(`select b.category_id from channel_category a inner join channel_category_thirdid b 
		on a.Id=b.Id  and a.channel_id=b.channel_id
		where a.channel_id=? and a.Id=? order by a.sort asc`, in.Id, ChannelJddjId).Get(&jdCategoryId)

		var childCategoryId []string
		engine.SQL(`select b.category_id from channel_category a inner join channel_category_thirdid b 
							on a.Id=b.Id  and a.channel_id=b.channel_id
							where a.channel_id=? and a.parent_id=? order by a.sort asc`, ChannelJddjId, in.Id)
		// 有数据返回
		if jdCategoryId > 0 && len(childCategoryId) > 0 {
			for _, storeMasterId := range storeMasterIdList {
				syncData := new(et.JddjSortShopCategoryRequest)
				syncData.Pid = int64(jdCategoryId)
				syncData.ChildIds = strings.Join(childCategoryId, ",")
				syncData.StoreMasterId = storeMasterId
				client := GetMtGlobalProductClient()
				//defer client.Close()
				res, err := client.JDDJPRODUCT.JddjSortShopCategory(client.Ctx, syncData)
				if err != nil {
					glog.Error(err)
				}
				if res.Code != "0" {
					glog.Warning("同步商品分类到京东失败 ", res.Msg)
				}
			}
		}

	}

	out.Code = 200
	return out, nil
}

//同步渠道分类
func (c *Product) SyncChannelCategory(ctx context.Context, in *pc.SyncChannelCategoryRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	if len(in.ChannelStoreId) == 0 {
		out.Message = "参数不能为空"
		return out, nil
	}

	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return out, nil
	}
	task := models.TaskList{
		ChannelId:        in.ChannelId,
		TaskContent:      17,
		OperationFileUrl: utils.ObjectToJsonString(in),
	}
	// 保存到异步任务
	InsertTaskList(task)

	out.Code = 200
	return out, nil
}

//删除渠道类别
func (c *Product) DelChannelCategory(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return nil, status.Error(codes.Internal, "用户不存在")
	}
	ctx = context.WithValue(ctx, "userInfo", userInfo)

	ids := strings.Join(utils.SliceUnique(utils.SliceFilter(strings.Split(in.Id, ","))), ",")
	if len(ids) == 0 {
		out.Code = 400
		out.Message = "ID不能为空"
		return out, nil
	}

	Engine := NewDbConn()

	//分类是否存在，不存在直接返回
	has, err := Engine.Select("id").Where("id in(" + ids + ")").Get(&models.ChannelCategory{})
	if err != nil || !has {
		if err != nil {
			glog.Error(err)
		}
		out.Code = 400
		out.Message = "分类不存在"
		return out, err
	}

	delete_ids := []string{}

	//删除分类
	if err := Engine.SQL("WITH RECURSIVE result (`id`,`parent_id`) AS (SELECT`id`,`parent_id` FROM channel_category WHERE id in (" + ids + ") UNION ALL SELECT i.`id`, i.parent_id FROM channel_category i JOIN result ON result.id = i.parent_id) SELECT id FROM result").Find(&delete_ids); err != nil {
		glog.Error(err)
		out.Code = 400
		out.Message = "数据库查询失败"
		return out, err
	}

	if len(delete_ids) > 0 {
		count, err := Engine.Table("channel_product").Where("category_id in (" + strings.Join(delete_ids, ",") + ")").Count()
		if count > 0 {
			out.Code = 400
			out.Message = "已经关联商品的子类不能删除"
			return out, err
		}

		childcount, err := Engine.Table("channel_category").Where("parent_id in (" + strings.Join(delete_ids, ",") + ")").Count()
		if childcount > 0 {
			out.Code = 400
			out.Message = "已经关联下级的分类不能删除"
			return out, err
		}

		// v6.4.0 分了类在任务中无法删除
		var num int32
		// 如果是二级分类父类在执行也不能删除
		var parent_id string
		Engine.SQL("select parent_id from dc_product.channel_category cc where id = ?;", in.Id).Get(&parent_id)
		var category_id = []string{cast.ToString(in.Id)}
		if len(parent_id) > 0 {
			category_id = append(category_id, parent_id)
		}
		sql := "select count(1) from dc_product.task_list tl where task_content =? and task_status in (1,2) and  category in ('" + strings.Join(category_id, "','") + "')"

		Engine.SQL(sql, SyncCategoryTaskContent).Get(&num)
		if num > 0 {
			out.Code = 400
			out.Message = "分类任务或者分类的一级任务已经在执行"
			return out, err
		}

		//查询分类名称
		cate := new(models.ChannelCategory)
		if _, err := Engine.Where("id = ?", delete_ids[0]).Get(cate); err != nil {
			out.Code = 400
			out.Message = "查询不到该分类"
			return out, err
		}

		if _, err := Engine.Exec("DELETE FROM channel_category WHERE id IN(" + strings.Join(delete_ids, ",") + ")"); err != nil {
			glog.Error(err)
			out.Code = 400
			out.Message = "删除失败"
			return out, err
		} else {
			c.syncChannelCategoryToThird(cate, 3, []string{}, []int{}, userInfo, in.IpAddr)
		}
	}

	return out, nil
}

// 同步特定分类到特定的门店列表
// syncType 1 新增 2修改 3 删除
// financeCode 需要同步的门店列表
// channelIds 需要同步的渠道,如果不传递则更新所有
// userNo 当前登录用户
func (c *Product) syncChannelCategoryToThird(model *models.ChannelCategory, syncType int, financeCode []string,
	channelIds []int, userInfo *models.LoginUserInfo, ipAddr string) {
	// 需要同步的渠道列表 饿了么与京东
	if len(channelIds) == 0 {
		channelIds = []int{ChannelElmId, ChannelJddjId, ChannelMtId}
	}
	// v6.4.0 分类调整 更新所有的渠道
	var params []*CategorySync
	for _, channelId := range channelIds {
		// 构造参数
		var param = &CategorySync{}
		param.FinanceCode = financeCode
		param.BuildSyncCategoryToThirdParams(int(model.Id), int(model.ParentId), channelId, int(model.Sort), syncType, model.Name, nil, nil)

		params = append(params, param)
	}
	// 保存到异步任务
	task := models.TaskList{
		TaskContent:      int32(SyncCategoryTaskContent),
		OperationFileUrl: utils.ObjectToJsonString(params),
		ExtendedData:     model.Name,              // 分类名称
		RequestHeader:    cast.ToString(syncType), // 操作类型
		CreateId:         userInfo.UserNo,
		CreateName:       userInfo.UserName,
		CreateIp:         ipAddr,
		Category:         model.Id,
	}

	InsertTaskList(task)
}

// --------------------------------------------   同步商品分类到第三方平台封装
// 同步分类至指定渠道
// A.实例化 CategorySync
// B.调用 SyncCategoryToThird
// C.(可选)调用Export将错误结果导出到七牛云
type CategorySync struct {
	// 同步类型-必须 0 未知 1 新增 2 修改 3 删除
	SyncType int
	// 渠道-必须
	ChannelId int
	// 分类Id-必须
	CategoryId int
	// 分类名称-必须
	CategoryName string
	// 上级分类id-必须
	ParentId int
	// 排序-必须
	Sort int
	// 财务代码(如果有此属性则只同步该门店,没有则同步全部渠道门店)
	FinanceCode []string
	// 渠道店铺代码(如果有此属性则只同步该门店,没有则同步全部渠道门店)
	ChannelStoreCode []string
	// 目前系统包含的该渠道的第三方分类信息
	ThirdCategoryList []*models.ChannelCategoryThirdid
	// 同步执行结果
	Result []*SyncCategoryToThirdParams_Result
}

// 同步执行结果
type SyncCategoryToThirdParams_Result struct {
	// 分类Id
	categoryId int
	// 分类名称
	categoryName string
	// 财务代码
	financeCode string
	// shopName 门店名称
	ShopName string
	// 是否同步成功
	IsSuccess bool
	// 执行结果
	Message string
	// channel_id
	ChannelId int
	// 操作类型 1新增 2 修改 3删除
	SyncType int
	// 第三方门店的id
	ChannelStoreId string
	// appChannel
	AppChannel int32
}

// 保存到分类与第三方分类id关联关系
func (params *CategorySync) insertCategoryThird(thirdFinanceCode, thirdCategoryId string) error {
	var model models.ChannelCategoryThirdid
	//todo ChannelCategoryThirdid表字段优化
	model.Id = int32(params.CategoryId)
	model.ChannelId = int32(params.ChannelId)
	model.ChannelStoreId = thirdFinanceCode
	model.CategoryId = thirdCategoryId
	_, err := engine.Insert(model)
	return err
}

// 删除第三方关联关系
func (params *CategorySync) deleteCategoryThird(thirdFinanceCode, thirdCategoryId string) error {
	var session = engine.Where("1=1")
	// 分类id
	if params.CategoryId > 0 {
		session.Where("id=?", params.CategoryId)
	}
	// 渠道id
	if params.ChannelId > 0 {
		session.Where("channel_id=?", params.ChannelId)
	}
	// 第三方门店编码
	if len(thirdFinanceCode) > 0 {
		session.Where("channel_store_id=?", thirdFinanceCode)
	}
	// 分类id
	if len(thirdCategoryId) > 0 {
		session.Where("category_id=?", thirdCategoryId)
	}
	// 调用数据库删除记录
	_, err := session.Delete(&models.ChannelCategoryThirdid{})
	return err
}

// 构造同步参数
func (params *CategorySync) BuildSyncCategoryToThirdParams(categoryId, parentCategoryId, channelId, categorySort, syncType int, categoryName string, financeCodes []string, channelStoreCodes []string) {
	params.SyncType = syncType
	params.CategoryId = categoryId
	params.CategoryName = categoryName
	params.ParentId = parentCategoryId
	params.ChannelId = channelId
	params.Sort = categorySort
	params.CategoryName = categoryName
	params.FinanceCode = financeCodes
	params.ChannelStoreCode = channelStoreCodes
}

// 新增，修改，删除后同步分类至第三方平台

//task_centent = 16 写入的的地方：syncChannelCategoryToThird rpc方法
//task_centent = 17 写入的的地方：SyncChannelCategory rpc方法
func (params *CategorySync) SyncCategoryToThird() {

	// 获取第三方配置信息
	err := engine.Where("channel_id=?", params.ChannelId).Find(&params.ThirdCategoryList)
	if err != nil {
		glog.Error(err)
	}
	//todo
	// 美团和饿了么
	appChannelStoreMap := make(map[string]int32)
	if params.ChannelId == ChannelElmId || params.ChannelId == ChannelMtId || params.ChannelId == ChannelJddjId {
		// 数据中心grpc
		datacenterGrpc := GetDataCenterClient()
		defer datacenterGrpc.Close()

		// 获取第三方平台配置的门店id
		queryStoreInfo := &dac.StoreInfoRequest{
			ChannelId:   int32(params.ChannelId),
			FinanceCode: params.FinanceCode,
			StoreCode:   params.ChannelStoreCode,
		}
		glog.Info("获取第三方平台配置的门店id参数：", kit.JsonEncode(queryStoreInfo))
		res, err := datacenterGrpc.RPC.QueryStoreInfo(datacenterGrpc.Ctx, queryStoreInfo)
		glog.Info("获取第三方平台配置的门店id参数：", kit.JsonEncode(queryStoreInfo), "；返回结果：", kit.JsonEncode(res))
		if err != nil || res.Code == 400 {
			glog.Error("SyncCategoryToThird-to-QueryStoreInfo rpc err:", err, "，调用方法：", kit.RunFuncName(2))
			params.Result = append(params.Result, &SyncCategoryToThirdParams_Result{
				categoryId:   params.CategoryId,
				categoryName: params.CategoryName,
				Message:      "获取第三方门店出错",
			})
			//此处必须return 否则后续请求地方放服务时拿到的appChannel错误的话，问题比较严重
			return
		}

		for _, v := range res.Details {
			params.ChannelStoreCode = append(params.ChannelStoreCode, v.ChannelStoreId)
			if v.AppChannel == 0 {
				v.AppChannel = 1
			}
			appChannelStoreMap[v.ChannelStoreId] = v.AppChannel
		}

		//处理所有的门店
		for storeCode, appChannel := range appChannelStoreMap {
			// 避免全局共用grpc客户端
			externalGrpc := et.GetExternalClient()
			defer externalGrpc.Close()
			// 请求结果
			var result SyncCategoryToThirdParams_Result
			result.categoryId = params.CategoryId
			result.categoryName = params.CategoryName
			result.financeCode = storeCode
			// 新增或修改
			if params.SyncType == 1 || params.SyncType == 2 {
				// 饿了么
				if params.ChannelId == ChannelElmId {

					err := params.syncEditToElm(storeCode, appChannel)
					if err != nil {
						glog.Error("syncEditToElm err:", err, ",参数：", kit.JsonEncode(params))
						result.Message = err.Error()
					} else {
						result.IsSuccess = true
					}
				}
				// 美团
				if params.ChannelId == ChannelMtId {
					//todo tp mt
					err := params.syncEditToMt(storeCode, appChannel)
					if err != nil {
						glog.Error("syncEditToMt err:", err, ",参数：", kit.JsonEncode(params))
						result.Message = err.Error()
					} else {
						result.IsSuccess = true
					}
				}
				// 京东到家
				if params.ChannelId == ChannelJddjId {
					err := params.syncEditToJd(appChannel)
					if err != nil {
						glog.Error("syncEditToJd err:", err, ",参数：", kit.JsonEncode(params))
						result.Message = err.Error()
					} else {
						result.IsSuccess = true
					}
				}

			}
			// 删除操作
			if params.SyncType == 3 {
				if params.ChannelId == ChannelElmId {

					err := params.syncDeleteToElm(storeCode, appChannel)
					if err != nil {
						glog.Error("syncDeleteToElm err:", err, ",参数：", kit.JsonEncode(params))
						result.Message = err.Error()
					} else {
						result.IsSuccess = true
					}
				}
				// 美团
				if params.ChannelId == ChannelMtId {

					err := params.syncDeleteToMt(storeCode, appChannel)
					if err != nil {
						glog.Error("syncDeleteToElm err:", err, ",参数：", kit.JsonEncode(params))
						result.Message = err.Error()
					} else {
						result.IsSuccess = true
					}
				}
				// 京东到家
				if params.ChannelId == ChannelJddjId {
					err := params.syncDeleteToJd(appChannel)
					if err != nil {
						glog.Error("syncDeleteToJd err:", err, ",参数：", kit.JsonEncode(params))
						result.Message = err.Error()
					} else {
						result.IsSuccess = true
					}
				}
			}
			params.Result = append(params.Result, &result)
		}
	}
}

func GetThirdStoreMaster(channel_id int32, finance_code string) ([]*dac.StoreInfo, error) {
	// 数据中心grpc

	datacenterGrpc := GetDataCenterClient()
	defer datacenterGrpc.Close()

	// 获取第三方平台配置的门店id
	//todo 测试使用需要删除财务编码查询
	queryStoreInfo := &dac.StoreInfoRequest{
		ChannelId: channel_id,
		//FinanceCode: []string{"CX0011"},
	}
	if len(finance_code) > 0 {
		queryStoreInfo.FinanceCode = []string{finance_code}
	}
	glog.Info("获取第三方平台配置的门店id参数：", kit.JsonEncode(queryStoreInfo))
	res, err := datacenterGrpc.RPC.QueryStoreInfo(datacenterGrpc.Ctx, queryStoreInfo)
	glog.Info("获取第三方平台配置的门店id参数：", kit.JsonEncode(queryStoreInfo), "；返回结果：", kit.JsonEncode(res))
	if err != nil || res.Code == 400 {
		glog.Error("SyncCategoryToThird-to-QueryStoreInfo rpc err:", err, "，调用方法：", kit.RunFuncName(2))
		//此处必须return 否则后续请求地方放服务时拿到的appChannel错误的话，问题比较严重
		return res.Details, errors.New("获取第三方配置门店失败")
	}

	for _, v := range res.Details {

		if v.AppChannel == 0 {
			v.AppChannel = 1
		}

	}

	return res.Details, nil
}

// 导出到线上
// fileUrl 线上url
// desc 说明
func (params *CategorySync) Export() (fileUrl, desc string) {
	var excelRow [][]string
	// excel文件头
	excelRow = append(excelRow, []string{"财务编码", "分类Id", "分类名称", "错误信息"})
	var successCount int
	for _, v := range params.Result {
		if v.IsSuccess == false {
			excelRow = append(excelRow, []string{v.financeCode, cast.ToString(v.categoryId), v.categoryName, v.Message})
		} else {
			successCount++
		}
	}
	if len(params.Result) == 0 {
		return
	}
	if successCount == len(params.Result) {
		desc = "成功"
	} else {

		url, err := ExportProductErr(excelRow)
		if err != nil {
			glog.Error("params-CategorySync-Export,err:", err, utils.ObjectToJsonString(excelRow))
		} else {
			fileUrl = url
		}

		if successCount == 0 {
			desc = "失败"
		} else {
			desc = fmt.Sprintf("部分成功(共:%d,成功:%d,失败:%d)", len(params.Result), successCount, len(params.Result)-successCount)
		}
	}

	return
}

// 导出到线上
// fileUrl 线上url
// desc 说明
func (params *CategorySync) ExportToExcel() (fileUrl, desc string) {
	var excelRow [][]string
	// excel文件头
	excelRow = append(excelRow, []string{"任务类型", "渠道", "操作分类", "财务编码", "门店名称", "错误信息"})
	var successCount int
	for _, v := range params.Result {
		if v.IsSuccess == false {

			syncTypeValue := ""
			if v.SyncType == 1 {
				syncTypeValue = "新增分类"
			} else if v.SyncType == 2 {
				syncTypeValue = "编辑分类"
			} else if v.SyncType == 3 {
				syncTypeValue = "删除分类"
			}
			channelValue := ""
			if v.ChannelId == 1 {
				channelValue = "阿闻渠道"
			} else if v.ChannelId == 2 {
				channelValue = "美团渠道"
			} else if v.ChannelId == 3 {
				channelValue = "饿了么渠道"
			} else if v.ChannelId == 4 {
				channelValue = "京东到家渠道"
			}

			excelRow = append(excelRow, []string{syncTypeValue, channelValue, v.categoryName, v.financeCode, v.ShopName, v.Message})
		} else {
			successCount++
		}
	}
	if len(params.Result) == 0 {
		return
	}
	if successCount == len(params.Result) {
		desc = "成功"
	} else {

		url, err := ExportProductErr(excelRow)
		if err != nil {
			glog.Error("params-CategorySync-Export,err:", err, utils.ObjectToJsonString(excelRow))
		} else {
			fileUrl = url
		}

		if successCount == 0 {
			desc = "失败"
		} else {
			desc = fmt.Sprintf("部分成功(共:%d,成功:%d,失败:%d)", len(params.Result), successCount, len(params.Result)-successCount)
		}
	}

	return
}

// 同步新增或修改至京东
func (params *CategorySync) syncEditToJd(storeMasterId int32) error {
	client := NewExternalClient()
	defer client.Close()
	// 构造参数
	var request et.JddjAddShopCategoryRequest
	request.ShopCategoryName = params.CategoryName
	request.Pid = int64(params.ParentId)

	request.StoreMasterId = storeMasterId
	flag := false
	if params.ParentId > 0 && params.SyncType == 1 {
		// 提取父分类在平台的代码
		for _, thirdCategory := range params.ThirdCategoryList {
			if thirdCategory.Id == int32(params.ParentId) && cast.ToString(storeMasterId) == thirdCategory.ChannelStoreId {
				flag = true
				request.Pid = cast.ToInt64(thirdCategory.CategoryId)
				requestStoreMasterId, _ := strconv.Atoi(thirdCategory.ChannelStoreId)
				request.StoreMasterId = int32(requestStoreMasterId)
				if request.StoreMasterId == 0 {
					request.StoreMasterId = 1
				}
				glog.Info("syncEditToJd 同步到京东分类参数1：", kit.JsonEncode(request), kit.JsonEncode(thirdCategory))

				if response, err := client.JddjProduct.JddjAddShopCategory(client.Ctx, &request); err != nil {
					return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
				} else {
					glog.Info("syncEditToJd 同步到京东分类参数1-2：", kit.JsonEncode(request), ";返回结果：", kit.JsonEncode(response))
					if cast.ToInt(response.Code) != 0 {
						return errors.New(response.Msg)
					} else {
						if params.SyncType == 1 {
							params.insertCategoryThird(thirdCategory.ChannelStoreId, response.Result.Id)
						}
					}
				}
			}

		}

		if !flag {
			return errors.New("未找到要修改的父分类id,当前appId不需要同步")
		}

	} else {

		if params.SyncType == 2 {
			glog.Info("syncEditToJd 同步到京东分类参数update：", kit.JsonEncode(request))
			var requestUpdta et.JddjUpdateShopCategoryRequest
			requestUpdta.StoreMasterId = storeMasterId
			requestUpdta.ShopCategoryName = params.CategoryName

			if requestUpdta.StoreMasterId == 0 {
				requestUpdta.StoreMasterId = 1
			}
			var category_ids []models.ChannelCategoryThirdid
			engine.SQL("select * from dc_product.channel_category_thirdid where id = ? and channel_id = ? and channel_store_id = ? ;",
				params.CategoryId, params.ChannelId, storeMasterId).Find(&category_ids)

			for _, v_category := range category_ids {
				if v_category.ChannelStoreId == cast.ToString(storeMasterId) {
					requestUpdta.Id = cast.ToInt64(v_category.CategoryId)
					response, err := client.JddjProduct.JddjUpdateShopCategory(client.Ctx, &requestUpdta)
					if err != nil {
						glog.Info("update data : ", kit.JsonEncode(response), " ERR:", err.Error())
						return err
					}
				}
			}
			if len(category_ids) <= 0 {
				return errors.New("未找到要修改的分类id,当前appId不需要同步")
			}

		} else {
			glog.Info("syncEditToJd 同步到京东分类参数2：", kit.JsonEncode(request))
			if response, err := client.JddjProduct.JddjAddShopCategory(client.Ctx, &request); err != nil {
				return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
			} else {
				glog.Info("syncEditToJd 同步到京东分类参数2-2：", kit.JsonEncode(request), ";返回结果：", kit.JsonEncode(response))
				if cast.ToInt(response.Code) != 0 {
					return errors.New(response.Msg)
				} else {
					if params.SyncType == 1 {
						params.insertCategoryThird(strconv.Itoa(int(request.StoreMasterId)), response.Result.Id)
					}
				}
			}
		}

	}

	// 需要调用jd的排序接口来进行重新排序
	prefix := "调用jd分类排序日志："
	//1： 获取本地分类
	var vo = pc.CategoryRequest{
		PageIndex: 1,
		PageSize:  math.MaxInt32,
		Where:     &pc.Category{ChannelId: 1, ParentId: int32(params.ParentId)},
	}
	category, err := new(Product).QueryChannelCategory(context.Background(), &vo)
	if err != nil {
		glog.Error(prefix, " QueryChannelCategory ", err.Error())
	}
	var sortAwen []string // 将父分类或者子分类的名称排序出来
	for i := range category.Details {
		if category.Details[i].ParentId == int32(params.ParentId) {
			sortAwen = append(sortAwen, category.Details[i].Name)
		}
	}
	glog.Info(prefix, " jd本地分类排序：", kit.JsonEncode(sortAwen))

	//2： 查询获取jd的分类,主要是为了排序准确本地分类和jd分类完全不一样，多偏
	categoryRequest := et.JddjGetShopCategoryRequest{
		StoreMasterId: storeMasterId,
		Fields:        "",
	}

	result, err := client.JddjProduct.JddjGetShopCategory(client.Ctx, &categoryRequest)
	if err != nil {
		glog.Error(prefix, " 调用JddjGetShopCategory排序异常：", err.Error())
	}
	//名称和分类
	JdCategoryNameMap := make(map[string]*et.JDCategoryResult, 0)
	// 父id的分类的集合
	parentIdMap := make(map[int32][]*et.JDCategoryResult, 0)

	//需要组装jd的分类数据
	if result.Code == "0" && result.Success == true {
		for jdi := range result.Result {
			categoryResult := result.Result[jdi]
			JdCategoryNameMap[categoryResult.ShopCategoryName] = categoryResult
			// 將pid和下面的分类对应起来
			parentIdMap[categoryResult.Pid] = append(parentIdMap[categoryResult.Pid], categoryResult)
		}
	}

	// 组装jd的排序参数
	var jdSort []int32
	var jdSortMap = make(map[int32]struct{}, 0)
	for i := range sortAwen {
		vt := sortAwen[i]
		if data, ok := JdCategoryNameMap[vt]; ok {
			jdSort = append(jdSort, data.Id) // jd的分类id
			jdSortMap[data.Id] = struct{}{}
		}
	}
	//判断第三方和本地的分类是否数量一样，不一样的话调用接口会报错
	var thirdPId int32
	var thirdId int32
	if data, ok := JdCategoryNameMap[params.CategoryName]; ok {
		thirdPId = data.Pid
		thirdId = data.Id
	} else {
		glog.Error(prefix, " 未获取到第三方分类", kit.JsonEncode(params), " 第三方分类集合： ", kit.JsonEncode(JdCategoryNameMap))
		return nil
	}

	if len(jdSort) != len(parentIdMap[thirdPId]) {
		// 有些分类如果在第三方创建，本地没有的，或者是各个环境的脏数据,参数没有传递完全会报错， 需要把jd那边的分类补上去
		for i := range parentIdMap[thirdPId] {
			categoryResult := parentIdMap[thirdPId][i]
			if _, ok := jdSortMap[categoryResult.Id]; !ok {
				jdSort = append(jdSort, categoryResult.Id) // 将没有的jd分类id加到排序参数里面
			}
		}
	}

	// 3:调用jd的排序接口进行排序，注意子分类和父分类排序的不同
	var childIdstr []string
	for i := range jdSort {
		childIdstr = append(childIdstr, cast.ToString(jdSort[i]))
	}

	var jdSortVo = &et.JddjSortShopCategoryRequest{
		ChildIds:      strings.Join(childIdstr, ","),
		StoreMasterId: storeMasterId,
	}
	if params.ParentId > 0 {
		jdSortVo.Pid = int64(thirdId)
	} else {
		jdSortVo.Pid = 0
	}

	shopCategory, err := client.JddjProduct.JddjSortShopCategory(client.Ctx, jdSortVo)
	glog.Info(prefix, " 调用JddjSortShopCategory返回：", kit.JsonEncode(shopCategory), " request:", kit.JsonEncode(request))
	if err != nil {
		glog.Error(prefix, " 调用jd排序接口异常：", err.Error())
	}

	return nil
}

// 同步新增或修改至美团
func (params *CategorySync) syncEditToMt(channelStoreId string, storeMasterId int32) error {
	// 构造参数
	var request et.RetailCatUpdateRequest
	request.AppPoiCode = channelStoreId
	request.StoreMasterId = storeMasterId
	request.Sequence = int32(params.Sort)
	// 创建分类
	if params.SyncType == 1 {
		if params.ParentId == 0 { //一级分类
			request.CategoryName = params.CategoryName
			request.CategoryCode = cast.ToString(params.CategoryId) // 将code也同步过去
			request.Sequence = cast.ToInt32(params.Sort)
		} else { // 二级分类
			var parentCategory models.ChannelCategory
			engine.ID(params.ParentId).Get(&parentCategory)
			request.CategoryName = parentCategory.Name
			request.CategoryCode = cast.ToString(parentCategory.Id)

			//request.CategoryNameOrigin = "YES02"
			request.CategoryCodeOrigin = cast.ToString(parentCategory.Id)

			request.SecondaryCategoryName = params.CategoryName
			request.SecondaryCategoryCode = cast.ToString(params.CategoryId)
			request.Sequence = cast.ToInt32(params.Sort)

		}
	}

	// 修改分类
	if params.SyncType == 2 {
		request.CategoryName = params.CategoryName
		request.CategoryCode = cast.ToString(params.CategoryId)
		request.Sequence = cast.ToInt32(params.Sort)
		request.CategoryCodeOrigin = cast.ToString(params.CategoryId) // 修改的原始分类的id

		//if params.ParentId > 0 {
		//	var parentCategory models.ChannelCategory
		//	engine.ID(params.ParentId).Get(&parentCategory)
		//request.CategoryNameOrigin = parentCategory.Name
		//}
	}

	// 同步到美团
	glog.Info("同步到美团分类参数：", kit.JsonEncode(request))
	client := NewExternalClient()
	defer client.Close()
	if out, err := client.RPC.RetailCatUpdate(client.Ctx, &request); err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else {
		glog.Info("同步到美团分类参数：", kit.JsonEncode(request), ";返回结果：", kit.JsonEncode(out))
		if out.Code != 200 {
			return errors.New(out.Error.Msg)
		}
	}
	return nil
}

// 同步新增或修改分类至饿了么
func (params *CategorySync) syncEditToElm(channelStoreId string, appChannel int32) error {
	// 构造参数
	var request et.NewElmShopCategoryRequest
	request.ShopId = channelStoreId
	request.Name = params.CategoryName

	eleMeSort := EleMeCategorySort - params.Sort // ele排序和美团是反的
	request.Rank = cast.ToString(eleMeSort)

	request.ParentCategoryId = cast.ToString(params.ParentId)
	request.AppChannel = appChannel

	// 提取第三方父分类id
	if params.ParentId > 0 || params.SyncType == 2 {
		for _, thirdCategory := range params.ThirdCategoryList {
			if params.ParentId > 0 {
				if thirdCategory.ChannelStoreId == channelStoreId && thirdCategory.Id == int32(params.ParentId) {
					request.ParentCategoryId = thirdCategory.CategoryId
					if params.SyncType == 1 {
						break
					}
				}
			}
			if params.SyncType == 2 {
				if thirdCategory.Id == int32(params.CategoryId) && thirdCategory.ChannelStoreId == channelStoreId {
					request.CategoryId = cast.ToString(thirdCategory.CategoryId)
					if params.ParentId == 0 {
						break
					}
				}
			}
		}
	}

	// 同步到饿了么
	glog.Info("同步到饿了么分类参数：", kit.JsonEncode(request))
	client := NewExternalClient()
	defer client.Close()
	if response, err := client.ELMPRODUCT.NewElmShopCategory(client.Ctx, &request); err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else {
		glog.Info("同步到饿了么分类参数：", kit.JsonEncode(request), ";返回结果：", kit.JsonEncode(response))
		if response.Code != 200 {
			return errors.New(response.Error)
		} else {
			if params.SyncType == 1 {
				// 保存第三方关联关系
				return params.insertCategoryThird(channelStoreId, response.Data[0].CategoryId)
			}
		}
	}

	return nil
}

// 删除分类在京东渠道
func (params *CategorySync) syncDeleteToJd(storeMasterId int32) error {
	// 请求参数
	var request et.JddjDeleteShopCategoryRequest
	// 提取父分类在平台的代码
	for _, thirdCategory := range params.ThirdCategoryList {
		if thirdCategory.Id == int32(params.CategoryId) && thirdCategory.ChannelStoreId == cast.ToString(storeMasterId) {
			request.Id = cast.ToInt64(thirdCategory.CategoryId)
			break
		}
	}
	if request.Id == 0 {
		return errors.New("未找到平台分类Id")
	}
	request.StoreMasterId = storeMasterId
	// 调用远程保存
	glog.Info("syncDeleteToJd同步到京东分类参数：", kit.JsonEncode(request))

	client := NewExternalClient()
	defer client.Close()
	response, err := client.JddjProduct.JddjDeleteShopCategory(context.Background(), &request)
	glog.Info("syncDeleteToJd同步到京东分类参数：", kit.JsonEncode(request), " resp: ", kit.JsonEncode(response), " err:", err)
	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else if response.Code != "0" {
		return errors.New(response.Msg)
	} else {
		glog.Info("syncDeleteToJd 同步到京东分类参数：", kit.JsonEncode(request), ";返回结果：", kit.JsonEncode(response))
		// 删除分类关联
		params.deleteCategoryThird("", cast.ToString(request.Id))
	}
	return nil
}

// 删除分类在美团渠道
func (params *CategorySync) syncDeleteToMt(channelStoreId string, storeMasterId int32) error {
	//构造删除参数
	var request et.MtRetailCatDeleteRequest
	request.AppPoiCode = channelStoreId
	request.CategoryName = cast.ToString(params.CategoryName)
	request.StoreMasterId = storeMasterId
	client := NewExternalClient()
	defer client.Close()
	glog.Info("MtRetailCatDelete req参数:", kit.JsonEncode(request))
	response, err := client.RPC.MtRetailCatDelete(client.Ctx, &request)
	glog.Info("MtRetailCatDelete: res", kit.JsonEncode(request), " resp:", kit.JsonEncode(response), " err:", err)
	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else if response.Code != 200 {
		return errors.New(response.Message)
	}
	return nil
}

// 删除分类在饿了么渠道
func (params *CategorySync) syncDeleteToElm(channelStoreId string, appChannel int32) error {
	// 构造删除参数
	var request et.DelElmShopCategoryRequest
	request.ShopId = channelStoreId
	request.AppChannel = appChannel
	// 提取父分类在平台的代码
	//todo 优化 直接使用sql查询 增加唯一键
	for _, thirdCategory := range params.ThirdCategoryList {
		if thirdCategory.ChannelStoreId == channelStoreId && thirdCategory.Id == int32(params.CategoryId) {
			request.CategoryId = thirdCategory.CategoryId
			break
		}
	}
	if len(request.CategoryId) == 0 {
		return errors.New("未找到平台分类Id")
	}
	client := NewExternalClient()
	defer client.Close()
	glog.Info("DelElmShopCategory req参数:", kit.JsonEncode(request))
	response, err := client.ELMPRODUCT.DelElmShopCategory(client.Ctx, &request)
	glog.Info("DelElmShopCategory res返回值:", kit.JsonEncode(request), " resp: ", kit.JsonEncode(response), " err:", err)
	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else if response.Code != 200 {
		if len(response.Message) > 0 {
			return errors.New(response.Message)
		} else {
			return errors.New(response.Error)
		}

	} else {
		// 删除第三方关联关系
		// todo 删除失败怎么办 下次插入得做比对或者主键约束
		params.deleteCategoryThird(channelStoreId, request.CategoryId)
	}
	return nil
}

// 保存微信视频号类目列表
func (this Product) SaveWxVideoCategoryList(ctx context.Context, in *pc.CategoryListRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400
	// 通过第三方接口获取类目列表
	wxGrpc := et.GetExternalClient()
	res, err := wxGrpc.WxVideo.GetWxCategoryList(context.Background(), &et.Empty{})
	if err != nil {
		glog.Error("SaveWxVideoCategoryList 通过微信视频号接口获取类目列表失败, err: ", err)
		return out, err
	}
	if res.Errcode != 0 {
		glog.Error("SaveWxVideoCategoryList 通过微信视频号接口获取类目列表失败, err: ", res.Errmsg)
		return out, err
	}

	// 筛选出宠物相关分类
	var wxCategoryList []*et.WxCategoryList
	for _, v := range res.ThirdCatList {
		if strings.Contains(v.FirstCatName, "宠物") {
			wxCategoryList = append(wxCategoryList, v)
		}
	}

	conn := NewDbConn()
	// 查询本地视频号分类
	var categoryList []models.WxVideoCategory
	err = conn.Find(&categoryList)
	if err != nil {
		glog.Error("SaveWxVideoCategoryList 查询本地视频号分类失败, err: ", err)
		return out, err
	}

	// 更新本地视频号类目
	err = updateWxVideoCategory(wxCategoryList, categoryList)
	if err != nil {
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 更新本地视频号类目
func updateWxVideoCategory(wxCategoryList []*et.WxCategoryList, categoryList []models.WxVideoCategory) error {
	session := NewDbConn().NewSession()
	defer session.Close()

	session.Begin()
	// 本地类目map,用于查找
	var categoryMap = make(map[int]*models.WxVideoCategory)
	for _, v := range categoryList {
		categoryMap[v.ThirdCatId] = &v
	}
	// 微信类目map.用于查找
	//var wxCategoryMap = make(map[int]*et.WxCategoryList)
	//for _, v := range wxCategoryList {
	//	wxCategoryMap[int(v.ThirdCatId)] = v
	//}

	// 入库列表
	var insertCategoryList []models.WxVideoCategory
	// 遍历微信类目
	for _, wx := range wxCategoryList {
		if cate, ok := categoryMap[int(wx.ThirdCatId)]; ok {
			// 若此分类双方信息有出入，则更新本地类目
			if cate.FirstCatName != wx.FirstCatName || cate.FirstCatId != int(wx.FirstCatId) || cate.SecondCatName != wx.SecondCatName ||
				cate.SecondCatId != int(wx.SecondCatId) || cate.ThirdCatName != wx.ThirdCatName || cate.Qualification != wx.Qualification ||
				cate.QualificationType != int(wx.QualificationType) || cate.ProductQualification != wx.ProductQualification ||
				cate.ProductQualificationType != int(wx.ProductQualificationType) {

				// 更新本地类目信息
				_, err := session.ID(cate.Id).Update(&models.WxVideoCategory{
					ThirdCatName:             wx.ThirdCatName,
					SecondCatId:              int(wx.SecondCatId),
					SecondCatName:            wx.SecondCatName,
					FirstCatId:               int(wx.FirstCatId),
					FirstCatName:             wx.FirstCatName,
					Qualification:            wx.Qualification,
					QualificationType:        int(wx.QualificationType),
					ProductQualification:     wx.ProductQualification,
					ProductQualificationType: int(wx.ProductQualificationType),
				})
				if err != nil {
					glog.Error("updateWxVideoCategory 更新本地视频号类目信息失败, err: ", err)
					session.Rollback()
					return err
				}
			}
		} else {
			// 若本地无此分类，则加入入库列表
			insertCategoryList = append(insertCategoryList, models.WxVideoCategory{
				ThirdCatId:               int(wx.ThirdCatId),
				ThirdCatName:             wx.ThirdCatName,
				Qualification:            wx.Qualification,
				QualificationType:        int(wx.QualificationType),
				ProductQualification:     wx.ProductQualification,
				ProductQualificationType: int(wx.ProductQualificationType),
				SecondCatId:              int(wx.SecondCatId),
				SecondCatName:            wx.SecondCatName,
				FirstCatId:               int(wx.FirstCatId),
				FirstCatName:             wx.FirstCatName,
			})
		}
	}

	//// 遍历本地类目列表
	//for _, cate := range categoryList {
	//	// 若微信无此分类
	//	if _, ok := wxCategoryMap[cate.ThirdCatId]; ok {
	//
	//	}
	//}

	// 入库
	_, err := session.Insert(&insertCategoryList)
	if err != nil {
		session.Rollback()
		glog.Error("updateWxVideoCategory 入库失败, err: ", err)
		return err
	}

	session.Commit()
	return nil
}

// 查询本地视频号类目
func (this Product) GetWxVideoCategoryList(ctx context.Context, in *pc.WxCategoryListRequest) (*pc.WxCategoryListResponse, error) {
	out := new(pc.WxCategoryListResponse)
	out.Errcode = 400
	session := engine.Table("wx_video_category").Where("1=1")
	// 三级类目id查询
	if in.ThirdCatId > 0 {
		session.And("third_cat_id = ?", in.ThirdCatId)
	}
	// 三级类目名称模糊查询
	if len(in.ThirdCatName) > 0 {
		session.And("third_cat_name like ?", "%"+in.ThirdCatName+"%")
	}
	// 二级类目名称模糊查询
	if len(in.SecondCatName) > 0 {
		session.And("second_cat_name like ?", "%"+in.SecondCatName+"%")
	}
	// 是否申请资质; 默认 0 全部, 1 不用申请资质或需要申请且已申请资质的
	if in.IsQualification == 1 {
		session.And("(is_qualification = 1 and qualification_type = 1) or qualification_type = 0")
	}
	// 类目资质类型,默认 -1 全部，0:不需要,1:必填,2:选填
	if in.QualificationType >= 0 {
		session.And("qualification_type = ?", in.QualificationType)
	}
	// 商品资质类型,默认 -1 全部，0:不需要,1:必填,2:选填
	if in.ProductQualificationType >= 0 {
		session.And("product_qualification_type = ?", in.ProductQualificationType)
	}

	err := session.Select(`third_cat_id, third_cat_name, qualification, qualification_type, product_qualification, product_qualification_type, 
			second_cat_id, second_cat_name, first_cat_id, first_cat_name, is_qualification, qualification_url`).Find(&out.ThirdCatList)
	if err != nil {
		glog.Error("GetWxVideoCategoryList 查询本地视频号类目失败, err: ", err)
		return out, err
	}
	out.Errcode = 200
	return out, nil
}

// v6.4.0 定时任务使用 后面改成分布式可以删除
func (sync CategorySync) DealWithMeituanChannel(storeMaster []*dac.StoreInfo) []*SyncCategoryToThirdParams_Result {

	glog.Info("DealWithMeituanChannel run :", len(storeMaster))
	var wg syncWait.WaitGroup
	MtfailResult := []*SyncCategoryToThirdParams_Result{}

	//externalGrpc := et.GetExternalClient()
	//defer externalGrpc.Close()
	chan_mt := make(chan SyncCategoryToThirdParams_Result, len(storeMaster))
	closeChannel := make(chan bool)

	defer func() {
		if _, ok := <-closeChannel; ok {
			closeChannel <- true
		}
	}()

	go func() {
		for true {
			select {
			case data := <-chan_mt:
				MtfailResult = append(MtfailResult, &data)
			case <-closeChannel:
				close(chan_mt)
				close(closeChannel)
				return
			}
		}
	}()

	//p, _ := ants.NewPool(4)
	//defer p.Release()
	for i := range storeMaster {

		info := storeMaster[i]
		var result SyncCategoryToThirdParams_Result

		result.ChannelId = ChannelMtId
		result.categoryId = sync.CategoryId
		result.categoryName = sync.CategoryName
		result.financeCode = info.FinanceCode
		result.ShopName = info.Name
		result.SyncType = sync.SyncType

		//fmt.Println(" info : ", kit.JsonEncode(info))
		// 处理新增 //处理编辑
		wg.Add(1)
		_ = AntsP.Submit(func() {
			defer func() {
				wg.Done()
			}()
			if sync.SyncType == 1 || sync.SyncType == 2 {

				err := sync.syncEditToMt(info.ChannelStoreId, info.AppChannel)
				if err != nil {
					glog.Error("syncEditToMt err:", err, ",参数：", kit.JsonEncode(sync))
					result.Message = err.Error()
					//MtfailResult = append(MtfailResult, &result)
					chan_mt <- result
				} else {
					result.IsSuccess = true
					// 同步更新或者插入数据到表
					if sync.SyncType == 1 {
						// 保存第三方关联关系
						err := sync.insertCategoryThird(info.ChannelStoreId, cast.ToString(sync.CategoryId))
						if err != nil {
							glog.Error("保存美团的本地第三方关联失败：", err.Error())
						}
					}
				}

			}

			// 处理删除
			if sync.SyncType == 3 {

				err := sync.syncDeleteToMt(info.ChannelStoreId, info.AppChannel)
				if err != nil {
					glog.Error("syncDeleteToElm err:", err, ",参数：", kit.JsonEncode(sync))
					result.Message = err.Error()
					//MtfailResult = append(MtfailResult, &result)
					chan_mt <- result
				} else {
					result.IsSuccess = true
					// 同步删除第三方关联关系
					_, err := engine.Exec("delete from dc_product.channel_category_thirdid "+
						"where channel_id = ? and channel_store_id = ? and id = ?", sync.ChannelId, info.ChannelStoreId, sync.CategoryId)
					if err != nil {
						glog.Error("同步删除美团本地三方关系异常：", err.Error())
					}
				}
			}

		})
	}

	wg.Wait()
	closeChannel <- true

	glog.Info("failResult_mt :", len(MtfailResult))
	return MtfailResult
}

func (sync CategorySync) DealWithElemeChannel(storeMaster []*dac.StoreInfo) []*SyncCategoryToThirdParams_Result {

	glog.Info("DealWithElemeChannel run :", len(storeMaster))
	var wg syncWait.WaitGroup
	var failResult []*SyncCategoryToThirdParams_Result
	//externalGrpc := et.GetExternalClient()
	//defer externalGrpc.Close()
	chan_ele := make(chan SyncCategoryToThirdParams_Result, len(storeMaster))
	closeChannel := make(chan bool)

	defer func() {
		if _, ok := <-closeChannel; ok {
			closeChannel <- true
		}
	}()

	go func() {
		for true {
			select {
			case data := <-chan_ele:
				failResult = append(failResult, &data)
			case <-closeChannel:
				close(chan_ele)
				close(closeChannel)
				return
			}
		}
	}()
	//p, _ := ants.NewPool(4)
	//defer p.Release()
	for i := range storeMaster {
		info := storeMaster[i]
		//fmt.Println(" info : ", kit.JsonEncode(info))

		var result SyncCategoryToThirdParams_Result

		result.ChannelId = ChannelElmId
		result.categoryId = sync.CategoryId
		result.categoryName = sync.CategoryName
		result.financeCode = info.FinanceCode
		result.ShopName = info.Name
		result.SyncType = sync.SyncType
		wg.Add(1)
		// 处理新增 //处理编辑
		_ = AntsP.Submit(func() {
			defer func() {
				wg.Done()
			}()
			if sync.SyncType == 1 || sync.SyncType == 2 {
				err := sync.syncEditToElm(info.ChannelStoreId, info.AppChannel)
				if err != nil {
					glog.Error("syncEditToElm err:", err, ",参数：", kit.JsonEncode(sync))
					result.Message = err.Error()
					//failResult = append(failResult, &result)
					chan_ele <- result
				} else {
					result.IsSuccess = true
				}
			}

			// 处理删除
			if sync.SyncType == 3 {
				err := sync.syncDeleteToElm(info.ChannelStoreId, info.AppChannel)
				if err != nil {
					glog.Error("syncDeleteToElm err:", err, ",参数：", kit.JsonEncode(sync))
					result.Message = err.Error()
					//failResult = append(failResult, &result)
					chan_ele <- result
				} else {
					result.IsSuccess = true
				}
			}

		})
	}
	wg.Wait()
	closeChannel <- true
	glog.Info("failResult_ele :", len(failResult))
	return failResult
}

func (sync CategorySync) DealWithJddjChannel(syncAppChannelMap map[int32]struct{}) []*SyncCategoryToThirdParams_Result {

	glog.Info("DealWithJddjChannel run :", kit.JsonEncode(syncAppChannelMap))
	var wg syncWait.WaitGroup
	failResult := []*SyncCategoryToThirdParams_Result{}
	//externalGrpc := et.GetExternalClient()
	//defer externalGrpc.Close()
	chan_jdj := make(chan SyncCategoryToThirdParams_Result, len(syncAppChannelMap))
	closeChannel := make(chan bool)

	defer func() {
		if _, ok := <-closeChannel; ok {
			closeChannel <- true
		}
	}()

	go func() {
		for true {
			select {
			case data := <-chan_jdj:
				failResult = append(failResult, &data)
			case <-closeChannel:
				close(chan_jdj)
				close(closeChannel)
				return
			}
		}
	}()
	//p, _ := ants.NewPool(2)
	//defer p.Release()
	for appChannel, v := range syncAppChannelMap {

		var result SyncCategoryToThirdParams_Result

		result.ChannelId = ChannelJddjId
		result.categoryId = sync.CategoryId
		result.categoryName = sync.CategoryName
		result.SyncType = sync.SyncType

		app_channel := appChannel
		glog.Info("syncAppChannelMap-k: ", appChannel, "syncAppChannelMap-v ", v)

		wg.Add(1)
		_ = AntsP.Submit(func() {

			defer func() {
				wg.Done()
			}()
			// 处理新增  //处理编辑
			if sync.SyncType == 1 || sync.SyncType == 2 {
				err := sync.syncEditToJd(app_channel)
				if err != nil {
					glog.Error("syncEditToJd err:", err, ",参数：", kit.JsonEncode(sync))
					result.Message = err.Error()
					//failResult = append(failResult, &result)
					chan_jdj <- result
				} else {
					result.IsSuccess = true
				}
			}

			// 处理删除
			if sync.SyncType == 3 {
				err := sync.syncDeleteToJd(app_channel)
				if err != nil {
					glog.Error("syncDeleteToJd err:", err, ",参数：", kit.JsonEncode(sync))
					result.Message = err.Error()
					//failResult = append(failResult, &result)
					chan_jdj <- result
				} else {
					result.IsSuccess = true
				}
			}
		})
	}

	wg.Wait()
	closeChannel <- true

	glog.Info("failResult_jddj :", len(failResult))
	return failResult

}

// V6.4.0分布式调用改造
func (c *Product) DealWithMeiTuanChannel(ctx context.Context, in *pc.SyncCategoryScheduleRequest) (*pc.SyncCategoryScheduleResponse, error) {

	glog.Info("DealWithMeiTuanChannel : ", kit.JsonEncode(in))
	var resp pc.SyncCategoryScheduleResponse
	resp.Code = 200
	//externalGrpc := et.GetExternalClient()
	//defer externalGrpc.Close()

	sync := &CategorySync{
		SyncType:     int(in.SyncType),
		ChannelId:    int(in.ChannelId),
		CategoryId:   int(in.CategoryId),
		CategoryName: in.CategoryName,
		ParentId:     int(in.ParentId),
		Sort:         int(in.Sort),
	}

	//engine.ShowSQL(true)
	err := engine.Where("channel_id=?", in.ChannelId).Find(&sync.ThirdCategoryList)
	if err != nil {
		glog.Error("获取第三方分类异常：", err)
	}

	// 处理新增 //处理编辑
	if sync.SyncType == 1 || sync.SyncType == 2 {

		err := sync.syncEditToMt(in.ChannelStoreId, in.AppChannel)
		if err != nil {
			glog.Error("syncEditToMt err:", err, ",参数：", kit.JsonEncode(sync))
			in.Message = err.Error()

			resp.Code = 400
			resp.Message = err.Error()
		} else {
			in.IsSuccess = true
			// 同步更新或者插入数据到表
			if sync.SyncType == 1 {
				// 保存第三方关联关系
				err := sync.insertCategoryThird(in.ChannelStoreId, cast.ToString(sync.CategoryId))
				if err != nil {
					glog.Error("保存美团的本地第三方关联失败：", err.Error())
				}
			}
		}
	}

	// 处理删除
	if sync.SyncType == 3 {
		err := sync.syncDeleteToMt(in.ChannelStoreId, in.AppChannel)
		if err != nil {
			glog.Error("syncDeleteToElm err:", err, ",参数：", kit.JsonEncode(sync))
			in.Message = err.Error()

			resp.Code = 400
			resp.Message = err.Error()

		} else {
			in.IsSuccess = true
			// 同步删除第三方关联关系
			_, err := engine.Exec("delete from dc_product.channel_category_thirdid "+
				"where channel_id = ? and channel_store_id = ? and id = ?", sync.ChannelId, in.ChannelStoreId, sync.CategoryId)
			if err != nil {
				glog.Error("同步删除美团本地三方关系异常：", err.Error())
			}
		}
	}

	resp.Data = in
	return &resp, nil
}

func (c *Product) DealWithEleMeChannel(ctx context.Context, in *pc.SyncCategoryScheduleRequest) (*pc.SyncCategoryScheduleResponse, error) {
	glog.Info("DealWithEleMeChannel : ", kit.JsonEncode(in))
	var resp pc.SyncCategoryScheduleResponse
	resp.Code = 200
	//externalGrpc := et.GetExternalClient()
	//defer externalGrpc.Close()
	sync := &CategorySync{
		SyncType:     int(in.SyncType),
		ChannelId:    int(in.ChannelId),
		CategoryId:   int(in.CategoryId),
		CategoryName: in.CategoryName,
		ParentId:     int(in.ParentId),
		Sort:         int(in.Sort),
	}
	err := engine.Where("channel_id=?", in.ChannelId).Find(&sync.ThirdCategoryList)
	if err != nil {
		glog.Error("获取第三方分类异常：", err)
	}

	// 处理新增 //处理编辑
	if sync.SyncType == 1 || sync.SyncType == 2 {
		err := sync.syncEditToElm(in.ChannelStoreId, in.AppChannel)
		if err != nil {
			glog.Error("syncEditToElm err:", err, ",参数：", kit.JsonEncode(sync))
			in.Message = err.Error()
			resp.Code = 400
			resp.Message = err.Error()

		} else {
			in.IsSuccess = true
		}
	}

	// 处理删除
	if sync.SyncType == 3 {
		err := sync.syncDeleteToElm(in.ChannelStoreId, in.AppChannel)
		if err != nil {
			glog.Error("syncDeleteToElm err:", err, ",参数：", kit.JsonEncode(sync))
			in.Message = err.Error()
			resp.Code = 400
			resp.Message = err.Error()
		} else {
			in.IsSuccess = true
		}
	}

	resp.Data = in
	return &resp, nil
}

func (c *Product) DealWithJDDJChannel(ctx context.Context, in *pc.SyncCategoryScheduleRequest) (*pc.SyncCategoryScheduleResponse, error) {
	glog.Info("DealWithJDDJChannel : ", kit.JsonEncode(in))
	var resp pc.SyncCategoryScheduleResponse
	resp.Code = 200
	externalGrpc := et.GetExternalClient()
	defer externalGrpc.Close()
	sync := &CategorySync{
		SyncType:     int(in.SyncType),
		ChannelId:    int(in.ChannelId),
		CategoryId:   int(in.CategoryId),
		CategoryName: in.CategoryName,
		ParentId:     int(in.ParentId),
		Sort:         int(in.Sort),
	}
	err := engine.Where("channel_id=?", in.ChannelId).Find(&sync.ThirdCategoryList)
	if err != nil {
		glog.Error("获取第三方分类异常：", err)
	}

	// 处理新增  //处理编辑
	if sync.SyncType == 1 || sync.SyncType == 2 {
		glog.Info("新增或者修改jd分类：", kit.JsonEncode(sync), " appChannel ：", in.AppChannel)
		err := sync.syncEditToJd(in.AppChannel)
		glog.Info("新增或者修改jd分类：", kit.JsonEncode(sync), " err:", err, " appChannel ：", in.AppChannel)
		if err != nil {
			glog.Error("syncEditToJd err:", err, ",参数：", kit.JsonEncode(sync))
			in.Message = err.Error()

			resp.Code = 400
			resp.Message = err.Error()

		} else {
			in.IsSuccess = true
		}
	}

	// 处理删除
	if sync.SyncType == 3 {
		glog.Info("删除jd分类：", kit.JsonEncode(sync))
		err := sync.syncDeleteToJd(in.AppChannel)
		glog.Info("删除jd分类：", kit.JsonEncode(sync), " err:", err)
		if err != nil {
			glog.Error("syncDeleteToJd err:", err, ",参数：", kit.JsonEncode(sync))
			in.Message = err.Error()

			resp.Code = 400
			resp.Message = err.Error()
		} else {
			in.IsSuccess = true
		}
	}

	resp.Data = in
	return &resp, nil

}

// 结果回调通知
func (c *Product) SyncCategorySchedulerCallBack(ctx context.Context, req *pc.SyncCategoryScheduleCallBackRequest) (*pc.BaseResponse, error) {

	defer func() {
		if err1 := recover(); err1 != nil {
			glog.Error("结果回调通知异常捕获：", err1)
		}
	}()

	glog.Info("SyncCategorySchedulerCallBack结果回调通知参数", kit.JsonEncode(req))
	var resp pc.BaseResponse
	resp.Code = 200

	if req.TaskId == 0 {
		glog.Error("Product/SyncCategorySchedulerCallBack参数taskId不能为空")
		resp.Code = 400
		return &resp, nil
	}
	glog.Info("PWT000 ")
	taskResp, err := c.GetTaskList(ctx, &pc.GetTaskListRequest{
		Id:          int32(req.TaskId),
		TaskContent: int32(SyncCategoryTaskContent),
	})
	glog.Info("PWT000 taskResp", kit.JsonEncode(taskResp))
	if err != nil {
		glog.Errorf("Product/SyncCategorySchedulerCallBack 查询任务详情失败,taskId:%d,%+v", req.TaskId, err)
		return &resp, fmt.Errorf("查询任务详情失败,%+v", err)
	}
	if len(taskResp.TaskList) == 0 {
		glog.Error("Product/SyncCategorySchedulerCallBack 查询不到任务信息,taskId:", req.TaskId)
		resp.Code = 400
		return &resp, nil
	}

	task := taskResp.TaskList[0]
	glog.Info("task :", kit.JsonEncode(task))
	// 处理数据
	glog.Info("PWT000 处理数据")
	failResult, detail := c.mergeCategoryData(req.Vo, task.TaskDetail)
	//glog.Info("PWT000 failResult", kit.JsonEncode(failResult))
	sync := CategorySync{}
	sync.Result = failResult
	fileUrl, taskDetail := sync.ExportToExcel()
	glog.Info("PWT000 taskdetail : ", taskDetail)
	// 更新任务信息
	updateModel := models.TaskList{
		TaskStatus:     3,
		TaskDetail:     detail,
		ModifyTime:     time.Now(),
		ResulteFileUrl: fileUrl,
	}

	_, err = engine.Where("id=?", task.Id).Update(updateModel)
	if err != nil {
		glog.Errorf("Product/SyncCategorySchedulerCallBack 更新任务状态异常,taskId:%d,%+v", req.TaskId, err)
		return &resp, fmt.Errorf("更新任务状态异常,%+v", err)
	}

	return &resp, nil
}

// 合并切仓结果数据
func (c *Product) mergeCategoryData(vo []*pc.SyncCategoryScheduleRequest, taskDetail string) ([]*SyncCategoryToThirdParams_Result, string) {

	glog.Info("mergeCategoryData: ", kit.JsonEncode(vo), " detail : ", kit.JsonEncode(taskDetail))

	allFail := []*SyncCategoryToThirdParams_Result{}
	MtfailResults := []*SyncCategoryToThirdParams_Result{}
	EleMefailResults := []*SyncCategoryToThirdParams_Result{}
	jddjfailResults := []*SyncCategoryToThirdParams_Result{}

	MtSuccessResults := 0
	EleMeSuccessResults := 0
	jddjSuccessResults := 0
	for i := range vo {
		request := vo[i]

		if !request.IsSuccess {
			var dto = SyncCategoryToThirdParams_Result{
				categoryId:     int(request.CategoryId),
				categoryName:   request.CategoryName,
				financeCode:    request.FinanceCode,
				ShopName:       request.ShopName,
				IsSuccess:      false,
				Message:        request.Message,
				ChannelId:      int(request.ChannelId),
				SyncType:       int(request.SyncType),
				ChannelStoreId: request.ChannelStoreId,
				AppChannel:     request.AppChannel,
			}

			if len(request.SystemError) > 0 {
				dto.Message = request.SystemError
			}
			if request.ChannelId == ChannelMtId {
				MtfailResults = append(MtfailResults, &dto)
			}
			if request.ChannelId == ChannelElmId {
				EleMefailResults = append(EleMefailResults, &dto)
			}

			if request.ChannelId == ChannelJddjId {
				jddjfailResults = append(jddjfailResults, &dto)
			}
		} else {
			if request.ChannelId == ChannelMtId {
				MtSuccessResults++
			}
			if request.ChannelId == ChannelElmId {
				EleMeSuccessResults++
			}

			if request.ChannelId == ChannelJddjId {
				jddjSuccessResults++
			}
		}
	}

	var details []*models.TaskDetail
	err := json.Unmarshal([]byte(taskDetail), &details)
	if err != nil {
		glog.Error("json解析异常：", err.Error())
	}

	for e := range details {
		detail := details[e]
		if detail.ChannelId == ChannelMtId {
			detail.FailNum = len(MtfailResults)
			detail.SuccessNum = MtSuccessResults
		}
		if detail.ChannelId == ChannelElmId {
			detail.FailNum = len(EleMefailResults)
			detail.SuccessNum = EleMeSuccessResults
		}
		if detail.ChannelId == ChannelJddjId {
			detail.FailNum = len(jddjfailResults)
			detail.SuccessNum = jddjSuccessResults
		}

	}

	allFail = append(allFail, MtfailResults...)
	allFail = append(allFail, EleMefailResults...)
	allFail = append(allFail, jddjfailResults...)
	return allFail, kit.JsonEncode(details)
}
