package services

import (
	"_/proto/pc"
	"context"
	"fmt"
	"testing"
)

func TestProduct_GetUploadSkuList(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.UploadSkuListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.UploadSkuListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &pc.UploadSkuListRequest{
					AppId:     6,
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := p.GetUploadSkuList(tt.args.ctx, tt.args.in)
			fmt.Println(got)
			fmt.Println(err)
		})
	}
}

func TestProduct_RemoveUploadSku(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.UploadSkuDeleteRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.UploadSkuDeleteResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &pc.UploadSkuDeleteRequest{
					Id: 18,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := p.RemoveUploadSku(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}

func TestProduct_IsUpProduct(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.UploadSkuDeleteRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.UploadSkuDeleteResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &pc.UploadSkuDeleteRequest{
					Id: 18,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := p.IsUpProduct(tt.args.ctx, 60, 0)
			fmt.Println(got, err)
		})
	}
}
