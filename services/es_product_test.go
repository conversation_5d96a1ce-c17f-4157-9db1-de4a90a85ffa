package services

import (
	"_/proto/es"
	"context"
	"fmt"
	"testing"

	"github.com/olivere/elastic/v7"
	"github.com/techoner/gophp"
)

/*
DELETE FROM tmp_product_info;
INSERT tmp_product_info(channel_id,finance_code,sku_id,json_data) select channel_store_product.sales_volume,channel_store_product.channel_id,channel_store_product.finance_code,channel_store_product.sku_id,channel_product_snapshot.json_data from channel_store_product INNER JOIN channel_product_snapshot ON channel_product_snapshot.id=channel_store_product.snapshot_id WHERE channel_store_product.up_down_state=1;


DROP TABLE tmp_product_info;
CREATE TABLE tmp_product_info AS (select channel_store_product.sales_volume,channel_store_product.channel_id,channel_store_product.finance_code,channel_store_product.sku_id,channel_product_snapshot.json_data from channel_store_product INNER JOIN channel_product_snapshot ON channel_product_snapshot.id=channel_store_product.snapshot_id WHERE channel_store_product.up_down_state=1);

*/

/**/

func init() {
	//"readonly:dsax45677uDHR3gGGFJU-@(s2b2c-master.mysql.polardb.rds.aliyuncs.com:3339)/dc_product?charset=utf8mb4"
	//engine = NewDbConn()
	// esClient, _ = elastic.NewClient(elastic.SetSniff(false), elastic.SetURL("http://10.1.1.248:9201"), elastic.SetBasicAuth("elastic", "kCC6y672Wexjw44a"))
	// esClient, _ = elastic.NewClient(elastic.SetSniff(false), elastic.SetURL("http://10.11.12.12:9200"), elastic.SetBasicAuth("elastic", "kCC6y672Wexjw44a"))
	//esClient, _ = elastic.NewClient(elastic.SetSniff(false), elastic.SetURL("http://es-cn-n6w1rdbxt0002d0jl.public.elasticsearch.aliyuncs.com:9200"), elastic.SetBasicAuth("elastic", "kCC6y672Wexjw44a"))
}

func Test_importChannelStoreProduct(t *testing.T) {
	tests := []struct {
		name      string
		indexName string
		wantErr   bool
	}{
		{
			name:      "导入渠道门店商品到es",
			indexName: "channel_store_product",
			wantErr:   false,
		},
	}

	// client := es.NewEsClient()
	// client := es.NewEsClient("http://es-cn-m7r1uawz10006espz.public.elasticsearch.aliyuncs.com:9200", "elastic", "dk3aOf6U")

	// client.DeleteByQuery("channel_store_product").Query(elastic.NewMatchAllQuery()).Do(context.Background())

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := importChannelStoreProduct(tt.indexName); (err != nil) != tt.wantErr {
				t.Errorf("importChannelStoreProduct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_importUpetGoods(t *testing.T) {
	tests := []struct {
		name      string
		indexName string
		wantErr   bool
	}{
		{
			name:      "导入电商商品到es",
			indexName: "channel_store_product",
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := importUpetGoods(tt.indexName); (err != nil) != tt.wantErr {
				t.Errorf("importUpetGoods() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSpec(t *testing.T) {
	spec, _ := gophp.Unserialize([]byte(`a:2:{i:1;s:12:"种　　类";i:2;s:12:"规　　格";}`))
	specValue, _ := gophp.Unserialize([]byte(`a:2:{i:236;s:9:"鸡肉味";i:240;s:16:"8寸（265g）*6";}`))

	if spec != nil && specValue != nil {
		var arrSpec, arrSpecValue []string
		for _, kv := range spec.(map[string]interface{}) {
			arrSpec = append(arrSpec, kv.(string))
		}
		for _, kv := range specValue.(map[string]interface{}) {
			arrSpecValue = append(arrSpecValue, kv.(string))
		}
		for i := 0; i < len(arrSpec); i++ {
			println(arrSpec[i], arrSpecValue[i])
		}
	}
}

func TestAllProduct(t *testing.T) {
	// engine := NewDbConn("readonly:dsax45677uDHR3gGGFJU-@(211.154.155.93:3366)/dc_product?charset=utf8mb4")

	// var code []string
	// // engine.ShowSQL(true)
	// engine.Table("channel_store_product").Select("finance_code").Distinct().Where("channel_id=1 AND up_down_state=1").Find(&code)
	// client := es.NewEsClient("http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200", "elastic", "PRO2GUm1ZIw4AFNitdz")

	// for _, v := range code {

	// 	var products []*pc.ChannelProductEsBaseData

	// 	engine.SQL("SELECT channel_store_product.id,channel_store_product.update_date, channel_store_product.sales_volume, channel_store_product.channel_id, channel_store_product.finance_code, channel_store_product.sku_id, channel_store_product.up_down_state, REPLACE(REPLACE(CONCAT(species,',', varieties,',', sex,',', shape,',', age,',', special_stage,',', is_sterilization,',', content_type,',', `status`),'不限,',''),',不限','') as tags, channel_product_snapshot.json_data FROM channel_store_product INNER JOIN channel_product_snapshot ON channel_product_snapshot.id = channel_store_product.snapshot_id LEFT JOIN product_tag on product_tag.sku_id=channel_store_product.sku_id AND product_tag.product_type=3 WHERE channel_store_product.channel_id=1 AND channel_store_product.up_down_state=1 AND channel_store_product.finance_code=?", v).Find(&products)

	// 	println(v, len(products))

	// 	bulkRequest := client.Bulk()
	// 	for _, p := range products {
	// 		m := es.FormatChannelProductRequestEs(p)
	// 		bulkRequest.Add(elastic.NewBulkIndexRequest().Index(es.IndexChannelStoreProduct).Id(fmt.Sprintf("%s-1-%d", m.FinanceCode, p.SkuId)).Doc(m))
	// 	}
	// 	bulkRequest.Do(context.Background())
	// }
}

func TestAllStore(t *testing.T) {
	// engine := NewDbConn("readonly:dsax45677uDHR3gGGFJU-@(211.154.155.93:3366)/dc_product?charset=utf8mb4")

	// var code []string
	// // engine.ShowSQL(true)
	// engine.Table("channel_store_product").Select("finance_code").Distinct().Where("channel_id=1 AND up_down_state=1").Find(&code)
	// client := es.NewEsClient("http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200", "elastic", "PRO2GUm1ZIw4AFNitdz")

	// for _, v := range code {
	// 	r, _ := client.Search(es.IndexStore).Query(elastic.NewTermQuery("_id", v)).Do(context.Background())
	// 	if r.TotalHits() == 0 {
	// 		client.DeleteByQuery(es.IndexChannelStoreProduct).Query(elastic.NewTermQuery("finance_code.keyword", v)).Do(context.Background())
	// 		println(v)
	// 	}
	// }
}

func TestAllStore2(t *testing.T) {
	engine := NewDbConn("readonly:dsax45677uDHR3gGGFJU-@(211.154.155.93:3366)/dc_product?charset=utf8mb4")

	var code []string
	// engine.ShowSQL(true)
	engine.Table("channel_store_product").Select("finance_code").Distinct().Where("channel_id=1 AND up_down_state=1").Find(&code)
	client := es.NewEsClient("http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200", "elastic", "PRO2GUm1ZIw4AFNitdz")

	for _, v := range code {
		r, _ := client.Search(es.IndexStore).Query(elastic.NewTermQuery("_id", v)).Do(context.Background())
		if r.TotalHits() == 0 {
			// client.DeleteByQuery(es.IndexChannelStoreProduct).Query(elastic.NewTermQuery("finance_code.keyword", v)).Do(context.Background())
			println(v)
		}
	}
}

func TestDeletePrice0(t *testing.T) {
	client := es.NewEsClient("http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200", "elastic", "PRO2GUm1ZIw4AFNitdz")
	r, _ := client.Search(es.IndexChannelStoreProduct).Query(elastic.NewTermQuery("sku_info.market_price", 0)).Do(context.Background())
	client.DeleteByQuery(es.IndexChannelStoreProduct).Query(elastic.NewTermQuery("sku_info.market_price", 0)).Do(context.Background())
	fmt.Println(r.Hits.TotalHits.Value)
}

func TestDiffFinaceCode(t *testing.T) {
	engine := NewDbConn("readonly:dsax45677uDHR3gGGFJU-@(211.154.155.93:3366)/dc_product?charset=utf8mb4")
	// client := es.NewEsClient("http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200", "elastic", "PRO2GUm1ZIw4AFNitdz")
	var products []string
	engine.SQL("SELECT CONCAT(finance_code,'-',channel_id,'-',sku_id) FROM channel_store_product WHERE channel_id=1 AND channel_store_product.up_down_state=0 and finance_code='YC0026'").Find(&products)
	println(len(products))
	size := 5000
	for {
		if size > len(products) {
			size = len(products)
		}
		println(len(products[:size]))
		// bulkRequest := client.Bulk()
		// for _, v := range products[:size] {
		// 	bulkRequest.Add(elastic.NewBulkDeleteRequest().Index(es.IndexChannelStoreProduct).Id(v))
		// }
		// bulkRequest.Do(context.Background())
		products = products[size:]
		if len(products) == 0 {
			break
		}
	}
}
