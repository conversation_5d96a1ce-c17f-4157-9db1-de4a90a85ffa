package services

import (
	"_/enum"
	"_/models"
	"strings"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 获取所有的上架商品
func (p *Product) GetAllUpProducts(chinnel_id int, financeOne string, skuIds []int) ([]*models.UpProduct, error) {
	db := NewDbConn()
	var data []*models.UpProduct
	if len(skuIds) <= 0 {
		return data, nil
	}
	err := db.Table(&models.ChannelStoreProduct{}).Select("id, channel_id, finance_code ,product_id, sku_id").Where("up_down_state= ? ", 1).
		And("channel_id =? ", chinnel_id).And("finance_code= ? ", financeOne).In("sku_id", skuIds).Find(&data)

	if err != nil {
		return data, err
	}

	return data, nil
}

// 获取渠道有上架的门店
func (p *Product) GetUpProductsFinanceCodes(chinnel_id, tid int) ([]string, error) {
	db := NewDbConn()

	var data []string
	if tid == 2 {
		if err := db.SQL("select finance_code from dc_product.channel_store_product "+
			"where up_down_state = 1 and down_type =5 and channel_id = ? group by finance_code ;", chinnel_id).
			Find(&data); err != nil {
			return data, err
		}
	} else {
		if err := db.SQL("select finance_code from dc_product.channel_store_product "+
			"where up_down_state = 1 and channel_id = ? group by finance_code ;", chinnel_id).
			Find(&data); err != nil {
			return data, err
		}
	}

	return data, nil
}

// 获取7天无库存的商品数据
func (p *Product) WarehouseHasNoStock(warehouse_id []int32) ([]*models.GoodsStock, error) {

	conn := NewDbConn()

	var warehosueIds []string
	for i := range warehouse_id {
		warehosueIds = append(warehosueIds, cast.ToString(warehouse_id[i]))
	}

	var data = []*models.GoodsStock{}
	sql := `select * from (
	SELECT 
	a.goodsid,
	(IFNULL(a.stock , 0 ) - IFNULL(b.stock , 0 )) AS stock,
	a.warehouse_id
	FROM dc_order.warehouse_goods a 
	LEFT JOIN dc_order.order_freeze_stock b ON a.warehouse_id = b.warehouse_id  AND a.goodsid = b.sku_id 
	WHERE a.warehouse_id in (` + strings.Join(warehosueIds, ",") + ") " + ` and date(a.lastdate) < date_add(curdate(), interval -7 day)
	GROUP BY a.warehouse_id, a.goodsid) a where a.stock <= 0;
`
	err := conn.SQL(sql).Find(&data)
	if err != nil {
		glog.Error("查询仓库库存异常：", err.Error())
		return data, err
	}

	return data, nil
}

// 获取子龙变不可销商品
func (p *Product) ChannelHasUncansell(channelId int, financeCode string) ([]*models.UpProduct, error) {
	db := NewDbConn()
	var data []*models.UpProduct
	err := db.SQL("select id, channel_id, finance_code ,product_id, sku_id from dc_product.channel_store_product "+
		"where down_type = 5 and up_down_state = 1 and channel_id =? and finance_code = ? order by update_date desc;", channelId, financeCode).Find(&data)
	if err != nil {
		return data, err
	}
	return data, nil
}

/*
*
针对阿闻渠道的外卖和自提不同仓库的sku取值
取出7天无库存数据的交集以及 外卖仓库7天的在自提仓库不存在的或者自提7天库存在外卖中不存在的sku数据
*/
func (p *Product) DifferSku(in []*models.GoodsStock, warehouseIds []int32) ([]int, error) {

	//glog.Info("DifferSku入参：", kit.JsonEncode(in), " 仓库id：", warehouseIds)

	codeMap1 := make(map[int]struct{}, 0)
	codeMap2 := make(map[int]struct{}, 0)
	for i := range in {
		if in[i].WarehouseId == int(warehouseIds[0]) {
			codeMap1[in[i].Goodsid] = struct{}{}
		}
		if in[i].WarehouseId == int(warehouseIds[1]) {
			codeMap2[in[i].Goodsid] = struct{}{}
		}
	}

	//交集
	var Intersections = []int{}

	for i, _ := range codeMap1 {
		k1 := i
		if _, ok := codeMap2[k1]; ok {
			Intersections = append(Intersections, k1)
		}
	}

	conn := NewDbConn()

	var wares []string
	for i := range warehouseIds {
		wares = append(wares, cast.ToString(warehouseIds[i]))
	}

	sql := "select  goodsid , warehouse_id from  dc_order.warehouse_goods wg where warehouse_id in (" + strings.Join(wares, ",") + ")"

	var vd = []models.GoodsStock{}
	err := conn.SQL(sql).Find(&vd)
	if err != nil {
		glog.Error("查询仓库数据异常：", err.Error())
		return nil, err
	}

	map1 := make(map[int]struct{}) // 仓库1的sku数据
	map2 := make(map[int]struct{}) // 仓库2的sku数据
	for i := range vd {
		stock := vd[i]
		if stock.WarehouseId == int(warehouseIds[0]) {
			map1[stock.Goodsid] = struct{}{}
		} else {
			map2[stock.Goodsid] = struct{}{}
		}
	}

	// codeMap1差集判断
	for i := range codeMap1 {
		k1 := i
		if _, ok := map2[k1]; !ok { // 在仓库2中没有的加到交集里面
			Intersections = append(Intersections, k1)
		}

	}
	// codeMap1差集判断
	for i := range codeMap2 {
		k1 := i
		if _, ok := map1[k1]; !ok { // 在仓库1中没有的加到交集里面
			Intersections = append(Intersections, k1)
		}
	}

	glog.Info("DifferSku返回：", " 仓库id：", warehouseIds, " 集合：", Intersections)
	return Intersections, nil
}

func (p *Product) DownProducts(tid, channel_id int, in []*models.UpProduct, financeOne string) error {

	prefix := "执行下架,自动下架DownProducts方法调用： "
	glog.Info(prefix, " 参数输入门店：", financeOne, " 渠道：", channel_id, " data:", kit.JsonEncode(in))

	products := []string{}
	for i := range in {
		products = append(products, cast.ToString(in[i].ProductId))
	}
	var (
		UserNo   = DownType7DaysNoStock
		UserName = DownType7DaysNoStock
		DownType = DaysNoStockDownType
	)
	if tid == 2 {
		UserNo = DownTypeCansele
		UserName = DownTypeCansele
		DownType = ZlCanseleDownType
	}
	down := &ChannelProductUpDown{
		ProductIds:   products,
		ChannelId:    channel_id,
		FinanceCodes: []string{financeOne},
		UserNo:       UserNo,
		UserName:     UserName,
		IsSyncPrice:  true,
		DownType:     DownType,
	}

	// 调用下架接口
	down.DownPorudct()
	//glog.Info(prefix, " 下架返回门店：", financeOne, " 渠道channel_id: ", channel_id, " 返回数据结果： ", kit.JsonEncode(down.UpResult))

	return nil
}

// 获取门店7天无库存下架的商品数据 v6.27.2
func (p *Product) GetAutoDownProduct(channel_id int, financeOne string, tid int) ([]*models.UpProduct, error) {

	prefix := "获取门店7天无库存下架的商品数据： "
	glog.Info(prefix, " 入参： ", channel_id, " 门店编码：", financeOne)

	conn := NewDbConn()

	var data []*models.UpProduct
	if tid == 1 {
		err := conn.SQL("select np.id, np.channel_id, np.finance_code ,np.product_id, np.sku_id from dc_product.channel_store_product np "+
			"INNER JOIN dc_product.product p ON np.product_id=p.id INNER JOIN datacenter.store s ON s.finance_code=np.finance_code"+
			" where np.channel_id =? and np.finance_code =? and np.down_type = ? and np.up_down_state = 0 AND (p.is_drugs=0 OR (s.sell_drugs=1 AND FIND_IN_SET(np.channel_id, s.drugs_channel_ids)));", channel_id, financeOne, DaysNoStockDownType).Find(&data)
		if err != nil {
			glog.Error("查询七天无库存下架商品报错", err.Error())
			return data, err
		}
	} else {
		err := conn.SQL("select np.id, np.channel_id, np.finance_code ,np.product_id, np.sku_id from dc_product.channel_store_product np "+
			"INNER JOIN dc_product.product p ON np.product_id=p.id INNER JOIN datacenter.store s ON s.finance_code=np.finance_code"+
			" where np.channel_id =? and np.finance_code =? and np.down_type = ? and np.up_down_state = 0 AND (p.is_drugs=0 OR (s.sell_drugs=1 AND FIND_IN_SET(np.channel_id, s.drugs_channel_ids)));", channel_id, financeOne, ZlCanseleUpType).Find(&data)
		if err != nil {
			glog.Error("查询七天无库存下架商品报错", err.Error())
			return data, err
		}
	}

	glog.Info(prefix, " 门店：", financeOne, " 渠道channel_id: ", channel_id, " 返回数据结果： ", data)

	return data, nil
}

// 1:获取7天无库存下架的门店，2:获取可销商品的门店
func (p *Product) GetAutoDownFinance(channel_id, tid int) ([]string, error) {
	conn := NewDbConn()

	var data []string
	//只处理不是药品的数据
	if tid == 2 {
		if err := conn.SQL("select distinct finance_code from dc_product.channel_store_product  "+
			"where down_type = ? and up_down_state = 0 and channel_id = ? ; ", ZlCanseleUpType, channel_id).
			Find(&data); err != nil {
			return data, err
		}
	} else {
		if err := conn.SQL("select distinct finance_code  from dc_product.channel_store_product csp  "+
			"where  down_type = ? and up_down_state = 0 and channel_id = ? ; ", DaysNoStockDownType, channel_id).
			Find(&data); err != nil {
			return data, err
		}
	}
	glog.Info(" 渠道channel_id: ", channel_id, " 返回数据结果： ", data)

	return data, nil
}

// 获取渠道的门店仓库配置
func (p *Product) GetFinanceWarehouseIds(finances []string, chinnel_id int) (map[string][]int32, error) {
	prefix := "获取渠道的门店仓库配置"

	// 门店和仓库的对应map,查库存使用
	financeWarehouseIds := make(map[string][]int32, 0)

	// 查询门店的仓库配置，方便库存查询
	warehouses, err := p.GetChannelWarehouses(finances, int32(chinnel_id))
	if err != nil {
		glog.Error(prefix, " 查询门店的仓库配置失败：", chinnel_id, err.Error())
		return financeWarehouseIds, err
	}

	if chinnel_id == ChannelAwenId { // 阿闻渠道的带上自提
		awenPick, err := p.GetChannelWarehouses(finances, int32(ChannelAwenPickUpId))
		if err != nil {
			glog.Error(prefix, " GetChannelWarehouses失败：", ChannelAwenPickUpId, err.Error())

		}
		warehouses = append(warehouses, awenPick...)
	}

	for pi := range warehouses {
		warehouse := warehouses[pi]
		financeWarehouseIds[warehouse.ShopId] = append(financeWarehouseIds[warehouse.ShopId], int32(warehouse.WarehouseId))
	}

	return financeWarehouseIds, nil
}

// 查询仓库下面对应的库存数据
func (c *Product) WarehouseHasHasStock(warehouseIds []int32, skuIds []int) ([]*models.GoodsStock, error) {

	conn := NewDbConn()

	var data = []*models.GoodsStock{}

	var cIds []string
	for i := range warehouseIds {
		cIds = append(cIds, cast.ToString(warehouseIds[i]))
	}

	var skus []string
	for i := range skuIds {
		skus = append(skus, cast.ToString(skuIds[i]))
	}

	sql := `  select * from (
         SELECT 
			a.goodsid,
			(IFNULL(a.stock , 0 ) - IFNULL(b.stock , 0 )) AS stock,
			a.warehouse_id
		FROM dc_order.warehouse_goods a 
		LEFT JOIN dc_order.order_freeze_stock b ON a.warehouse_id = b.warehouse_id  AND a.goodsid = b.sku_id 
		WHERE a.warehouse_id in (` + strings.Join(cIds, ",") + ") " +
		`and a.goodsid in (` + strings.Join(skus, ",") + ") " +
		`GROUP BY a.warehouse_id, a.goodsid ) b where b.stock > 0;
`
	err := conn.SQL(sql).Find(&data)
	if err != nil {
		glog.Error("查询仓库下面对应的库存数据异常：", warehouseIds, " skuIds: ", skuIds)
		return data, err
	}

	return data, nil
}

// 自动上架阿闻处理
func (c *Product) AddWarehouseSku(stocks []*models.GoodsStock, ids []int32) ([]int, error) {

	warehouse1 := make(map[int]int, 0) // 仓库1的map集合
	warehouse2 := make(map[int]int, 0) // 仓库2的map集合

	for i := range stocks {
		stock := stocks[i]
		if stock.WarehouseId == int(ids[0]) {
			warehouse1[stock.Goodsid] = stock.Stock
		}
		if stock.WarehouseId == int(ids[1]) {
			warehouse2[stock.Goodsid] = stock.Stock
		}
	}

	var dataMap = make(map[int]struct{}, 0)

	for k, v := range warehouse1 {
		skuid := k
		stock := v
		if st, ok := warehouse2[skuid]; ok {
			if stock+st > 0 { // 两个仓库的库存之和 > 0
				dataMap[skuid] = struct{}{}
			}
		} else {
			dataMap[skuid] = struct{}{}
		}
	}

	for k, v := range warehouse2 {
		skuid := k
		stock := v
		if st, ok := warehouse1[skuid]; ok {
			if stock+st > 0 { // 两个仓库的库存之和 > 0
				dataMap[skuid] = struct{}{}
			}
		} else {
			dataMap[skuid] = struct{}{}
		}
	}

	var data []int
	for k := range dataMap {
		sku := k
		data = append(data, sku)
	}

	return data, nil

}

func (c *Product) UpProducts(tid, channel_id int, financeOne string, skuId []int, downData []*models.UpProduct) error {

	prefix := "执行上架,自动上架UpProducts方法调用： "
	glog.Info(prefix, " 参数输入门店：", financeOne, " 渠道：", channel_id, " sku: ", skuId, " data:", kit.JsonEncode(downData))

	skuMap := make(map[int]struct{}, 0)
	for i := range skuId {
		skuMap[skuId[i]] = struct{}{}
	}

	products := []string{}
	for i := range downData {
		data := downData[i]
		if _, ok := skuMap[data.SkuId]; ok {
			products = append(products, cast.ToString(data.ProductId))
		}
	}
	var (
		UserNo   = DownType7DaysNoStock
		UserName = DownType7DaysNoStock
	)
	DownType := 0
	if tid == 2 {
		UserNo = UpTypeCansele
		UserName = UpTypeCansele
		DownType = 7
	}
	down := &ChannelProductUpDown{
		ProductIds:   products,
		ChannelId:    channel_id,
		FinanceCodes: []string{financeOne},
		UserNo:       UserNo,
		UserName:     UserName,
		IsSyncPrice:  true,
		UpType:       enum.RecordTypeAutoUp,
		DownType:     DownType,
	}

	// 调用上架接口
	//glog.Info(prefix, " 上架调用参数：", financeOne, " 渠道channel_id: ", channel_id, " 返回数据结果： ", kit.JsonEncode(down))
	down.UpPorudct()

	//上架不成功则更新为0，不再定时跑数据
	//glog.Info(prefix, " 上架返回门店：", financeOne, " 渠道channel_id: ", channel_id, " 返回数据结果： ", kit.JsonEncode(down.UpResult))

	return nil
}
