package services

const (
	//渠道Id
	channelIdOmit        = iota //忽略的值不要使用
	ChannelAwenId               //阿闻
	ChannelMtId                 //美团
	ChannelElmId                //饿了么
	ChannelJddjId               //京东到家
	ChannelMallId               //阿闻电商
	ChannelStore                // 门店
	ChannelBaiDu                // 百度
	ChannelH5                   // h5
	ChannelDigitalHealth        // 医疗互联网渠道
	ChannelAwenPickUpId         // 阿闻竖屏自提

	DatacenterExchange = "datacenter"

	//保存渠道商品上下架状态队列
	SaveChannelStoreProductQueue = "dc-sz-product-center-new-channel-store-product"
	//渠道商品上架队列
	ChannelProductUpQueue_will = "dc-sz-product-center-mt-single-product"
	ChannelProductUpQueueElm   = "dc-sz-product-center-elm-single-product"
	//渠道商品批量新增/更新队列
	ChannelProductBatchQueue     = "dc-sz-product-center-mt-Batch-product"
	ChannelProductBatchQueueElm  = "dc-sz-product-center-elm-Batch-product"
	ChannelProductBatchQueueJddj = "dc-sz-product-center-jddj-Batch-product-test" //本地加test，测试环境248,70去掉test
	//批量上架任务锁
	ChannelProductUpLockPrefix = "productcenter:task:channelProductOneKeyUpLock:"

	//上下架同步es
	ChannelProductToEs = "dc-sz-product-center-sync-product-to-es"
	//更新阿闻商城搜索es
	ChannelProductEsUpdateQueue = "dc-sz-product-center-channel-product-es-update"
	//库存变更通知
	//本地的mq
	StockChangeLocal = "dc_sz_stock_mq_tj_local"
	//电商的mq
	StockChangeUPet = "dc_sz_stock_mq_tj_upet"

	//价格同步至阿闻的mq
	ProductPriceSyncAW = "dc-sz-product-center-aw-product-price"
	//价格同步至饿了么的mq
	ProductPriceSyncElm = "dc-sz-product-center-elm-product-price"
	//价格同步至美团的mq
	ProductPriceSyncMT = "dc-sz-product-center-mt-product-price"
	//价格同步至京东的mq
	ProductPriceSyncJD = "dc-sz-product-center-jd-product-price"

	// 前置仓价格同步至四大渠道mq
	A8PriceSync = "dc-sz-product-center-a8-price"
)

var (
	exchange       = "datacenter"
	queueCategory  = "dc-sz-product-center-product-category"
	queueAttr      = "dc-sz-product-center-product-attr"
	queueAttrValue = "dc-sz-product-center-product-attrvalue"
	queueBrand     = "dc-sz-product-center-product-brand"
	//queueErp = "dc-sz-product-center-product-erp"
	queueSpec      = "dc-sz-product-center-product-spec"
	queueSpecValue = "dc-sz-product-center-product-specvalue"
	queueType      = "dc-sz-product-center-product-type"
	queueTag       = "dc-sz-product-center-product-tag"

	copyAllLockKey = "productcenter:channel-product:copy-all-248"

	ProductEsIndex   = "channel_store_product"
	StoreEsIndex     = "store"
	JddjBrandEsIndex = "jddj_brand"

	//渠道名称
	ChannelName = map[int32]string{
		ChannelAwenId: "阿闻到家",
		ChannelMtId:   "美团",
		ChannelElmId:  "饿了么",
		ChannelJddjId: "京东到家",
	}

	// 饿了么分类sort排序字段
	EleMeCategorySort = 10001
	JDBrandId         = 35247
	JDCategoryId      = 24479
)

//taskContent  变量后面的类型统一放到这里方便维护
var (
	SyncCategoryTaskContent = 22 // 同步第三方分类任务
	IntsChannel             = []int{ChannelElmId, ChannelMtId, ChannelJddjId}

	SyncOmsProductTaskContent = 33 // 处理oms任务
	MoveCategoryProduct       = 40 // 移动商品任务
)

var (
	// 下架用户标识
	DownType7DaysNoStock = "7DayNoStockAutoDown"
	UpType7DaysHasStock  = "7DayHasStockAutoUp"
	UpTypeCansele        = "UpTypeCansele"   //可销可上架
	DownTypeCansele      = "DownTypeCansele" //不可销下架

	// 7天无理由自动下架
	DaysNoStockDownType = 1

	// 子龙变可销上架
	ZlCanseleUpType = 7
	// 子龙不可销下架
	ZlCanseleDownType = 5

	MoveProductNameMap = make(map[int]string, 0)
)

const (
	//1：阿闻渠道（本地） 2.美团渠道（线上）3.饿了么渠道（线上） ）4.京东渠道（线上）
	// 5：阿闻管家（本地） 6.美团渠道（本地） 7.饿了么渠道（本地）  8.京东渠道（本地 9：互联网医疗本地
	//渠道Id
	MoveProductOmit = iota //忽略的值不要使用
	MoveProductAwenChannel
	MoveProductMtThird
	MoveProductELEmeThird
	MoveProductJDdjThird
	MoveProductAwenGj
	MoveProductMtBenDi
	MoveProductELEmeBenDi
	MoveProductJDdjBenDi
	MoveProductInternet
)

func init() {

	MoveProductNameMap[MoveProductAwenGj] = "阿闻管家(本地)"
	MoveProductNameMap[MoveProductAwenChannel] = "阿闻渠道(本地)"
	MoveProductNameMap[MoveProductMtBenDi] = "美团渠道(本地)"
	MoveProductNameMap[MoveProductMtThird] = "美团渠道(线上)"
	MoveProductNameMap[MoveProductJDdjBenDi] = "京东渠道(本地)"
	MoveProductNameMap[MoveProductJDdjThird] = "京东渠道(线上)"
	MoveProductNameMap[MoveProductELEmeBenDi] = "饿了么渠道(本地)"
	MoveProductNameMap[MoveProductELEmeThird] = "饿了么渠道(线上)"
	MoveProductNameMap[MoveProductInternet] = "互联网医疗(本地)"
}

//仓库类型

const (
	// 电商仓
	MallWarehouse int = 1

	//门店仓
	StoreWarehouse int = 3

	// 前置仓
	FrontWarehouse int = 4

	// 虚拟仓
	virtualWarehouse int = 5
)
