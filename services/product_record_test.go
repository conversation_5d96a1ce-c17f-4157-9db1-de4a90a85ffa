package services

import (
	"_/proto/pc"
	"context"
	"fmt"
	"testing"
)

func TestRecord_ProductRecords(t *testing.T) {

	type args struct {
		ctx context.Context
		in  *pc.ProductRecordsVo
	}
	tests := []struct {
		name    string
		args    args
		want    *pc.ProductRecordsVo
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: context.TODO(),
				in: &pc.ProductRecordsVo{
					ProductId:  100500,
					StartTime:  "2022-01-12 10:20:30",
					EndTime:    "2022-10-12 10:20:30",
					Where:      "sku_id",
					Value:      "1005001",
					RecordType: 1,
					PageIndex:  1,
					PageSize:   10,
				},
			},
		},
	}

	SetupDB()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := Product{}
			got, err := c.ProductRecords(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}

//ChannelProductRecords

func TestRecord_ChannelProductRecords(t *testing.T) {

	type args struct {
		ctx context.Context
		in  *pc.ChannelProductRecordsVo
	}
	tests := []struct {
		name    string
		args    args
		want    *pc.ChannelProductRecordsVo
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: context.TODO(),
				in: &pc.ChannelProductRecordsVo{
					ProductId:  100500,
					StartTime:  "2022-01-12 10:20:30",
					EndTime:    "2022-10-12 10:20:30",
					Where:      "sku_id",
					Value:      "1005001",
					RecordType: 1,
					PageIndex:  1,
					PageSize:   10,
				},
			},
		},
	}

	SetupDB()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := Product{}
			got, err := c.ChannelProductRecords(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}

//DeleteChannelAwenProduct 测试es删除商品上架商品
func TestRecord_DeleteChannelAwenProduct(t *testing.T) {

	type args struct {
		ctx         context.Context
		in          *pc.DeleteProductVo
		shopIds     []string
		isDeleteAll bool
	}
	tests := []struct {
		name    string
		args    args
		want    *pc.ChannelProductRecordsVo
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: context.TODO(),
				in: &pc.DeleteProductVo{
					ProductId:    103808,
					CreateId:     "",
					CreateName:   "",
					CreateMobile: "",
					CreateIp:     "",
					IpLocation:   "",
					ChannelId:    1,
				},
				isDeleteAll: false,
				shopIds:     []string{"AB0072", "AN0020"},
			},
		},
	}

	SetupDB()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := Product{}
			err := c.DeleteChannelAwenProduct(tt.args.in, tt.args.shopIds, tt.args.isDeleteAll)
			if err != nil {
				return
			}
		})
	}
	//290,687  - 15
	//product.id : 	1005168
}
