package services

import (
	"_/enum"
	"_/models"
	"_/proto/pc"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

//同步oms的商品库商品到阿闻目前主要是实物商品
func (c *Product) NewProductFromOms(ctx context.Context, in *pc.ProductRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: 400}
	glog.Info("NewProduct请求参数", kit.JsonEncode(in))
	if len(in.Product.Name) == 0 {
		out.Message = "商品名称不能为空"
		return out, nil
	}
	if in.Product.CategoryId == 0 {
		out.Message = "商品分类不能为空"
		return out, nil
	}

	if len(in.SkuInfo) == 0 {
		out.Message = "SKU规格信息为空"
		return out, nil
	}
	if len(in.SkuInfo[0].SkuThird) == 0 && in.Product.ProductType != 3 {
		out.Message = "第三方货号信息不能为空"
		return out, nil
	}

	//商品主表
	product := models.Product{
		//Id:         int(in.Product.Id),
		CategoryId: int(in.Product.CategoryId),
		BrandId:    int(in.Product.BrandId),
		Name:       in.Product.Name,
		BarCode:    in.Product.BarCode,
		//IsDel:         int(in.Product.IsDel),
		IsGroup:              int(in.Product.IsGroup),
		CreateDate:           time.Now(),
		UpdateDate:           time.Now(),
		Pic:                  in.Product.Pic,
		SellingPoint:         in.Product.SellingPoint,
		Video:                in.Product.Video,
		ContentPc:            in.Product.ContentPc,
		ContentMobile:        in.Product.ContentMobile,
		IsDiscount:           int(in.Product.IsDiscount),
		ProductType:          int(in.Product.ProductType),
		IsUse:                int(in.Product.IsUse),
		ChannelId:            in.Product.ChannelId,
		CategoryName:         in.Product.CategoryName,
		IsDrugs:              int(in.Product.IsDrugs),
		UseRange:             in.Product.UseRange,
		TermType:             int(in.Product.TermType),
		TermValue:            int(in.Product.TermValue),
		GroupType:            int(in.Product.GroupType),
		VirtualInvalidRefund: int(in.Product.VirtualInvalidRefund),
		WarehouseType:        int(in.Product.WarehouseType),
		IsIntelGoods:         int(in.Product.IsIntelGoods), // 是否医疗互联网商品
		Disabled:             int(in.Product.Disabled),
		FromOms:              1,
		SourceType:           1,
	}
	Engine := NewDbConn()
	session := Engine.NewSession()
	defer session.Close()
	session.Begin()

	//商品主表
	if _, err := session.Insert(&product); err != nil {
		glog.Error(err)
		session.Rollback()
		return out, err
	}
	//管家商品主表
	if _, err := session.Table("gj_product").Insert(&product); err != nil {
		glog.Error(err)
		session.Rollback()
		return out, err
	}
	out.Details = append(out.Details, fmt.Sprintf("%d", product.Id))

	var sku []models.Sku           //商品SKU
	var gjSku []models.GjSku       //商品SKU
	var skuThird []models.SkuThird //第三方SKU货号
	var skuValue []models.SkuValue //商品SKU规格组合
	var attr []models.ProductAttr  //商品自定义属性
	sort := 1                      //排序

	//验证第三方货号不能重复
	thirdID := make(map[string]string)

	var group_skuId int32

	for i, v := range in.SkuInfo {
		var skuId int32
		if product.ProductType != 3 {
			if v.SkuId == 0 {
				sID, _ := strconv.Atoi(fmt.Sprintf("%d%03d", product.Id, i+1))
				skuId = int32(sID)
			} else {
				skuId = v.SkuId
			}
		} else {
			sID, _ := strconv.Atoi(fmt.Sprintf("%d099", product.Id))
			skuId = int32(sID)
			group_skuId = skuId
		}

		// 虚拟商品或组合商品自动生成条码

		sku = append(sku, models.Sku{
			Id:            skuId,
			ProductId:     int32(product.Id),
			MarketPrice:   v.MarketPrice,
			RetailPrice:   v.RetailPrice,
			BarCode:       v.BarCode,
			WeightForUnit: v.WeightForUnit,
			UpdateDate:    time.Now(),
		})

		gjSkuModel := models.GjSku{
			Id:            skuId,
			ProductId:     int32(product.Id),
			MarketPrice:   v.MarketPrice,
			RetailPrice:   v.RetailPrice,
			BarCode:       v.BarCode,
			StorePrice:    v.MarketPrice,
			PreposePrice:  v.MarketPrice,
			WeightForUnit: v.WeightForUnit,
			UpdateDate:    time.Now(),
		}
		//虚拟商品的重量在管家商品库默认填入0.01
		if in.Product.ProductType == 2 {
			gjSkuModel.WeightForUnit = 0.01
			gjSkuModel.MarketPrice = v.RetailPrice
			gjSkuModel.StorePrice = v.RetailPrice
			gjSkuModel.PreposePrice = v.RetailPrice
		}
		gjSku = append(gjSku, gjSkuModel)

		//第三方货号
		if product.ProductType == 1 {
			for _, t := range v.SkuThird {
				third := models.SkuThird{
					SkuId:      skuId,
					ThirdSkuId: t.ThirdSkuId,
					ThirdSpuId: t.ThirdSpuId,
					ErpId:      t.ErpId,
					ProductId:  int32(product.Id),
				}

				if t.ThirdSkuId != "" || t.ThirdSpuId != "" {
					third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", t.ThirdSpuId, t.ThirdSkuId)
				} else {
					continue
				}

				if _, ok := thirdID[third.ThirdSpuSkuId]; !ok {
					thirdID[third.ThirdSpuSkuId] = ""
				} else {
					out.Message = "第三方货号不能重复"
					return out, nil
				}

				if has, err := Engine.Table("sku_third").Where("third_spu_sku_id=?", third.ThirdSpuSkuId).Exist(); err != nil {
					glog.Error(err)
				} else if has {
					out.Message = fmt.Sprintf("第三方货号[%s]在系统已经存在", strings.Trim(third.ThirdSpuSkuId, ","))
					return out, nil
				}

				skuThird = append(skuThird, third)
			}
		} else if in.Product.ProductType == 2 {
			third := models.SkuThird{
				SkuId:      skuId,
				ThirdSpuId: "",
				ProductId:  int32(product.Id),
			}

			third.ErpId = 2
			third.ThirdSkuId = "A8HH" + cast.ToString(skuId)
			third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", third.ThirdSpuId, third.ThirdSkuId)
			skuThird = append(skuThird, third)

			third.ErpId = 4
			third.ThirdSkuId = "ZLHH" + cast.ToString(skuId)
			third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", third.ThirdSpuId, third.ThirdSkuId)
			skuThird = append(skuThird, third)
		} else if in.Product.ProductType == 3 {
			// 组合商品货号为skuId
			third := models.SkuThird{
				SkuId:      skuId,
				ThirdSkuId: cast.ToString(skuId),
				ThirdSpuId: "",
				ProductId:  int32(product.Id),
			}
			if strings.Contains(product.UseRange, "2") || strings.Contains(product.UseRange, "1") {
				third.ErpId = 2
				third.ThirdSpuSkuId = fmt.Sprintf("2,%s", third.ThirdSkuId)
				skuThird = append(skuThird, third)
			}
			if strings.Contains(product.UseRange, "3") {
				third.ErpId = 4
				third.ThirdSpuSkuId = fmt.Sprintf("4,%s", third.ThirdSkuId)
				skuThird = append(skuThird, third)
			}
		}

		//SKU规格组合
		for _, s := range v.Skuv {
			skuValue = append(skuValue, models.SkuValue{
				SpecId:      s.SpecId,
				SpecValueId: s.SpecValueId,
				SkuId:       skuId,
				ProductId:   int32(product.Id),
				Pic:         s.Pic,
				Sort:        int32(sort),
			})
			sort++
		}
	}

	//商品自定义属性
	for _, v := range in.ProductAttr {
		attr = append(attr, models.ProductAttr{
			ProductId:   int32(product.Id),
			AttrId:      v.AttrId,
			AttrValueId: v.AttrValueId,
		})
	}

	//SKU
	if _, err := session.Insert(&sku); err != nil {
		glog.Error(err)
		session.Rollback()
		return out, err
	}
	//管家sku
	if _, err := session.Insert(gjSku); err != nil {
		glog.Error(err)
		session.Rollback()
		return out, err
	}

	for _, v := range skuThird {

		//第三方SKU货号
		if _, err := session.Insert(&v); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
		//管家第三方SKU货号
		if _, err := session.Table("gj_sku_third").Insert(&v); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
	}

	if in.Product.ProductType != 3 {
		for _, v := range skuValue {

			//商品SKU规格组合
			if _, err := session.Insert(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			//管家商品SKU规格组合
			if _, err := session.Table("gj_sku_value").Insert(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
		}
	}

	// 写入组合商品
	if in.Product.ProductType == 3 {
		if len(in.SkuInfo[0].SkuGroup) == 0 {
			out.Message = "组合商品信息不能为空"
			return out, nil
		}
		// 写入组合商品
		isGroup, isDrugs, isPrescribedDrug, price, err := updateGroupProduct(session, int32(product.Id), sku[0].Id, in.SkuInfo[0].SkuGroup)
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}
		_, err = session.ID(product.Id).Update(&models.Product{
			GroupType:        isGroup,
			IsDrugs:          isDrugs,
			IsPrescribedDrug: isPrescribedDrug,
		})
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}
		_, err = session.ID(product.Id).Update(&models.GjProduct{
			GroupType:        isGroup,
			IsDrugs:          isDrugs,
			IsPrescribedDrug: isPrescribedDrug,
		})
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}
		//组合商品写入默认的sku_value
		pic := strings.Split(product.Pic, ",")[0]

		sku_valuemode := models.SkuValue{
			SpecId:      1,
			SpecValueId: cast.ToInt32(config.GetString("spec_value_id")),
			SkuId:       group_skuId,
			ProductId:   int32(product.Id),
			Pic:         pic,
			Sort:        int32(sort),
		}

		glog.Info("读取规格信息：", kit.JsonEncode(sku_valuemode), "  获取配置：", config.GetString("spec_value_id"))
		_, err = session.Insert(&sku_valuemode)
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}

		_, err = session.Insert(&models.GjSkuValue{
			Id:          int(sku_valuemode.Id),
			SpecId:      1,
			SpecValueId: cast.ToInt(config.GetString("spec_value_id")),
			SkuId:       int(group_skuId),
			ProductId:   product.Id,
			Pic:         pic,
			Sort:        sort,
		})
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}

		if price > 0 {
			_, err = session.ID(sku[0].Id).Update(&models.Sku{MarketPrice: int32(price)})
			if err != nil {
				out.Message = err.Error()
				session.Rollback()
				return out, nil
			}
			_, err = session.ID(sku[0].Id).Update(&models.GjSku{
				MarketPrice:  int32(price),
				StorePrice:   int32(price),
				PreposePrice: int32(price),
			})
			if err != nil {
				out.Message = err.Error()
				session.Rollback()
				return out, nil
			}
		}
	}

	//商品自定义属性
	if len(attr) > 0 && in.Product.ProductType != 3 {
		if _, err := session.Insert(&attr); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
	}

	if in.ProductTags != nil && in.Product.ProductType != 3 {
		//组装商品标签数据
		produtcTagModel := models.ProductTag{
			SkuId:           int(sku[0].Id),
			ProductId:       int(sku[0].ProductId),
			ProductType:     int(in.ProductTags.ProductType),
			Species:         in.ProductTags.Species,
			Varieties:       in.ProductTags.Varieties,
			Sex:             in.ProductTags.Sex,
			Shape:           in.ProductTags.Shape,
			Age:             in.ProductTags.Age,
			SpecialStage:    in.ProductTags.SpecialStage,
			IsSterilization: in.ProductTags.IsSterilization,
			ContentType:     in.ProductTags.ContentType,
			Status:          in.ProductTags.Status,
		}
		//创建商品标签数据
		if _, err := session.Insert(&produtcTagModel); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
	}

	if err := session.Commit(); err != nil {
		session.Rollback()
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 跟新oms商品到阿闻
func (c *Product) UpdateProductFromOms(ctx context.Context, in *models.R1Sku, product *models.Product) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: 400}
	glog.Info("UpdateProductFromOms请求参数", kit.JsonEncode(in))

	// 只需要跟新商品的disabled信息即可
	var mapProduct = map[string]interface{}{
		//"name":     in.SkuName,
		//"pic":      in.Pic,
		"disabled": in.EnabledSku,
		//"bar_code": in.Barcode,
	}

	engine.Table("product").Where("id = ? ", product.Id).Update(mapProduct)
	engine.Table("gj_product").Where("id = ? ", product.Id).Update(mapProduct)

	//var mapSku = map[string]interface{}{
	//"bar_code":        in.Barcode,
	//"weight_for_unit": in.NetWeight,
	//}

	//engine.Table("sku").Where("id = ? ").Update(mapSku)

	out.Code = 200
	return out, nil
}

// 保存数据库，后面定时任务处理
func (c *Product) AddTaskProductFromOms(ctx context.Context, in *pc.AddTaskProductFromOmsVo) (*pc.BaseResponse, error) {

	glog.Info("AddTaskProductFromOms: ", kit.JsonEncode(in))
	response := pc.BaseResponse{Code: 400}

	list := models.TaskList{
		TaskContent:      int32(SyncOmsProductTaskContent),
		TaskStatus:       1,
		OperationFileUrl: in.Data,
		Status:           1,
		CreateTime:       time.Now(),
		ModifyTime:       time.Now(),
	}

	_, err := engine.Insert(&list)

	if err != nil {
		response.Message = "插入task任务异常" + err.Error()
		return &response, nil
	}

	response.Code = 200

	return &response, nil
}

// 格式化商品数据
func (c *Product) FormatOmsData(data *models.R1Sku) (*pc.ProductRequest, error) {
	request := pc.ProductRequest{}

	category_id_str := config.GetString("oms_category_id")
	category_id := cast.ToInt32(category_id_str)

	if category_id <= 0 {
		// 未分类
		category_id = 1208
	}

	pic := c.dealWithPic(data.Pic)

	category := models.Category{}
	engine.SQL("select * from dc_product.category c where id = ?", category_id).Get(&category)
	request.Product = &pc.Product{
		//Id:                   0,
		CategoryId: category.Id, //
		BrandId:    0,
		Name:       data.SkuName,
		Code:       "",
		//BarCode:              data.Barcode, // 去除barcode，后面上架京东的时候有问题
		BarCode:              "",
		CreateDate:           time.Now().Format(kit.DATETIME_LAYOUT),
		UpdateDate:           "",
		IsDel:                0,
		IsGroup:              0,
		Pic:                  pic,
		SellingPoint:         "",
		Video:                "",
		ContentPc:            "",
		ContentMobile:        "",
		IsDiscount:           0,
		ProductType:          1,
		IsUse:                0,
		CategoryName:         category.Name,
		Sku:                  nil,
		Attr:                 nil,
		ChannelId:            "",
		BrandName:            "",
		JdCategoryId:         0,
		IsDrugs:              0,
		UseRange:             "",
		TermType:             0,
		TermValue:            0,
		GroupType:            0,
		VirtualInvalidRefund: 0,
		WarehouseType:        0,
		IsIntelGoods:         0,
		Disabled:             int32(data.EnabledSku),
	}

	fen, err := c.ParsePrice(data.Price)
	if err != nil {
		return &request, err
	}
	if fen == 0 {
		fen = 5000 * 100
	}

	var info = &pc.SkuInfo{
		//SkuThird:             []*pc.SkuThird{},
		Skuv:        nil,
		RetailPrice: int32(fen),
		SkuId:       0,
		ProductId:   0,
		MarketPrice: int32(fen),
		SkuGroup:    nil,
		BarCode:     data.Barcode,
		ChannelId:   0,
		IsUse:       0,
		//WeightForUnit:  data.NetWeight,
		//WeightUnit:     data.WeightUnitName,
		MinOrderCount:  0,
		PriceUnit:      "",
		PreposePrice:   0,
		StorePrice:     0,
		SalesVolume:    0,
		MemberPrice:    0,
		PromotionType:  0,
		PromotionPrice: 0,
	}
	info.SkuThird = append(info.SkuThird, &pc.SkuThird{
		//Id:                   0,
		SkuId:      0,
		ThirdSkuId: data.SkuNo,
		ErpId:      2, // A8
		ProductId:  0,
		ErpName:    "A8货号",
		ThirdSpuId: "",
		ChannelId:  0,
		IsUse:      0,
	})

	value := models.SpecValue{}
	engine.SQL("select * from dc_product.spec_value sv where value = ? ;", data.SpecValue).Get(&value)

	split := strings.Split(pic, ",")
	var sku_pic string
	if len(split) > 0 {
		sku_pic = split[0]
	}

	if value.Id > 0 { //查询未分类的规格
		info.Skuv = append(info.Skuv, &pc.SkuValue{
			//Id:                   0,
			SpecId:         value.SpecId,
			SpecValueId:    value.Id,
			SkuId:          0,
			ProductId:      0,
			Pic:            sku_pic,
			SpecName:       data.SpecValue,
			SpecValueValue: data.SpecValue,
			Details:        nil,
			ChannelId:      0,
		})
	} else { //
		//  创建规格
		spec_id_str := config.GetString("oms_spec_id")
		spec_id := cast.ToInt32(spec_id_str)
		if spec_id <= 0 {
			spec_id = 1 // 创建种类下面
		}

		spec := models.Spec{}
		engine.SQL("select * from dc_product.spec s where id = ?;", spec_id).Get(&spec)
		value := models.SpecValue{
			SpecId: spec_id,
			Value:  data.SpecValue,
		}
		_, err := engine.Table("spec_value").Insert(&value)
		if err != nil {
			glog.Error("插入规格异常")
			return &request, err
		}

		info.Skuv = append(info.Skuv, &pc.SkuValue{
			//Id:                   0,
			SpecId:         spec_id,
			SpecValueId:    value.Id,
			SkuId:          0,
			ProductId:      0,
			Pic:            data.Pic,
			SpecName:       spec.Name,
			SpecValueValue: data.SpecValue,
			Details:        nil,
			ChannelId:      0,
		})
	}

	// 标签
	request.ProductTags = &pc.ProductTags{
		//Id:                   0,
		SkuId:           0,
		ProductId:       0,
		ProductType:     1,
		Species:         "不限",
		Varieties:       "不限",
		Sex:             "不限",
		Shape:           "不限",
		Age:             "不限",
		SpecialStage:    "不限",
		IsSterilization: "不限",
		ContentType:     "不限",
		Status:          "不限",
	}

	// 属性
	attrs := make([]models.ProductAttr, 0)
	engine.SQL("select * from dc_product.attr a ;").Find(&attrs)

	for i := range attrs {
		attr := attrs[i]
		request.ProductAttr = append(request.ProductAttr, &pc.ProductAttr{
			ProductId:   0,
			AttrId:      attr.Id,
			AttrValueId: 0,
		})
	}

	request.SkuInfo = append(request.SkuInfo, info)

	return &request, nil

}

// 停用商品下架
func (c *Product) DownOmsProduct(data *models.R1Sku, product *models.Product) error {
	glog.Info("DownOmsProduct run ...")
	var taskLog strings.Builder
	taskLog.WriteString("DownOmsProduct 开始执行")
	defer func() {
		if err := recover(); err != nil {
			taskLog.WriteString("任务出现了未处理异常")
			glog.Error("DownOmsProduct ", err)
		}
		glog.Info(taskLog.String())

	}()
	if product.FromOms == 1 && data.EnabledSku == 0 { // 查询到有商品并且是oms是停用就执行下架

		var db = NewDbConn()
		// 记录任务执行日志
		// 依次处理商品下架
		// 查询过期商品的上架信息
		var financeCodes []models.ChannelStoreProduct
		err := db.Table(&models.ChannelStoreProduct{}).Select("*").
			And("product_id=?", product.Id).And("up_down_state=1").Find(&financeCodes)
		if err != nil {
			taskLog.WriteString(fmt.Sprintf("DownOmsProduct查询oms商品上架异常:%s", err.Error()))
			return err
		}

		for i := range financeCodes {
			//是从前置仓/前置虚拟仓取的库存。那么就将该商品从该门店所对应的渠道下架
			storeProduct := financeCodes[i]
			warehouse := LoadWarehouseRelationCache(storeProduct.FinanceCode)
			if len(warehouse) == 0 {
				glog.Info("没有查询到仓库关联关系warehouse:store:relation")
				continue
			}

			var flag bool
			for i := range warehouse {
				st := warehouse[i]
				if st.Category == 4 || st.Category == 5 {
					flag = true
				}
			}

			if !flag {
				glog.Info("仓库属于门店仓无需下架：", kit.JsonEncode(storeProduct))
				continue
			}

			// 查询单个商品组成的组合商品
			// 开始下架
			var channelProductUpDown ChannelProductUpDown
			channelProductUpDown.ProductIds = append(channelProductUpDown.ProductIds, cast.ToString(product.Id))
			channelProductUpDown.ChannelId = int(storeProduct.ChannelId)
			channelProductUpDown.FinanceCodes = append(channelProductUpDown.FinanceCodes, storeProduct.FinanceCode)
			channelProductUpDown.UserNo = "oms_disabled"
			channelProductUpDown.UserName = "oms_disabled"
			channelProductUpDown.DownType = enum.DownRecordTypeOmsDisable
			channelProductUpDown.DownPorudct()
			if channelProductUpDown.UnknownError != nil {
				taskLog.WriteString(fmt.Sprintf("渠道:%d,商品：%d,门店:%s 下架失败:%s",
					storeProduct.ChannelId, product.Id, storeProduct.FinanceCode, channelProductUpDown.UnknownError))
			}
			for _, v := range channelProductUpDown.UpResult {
				if v.IsSuccess == false {
					taskLog.WriteString(fmt.Sprintf("渠道:%d,商品：%d,门店:%s 下架失败:%s",
						storeProduct.ChannelId, product.Id, v.StoreProduct.StoreFinanceCode, v.Message))
				}
			}
		}

		//处理组合商品下架
		glog.Info("处理组合商品下架 run ")
		var dataProduct = make([]models.ChannelStoreProduct, 0)
		db.Table("channel_store_product").SQL(`SELECT * FROM dc_product.channel_store_product b WHERE b.product_id IN (
							SELECT DISTINCT product_id FROM dc_product.channel_sku_group a WHERE a.group_product_id = ? ) AND b.up_down_state = 1 ;`, product.Id).Find(&dataProduct)
		glog.Info("处理组合商品下架列表： ", len(dataProduct))
		for _, v_group := range dataProduct {
			// 开始下架
			glog.Info("下架组合商品。。。", len(dataProduct), " 組合商品的id： ", v_group.ProductId, " 下架渠道：", v_group.ChannelId)
			var channelProductUpDown ChannelProductUpDown
			channelProductUpDown.ProductIds = append(channelProductUpDown.ProductIds, cast.ToString(v_group.ProductId))
			channelProductUpDown.ChannelId = int(v_group.ChannelId)
			channelProductUpDown.FinanceCodes = append(channelProductUpDown.FinanceCodes, v_group.FinanceCode)
			channelProductUpDown.UserNo = "oms_disabled"
			channelProductUpDown.UserName = "oms_disabled"
			channelProductUpDown.DownType = enum.DownRecordTypeOmsDisable
			channelProductUpDown.DownPorudct()
			if channelProductUpDown.UnknownError != nil {
				taskLog.WriteString(fmt.Sprintf("渠道组合商品下架:%d,商品：%d,门店:%s 过期下架出现了未处理异常:%s",
					v_group.ChannelId, v_group.ProductId, v_group.FinanceCode, err))
			}
			for _, v := range channelProductUpDown.UpResult {
				if v.IsSuccess == false {
					taskLog.WriteString(fmt.Sprintf("渠道组合商品下架:%d,商品：%d,门店:%s 下架失败:%s",
						v_group.ChannelId, v_group.ProductId, v.StoreProduct.StoreFinanceCode, v.Message))
				}
			}
		}
	}

	return nil
}

// 查询货号是否存在
func (c *Product) FindThird(data *models.R1Sku) (*models.SkuThird, error) {

	glog.Info("查询是否存在货号：", kit.JsonEncode(data))

	third := models.SkuThird{}
	_, err := engine.SQL("select * from dc_product.sku_third st  where third_sku_id  = ? ", data.SkuNo).Get(&third)

	if err != nil {
		return &third, err
	}
	return &third, nil
}

// 查询oms商品有没有详情
func (c *Product) FindOmsProduct(id int32) (*models.Product, error) {

	product := models.Product{}
	_, err := engine.SQL("select * from  dc_product.product p where id = ?;", id).Get(&product)
	if err != nil {
		return &product, err
	}

	return &product, nil

}

// 处理价格数据
func (c *Product) ParsePrice(price string) (int, error) {

	if len(strings.TrimSpace(price)) <= 0 {
		return 0, nil
	}
	glog.Info("解析R1价格参数：", price)
	f, err := strconv.ParseFloat(price, 64)
	if err != nil {
		return 0, err
	}

	fen := kit.YuanToFen(f)

	glog.Info("解析R1价格返回：", fen, err)
	return fen, nil
}

//处理图片
func (c *Product) dealWithPic(pic string) string {

	glog.Info("处理图片数据参数：", pic)
	split := strings.Split(pic, ",")

	num := 5 - len(split) // 阿闻图片有五张
	for i := 0; i < num; i++ {
		split = append(split, "")
	}
	join := strings.Join(split, ",")
	glog.Info("处理图片返回", join)

	return join
}
