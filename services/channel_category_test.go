package services

import (
	"_/proto/pc"
	"context"
	"reflect"
	"testing"
)

func TestProduct_SaveWxVideoCategoryList(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.CategoryListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "保存微信视频号类目列表",
			args: args{
				ctx: context.Background(),
				in:  &pc.CategoryListRequest{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := this.SaveWxVideoCategoryList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaveWxVideoCategoryList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("SaveWxVideoCategoryList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPoduct_DealWithMeiTuanChannel(t *testing.T) {

	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.SyncCategoryScheduleRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{

			//{"CategoryId":201540332,"CategoryName":"PWT00901","FinanceCode":"CX0004","ShopName":"宠颐生北京爱之都",
			// "IsSuccess":false,"Message":"","ChannelId":2,"SyncType":1,"ChannelStoreId":"6411_2701032","AppChannel":2,"ParentId":201540331,"Sort":166,"SystemError":""}
			name: "处理美团同步分类",
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryScheduleRequest{
					CategoryId:     10104040,
					CategoryName:   "PXT00A",
					FinanceCode:    "CX0011",
					ShopName:       "宠颐生北京爱之源1234",
					IsSuccess:      false,
					Message:        "",
					ChannelId:      2,
					SyncType:       1,
					ChannelStoreId: "6411_2700983",
					AppChannel:     2,
					ParentId:       201540331,
					Sort:           166,
				},
			},
		},
	}

	SetupDB()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := this.DealWithMeiTuanChannel(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaveWxVideoCategoryList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SaveWxVideoCategoryList() got = %v, want %v", got, tt.want)
			}
		})
	}

}

//{"CategoryId":201540326,"CategoryName":"PWT003","FinanceCode":"AA0193","ShopName":"安安广州中山小榄","IsSuccess":false,
// "Message":"","ChannelId":3,"SyncType":1,"ChannelStoreId":"MINHIN50","AppChannel":1,"ParentId":0,"Sort":0}
func TestPoduct_DealWithEleMeChannel(t *testing.T) {

	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.SyncCategoryScheduleRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "处理美团同步分类",
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryScheduleRequest{
					CategoryId:     201540329,
					CategoryName:   "PWT008",
					FinanceCode:    "CX0010",
					ShopName:       "宠颐生北京爱福",
					IsSuccess:      false,
					Message:        "",
					ChannelId:      3,
					SyncType:       1,
					ChannelStoreId: "32267735205",
					AppChannel:     1,
					ParentId:       0,
					Sort:           163,
				},
			},
		},
	}
	//{"CategoryId":201540329,"CategoryName":"PWT00501","FinanceCode":"CX0010",
	// "ShopName":"宠颐生北京爱福","IsSuccess":false,"Message":"","ChannelId":3,"SyncType":1,
	// "ChannelStoreId":"32267735205","AppChannel":1,
	// "ParentId":201540328,"Sort":163,"SystemError":""}
	SetupDB()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := this.DealWithEleMeChannel(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaveWxVideoCategoryList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SaveWxVideoCategoryList() got = %v, want %v", got, tt.want)
			}
		})
	}

}

//DealWithJDDJChannel
func TestPoduct_DealWithJDDJChannel(t *testing.T) {

	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.SyncCategoryScheduleRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			//{"CategoryId":201540331,"CategoryName":"PWT009","FinanceCode":"","ShopName":"","IsSuccess":false,"Message":"",
			// "ChannelId":4,"SyncType":1,"ChannelStoreId":"","AppChannel":1,"ParentId":0,"Sort":165,"SystemError":""}
			name: "处理美团同步分类",
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryScheduleRequest{
					CategoryId:     201540331,
					CategoryName:   "PWT009",
					FinanceCode:    "",
					ShopName:       "",
					IsSuccess:      false,
					Message:        "",
					ChannelId:      4,
					SyncType:       1,
					ChannelStoreId: "",
					AppChannel:     2,
					ParentId:       0,
					Sort:           165,
				},
			},
		},
	}
	//{"CategoryId":201540329,"CategoryName":"PWT00501","FinanceCode":"CX0010",
	// "ShopName":"宠颐生北京爱福","IsSuccess":false,"Message":"","ChannelId":3,"SyncType":1,
	// "ChannelStoreId":"32267735205","AppChannel":1,
	// "ParentId":201540328,"Sort":163,"SystemError":""}
	SetupDB()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := this.DealWithJDDJChannel(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaveWxVideoCategoryList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SaveWxVideoCategoryList() got = %v, want %v", got, tt.want)
			}
		})
	}

}
