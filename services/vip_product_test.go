package services

import (
	"_/proto/pc"
	"context"
	"reflect"
	"testing"

	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc"
)

func TestProduct_ShopCateList(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.ShopCateReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ShopCateListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ShopCateList",
			args: args{
				ctx: context.Background(),
				in:  &pc.ShopCateReq{
					//UserId: "057556b0ccfb40f3a66a4f7f054fd6e9",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.ShopCateList(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("ShopCateList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_AwenCateList(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.AwenCateListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ShopCateListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "AwenCateList",
			args: args{
				ctx: context.Background(),
				in: &pc.AwenCateListReq{
					FinanceCode: "CX0010",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.AwenCateList(tt.args.ctx, tt.args.in)
			kit.JsonEncode(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("AwenCateList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_ShopVipGoodsList(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx  context.Context
		in   *pc.VipGoodsRequest
		opts []grpc.CallOption
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.VipGoodsResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ShopVipGoodsList",
			args: args{
				ctx: context.Background(),
				in: &pc.VipGoodsRequest{
					//GcId:     "1062,1070",
					GcId:     "1068",
					GoodName: "",
					//PriceState:  1,
					//SalesState:  1,
					FinanceCode: "",
					Type:        3, //1-电商精品推荐 2-电商商城商品 3-混合上下分布
					//UserId:      "2abaad6b463f4d7fab9d4a565cab958a", //sit
					//UserId: "6601ae33cb7b4393bfb98ac92810777b", //sit zhangsi 5
					//UserId: "3928fb9aef33430ebcd022d996539c91", //uat
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.ShopVipGoodsList(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("ShopVipGoodsList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

//func TestProduct_AwenVipGoodsList(t *testing.T) {
//	type fields struct {
//		categoryNames []string
//		oldSnap       string
//	}
//	type args struct {
//		ctx context.Context
//		in  *pc.VipGoodsRequest
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantOut *pc.VipGoodsResponse
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "TestProduct_AwenVipGoodsList",
//			args: args{
//				ctx: context.Background(),
//				in: &pc.VipGoodsRequest{
//					GcId:        "0",
//					GoodName:    "",
//					PriceState:  1,
//					SalesState:  1,
//					FinanceCode: "CX0010",
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Product{
//				categoryNames: tt.fields.categoryNames,
//				oldSnap:       tt.fields.oldSnap,
//			}
//			gotOut, err := c.AwenVipGoodsList(tt.args.ctx, tt.args.in)
//			t.Log(kit.JsonEncode(gotOut))
//			if (err != nil) != tt.wantErr {
//				t.Errorf("AwenVipGoodsList() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//
//		})
//	}
//}

func Test_getChildCate(t *testing.T) {
	type args struct {
		id []string
	}
	var ids []string
	ids = append(ids, "1145")
	tests := []struct {
		name    string
		args    args
		wantIds []*int32
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				id: ids,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotIds, err := getChildCate(tt.args.id)
			t.Log(kit.JsonEncode(gotIds))
			if (err != nil) != tt.wantErr {
				t.Errorf("getChildCate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_ShopVipCardGoodsList(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.ShopVipCardGoodsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ShopVipCardGoodsResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &pc.ShopVipCardGoodsRequest{
					Export:    0,
					SkuId:     0,
					SpuId:     0,
					GoodsName: "",
					PageIndex: 0,
					PageSize:  0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.ShopVipCardGoodsList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ShopVipCardGoodsList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("ShopVipCardGoodsList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestProduct_DelShopVipCardGoods(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.DelShopVipCardGoodsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ProductBaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				ctx: context.Background(),
				in: &pc.DelShopVipCardGoodsReq{
					SkuId:    1000115,
					UserId:   "test01",
					UserName: "test",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.DelShopVipCardGoods(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("DelShopVipCardGoods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_ShopGoodsList(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.GoodsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.VipGoodsResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ShopGoodsList",
			args: args{
				ctx: context.Background(),
				in: &pc.GoodsRequest{
					PageIndex: 1,
					PageSize:  10,
					//GoodsId:   1035432001,
					//GoodName: "未卡果味肉酱包牛肉蔓越莓80g 80g",
					//UserId: "2abaad6b463f4d7fab9d4a565cab958a", //sit
					//UserId: "3928fb9aef33430ebcd022d996539c91", //uat
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.ShopGoodsList(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("ShopGoodsList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_ShopCateListByClassify(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.ShopCateListReq
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		wantOut *pc.ShopCateListByClassifyResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		// {
		// 	name: "会员价商品类型",
		// 	c:    &Product{categoryNames: []string{}, oldSnap: ""},
		// 	args: args{
		// 		ctx: context.Background(),
		// 		in: &pc.ShopCateListReq{
		// 			Type:     1,
		// 			Classify: 0,
		// 		},
		// 	},
		// 	wantOut: &pc.ShopCateListByClassifyResponse{},
		// 	wantErr: false,
		// },
		{
			name: "会员价商品类型",
			c:    &Product{categoryNames: []string{}, oldSnap: ""},
			args: args{
				ctx: context.Background(),
				in: &pc.ShopCateListReq{
					Type:     1,
					Classify: 2,
					//UserId:   "2abaad6b463f4d7fab9d4a565cab958a",
					//UserId: "2208378681f945fe90e725ff943a7591", //zhangsi cardid=5
					UserId: "6601ae33cb7b4393bfb98ac92810777b", //zhangsi cardid=1
				},
			},
			wantOut: &pc.ShopCateListByClassifyResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, _ := tt.c.ShopCateListByClassify(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))

		})
	}
}
