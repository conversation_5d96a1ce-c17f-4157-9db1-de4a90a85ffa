package services

import (
	"_/models"
	"_/proto/pc"
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/go-xorm/xorm"
	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

//【管家商品库】编辑管家商品库商品
func (c *Product) EditGjProduct(ctx context.Context, in *pc.ProductRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: 400}
	Engine := NewDbConn()

	session := Engine.NewSession()
	defer session.Close()
	session.Begin()

	if in.Product.ProductType != 3 {
		//管家商品表
		if _, err := session.ID(in.Product.Id).Cols("selling_point,name,short_name,pic,video,content_pc,brand_id,category_id,category_name").
			Update(&models.GjProduct{SellingPoint: in.Product.SellingPoint, Name: in.Product.Name, ShortName: in.Product.ShortName, Pic: in.Product.Pic, Video: in.Product.Video, ContentPc: in.Product.ContentPc, BrandId: int(in.Product.BrandId), CategoryId: int(in.Product.CategoryId), CategoryName: in.Product.CategoryName}); err != nil {
			glog.Error(err)
			session.Rollback()
			return nil, err
		}

		//规格图片
		for _, vSku := range in.SkuInfo {
			for _, vv_sku := range vSku.Skuv {
				if _, err := session.ID(vv_sku.Id).Cols("pic").Update(&models.GjSkuValue{Pic: vv_sku.Pic}); err != nil {
					glog.Error(err)
					session.Rollback()
					return nil, err
				}

			}
		}

		//sku
		for _, vSku := range in.SkuInfo {
			if _, err := session.ID(vSku.SkuId).Cols("weight_for_unit,prepose_price,store_price").Update(&models.GjSku{WeightForUnit: vSku.WeightForUnit, PreposePrice: vSku.PreposePrice, StorePrice: vSku.StorePrice}); err != nil {
				glog.Error(err)
				session.Rollback()
				return nil, err
			}
		}

	} else {
		if in.Product.CategoryId == 0 {
			out.Message = "组合商品分类不能为空"
			return out, nil
		}
		for _, vsku := range in.SkuInfo {
			if vsku.WeightForUnit <= 0 {
				out.Message = "组合商品重量不能为空"
				return out, nil
			}
		}

		if len(in.Product.Pic) == 0 {
			out.Message = "组合商品图片不能为空"
			return out, nil
		}
		//管家商品表
		if _, err := session.ID(in.Product.Id).
			Cols("selling_point,name,short_name,pic,video,content_pc,brand_id,category_id,category_name, use_range").
			Update(&models.GjProduct{
				SellingPoint: in.Product.SellingPoint,
				Name:         in.Product.Name,
				ShortName:    in.Product.ShortName,
				Pic:          in.Product.Pic,
				Video:        in.Product.Video,
				ContentPc:    in.Product.ContentPc,
				BrandId:      int(in.Product.BrandId),
				CategoryId:   int(in.Product.CategoryId),
				CategoryName: in.Product.CategoryName,
				UseRange:     in.Product.UseRange,
			}); err != nil {
			glog.Error(err)
			session.Rollback()
			return nil, err
		}

		//sku
		for _, vSku := range in.SkuInfo {
			if _, err := session.ID(vSku.SkuId).
				Cols("weight_for_unit").
				Update(&models.GjSku{WeightForUnit: vSku.WeightForUnit}); err != nil {
				glog.Error(err)
				session.Rollback()
				return nil, err
			}

		}
	}

	if err := session.Commit(); err != nil {
		glog.Error(err)
		session.Rollback()
		return nil, err
	}

	out.Code = 200
	return out, nil
}

//【管家商品库】根据一个或多个商品ID/SKUID查询商品信息（不包括SKU信息）
func (c *Product) QueryGjProductOnly(ctx context.Context, in *pc.OneofIdRequest) (*pc.ProductResponse, error) {
	out := new(pc.ProductResponse)
	out.Code = 400

	productId := in.GetProductId().GetValue()

	//如果传的是skuid，先查出商品id，再查商品
	Engine := NewDbConn()
	if len(in.GetSkuId().GetValue()) > 0 {
		if err := Engine.Table("gj_sku").In("id", in.GetSkuId().GetValue()).Select("product_id").Find(&productId); err != nil {
			glog.Error(err)
			return out, err
		}
	}

	if err := Engine.Table("gj_product").Select("`id`, `category_id`, `brand_id`, `name`, `short_name`,`code`, `bar_code`, `create_date`, `update_date`, `is_del`, `is_group`, `pic`, `selling_point`, `video`, `content_pc`, `content_mobile`, `is_discount`, `product_type`, `is_use`, `category_name`,`brand_name`,`use_range`, `group_type`, `term_type`, `term_value`, `virtual_invalid_refund`, `warehouse_type`,`is_intel_goods`").In("id", productId).Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

//【管家商品库】根据商品ID查询商品属性
func (c *Product) QueryGjProductAttr(ctx context.Context, in *pc.IdRequest) (*pc.ProductAttrResponse, error) {
	out := new(pc.ProductAttrResponse)
	out.Code = 400

	Engine := NewDbConn()
	if err := Engine.Table("gj_product_attr").Select("`id`, `product_id`, `attr_id`, `attr_value_id`").Where("product_id=?", in.Id).Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

//【管家商品库】根据商品ID查询商品渠道信息
func (c *Product) QueryGjProductChannel(ctx context.Context, in *pc.IdRequest) (*pc.ProductChannelResponse, error) {
	out := new(pc.ProductChannelResponse)
	out.Code = http.StatusBadRequest
	if in.Id == "" {
		return out, fmt.Errorf("ID参数不能为空！")
	}

	Engine := NewDbConn()
	var list []*models.GjProductChannel
	if err := Engine.Table("gj_product_channel").Select("`id`, `product_id`,`channel_id`, `category_id`").Where("product_id=?", in.Id).Find(&list); err != nil {
		glog.Errorf("Product/QueryGjProductChannel 查询商品渠道信息异常,err：%+v", err)
		return out, err
	}
	var attrList []*models.GjProductChannelAttr
	if err := Engine.Table("gj_product_channel_attr").Select("`id`, `product_id`,`channel_id`, `attr_id`, `attr_name`, `attr_value_id`, `attr_value`").Where("product_id=?", in.Id).Find(&attrList); err != nil {
		glog.Errorf("Product/QueryGjProductChannel 查询商品渠道属性异常,err：%+v", err)
		return out, err
	}

	attrMap := make(map[string][]*pc.SimpleChannelAttr)
	var k string
	for _, item := range attrList {
		k = fmt.Sprintf("%d_%d", item.ProductId, item.ChannelId)
		attrMap[k] = append(attrMap[k], &pc.SimpleChannelAttr{
			Id:          item.Id,
			AttrId:      item.AttrId,
			AttrValueId: item.AttrValueId,
			AttrName:    item.AttrName,
			AttrValue:   item.AttrValue,
		})
	}

	for _, item := range list {
		k = fmt.Sprintf("%d_%d", item.ProductId, item.ChannelId)
		attrs := attrMap[k]
		if attrs == nil {
			attrs = []*pc.SimpleChannelAttr{}
		}
		out.Details = append(out.Details, &pc.ProductChannel{
			ProductId:  item.ProductId,
			ChannelId:  item.ChannelId,
			CategoryId: item.CategoryId,
			Attr:       attrs,
		})
	}

	out.Code = http.StatusOK
	return out, nil
}

// 【管家商品库】保存商品渠道信息
func (c *Product) SaveGjProductChannel(ctx context.Context, in *pc.SaveGjProductChannelRequest) (*pc.BaseResponse, error) {
	if len(in.List) == 0 {
		glog.Warningf("Product/SaveGjProductChannel 没有商品渠道信息需要保存")
		return &pc.BaseResponse{}, nil
	}

	Engine := NewDbConn()
	var list []*models.GjProductChannel
	if err := Engine.Table("gj_product_channel").Select("`id`, `product_id`,`channel_id`, `category_id`").Where("product_id=?", in.List[0].ProductId).Find(&list); err != nil {
		glog.Errorf("Product/SaveGjProductChannel 查询商品渠道信息异常,data:%s,err：%+v", kit.JsonEncode(in), err)
		return &pc.BaseResponse{}, err
	}
	_, err := Engine.Transaction(func(session *xorm.Session) (interface{}, error) {
		var err error
		var insertList []*models.GjProductChannel
		var insertAttrList []*models.GjProductChannelAttr
		for _, item := range in.List {
			var row *models.GjProductChannel
			for _, old := range list {
				if item.ChannelId == old.ChannelId {
					row = old
					break
				}
			}
			// 更新渠道信息
			if row != nil {
				err = c.updateProductChannel(session, row, item)
				if err != nil {
					return nil, err
				}
				continue
			}

			// 新增渠道信息
			insertList = append(insertList, &models.GjProductChannel{
				ProductId:  item.ProductId,
				ChannelId:  item.ChannelId,
				CategoryId: item.CategoryId,
			})
			for _, v := range item.Attr {
				insertAttrList = append(insertAttrList, &models.GjProductChannelAttr{
					ProductId:   item.ProductId,
					ChannelId:   item.ChannelId,
					AttrId:      v.AttrId,
					AttrName:    v.AttrName,
					AttrValueId: v.AttrValueId,
					AttrValue:   v.AttrValue,
				})
			}
		}
		if len(insertList) == 0 {
			return nil, nil
		}
		_, err = session.InsertMulti(&insertList)
		if err != nil {
			return nil, err
		}
		if len(insertAttrList) == 0 {
			return nil, nil
		}
		_, err = session.InsertMulti(&insertAttrList)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})

	if err != nil {
		glog.Errorf("Product/SaveGjProductChannel 保存商品渠道信息异常，data:%s,err：%+v", kit.JsonEncode(in), err)
		return &pc.BaseResponse{}, err
	}
	return &pc.BaseResponse{}, nil
}

// 更新产品渠道信息
func (c *Product) updateProductChannel(session *xorm.Session, row *models.GjProductChannel, item *pc.ProductChannel) error {
	row.CategoryId = item.CategoryId
	_, err := session.Where("id=?", row.Id).Cols("category_id").Update(row)
	if err != nil {
		return err
	}
	// 更新渠道属性，先删除后新增
	_, err = session.Where("product_id=? and channel_id=?", row.ProductId, row.ChannelId).Delete(&models.GjProductChannelAttr{})
	if err != nil {
		return err
	}
	if len(item.Attr) == 0 {
		return nil
	}
	insertList := make([]*models.GjProductChannelAttr, 0, len(item.Attr))
	for _, v := range item.Attr {
		insertList = append(insertList, &models.GjProductChannelAttr{
			ProductId:   item.ProductId,
			ChannelId:   item.ChannelId,
			AttrId:      v.AttrId,
			AttrName:    v.AttrName,
			AttrValueId: v.AttrValueId,
			AttrValue:   v.AttrValue,
		})
	}
	_, err = session.InsertMulti(&insertList)
	if err != nil {
		return err
	}
	return nil
}

//【管家商品库】根据商品ID，查询SKU信息，不包含商品主体信息
func (c *Product) QueryGjSku(ctx context.Context, in *wrappers.Int32Value) (*pc.SkuResponse, error) {
	out := new(pc.SkuResponse)
	out.Code = 400

	Engine := NewDbConn()
	if err := Engine.Table("gj_sku").Select("`id`, `product_id`, `market_price`, `retail_price`,`bar_code`,weight_for_unit,prepose_price,store_price").Where("`product_id`=?", in.Value).Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	if len(out.Details) <= 0 {
		return out, errors.New("gj_sku商品数据不存在")
	}

	//查询SKU组合的值信息
	var skuValue []*pc.SkuValue
	if res, err := c.QueryGjSkuValue(ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{out.Details[0].ProductId}}}}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		skuValue = res.Details
	}

	//第三方货号信息
	var skuThird []*pc.SkuThird
	if res, err := c.QueryGjSkuThird(ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{out.Details[0].ProductId}}}}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		skuThird = res.Details
	}

	//获得specid,specvalueid
	var specID strings.Builder
	var specValueID strings.Builder
	for i, v := range skuValue {
		specID.WriteString(strconv.Itoa(int(v.SpecId)))
		specValueID.WriteString(strconv.Itoa(int(v.SpecValueId)))
		if i != len(skuValue)-1 {
			specID.WriteString(",")
			specValueID.WriteString(",")
		}
	}

	var spec map[int32]*pc.Spec
	var specValue map[int32]*pc.SpecValue
	if res, err := c.QuerySpecMap(ctx, &pc.IdRequest{Id: specID.String()}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		spec = res.Spec
	}
	if res, err := c.QuerySpecValueMap(ctx, &pc.IdRequest{Id: specValueID.String()}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		specValue = res.SpecValue
	}

	for _, v := range out.Details {
		//var skuValue *pc.SkuValue
		for _, s := range skuValue {
			if v.Id == s.SkuId {
				sv := *s
				sv.SpecName = spec[sv.SpecId].Name
				sv.SpecValueValue = specValue[sv.SpecValueId].Value
				v.SkuValue = append(v.SkuValue, &sv)
			}
		}

		for _, t := range skuThird {
			if v.Id == t.SkuId {
				v.SkuThird = append(v.SkuThird, t)
			}
		}
	}

	out.Code = 200
	return out, nil
}

//【管家商品库】根据商品ID，查询SKU规格详细信息(系统默认)
func (c *Product) QueryGjSkuValue(ctx context.Context, in *pc.OneofIdRequest) (*pc.SkuValueResponse, error) {
	out := new(pc.SkuValueResponse)
	out.Code = 400

	if len(in.GetSkuId().GetValue()) == 0 && len(in.GetProductId().GetValue()) == 0 {
		out.Message = "sku_id与product_id必须填写一个"
		return out, nil
	}

	Engine := NewDbConn()
	session := Engine.Table("gj_sku_value").Select("`id`, `spec_id`, `spec_value_id`, `sku_id`, `product_id`, `pic`")
	if len(in.GetSkuId().GetValue()) != 0 {
		session = session.In("sku_id", in.GetSkuId().GetValue())
	} else {
		session = session.In("product_id", in.GetProductId().GetValue())
	}

	if err := session.OrderBy("sort").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

//【管家商品库】根据商品ID，查询第三方货号
func (c *Product) QueryGjSkuThird(ctx context.Context, in *pc.OneofIdRequest) (*pc.SkuThirdResponse, error) {
	out := new(pc.SkuThirdResponse)
	out.Code = 400

	Engine := NewDbConn()

	session := Engine.Table("gj_sku_third").Join("INNER", "erp", "gj_sku_third.erp_id=erp.id").Select("gj_sku_third.`id`, gj_sku_third.`sku_id`, gj_sku_third.`third_spu_id`,gj_sku_third.`third_sku_id`, gj_sku_third.`erp_id`, gj_sku_third.`product_id`,erp.`name` as erp_name")
	if len(in.GetSkuId().GetValue()) > 0 {
		session.In("gj_sku_third.sku_id", in.GetSkuId().GetValue())
	} else if len(in.GetProductId().GetValue()) > 0 {
		session.In("gj_sku_third.product_id", in.GetProductId().GetValue())
	} else if len(in.GetErpId().GetValue()) > 0 {
		session.In("gj_sku_third.erp_id", in.GetErpId().GetValue())
	}

	if err := session.Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}
