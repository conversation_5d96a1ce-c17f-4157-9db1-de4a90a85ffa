package services

import (
	"_/proto/pc"
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/techoner/gophp"
)

func Test_PhpConv(t *testing.T) {
	r, _ := gophp.Unserialize([]byte(`a:6:{i:7;a:2:{s:4:"name";s:10:"重量(kg)";s:5:"value";s:1:"5";}i:8;a:2:{s:4:"name";s:9:"保质期";s:5:"value";s:2:"60";}i:9;a:2:{s:4:"name";s:12:"包装清单";s:5:"value";s:0:"";}i:16;a:2:{s:4:"name";s:12:"商品规格";s:5:"value";s:0:"";}i:17;a:2:{s:4:"name";s:12:"商品单位";s:5:"value";s:3:"包";}i:18;a:2:{s:4:"name";s:9:"条形码";s:5:"value";s:0:"";}}`))
	for k, v := range r.(map[string]interface{}) {
		fmt.Println(k, v.(map[string]interface{})["name"], v.(map[string]interface{})["value"])
	}
}

func TestProduct_SyncGoods(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.ChannelStoreProduct
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "同步商品",
			args: args{ctx: nil, in: &pc.ChannelStoreProduct{}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pr := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := pr.SyncGoods(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SyncGoods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SyncGoods() got = %v, want %v", got, tt.want)
			}
		})
	}
}