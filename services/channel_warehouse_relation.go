package services

import (
	"_/models"
	"_/proto/pc"
	"context"
	"errors"
	"fmt"

	"github.com/maybgit/glog"
)

/**
更具门店和渠道设置的门店返回仓库id
*/

func (c *Product) GetFinanceCodeWarehouseRelation(ctx context.Context, in *pc.GetFinanceCodeWarehouseRelationVO) (*pc.GetFinanceCodeWarehouseRelationResp, error) {
	//glog.Info("GetFinanceCodeWarehouseRelation入参：", kit.JsonEncode(in))
	resp := pc.GetFinanceCodeWarehouseRelationResp{}

	warehouses, err := c.GetChannelWarehouses([]string{in.FinanceCode}, 0)
	if err != nil {
		glog.Info("获取渠道配置信息异常：", err.Error())
		return &resp, err
	}

	channelWarehouseMap := make(map[int]models.ChannelWarehouse, 0)
	for i := range warehouses {
		warehouse := warehouses[i]
		channelWarehouseMap[warehouse.ChannelId] = warehouse
	}

	//glog.Info("warehouses: ", kit.JsonEncode(warehouses), " channelWarehouseMap: ", kit.JsonEncode(channelWarehouseMap))
	if in.ChannelId == ChannelAwenId { // 阿闻渠道

		/**
			1.如果“阿闻渠道”和“阿闻竖屏-自提”都是从xx仓取的库存，那么校验该商品在xx仓的库存
			2.如果“阿闻渠道”和“阿闻竖屏-自提”是从不同的仓库取的库存，比如“阿闻渠道”是从前置仓取库存，“阿闻竖屏-自提”是虚拟仓取库存，
				那么校验的时候就校验这两个仓库对于该商品的库存之和
		    3：门店和前置不能同时配置
		*/

		warehouseAwen, isOkAwen := channelWarehouseMap[ChannelAwenId]                   // 阿闻id
		warehouseAwenPickUp, isOkAwenPickUp := channelWarehouseMap[ChannelAwenPickUpId] // 阿闻竖屏id

		if !isOkAwen && !isOkAwenPickUp {
			return &resp, errors.New("没有绑定渠道关系")
		} else {
			if warehouseAwen.WarehouseId == warehouseAwenPickUp.WarehouseId {
				resp.IsSame = true
				resp.WarehouseIds = append(resp.WarehouseIds, int32(warehouseAwen.WarehouseId))
				resp.Category = int32(warehouseAwen.Category)

			} else {

				var categorys []int // 判断门店和前置不能同时存在

				var ismap = map[int]struct{}{} // 仓库去重

				if isOkAwen {
					if _, ok := ismap[warehouseAwen.WarehouseId]; !ok {
						resp.WarehouseIds = append(resp.WarehouseIds, int32(warehouseAwen.WarehouseId))
					}
					categorys = append(categorys, warehouseAwen.Category)
					resp.Category = int32(warehouseAwen.Category)
				}
				if isOkAwenPickUp {
					if _, ok := ismap[warehouseAwenPickUp.WarehouseId]; !ok {
						resp.WarehouseIds = append(resp.WarehouseIds, int32(warehouseAwenPickUp.WarehouseId))
					}
					categorys = append(categorys, warehouseAwenPickUp.Category)
					resp.Category = int32(warehouseAwen.Category)
				}
				var isCategoryzl, isCategoryqz bool
				for i := range categorys {
					if categorys[i] == 3 {
						isCategoryzl = true
					}
					if categorys[i] == 4 || categorys[i] == 5 {
						isCategoryqz = true
					}
				}
				if isCategoryzl && isCategoryqz {
					return &resp, errors.New("门店仓和前置仓不能同时配置绑定关系")
				}

			}
		}
	} else {
		// 其他渠道,直接通过仓库id去查询
		warehouse, isOk := channelWarehouseMap[int(in.ChannelId)]
		if !isOk {
			return &resp, errors.New("没有绑定渠道关系")

		} else {
			resp.WarehouseIds = append(resp.WarehouseIds, int32(warehouse.WarehouseId))
			resp.Category = int32(warehouse.Category)
		}
	}

	resp.ChannelId = in.ChannelId
	//glog.Info("GetFinanceCodeWarehouseRelation返回值：", kit.JsonEncode(resp), " in参数: ", kit.JsonEncode(in))
	return &resp, nil
}

// 查询渠道的仓库设置信息
// args 第一位 仓类型 []int32，第二位 前置虚拟仓需要附加前置仓 0,1
func (c *Product) GetChannelWarehouses(finance_code []string, channel_id int32, args ...interface{}) (cws []models.ChannelWarehouse, err error) {
	db := NewDbConn()
	s := db.Table("dc_dispatch.warehouse_relation_shop").Alias("r").
		Join("inner", "dc_dispatch.warehouse w", "w.id = r.warehouse_id").Where("w.org_id =1")

	if len(finance_code) > 0 {
		s.In("r.shop_id", finance_code)
	}
	if channel_id > 0 {
		// 当渠道绑定前置虚拟仓，那么要把前置仓也查出来
		s.Where("r.channel_id = ?", channel_id)
	}

	// 过滤仓类型
	if len(args) > 0 && args[0] != nil {
		s.In("w.category", args[0])
	}

	if err = s.Select(`r.shop_id,r.shop_name,r.channel_id,w.code as warehouse_code,w.id as warehouse_id,w.category`).
		Find(&cws); err != nil {
		glog.Error("查询渠道的仓库设置信息 出错：", err.Error())
		return
	}

	return
}

/*
*
获取门店仓的门店数据
*/
func (c *Product) GezZLWarehouseList(channel_id int32) ([]models.ChannelWarehouse, error) {
	return c.GetChannelWarehouses(nil, channel_id, []int32{3})
}

// GezQZWarehouseList 获取前置仓数据 如果渠道配置的是虚拟仓需要加上前置仓来执行自动上架
func (c *Product) GezQZWarehouseList(channel_id int32) (cws []models.ChannelWarehouse, err error) {
	return c.GetChannelWarehouses(nil, channel_id, []int32{4, 5}, 1)
}

// GetChannelWarehouseByCategory 获取渠道和仓库类型获取门店仓库数据
func GetChannelWarehouseByCategory(channelId, category int32) (cws []models.ChannelWarehouse, err error) {
	db := NewDbConn()
	err = db.Table("dc_dispatch.warehouse_relation_shop").Alias("wrs").
		Join("inner", "dc_dispatch.warehouse w", fmt.Sprintf("w.id = wrs.warehouse_id and w.category = %d", category)).
		Join("inner", "datacenter.store_relation r", "r.finance_code = wrs.shop_id and r.channel_id = wrs.channel_id").
		Select("distinct wrs.shop_id,wrs.shop_name,w.code as warehouse_code,wrs.warehouse_id,w.category,wrs.channel_id").
		Where("r.channel_store_id is not null and r.channel_store_id != '' and w.org_id = 1").
		Where("wrs.channel_id = ?", channelId).Find(&cws)
	return
}

func (c *Product) GetChannelWarehousesShops(channelId int32, WarehouseId int) (ShopIds []string) {
	db := NewDbConn()
	db.SQL(`select shop_id from dc_dispatch.warehouse_relation_shop where channel_id =? and warehouse_id =?;`, channelId, WarehouseId).Find(&ShopIds)
	return
}
