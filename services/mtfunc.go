package services

import (
	"_/proto/dac"
	"context"
	"encoding/json"

	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

var (
	storeExternalRedisKey = "datacenter:store:external"
	orderSnRedisKey       = "datacenter:store:ordersn:"
)

//GetAppChannelByStoreId
//返回1.阿闻自有,2.TP代运营,0查不到门店
func GetAppChannelByStoreId(channelStoreId string) int32 {
	if len(channelStoreId) <= 0 {
		return 0
	}
	redisConn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}
	data := redisConn.HGetAll(storeExternalRedisKey).Val()
	if len(data) <= 0 {
		setRedisData()
	}
	str := redisConn.HGet(storeExternalRedisKey, channelStoreId).Val()
	if len(str) <= 0 {
		return setRedisOne(channelStoreId)
	}
	store := dac.StoreChannelExternalData{}
	err := json.Unmarshal([]byte(str), &store)
	if err != nil {
		glog.Error("解析data数据错误:StoreChannelExternalData", err)
		return 0
	}
	return store.AppChannel
}

func setRedisOne(channelStoreId string) int32 {
	data := getStoreChannelExternalData(channelStoreId)
	if len(data) > 0 {
		redisConn := GetRedisConn()
		if kit.EnvCanCron() {
			defer redisConn.Close()
		}
		isOk := redisConn.HSet("datacenter:store:external", data[0].ChannelStoreId, kit.JsonEncode(data[0])).Val()
		if !isOk {
			glog.Error("set redis,datacenter:store:external:err", kit.JsonEncode(data))
			return 0
		}
		return data[0].AppChannel
	}
	return 0
}

func setRedisData() {
	data := getStoreChannelExternalData("")
	if len(data) > 0 {
		redisValue := make(map[string]interface{}, 0)
		for _, v := range data {
			if len(v.ChannelStoreId) > 0 {
				redisValue[v.ChannelStoreId] = kit.JsonEncode(v)
			}
		}
		redisConn := GetRedisConn()
		if kit.EnvCanCron() {
			defer redisConn.Close()
		}
		val := redisConn.HMSet("datacenter:store:external", redisValue)
		if val.Val() != "Ok" {
			glog.Error(val)
		}
	}
}

func getStoreChannelExternalData(channelStoreId string) []*dac.StoreChannelExternalData {
	data := make([]*dac.StoreChannelExternalData, 0)
	dacClient := dac.GetDataCenterClient()
	grpcRes, err := dacClient.RPC.GetStoreChannelExternal(context.Background(), &dac.GetStoreChannelExternalReq{ChannelId: 2, ChannelStoreId: channelStoreId})
	if err != nil {
		glog.Error("调用GetStoreChannelExternal失败，", err, "，参数：2", err)
		return data
	}
	if grpcRes.Code != 200 {
		glog.Error("调用GetStoreChannelExternal失败，", err, "，参数：2", kit.JsonEncode(grpcRes))
		return data
	}
	return grpcRes.Data
}
