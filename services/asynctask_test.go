package services

import (
	"testing"
)

func TestDealAsyncTaskListByChannel(t *testing.T) {
	type args struct {
		channelId int32
	}
	tests := []struct {
		name string
		args args
		want func() bool
	}{
		// TODO: Add test cases.
		{
			name: "测试饿了么商品上架",
			args: args{
				channelId: 3,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := DealAsyncTaskListByChannel(tt.args.channelId)
			got()
		})
	}
}

func TestDealAsyncTaskListAll(t *testing.T) {
	tests := []struct {
		name string
		want bool
	}{
		{
			name: "定时任务测试",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := DealAsyncTaskListAll(); got != tt.want {
				t.Errorf("DealAsyncTaskListAll() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDealAsyncTaskListByQzcPrice(t *testing.T) {
	DealAsyncTaskListByQzcPrice(19)()
}
