package services

import (
	"log"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/ppkg/distributed-worker/core"
)

var workerCtx *core.ApplicationContext

func init() {
	opts := []core.Option{
		core.WithNacosAddrOption(config.GetString("nacos.addr")),
		core.WithNacosNamespaceOption(config.GetString("nacos.namespace")),
		core.WithNacosServiceGroupOption(config.GetString("nacos.serviceGroup")),
		core.WithSchedulerServiceNameOption(config.GetString("schedulerServiceName")),
	}
	workerCtx = core.NewApp(opts...)
	err := workerCtx.Init()

	if err != nil {
		glog.Errorf("初始化worker失败,%v", err)
		log.Fatal(err)
	}
}
