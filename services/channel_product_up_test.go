package services

import (
	"_/enum"
	"_/proto/et"
	"_/proto/pc"
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"testing"

	kit "github.com/tricobbler/rp-kit"
)

func TestChannelProductUpDown_upProductToElm(t *testing.T) {
	type fields struct {
		ProductIds    []string
		ChannelId     int
		FinanceCodes  []string
		UserNo        string
		IsSyncPrice   bool
		StoreProducts []*ChannelProductUp_StoreProduct
		UpResult      []*ChannelProductUp_Result
		UnknownError  error
	}
	type args struct {
		storeProduct *ChannelProductUp_StoreProduct
		client       *et.Client
	}
	var grpcClient = et.GetExternalClient()
	defer grpcClient.Close()
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "测试上架",
			args: args{
				client: grpcClient,
				storeProduct: &ChannelProductUp_StoreProduct{
					ProductSkuId:             "1023527001",
					StoreFinanceCode:         "CX0010",
					StoreChannelFinanceCodes: "test_894841_49101183",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &ChannelProductUpDown{
				ProductIds:    tt.fields.ProductIds,
				ChannelId:     tt.fields.ChannelId,
				FinanceCodes:  tt.fields.FinanceCodes,
				UserNo:        tt.fields.UserNo,
				IsSyncPrice:   tt.fields.IsSyncPrice,
				StoreProducts: tt.fields.StoreProducts,
				UpResult:      tt.fields.UpResult,
				UnknownError:  tt.fields.UnknownError,
			}

			if err := this.upProductToElm(tt.args.storeProduct, tt.args.client); (err != nil) != tt.wantErr {
				t.Errorf("upProductToElm() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChannelProductUpDown_downProductToElm(t *testing.T) {
	type fields struct {
		ProductIds    []string
		ChannelId     int
		FinanceCodes  []string
		UserNo        string
		IsSyncPrice   bool
		StoreProducts []*ChannelProductUp_StoreProduct
		UpResult      []*ChannelProductUp_Result
		UnknownError  error
	}
	type args struct {
		storeProduct *ChannelProductUp_StoreProduct
		client       *et.Client
	}
	var grpcClient = et.GetExternalClient()
	defer grpcClient.Close()
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "测试上架",
			args: args{
				client: grpcClient,
				storeProduct: &ChannelProductUp_StoreProduct{
					ProductSkuId:             "1023527001",
					StoreFinanceCode:         "CX0010",
					StoreChannelFinanceCodes: "test_894841_49101183",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &ChannelProductUpDown{
				ProductIds:    tt.fields.ProductIds,
				ChannelId:     tt.fields.ChannelId,
				FinanceCodes:  tt.fields.FinanceCodes,
				UserNo:        tt.fields.UserNo,
				IsSyncPrice:   tt.fields.IsSyncPrice,
				StoreProducts: tt.fields.StoreProducts,
				UpResult:      tt.fields.UpResult,
				UnknownError:  tt.fields.UnknownError,
			}
			if err := this.downProductToElm(tt.args.storeProduct, tt.args.client); (err != nil) != tt.wantErr {
				t.Errorf("downProductToElm() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// 测试北京虚拟商品的应用范围
func TestChannelProductUpDown_CheckVirtualProductUseRange(t *testing.T) {
	type fields struct {
		ProductIds    []string
		ChannelId     int
		FinanceCodes  []string
		UserNo        string
		IsSyncPrice   bool
		StoreProducts []*ChannelProductUp_StoreProduct
		UpResult      []*ChannelProductUp_Result
		UnknownError  error
	}
	type args struct {
		storeProduct *ChannelProductUp_StoreProduct
		client       *et.Client
	}
	var grpcClient = et.GetExternalClient()
	defer grpcClient.Close()
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "测试上架",
			args: args{
				client: grpcClient,
				storeProduct: &ChannelProductUp_StoreProduct{
					ProductSkuId:             "1031439001",
					StoreFinanceCode:         "CX0011",
					StoreChannelFinanceCodes: "test_894841_49101183",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if useRange, err := CheckVirtualProductUseRange(tt.args.storeProduct, 3); err != nil {
				t.Errorf("downProductToElm() error = %v, wantErr %v", err, tt.wantErr)
			} else {
				data, ok := useRange["CX0011"]
				t.Log("适应门店范围：", data, ok)
			}
		})
	}
}

func TestChannelProductUpDown_buildStoreProducts(t *testing.T) {
	type fields struct {
		ProductIds             []string
		ChannelId              int
		FinanceCodes           []string
		UserNo                 string
		UserName               string
		IsSyncPrice            bool
		StoreProducts          []*ChannelProductUp_StoreProduct
		UpResult               []*ChannelProductUp_Result
		UnknownError           error
		Ctx                    context.Context
		TaskId                 int
		FinanceCodesAppChannel map[string]int
		DownType               int
		UpType                 int
	}
	type args struct {
		channelId    int
		productIds   []string
		financeCodes []string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*ChannelProductUp_StoreProduct
	}{
		// TODO: Add test cases.
		{name: "11"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &ChannelProductUpDown{
				ProductIds:             tt.fields.ProductIds,
				ChannelId:              tt.fields.ChannelId,
				FinanceCodes:           tt.fields.FinanceCodes,
				UserNo:                 tt.fields.UserNo,
				UserName:               tt.fields.UserName,
				IsSyncPrice:            tt.fields.IsSyncPrice,
				StoreProducts:          tt.fields.StoreProducts,
				UpResult:               tt.fields.UpResult,
				UnknownError:           tt.fields.UnknownError,
				Ctx:                    tt.fields.Ctx,
				TaskId:                 tt.fields.TaskId,
				FinanceCodesAppChannel: tt.fields.FinanceCodesAppChannel,
				DownType:               tt.fields.DownType,
				UpType:                 tt.fields.UpType,
			}
			var product = new(Product)
			var requestdown = new(pc.ThirdDownChannelProductRequest)
			requestdown.ChannelId = 2
			item := new(pc.ThirdDownChannelProductDetail)
			item.AppPoiCode = "4889_2705535"
			item.ProductId = append(item.ProductId, "1035233")

			requestdown.DetailThird = append(requestdown.DetailThird, item)

			product.ThirdDownChannelProduct(context.Background(), requestdown)

			if got := this.buildStoreProducts(2, tt.args.productIds, tt.args.financeCodes); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("buildStoreProducts() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestQueryAwenProductUpInfoByIds(t *testing.T) {
	type args struct {
		productIds []int
	}
	tests := []struct {
		name        string
		args        args
		wantUpInfos map[int]*AwenProductUpInfo
		wantErr     bool
	}{
		{
			args: args{productIds: []int{1033695, 1034960}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotUpInfos, err := QueryAwenProductUpInfoByIds(tt.args.productIds, 3)

			if err != nil {
				t.Error(err)
			}

			for _, info := range gotUpInfos {
				if err = info.Check(3, "CX0011", 1, ""); err != nil {
					t.Errorf("%d %v", info.ProductId, err)
				}
			}
		})
	}
}

func TestProduct_UpChannelProduct(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx     context.Context
		request *pc.UpDownChannelProductRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestProduct_UpChannelProduct",
			args: args{
				ctx: context.Background(),
				//request: &pc.UpDownChannelProductRequest{
				//	ProductId:   []string{"1051257"}, //组合
				//	FinanceCode: []string{"CX0004"},
				//	ChannelId:   2,
				//	UserNo:      "test",
				//	UserName:    "test",
				//},
				//request: &pc.UpDownChannelProductRequest{
				//	ProductId:   []string{"1032747"}, //药品渠道测试
				//	FinanceCode: []string{"CX0011"},  //爱之源
				//	ChannelId:   2,
				//	UserNo:      "test",
				//	UserName:    "test",
				//},
				request: &pc.UpDownChannelProductRequest{
					ProductId:   []string{"1020687,1020686"}, //药品渠道测试
					FinanceCode: []string{"CX0011"},          //爱之源
					ChannelId:   1,
					UserNo:      "test",
					UserName:    "test",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := c.UpChannelProduct(tt.args.ctx, tt.args.request)
			fmt.Println(kit.JsonEncode(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("UpChannelProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("UpChannelProduct() got = %v, want %v", got, tt.want)
			//}
		})
	}
}

func Test_checkGroupProduct(t *testing.T) {
	type args struct {
		productId string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_checkGroupProduct",
			args: args{
				productId: "1051257",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CheckGroupProduct(tt.args.productId, 2); got != tt.want {
				t.Errorf("checkGroupProduct() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestQueryStoredrugInfo(t *testing.T) {
	type args struct {
		finance_codes []string
	}
	tests := []struct {
		name        string
		args        args
		wantUpInfos map[string]string
	}{
		// TODO: Add test cases.
		{name: "test",
			args: args{
				finance_codes: []string{"CX0011", "CX0013"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotUpInfos := QueryStoredrugInfo(tt.args.finance_codes, 1); !reflect.DeepEqual(gotUpInfos, tt.wantUpInfos) {
				t.Errorf("QueryStoredrugInfo() = %v, want %v", gotUpInfos, tt.wantUpInfos)
			}
		})
	}
}

func TestChannelProductUpDown_UpPorudct(t *testing.T) {
	type fields struct {
		ProductIds             []string
		ChannelId              int
		FinanceCodes           []string
		UserNo                 string
		UserName               string
		IsSyncPrice            bool
		StoreProducts          []*ChannelProductUp_StoreProduct
		UpResult               []*ChannelProductUp_Result
		UnknownError           error
		Ctx                    context.Context
		TaskId                 int
		FinanceCodesAppChannel map[string]int
		DownType               int
		UpType                 int
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{
			name:   "test",
			fields: fields{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &ChannelProductUpDown{
				ProductIds:   []string{"1050425"},
				ChannelId:    2,
				FinanceCodes: []string{"CX0013"},
				UserNo:       "UpTypeCansele",
				UserName:     "UpTypeCansele",
				IsSyncPrice:  true,
				UpType:       enum.RecordTypeAutoUp,
				DownType:     7,
			}
			this.UpPorudct()
		})
	}
}

func TestMtProductThirdId(t *testing.T) {
	type args struct {
		mtRes       *et.RetailSellStatusResult
		foodData    []*et.RetailBatchinitdata
		financeCode string
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			name: "TEST",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			foodDatajson := `{"data":"ng","msg":"","Error":{"code":1,"msg":"[{\"app_food_code\":\"1000728\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1000750\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1000794\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1000795\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001261\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001276\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001594\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001595\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001646\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000145]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001873\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000134]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"100203\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1002847\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000260,1200000101,1200000210]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1002848\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000260,1200000101,1200000210]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"100296\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1003535\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000210]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1004376\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000140,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1005487\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1007284\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1007348\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1009054\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000132,1200005293,1200000356]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1010210\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1010383\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1014424\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000132,1200000134,1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1014425\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000132,1200000134,1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1018365\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000210,1200000132,1200000134]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1019054\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000140,1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102516\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000140,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102628\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102629\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102630\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102898\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"103863\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1039325\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000145]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1041822\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1042236\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1046280\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1046496\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"105101\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000145]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001646\",\"app_spu_code\":\"1001646\",\"blockFlag\":2,\"error_msg\":\"l | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d08b830b59450fcb265603f1c6f66bc8.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d3a3062075bee5abdc1573cd9296f16a.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/f5122ecd83ae012567ef6a7adf10ca0d.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/8642db76f2921a6169fa4e0e9dcb22e3.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/c8835d443ad3659fe306fb787e6dda4d.gif, 图片不支持gif格式，请重新上传\"},{\"app_food_code\":\"105101\",\"app_spu_code\":\"105101\",\"blockFlag\":2,\"error_msg\":\"l | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d08b830b59450fcb265603f1c6f66bc8.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d3a3062075bee5abdc1573cd9296f16a.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/f5122ecd83ae012567ef6a7adf10ca0d.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/8642db76f2921a6169fa4e0e9dcb22e3.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/c8835d443ad3659fe306fb787e6dda4d.gif, 图片不支持gif格式，请重新上传\"}]"},"Code":400,"message":"[{\"app_food_code\":\"1000728\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1000750\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1000794\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1000795\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001261\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001276\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001594\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001595\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001646\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000145]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001873\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000134]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"100203\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1002847\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000260,1200000101,1200000210]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1002848\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000260,1200000101,1200000210]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"100296\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1003535\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000210]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1004376\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000140,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1005487\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1007284\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1007348\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1009054\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000132,1200005293,1200000356]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1010210\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1010383\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1014424\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000132,1200000134,1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1014425\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000132,1200000134,1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1018365\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000210,1200000132,1200000134]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1019054\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000140,1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102516\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132,1200000140,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102628\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102629\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102630\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"102898\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"103863\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1039325\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000145]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1041822\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1042236\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1046280\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1046496\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"105101\",\"blockFlag\":1,\"error_msg\":\"类目属性id：[1200000088,1200000145]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段\"},{\"app_food_code\":\"1001646\",\"app_spu_code\":\"1001646\",\"blockFlag\":2,\"error_msg\":\"l | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d08b830b59450fcb265603f1c6f66bc8.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d3a3062075bee5abdc1573cd9296f16a.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/f5122ecd83ae012567ef6a7adf10ca0d.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/8642db76f2921a6169fa4e0e9dcb22e3.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/c8835d443ad3659fe306fb787e6dda4d.gif, 图片不支持gif格式，请重新上传\"},{\"app_food_code\":\"105101\",\"app_spu_code\":\"105101\",\"blockFlag\":2,\"error_msg\":\"l | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d08b830b59450fcb265603f1c6f66bc8.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d3a3062075bee5abdc1573cd9296f16a.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/f5122ecd83ae012567ef6a7adf10ca0d.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/8642db76f2921a6169fa4e0e9dcb22e3.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/c8835d443ad3659fe306fb787e6dda4d.gif, 图片不支持gif格式，请重新上传\"}]","error_list":[{"app_spu_code":"1000728","msg":"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1000750","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1000794","msg":"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1000795","msg":"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1001261","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1001276","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1001594","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1001595","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1001646","msg":"类目属性id：[1200000145]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1001873","msg":"类目属性id：[1200000134]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"100203","msg":"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1002847","msg":"类目属性id：[1200000260,1200000101,1200000210]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1002848","msg":"类目属性id：[1200000260,1200000101,1200000210]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"100296","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1003535","msg":"类目属性id：[1200000210]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1004376","msg":"类目属性id：[1200000132,1200000140,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1005487","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1007284","msg":"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1007348","msg":"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1009054","msg":"类目属性id：[1200000088,1200000132,1200005293,1200000356]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1010210","msg":"类目属性id：[1200000132,1200000462,1200000101,1200000134,1200000210,1200000140,1200000141,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1010383","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1014424","msg":"类目属性id：[1200000088,1200000132,1200000134,1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1014425","msg":"类目属性id：[1200000088,1200000132,1200000134,1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1018365","msg":"类目属性id：[1200000088,1200000210,1200000132,1200000134]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1019054","msg":"类目属性id：[1200000088,1200000140,1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"102516","msg":"类目属性id：[1200000132,1200000140,1200000135]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"102628","msg":"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"102629","msg":"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"102630","msg":"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"102898","msg":"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"103863","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1039325","msg":"类目属性id：[1200000145]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1041822","msg":"类目属性id：[1200000088,1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1042236","msg":"类目属性id：[1200000132]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1046280","msg":"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1046496","msg":"类目属性id：[1200005293]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"105101","msg":"类目属性id：[1200000088,1200000145]必填，“common_attr_value”存在必填字段未填写，可通过接口category/attr/list（https://opendj.meituan.com/home/<USER>/386）查询每个商品类目下必填的类目属性字段","blockFlag":1,"code":1},{"app_spu_code":"1001646","msg":"l | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d08b830b59450fcb265603f1c6f66bc8.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d3a3062075bee5abdc1573cd9296f16a.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/f5122ecd83ae012567ef6a7adf10ca0d.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/8642db76f2921a6169fa4e0e9dcb22e3.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/c8835d443ad3659fe306fb787e6dda4d.gif, 图片不支持gif格式，请重新上传","blockFlag":2,"code":0},{"app_spu_code":"105101","msg":"l | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d08b830b59450fcb265603f1c6f66bc8.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/d3a3062075bee5abdc1573cd9296f16a.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/f5122ecd83ae012567ef6a7adf10ca0d.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/8642db76f2921a6169fa4e0e9dcb22e3.gif, 图片不支持gif格式，请重新上传 | 图片url：https://oss.upetmart.com/www/shop/store/repgoods/105101/c8835d443ad3659fe306fb787e6dda4d.gif, 图片不支持gif格式，请重新上传","blockFlag":2,"code":0}],"result_code":3}`
			mtResjson := `{"app_poi_code":"8534192","operate_type":1,"food_data":[{"app_food_code":"1018365","box_num":0,"box_price":0,"category_name":"休闲零食","is_sold_out":1,"min_order_count":1,"name":"希宝夹心酥三文鱼拼金枪鱼味成猫零食135g","description":"","unit":"","picture":"http://file.vetscloud.com/81a5368cf06df5bf0ae4b79d4dd9cf41.png","sequence":0,"price":0,"tag_id":200002359,"zh_name":"","product_name":"希宝夹心酥三文鱼拼金枪鱼味成猫零食135g","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"1018365001","spec":"1袋","upc":"6914973705692","price":"42.5","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.14","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/3026012b6d7ee779b22012bb1361b5ea.jpg,http://file.vetscloud.com/66c7ff61850f7c51d8d72fe5b5587996.jpg,http://file.vetscloud.com/da3042f6bb51cc1239785edaf23b2155.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":70,"category_code":"","sell_point":""},{"app_food_code":"102629","box_num":0,"box_price":0,"category_name":"进口猫粮","is_sold_out":1,"min_order_count":1,"name":"【猫粮】新西兰ZIWI巅峰牛肉配方风干猫粮400g","description":"","unit":"","picture":"http://file.vetscloud.com/395f1fc79579b6edc4331206c1e49ec5,http://file.vetscloud.com/4075c201a2fdb773db93842fff9aa779,http://file.vetscloud.com/9916d270b35d3a2854ef97183ef1848d,,","sequence":0,"price":0,"tag_id":200002357,"zh_name":"","product_name":"【猫粮】新西兰ZIWI巅峰牛肉配方风干猫粮400g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2568924,"value":"ZIWI Peak"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000004,"value":"进口"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300006496,"value":"成猫"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000874,"value":"全部猫种"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000222,"value":"牛肉味"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000144,"value":"新西兰"}]},{"attrId":1200000135,"attrName":"包装方式","valueList":[{"valueId":1300000231,"value":"袋装"}]},{"attrId":1200000136,"attrName":"配方","valueList":[{"valueId":1300017136,"value":"风干粮"}]},{"attrId":1200000356,"attrName":"净含量","valueList":[{"valueId":0,"value":"0.4kg"}]}],"skus":[{"sku_id":"105215","spec":"400g","upc":"9421016593309","price":"235","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.4","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/102629/cccfcf00db59d543b63e72b608e8badc.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102629/45611007d23435a2bf5a459db51fab2b.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102629/b2b6ec5f3b867fff55226276f3746d1d.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102629/e55ae90873844e12457d4d312fd1e6ab.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102629/eaf5ce2f7d5afde15deaf35462a832a6.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102629/59e1a6651808c62f26e6ec53eaba05ce.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102629/3b724979d55ed4ec649ef274dfe90dd9.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102629/a2b66679f3c196137e0c14db348064c1.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102629/e5898325b81f50890d8dd75d612c58fb.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102629/02e69ab63cae45d2dfa4c829f02d38a3.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":72,"category_code":"","sell_point":""},{"app_food_code":"102898","box_num":0,"box_price":0,"category_name":"国产狗粮","is_sold_out":1,"min_order_count":1,"name":"【狗粮】伯纳天纯草原红肉高能活力配方全期犬粮2kg","description":"","unit":"","picture":"https://oss.upetmart.com/www/shop/store/goods/1/2020/1_06347745931938500.jpg","sequence":0,"price":0,"tag_id":200002358,"zh_name":"","product_name":"【狗粮】伯纳天纯草原红肉高能活力配方全期犬粮2kg","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":1118066,"value":"伯纳天纯"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000003,"value":"国产"}]},{"attrId":1200000440,"attrName":"适用犬型","valueList":[{"valueId":1300002258,"value":"通用"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000220,"value":"混合口味"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000050,"value":"中国"}]},{"attrId":1200000356,"attrName":"净含量","valueList":[{"valueId":1300006465,"value":"1.1-2.9kg"}]}],"skus":[{"sku_id":"105606","spec":"2KG","upc":"6951521803257","price":"219","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"2","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/102898/b117706139f3abba762ccc759a5fbb3a.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102898/86db90d5e372031a5adfa217b7eff94d.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102898/20e0e2e11ae97d2b4bf2ba61d2def0ad.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102898/19f207ce61679d66e3d76e29b6a57e18.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102898/dff33e76ca89fa7cfa27615344f30f06.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102898/d2ff9237b31f0a59e1c4a5f1d182dafa.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102898/05fddad0f703f4ce363213dead5adef2.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102898/dd9e6366a622988aa5540d2806de92dd.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102898/b39af7ffe91528c863969552c4451162.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102898/8288003b2afde89c758e5426d228e291.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":82,"category_code":"","sell_point":""},{"app_food_code":"1003535","box_num":0,"box_price":0,"category_name":"皮肤护理","is_sold_out":1,"min_order_count":1,"name":"【抗菌制剂】宠儿香益通生溶菌宁溶菌酶喷剂犬猫通用25ml","description":"","unit":"","picture":"http://file.vetscloud.com/9737122717a81f8a5388375b51c2188b.jpg","sequence":0,"price":0,"tag_id":200005164,"zh_name":"","product_name":"【抗菌制剂】宠儿香益通生溶菌宁溶菌酶喷剂犬猫通用25ml","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000260,"attrName":"功能","valueList":[{"valueId":1300005369,"value":"杀菌"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000873,"value":"全部犬种"}]}],"skus":[{"sku_id":"1003535001","spec":"25ml/瓶","upc":"6909222683500","price":"60","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.02","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/6277edafe09d59f8033c426d7dba3ae5.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":57,"category_code":"","sell_point":""},{"app_food_code":"1000794","box_num":0,"box_price":0,"category_name":"进口猫粮","is_sold_out":1,"min_order_count":1,"name":"【进口猫粮】新西兰巅峰ZIWI鸡肉风干猫粮 400g","description":"","unit":"","picture":"https://file.vetscloud.com/24acc15ad33980b3e17d24e8c6707e56.jpg,https://file.vetscloud.com/4ca9438c9c3b424f087813e73475a39f.jpg,,,","sequence":0,"price":0,"tag_id":200002357,"zh_name":"","product_name":"【进口猫粮】新西兰巅峰ZIWI鸡肉风干猫粮 400g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2412564,"value":"Ziwi"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000004,"value":"进口"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300006496,"value":"成猫"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000223,"value":"鸡肉味"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000144,"value":"新西兰"}]},{"attrId":1200000356,"attrName":"净含量","valueList":[{"valueId":1300006468,"value":"0-1kg"}]}],"skus":[{"sku_id":"1000794001","spec":"400g","upc":"9421016594849","price":"235","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.4","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://img10.360buyimg.com/imgzone/jfs/t1/128691/5/6173/179235/5efff46aEd5a0d9c8/a7d26951882936ba.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/64990/6/5015/407638/5d319241E64147560/e2e8aebfab662b85.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/11272/39/1774/273018/5c0df928Efdf2db39/171594e08b66dfe6.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/27277/29/926/164598/5c0df928Ec0e2f5e1/536d94859aa2ae45.jpg,http://img30.360buyimg.com/popWaterMark/jfs/t1/21555/21/5069/271719/5c384570E9898f7f6/f90d5a73097e4540.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/21500/2/866/250779/5c0df929Ed5c1f625/c0be44e27caeb77e.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/17525/39/906/250635/5c0df929Ecc525200/164fca5d24e9dfba.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/12058/24/1060/364747/5c0df92aEb2a0c877/75345e1a24e79777.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/28881/23/890/164482/5c0df92aEaef53908/f77c88c660b9e0c6.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/25647/2/939/119583/5c0df92aEc68e2194/3387396a84522956.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/19494/34/884/47826/5c0df92bE3d434036/94347cc5952caec5.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/21518/3/898/160388/5c0df92bEa7a34508/7f82dcaf7b6e7d33.jpg,https://img10.360buyimg.com/imgzone/jfs/t1/40596/1/4209/564487/5cd15498E09e0e7d2/545304185adc4a10.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":72,"category_code":"","sell_point":""},{"app_food_code":"102630","box_num":0,"box_price":0,"category_name":"进口狗粮","is_sold_out":1,"min_order_count":1,"name":"【狗粮】新西兰ZIWI巅峰风干鹿肉犬粮1kg","description":"","unit":"","picture":"http://file.vetscloud.com/0e4c52304cc42c59d7dd5cc387c807c4.jpg,http://file.vetscloud.com/2e0bb9a5c6c8ec03ea9b9ad68611137b.jpg,http://file.vetscloud.com/5c3023b35dbf4ad3ef5d374d9eb5eb25.jpg,http://file.vetscloud.com/d30c76ef3a78a34b94a07c7fb187da4e.jpg,","sequence":0,"price":0,"tag_id":200002358,"zh_name":"","product_name":"【狗粮】新西兰ZIWI巅峰风干鹿肉犬粮1kg","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2303009,"value":"滋益巅峰"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000004,"value":"进口"}]},{"attrId":1200000440,"attrName":"适用犬型","valueList":[{"valueId":1300002258,"value":"通用"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000221,"value":"鹿肉味"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000144,"value":"新西兰"}]},{"attrId":1200000356,"attrName":"净含量","valueList":[{"valueId":1300006468,"value":"0-1kg"}]}],"skus":[{"sku_id":"105216","spec":"1袋","upc":"9421016590612","price":"698","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"1","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/102630/9370298e27a320ff3cf88ebc9180c20d.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102630/23d4fa4853027f283258a4a14ef49692.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102630/8adbd098a9367c7bad9704cfc244391e.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102630/ede6b29933f23d848bae9054df06a7e6.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102630/45a18dcafa618afaee2eb223688171be.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102630/67ba355fa98858ece80a842ef2513cb3.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102630/20368181e739724dd8470e23279ef4e4.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102630/5b76d33b28115c7c2a3623e108bb36d2.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102630/43570db9be83d65d02b7fea0623a8a92.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102630/ff2dcb4865f5db756c6178a94513d83b.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":81,"category_code":"","sell_point":""},{"app_food_code":"1000728","box_num":0,"box_price":0,"category_name":"国产狗粮","is_sold_out":1,"min_order_count":1,"name":"宠儿香母乳化配方奶粉（6周龄以上）20g*12袋","description":"","unit":"","picture":"http://file.vetscloud.com/8585841e6db26fbc0130522116830b1d,http://file.vetscloud.com/5b8c42b6a8013bdf5c10a66bec442d58,http://file.vetscloud.com/b238b6d11a057660a1ffe6dbf8841a33,http://file.vetscloud.com/2724879007fc611216283de1ff416faf,","sequence":0,"price":0,"tag_id":200002358,"zh_name":"","product_name":"宠儿香母乳化配方奶粉（6周龄以上）20g*12袋","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":1515178,"value":"宠儿香"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000003,"value":"国产"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":520113,"value":"白云区"}]},{"attrId":1200000136,"attrName":"配方","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000356,"attrName":"净含量","valueList":[{"valueId":1300006468,"value":"0-1kg"}]}],"skus":[{"sku_id":"1000728001","spec":"20g*12袋","upc":"6909222733212","price":"98","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.24","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":82,"category_code":"","sell_point":""},{"app_food_code":"102628","box_num":0,"box_price":0,"category_name":"进口猫粮","is_sold_out":1,"min_order_count":1,"name":"【猫粮】新西兰ZIWI巅峰马鲛鱼羊肉配方风干猫粮400g","description":"","unit":"","picture":"http://file.vetscloud.com/b331de0c2051eb2e249841c32ebb205e,http://file.vetscloud.com/f3cddcbd8877d9f45a0d507b615a2dbd,http://file.vetscloud.com/26ebaf2b6be276bdc58b3b32cf21a8e5,http://file.vetscloud.com/2a63b60dec42c543cc9014ba340065b2,","sequence":0,"price":0,"tag_id":200002357,"zh_name":"","product_name":"【猫粮】新西兰ZIWI巅峰马鲛鱼羊肉配方风干猫粮400g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2568924,"value":"ZIWI Peak"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000004,"value":"进口"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000874,"value":"全部猫种"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000144,"value":"新西兰"}]},{"attrId":1200000135,"attrName":"包装方式","valueList":[{"valueId":1300000231,"value":"袋装"}]},{"attrId":1200000136,"attrName":"配方","valueList":[{"valueId":1300017136,"value":"风干粮"}]},{"attrId":1200000356,"attrName":"净含量","valueList":[{"valueId":1300006468,"value":"0-1kg"}]}],"skus":[{"sku_id":"105214","spec":"400g","upc":"9421016594177","price":"248","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.4","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/102628/117a8b811c82eb04f87c588f5f94460d.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102628/d48e00087e680d975139b7cfb0836589.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102628/f90e0547bb4803610dab321647da118e.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102628/eae8a492b0db859b1d955de50a8216c1.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102628/a63d5ad7c7f4d16db2576a9f56cf7ff8.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102628/d755dc1476145e9ed70055474d1d9e03.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102628/239bc68e5ba91c9b3b150414ccbcaaa0.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102628/de12509dd8cb3ad2821917aa331e9f0d.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102628/3d427d84788511e197dda3cf9c8aef96.jpg,https://oss.upetmart.com/www/shop/store/repgoods/102628/1def9b0f718dbc41b6b66b17236ebbf2.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":72,"category_code":"","sell_point":""},{"app_food_code":"1046496","box_num":0,"box_price":0,"category_name":"进口猫粮","is_sold_out":1,"min_order_count":1,"name":"ACANA爱肯拿第一场盛宴幼猫粮1.8kg","description":"","unit":"","picture":"https://file.vetscloud.com/2b28ac66bfe96692023d9701721a7121.jpg,https://file.vetscloud.com/4c96862bca44bb8463b615f8f28f6eb6.jpg,https://file.vetscloud.com/ad84f28b779d9db805f2765f6326931c.jpg,https://file.vetscloud.com/57b5c8ecd98751bb7da25a5685d656a0.jpg,https://file.vetscloud.com/75c84c09d8be230c3cd0288540087f9e.jpg","sequence":0,"price":0,"tag_id":200002357,"zh_name":"","product_name":"ACANA爱肯拿第一场盛宴幼猫粮1.8kg","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2303897,"value":"爱肯拿"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000004,"value":"进口"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300006495,"value":"幼猫"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000874,"value":"全部猫种"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000036,"value":"加拿大"}]},{"attrId":1200000135,"attrName":"包装方式","valueList":[{"valueId":1300000231,"value":"袋装"}]},{"attrId":1200000136,"attrName":"配方","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000356,"attrName":"净含量","valueList":[{"valueId":1300017299,"value":"1-2.9kg"}]}],"skus":[{"sku_id":"1046496001","spec":"1.8kg/袋","upc":"64992714956","price":"248","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"1.8","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://file.vetscloud.com/a0be5b3cb15b78922bc40829fafa5ff5.jpg,https://file.vetscloud.com/7c6cf9bbcbdad8f142d1c61d6272fd17.jpg,https://file.vetscloud.com/bd28cc0cf05e45efc87fde8a2566fcb0.jpg,https://file.vetscloud.com/42a2e67c86fe31de5e175ab910028526.jpg,https://file.vetscloud.com/2c5b177eeecdd65fc4807527b286733a.jpg,https://file.vetscloud.com/bbf053dc9ee63e4801976d0717f07f08.jpg,https://file.vetscloud.com/873e2195d48f06de6a58e8f510b7684c.jpg,https://file.vetscloud.com/b3d6c2c6e7a12b70ac6c6c47e50dcdce.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":72,"category_code":"","sell_point":""},{"app_food_code":"102516","box_num":0,"box_price":0,"category_name":"犬类必备","is_sold_out":1,"min_order_count":1,"name":"【狗用】宠儿香犬尿石净膏80g","description":"","unit":"","picture":"https://oss.upetmart.com/www/shop/store/goods/1/2020/1_06339902461775904.png","sequence":0,"price":0,"tag_id":200002364,"zh_name":"","product_name":"【狗用】宠儿香犬尿石净膏80g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000462,"attrName":"功能功效","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000873,"value":"全部犬种"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]},{"attrId":1200000463,"attrName":"形态","valueList":[{"valueId":1300000865,"value":"膏状"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300002093,"value":"其他宠物营养用品"}]},{"attrId":1200000141,"attrName":"用法","valueList":[{"valueId":1300000252,"value":"内用"}]}],"skus":[{"sku_id":"105078","spec":"80g","upc":"6909222635394","price":"96","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.08","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":87,"category_code":"","sell_point":""},{"app_food_code":"100296","box_num":0,"box_price":0,"category_name":"狗罐湿粮","is_sold_out":1,"min_order_count":1,"name":"【狗零食】宝路妙鲜包成犬（鸡肝高汤）100g","description":"","unit":"","picture":"http://file.vetscloud.com/f4aae68608d888af7724b49f6743e218,http://file.vetscloud.com/06e45cfaa826c1caba417d9e0fb806fb,,,","sequence":0,"price":0,"tag_id":200002360,"zh_name":"","product_name":"【狗零食】宝路妙鲜包成犬（鸡肝高汤）100g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":1002233,"value":"宝路"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300002732,"value":"狗罐头"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000050,"value":"中国"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]}],"skus":[{"sku_id":"101514","spec":"100g","upc":"6914973700109","price":"5000","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.1","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/100296/7c9a62f60476fbf4476a9f5154c311a8.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100296/fa44096904924681aea5c13f54f823ff.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100296/5c39d01bc125dd55f3f968e835352613.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100296/af25b897612b4bd7042824af86c95e44.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100296/21f46247f866eb1e35b9be2682ac12f4.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100296/c4aa11fdc2e76bdeb3703b368fa64a1f.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":76,"category_code":"","sell_point":"有助毛发亮泽 帮助骨"},{"app_food_code":"1001646","box_num":0,"box_price":0,"category_name":"猫砂","is_sold_out":1,"min_order_count":1,"name":"【猫砂】天净经典豆腐绿茶猫砂6L","description":"","unit":"","picture":"http://file.vetscloud.com/aa79019bc9f3b31ef1bc2a9e0242632a,https://file.vetscloud.com/cb50e7dd51ff0882eed0cb687fb71fcc.png,https://file.vetscloud.com/6aa5ca3016b5234b38644074976faea4.png,https://file.vetscloud.com/38f53b006241d1fe176275a1e796c946.png,https://file.vetscloud.com/d3358df33f0f8d899165f07b0dc45dd8.png","sequence":0,"price":0,"tag_id":200002365,"zh_name":"","product_name":"【猫砂】天净经典豆腐绿茶猫砂6L","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2304290,"value":"天净"}]}],"skus":[{"sku_id":"109378","spec":"6L/6L","upc":"6944386690896","price":"28","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"2.5","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/105101/127528cc0d475229671e545ae2c6cf77.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105101/d08b830b59450fcb265603f1c6f66bc8.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/d3a3062075bee5abdc1573cd9296f16a.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/f5122ecd83ae012567ef6a7adf10ca0d.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/8642db76f2921a6169fa4e0e9dcb22e3.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/c8835d443ad3659fe306fb787e6dda4d.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/6d927411fc128ce611c08ccc1a9a24c2.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":65,"category_code":"","sell_point":""},{"app_food_code":"1046280","box_num":0,"box_price":0,"category_name":"进口猫粮","is_sold_out":1,"min_order_count":1,"name":"ACANA爱肯拿抓多多猫粮1.8kg","description":"","unit":"","picture":"https://file.vetscloud.com/4f4dd0102a309918d3d19420cf5f3af9.jpg,https://file.vetscloud.com/7ea4694abf46b48ca3ef8e1a5ef61f56.jpg,https://file.vetscloud.com/3295197a4a7c765166244d2fb26867fc.jpg,https://file.vetscloud.com/eb854223470f82dcd8c8baa7cb3b05d5.jpg,https://file.vetscloud.com/f0b5282f2183a339804b532beaeb9805.jpg","sequence":0,"price":0,"tag_id":200002357,"zh_name":"","product_name":"ACANA爱肯拿抓多多猫粮1.8kg","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2303897,"value":"爱肯拿"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000004,"value":"进口"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300006496,"value":"成猫"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000874,"value":"全部猫种"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000217,"value":"海鲜味"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000036,"value":"加拿大"}]},{"attrId":1200000135,"attrName":"包装方式","valueList":[{"valueId":1300000231,"value":"袋装"}]},{"attrId":1200000136,"attrName":"配方","valueList":[{"valueId":1300017135,"value":"通用粮"}]},{"attrId":1200000356,"attrName":"净含量","valueList":[{"valueId":0,"value":"1.8kg"}]}],"skus":[{"sku_id":"1046280001","spec":"袋","upc":"64991714995","price":"248","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"1.8","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://file.vetscloud.com/f0aba5341d1d4e5b1d5c6b3315dcac86.jpg,https://file.vetscloud.com/70645579052960205eeadaee5aade2a3.jpg,https://file.vetscloud.com/68b2737d28eda31f2b916eb3297c6239.jpg,https://file.vetscloud.com/a19240fdaf128d13d4a1730a746bcc54.jpg,https://file.vetscloud.com/4ee90fd4847ab8ff86baffecaf8866c0.jpg,https://file.vetscloud.com/658ca7cc2d141f94fda0d8c5a7104a6e.jpg,https://file.vetscloud.com/709eff82fe69da2d4f1a27408b78e6ad.jpg,https://file.vetscloud.com/80efa1a8bafc179a962953ecce11928a.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":72,"category_code":"","sell_point":""},{"app_food_code":"1000750","box_num":0,"box_price":0,"category_name":"磨牙洁齿","is_sold_out":1,"min_order_count":1,"name":"宝路幼犬钙奶棒60g","description":"","unit":"","picture":"http://file.vetscloud.com/2f443a7032b01ef887bdf8cb4ac9e19b.jpg,https://file.vetscloud.com/aa0cce9a5b0ace4c437fbaaffe7d003e.png,https://file.vetscloud.com/42ba39b90e67272ae607aa36e0587471.png,https://file.vetscloud.com/91905145742ad9ed1046a3de0c201390.png,https://file.vetscloud.com/b06d29009547a9686464e42d405065c3.png","sequence":0,"price":0,"tag_id":200002360,"zh_name":"","product_name":"宝路幼犬钙奶棒60g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":1002233,"value":"宝路"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300000248,"value":"磨牙棒"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000050,"value":"中国"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]}],"skus":[{"sku_id":"1000750001","spec":"60g","upc":"6914973107687","price":"10.4","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.06","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":78,"category_code":"","sell_point":""},{"app_food_code":"1019054","box_num":0,"box_price":0,"category_name":"国产猫粮","is_sold_out":1,"min_order_count":1,"name":"【猫粮】皇家成猫肝脏全价处方粮 HF26 /1.5kg","description":"","unit":"","picture":"http://file.vetscloud.com/d1a5decfa73b834e02d27a09ead7ec78.jpg,http://file.vetscloud.com/02be5f16760d68bfe4d1c5d2e1b9140e.jpg,http://file.vetscloud.com/cfa514dfb5dce44207d75750572223a8.jpg,http://file.vetscloud.com/c56e45c3c1f63bebc79cb7e5d1a70f91.jpg,http://file.vetscloud.com/a2fffd6c21248dd3ed6c9dd26d389263.jpg","sequence":0,"price":0,"tag_id":200005168,"zh_name":"","product_name":"【猫粮】皇家成猫肝脏全价处方粮 HF26 /1.5kg","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300006496,"value":"成猫"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000874,"value":"全部猫种"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":310115,"value":"浦东新区"}]}],"skus":[{"sku_id":"1019054001","spec":"1.5kg","upc":"6949047582602","price":"5000","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"1.5","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/5b021a94f48411dc64568980bc0573b2.jpg,http://file.vetscloud.com/9631cc1155df92c5005ced813597e4c9.jpg,http://file.vetscloud.com/7e46c6d533347ca23c1f235fe6f95f06.jpg,http://file.vetscloud.com/1c8b7995a148d7583477d9ed67234dff.jpg,http://file.vetscloud.com/8ab87e759e388e2aface5d80b90b4484.jpg,http://file.vetscloud.com/afb6a09798bdd424012d905fd36ba0f9.jpg,http://file.vetscloud.com/b6a73c6eaecd1951e24e4c3517221570.jpg,http://file.vetscloud.com/2cbb6cd9e65021e90f8f967ecd7291f6.jpg,http://file.vetscloud.com/e81dcf66d1a945e2abed8e8250d6366b.jpg,http://file.vetscloud.com/a9ed007bd0a69ba8ca8c377f76322f61.jpg,http://file.vetscloud.com/f6bc897661467421949428a80d02b128.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":73,"category_code":"","sell_point":""},{"app_food_code":"1002848","box_num":0,"box_price":0,"category_name":"清洁洗护","is_sold_out":1,"min_order_count":1,"name":"bikkar-清洁舒爽香波5L","description":"","unit":"","picture":"http://file.vetscloud.com/14ef9e03208321d9e0275241c5ee6bf8.jpg,http://file.vetscloud.com/93830b44f1ce11c9de114****4003cdc.jpg,http://file.vetscloud.com/34065ab7f93e69a606f9b87d5102b5c2.jpg,,","sequence":0,"price":0,"tag_id":200005164,"zh_name":"","product_name":"bikkar-清洁舒爽香波5L","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"1002848001","spec":"5L","upc":"6972312912156","price":"324","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"5","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/92511bea0a2d457ee12818b891b55b1e.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":95,"category_code":"","sell_point":""},{"app_food_code":"1010383","box_num":0,"box_price":0,"category_name":"肉质零食","is_sold_out":1,"min_order_count":1,"name":"【狗零食】麦富迪电影零食鸡肉卷甘薯100g","description":"","unit":"","picture":"http://file.vetscloud.com/710bdd522ec5441a9d3c9ff1443a2824,http://file.vetscloud.com/d8e020df18c650ed66366576a49d11c2,,,","sequence":0,"price":0,"tag_id":200002360,"zh_name":"","product_name":"【狗零食】麦富迪电影零食鸡肉卷甘薯100g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":1510476,"value":"麦富迪"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000050,"value":"中国"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]}],"skus":[{"sku_id":"1010383001","spec":"100g","upc":"6958862107440","price":"15","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.1","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/78d77f4db83d006819f947b54f3b8f8b,http://file.vetscloud.com/f91a499738ba5e8f10b818288a456339,http://file.vetscloud.com/81f511213b58ea6364d284579f3e99e9,http://file.vetscloud.com/4a9474f9b120f91ff0a12a5cb6e9e1c7,http://file.vetscloud.com/4771566c38ebe053e6e78420c245dc74","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":77,"category_code":"","sell_point":""},{"app_food_code":"103863","box_num":0,"box_price":0,"category_name":"口鼻护理","is_sold_out":1,"min_order_count":1,"name":"【犬用洁齿】多美洁-天然清新口气洁齿水118ml","description":"","unit":"","picture":"http://file.vetscloud.com/1bbb7129c6976647e5b43838f471272f,http://file.vetscloud.com/4d946f8c335bd7d9957d403948425be4,http://file.vetscloud.com/056ade5c19f9f2ab2f1468cb7b8968c4,http://file.vetscloud.com/e948c8626e4886843b2a05aaa7ce3baf,https://file.vetscloud.com/740f85ee9d6b01ccc7c8c515ab9fed1f.jpg","sequence":0,"price":0,"tag_id":400000733,"zh_name":"","product_name":"【犬用洁齿】多美洁-天然清新口气洁齿水118ml","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2304025,"value":"多美洁"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300016654,"value":"口腔清洁"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000873,"value":"全部犬种"}]},{"attrId":1200000463,"attrName":"形态","valueList":[{"valueId":1300000250,"value":"液体"}]},{"attrId":1200000141,"attrName":"用法","valueList":[{"valueId":1300000252,"value":"内用"}]},{"attrId":1200000005,"attrName":"注意事项","valueList":[{"valueId":0,"value":"."}]}],"skus":[{"sku_id":"106827","spec":"118ml/瓶","upc":"645095003064","price":"43","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.12","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/103863/2f37021f10bac9423b004823889165ec.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/a8d274508d9859c86c7e06e14835d47d.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/fadea9e77a73bf800b8d4413494a1fb1.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/b3b1b9f4bcd4602088dde36788ef6fa0.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/af33d8ad1e9a05444a4f93ce85ae91f1.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/12b3ce6292d59b6ea208f37806a1774c.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/89d4764324994d2ff20f04c66641bfd6.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/4536e9b34eb0f50cf8f6bcbb7d78432d.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/3f2bba409762516b7c754482974cbb62.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/1b252c24039874910c0d953c6a4e352b.jpg,https://oss.upetmart.com/www/shop/store/repgoods/103863/8360e71bf7b5f8eae1e89cd0d34f08d4.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":60,"category_code":"","sell_point":""},{"app_food_code":"1014424","box_num":0,"box_price":0,"category_name":"国产狗粮","is_sold_out":1,"min_order_count":1,"name":"优卡小成犬粮1kg","description":"","unit":"","picture":"https://file.vetscloud.com/no_product_img.png","sequence":0,"price":0,"tag_id":200002358,"zh_name":"","product_name":"优卡小成犬粮1kg","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"1014424001","spec":"1kg","upc":"6914973702189","price":"75","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"1","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":82,"category_code":"","sell_point":""},{"app_food_code":"1000795","box_num":0,"box_price":0,"category_name":"进口猫粮","is_sold_out":1,"min_order_count":1,"name":"【猫粮】新西兰ZIWI巅峰鸡肉配方风干猫粮1kg","description":"","unit":"","picture":"http://file.vetscloud.com/6442c71e3c722a276af5cbdc64c4d0b2,http://file.vetscloud.com/9d09ba9b6ee3a348ce15839241ba1f0d,http://file.vetscloud.com/f5039e62af249806ece70e29115f5ac5,,","sequence":0,"price":0,"tag_id":200002357,"zh_name":"","product_name":"【猫粮】新西兰ZIWI巅峰鸡肉配方风干猫粮1kg","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2568924,"value":"ZIWI Peak"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000004,"value":"进口"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300006496,"value":"成猫"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000874,"value":"全部猫种"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000223,"value":"鸡肉味"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000144,"value":"新西兰"}]},{"attrId":1200000135,"attrName":"包装方式","valueList":[{"valueId":1300000231,"value":"袋装"}]},{"attrId":1200000136,"attrName":"配方","valueList":[{"valueId":1300017136,"value":"风干粮"}]},{"attrId":1200000356,"attrName":"净含量","valueList":[{"valueId":1300006468,"value":"0-1kg"}]}],"skus":[{"sku_id":"1000795001","spec":"袋","upc":"9421016595815","price":"469","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"1","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/6f8b5f4f3579b4563783983165d2396c,http://file.vetscloud.com/5e43d61061039562a94000d682b4d181,http://file.vetscloud.com/40acfe7da731b96c6be203c0b373ef3d,http://file.vetscloud.com/b5865d2b1348610e6d6e60d8f032e889","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":72,"category_code":"","sell_point":""},{"app_food_code":"1039325","box_num":0,"box_price":0,"category_name":"猫砂","is_sold_out":1,"min_order_count":1,"name":"天净阿闻联名款原味豆腐猫砂6L","description":"","unit":"","picture":"https://file.vetscloud.com/fe46c76e62fba3c88e9767b006e8bd30.jpg","sequence":0,"price":0,"tag_id":200002365,"zh_name":"","product_name":"天净阿闻联名款原味豆腐猫砂6L","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2304290,"value":"天净"}]}],"skus":[{"sku_id":"1039325001","spec":"6L","upc":"6944386678825","price":"5000","stock":"0","unit":"袋","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"3","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":65,"category_code":"","sell_point":""},{"app_food_code":"1004376","box_num":0,"box_price":0,"category_name":"犬类必备","is_sold_out":1,"min_order_count":1,"name":"N红狗化毛膏100g","description":"","unit":"","picture":"https://file.vetscloud.com/no_product_img.png","sequence":0,"price":0,"tag_id":200002364,"zh_name":"","product_name":"N红狗化毛膏100g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000462,"attrName":"功能功效","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000873,"value":"全部犬种"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]},{"attrId":1200000463,"attrName":"形态","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300002093,"value":"其他宠物营养用品"}]},{"attrId":1200000141,"attrName":"用法","valueList":[{"valueId":1300000252,"value":"内用"}]}],"skus":[{"sku_id":"1004376001","spec":"1支","upc":"6970030370029","price":"5000","stock":"0","unit":"包","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.12","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":87,"category_code":"","sell_point":""},{"app_food_code":"1002847","box_num":0,"box_price":0,"category_name":"清洁洗护","is_sold_out":1,"min_order_count":1,"name":"bikkar-清洁留香香波5L","description":"","unit":"","picture":"http://file.vetscloud.com/0f76881dae1332387a485ce887cea0d1.jpg,http://file.vetscloud.com/dd5c00653be499c2d668fa9fd792440c.jpg,http://file.vetscloud.com/cdd0b6f5d17a36ec507c61f99790a7f3.jpg,,","sequence":0,"price":0,"tag_id":200005164,"zh_name":"","product_name":"bikkar-清洁留香香波5L","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"1002847001","spec":"5L","upc":"6972312912118","price":"312","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"5","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/c97d58e98e5ebd8f9d70470df05a7363.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":95,"category_code":"","sell_point":""},{"app_food_code":"1010210","box_num":0,"box_price":0,"category_name":"口鼻护理","is_sold_out":1,"min_order_count":1,"name":"【洁齿棒】路斯美牙棒牛肉犬用270g","description":"","unit":"","picture":"http://file.vetscloud.com/ed50e5bb9e3ee779f9db6ca68260c72e,http://file.vetscloud.com/4c8688d22c542e7f5f4b5ff5e39dfce9,http://file.vetscloud.com/081c6a999a995156a5affb86b7859d29,,","sequence":0,"price":0,"tag_id":200002364,"zh_name":"","product_name":"【洁齿棒】路斯美牙棒牛肉犬用270g","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"1010210001","spec":"1袋","upc":"6921499711687","price":"30","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.27","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/f6c94517dbfb9a175a7862ca91e79876,http://file.vetscloud.com/f689f4f70a8424f738491aa444fea888,http://file.vetscloud.com/ade57b335e4f1ae38d0a9ff0b40fc1bb","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":60,"category_code":"","sell_point":""},{"app_food_code":"1007348","box_num":0,"box_price":0,"category_name":"综合营养","is_sold_out":1,"min_order_count":1,"name":"发育宝NC4猫用基础营养膏125g","description":"","unit":"","picture":"http://file.vetscloud.com/e629a8834114562086e46c1fd66be670.jpg,http://file.vetscloud.com/5a4890a3ab95063d93ae606d30f9dbff.jpg,http://file.vetscloud.com/ed01138bc47e81bc53869d24123d260e.jpg,http://file.vetscloud.com/9807257a8054a7adf714ae59c93c5530.jpg,http://file.vetscloud.com/5020b553c6a009c6a5a503a4a7d77da8.jpg","sequence":0,"price":0,"tag_id":200002364,"zh_name":"","product_name":"发育宝NC4猫用基础营养膏125g","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"1007348001","spec":"125g(支)/盒","upc":"6970782131978","price":"78","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.125","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/36e56ac40afaf021d0d08bee3f8cc402.jpg,http://file.vetscloud.com/50075ccc8ab5beaf09d17fa175bbdde1.jpg,http://file.vetscloud.com/75eccffa2858791f7331b00d49aa36e3.jpg,http://file.vetscloud.com/02453e698ce9f3aab889d30a4a9ba22a.jpg,http://file.vetscloud.com/0321ae0a9c757d1046404771325200f0.jpg,http://file.vetscloud.com/c7beaff713501a14f43b7cc7b143512e.jpg,http://file.vetscloud.com/6f2a25a1209385151cedb52484df0cf9.jpg,http://file.vetscloud.com/4ffe26c8c3199f4d34ddea47a992a69b.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":88,"category_code":"","sell_point":""},{"app_food_code":"100203","box_num":0,"box_price":0,"category_name":"耳眼护理","is_sold_out":1,"min_order_count":1,"name":"康牧耳康复方达克罗宁喷剂25ml","description":"","unit":"","picture":"https://oss.upetmart.com/www/shop/store/goods/1/2018/1_05938851456883294.jpg","sequence":0,"price":0,"tag_id":200002364,"zh_name":"","product_name":"康牧耳康复方达克罗宁喷剂25ml","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"100570","spec":"25ml/瓶","upc":"6970593650019","price":"55","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.025","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/100203/1f35dad009d0294207bd00b576e05a41.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/2cfca3674107243616895f5f0441d86c.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/3b16bd5ef6a109e6a4fc0927a11d0dc5.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/9997dc4b244eb463b61eba97c5c25514.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/c15ef09f5649bbcafcca9a937eb87b57.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/4dc1e103cced12cccaad8542566f4349.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/7134b206e1efcecfff9a12ead05186d9.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/9827a677cb80067f6e4140f09975a8ef.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/12e010860ea9d9c54031022e56bf7628.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/9fe45adf4b3af3ac23917cb5d4f2da51.jpg,https://oss.upetmart.com/www/shop/store/repgoods/100203/5190ad4fbe6ced87c134da23fe4dcbe8.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":58,"category_code":"","sell_point":""},{"app_food_code":"1041822","box_num":0,"box_price":0,"category_name":"狗罐湿粮","is_sold_out":1,"min_order_count":1,"name":"Wanpy顽皮鲜盒全价全期犬粮（鸡肉＋牛肉＋蔬菜配方）120","description":"","unit":"","picture":"https://file.vetscloud.com/6a894e9a5476eb24614d258bc3420b5d.jpg","sequence":0,"price":0,"tag_id":200002360,"zh_name":"","product_name":"Wanpy顽皮鲜盒全价全期犬粮（鸡肉＋牛肉＋蔬菜配方）120","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000050,"value":"中国"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]}],"skus":[{"sku_id":"1041822001","spec":"120g/罐","upc":"6927749801085","price":"18","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.12","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/5a44b91e2203d093be5013212c8f63cb,http://file.vetscloud.com/e450902bf798d1e8668faba22c3e00b3,http://file.vetscloud.com/992c98f8d370f644469cbbdd1fcac8cb,http://file.vetscloud.com/bddddf506dbd3bfebb29db3cf013d95e,http://file.vetscloud.com/bade1d29abd8900d08ab5ccd2fdbfdeb,http://file.vetscloud.com/d662e617d511295111cbee3e7ca2e2c6,http://file.vetscloud.com/6e929bc6854d8025d4017783b10197c5,http://file.vetscloud.com/044ca4b51ef1cce5c8d2cde722d334e2,http://file.vetscloud.com/8ab1d14955f8a229e6442ed6fd586dcd,http://file.vetscloud.com/10bbe85f948545355e2e10df2f638fe4","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":76,"category_code":"","sell_point":""},{"app_food_code":"1042236","box_num":0,"box_price":0,"category_name":"猫罐头","is_sold_out":1,"min_order_count":1,"name":"LEONARDO里奥纳多（特别版）猫罐家禽肉200g","description":"","unit":"","picture":"https://file.vetscloud.com/772e47727786aaaf49b7371bd3711136.png","sequence":0,"price":0,"tag_id":200002359,"zh_name":"","product_name":"LEONARDO里奥纳多（特别版）猫罐家禽肉200g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2489455,"value":"里奥纳多"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300002729,"value":"猫罐头"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000012,"value":"澳大利亚"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]}],"skus":[{"sku_id":"1042236001","spec":"200g","upc":"4002633756923","price":"5000","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.2","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://file.vetscloud.com/8a2b133185c1bfda4a73f57d84cdfe68.png","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":67,"category_code":"","sell_point":""},{"app_food_code":"1014425","box_num":0,"box_price":0,"category_name":"国产狗粮","is_sold_out":1,"min_order_count":1,"name":"优卡小幼犬粮1kg","description":"","unit":"","picture":"https://file.vetscloud.com/no_product_img.png","sequence":0,"price":0,"tag_id":200002358,"zh_name":"","product_name":"优卡小幼犬粮1kg","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"1014425001","spec":"1kg","upc":"6914973702172","price":"80","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"1","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":82,"category_code":"","sell_point":""},{"app_food_code":"1009054","box_num":0,"box_price":0,"category_name":"进口猫粮","is_sold_out":1,"min_order_count":1,"name":"加拿大Orijen渴望猫粮鸡肉1kg","description":"","unit":"","picture":"http://file.vetscloud.com/1c7505ee1632d1077b57137b69c31bd0,http://file.vetscloud.com/3d033cbe98a23b9feb83af9a6928669a,http://file.vetscloud.com/fb10b4fd5ca54f8b9381ac20ef650ebf,http://file.vetscloud.com/31a131800b0276a3a52053d152f829ef,http://file.vetscloud.com/6dd9d1884b12d06a037366bfc3b7972c","sequence":0,"price":0,"tag_id":200002357,"zh_name":"","product_name":"加拿大Orijen渴望猫粮鸡肉1kg","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000223,"value":"鸡肉味"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000036,"value":"加拿大"}]}],"skus":[{"sku_id":"1009054001","spec":"1袋","upc":"064992681104","price":"5000","stock":"0","unit":"袋","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"1","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/6de9167abaa291895f4d5fecf6f638a8,http://file.vetscloud.com/a661fafe6f4786757f35d13efe1d43c0,http://file.vetscloud.com/56c87ebd3bebdb77c9aa8e6ed446457b,http://file.vetscloud.com/f33aae057bdaf5005b32e2cbf9c82f72,http://file.vetscloud.com/5ba618c3dda8b2decb412264cbf7ea96,http://file.vetscloud.com/c084305fb1ecadc30c959dd26151c9fb,http://file.vetscloud.com/ff16b64ae7104c17591a801c3cd633e6,http://file.vetscloud.com/e9bd76b648c39f0de28a65ff6702a82c","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":72,"category_code":"","sell_point":""},{"app_food_code":"105101","box_num":0,"box_price":0,"category_name":"猫砂","is_sold_out":1,"min_order_count":1,"name":"【猫砂】天净经典豆腐原味猫砂2.5kg","description":"","unit":"","picture":"http://file.vetscloud.com/046de56d223316c7534ba56d67bb431e.png,http://file.vetscloud.com/245d14cdf02ed0bb460c5ee1be6e65ed,http://file.vetscloud.com/7223d02dc18ac9d55730816bd830c273,,","sequence":0,"price":0,"tag_id":200002365,"zh_name":"","product_name":"【猫砂】天净经典豆腐原味猫砂2.5kg","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"109202","spec":"2.5kg/2.5kg","upc":"6944386690964","price":"28","stock":"0","unit":"包","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"2.5","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/105101/127528cc0d475229671e545ae2c6cf77.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105101/d08b830b59450fcb265603f1c6f66bc8.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/d3a3062075bee5abdc1573cd9296f16a.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/f5122ecd83ae012567ef6a7adf10ca0d.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/8642db76f2921a6169fa4e0e9dcb22e3.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/c8835d443ad3659fe306fb787e6dda4d.gif,https://oss.upetmart.com/www/shop/store/repgoods/105101/6d927411fc128ce611c08ccc1a9a24c2.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":65,"category_code":"","sell_point":""},{"app_food_code":"1001276","box_num":0,"box_price":0,"category_name":"肉质零食","is_sold_out":1,"min_order_count":1,"name":"【狗零食】朗诺犬零食纯肉系列 主粮伴侣三文鱼碎罐装60g","description":"","unit":"","picture":"http://file.vetscloud.com/d6558d68bf755b1a1dfa8333d38d4b50,http://file.vetscloud.com/619a2c85fc51c201c30d1f90ea0f5895,,,","sequence":0,"price":0,"tag_id":200002360,"zh_name":"","product_name":"【狗零食】朗诺犬零食纯肉系列 主粮伴侣三文鱼碎罐装60g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2303005,"value":"朗诺"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300000249,"value":"其他"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000050,"value":"中国"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300006486,"value":"成犬"}]}],"skus":[{"sku_id":"1001276001","spec":"60g","upc":"6930693203591","price":"39","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.1","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/867fcedc29b63bc9db7ccab044d394e7,http://file.vetscloud.com/93da3ae7f276f4177647bc201e8855e5,http://file.vetscloud.com/4ae7525b55afa1a7f1716e19fc325917,http://file.vetscloud.com/df2bd46b34a900a29e105ceaccc42508,http://file.vetscloud.com/9753561f81597e457bd91bcf050a31c9,http://file.vetscloud.com/c564f856a4425ebe99a5ac3c5ace66f9,http://file.vetscloud.com/7360988b4187e3a400e5c46fc7abe6e8,http://file.vetscloud.com/ffc767cccfac96cf4403797322ca19bc,http://file.vetscloud.com/674a6c893322938a8d429076883b56b7,http://file.vetscloud.com/421651990a8da1d838079c8063819007","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":77,"category_code":"","sell_point":""},{"app_food_code":"1001594","box_num":0,"box_price":0,"category_name":"休闲零食","is_sold_out":1,"min_order_count":1,"name":"【猫零食】希宝舔食软包罐海洋盛宴及三文鱼猫条12g*4条","description":"","unit":"","picture":"https://file.vetscloud.com/5e8397438fddc35e757493d5a28699e7.jpg","sequence":0,"price":0,"tag_id":200002359,"zh_name":"","product_name":"【猫零食】希宝舔食软包罐海洋盛宴及三文鱼猫条12g*4条","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":1017754,"value":"希宝"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300015617,"value":"猫条"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300000874,"value":"全部猫种"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000197,"value":"泰国"}]},{"attrId":1200000133,"attrName":"口味","valueList":[{"valueId":1300000220,"value":"混合口味"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300006496,"value":"成猫"}]},{"attrId":1200000343,"attrName":"功效","valueList":[{"valueId":1300014295,"value":"其它"}]},{"attrId":1200004593,"attrName":"包装形式","valueList":[{"valueId":1300000231,"value":"袋装"}]}],"skus":[{"sku_id":"105512004","spec":"48g","upc":"6914973704466","price":"15","stock":"0","unit":"袋","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.05","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://img.alicdn.com/imgextra/i1/3862422644/O1CN01bSZdau1VOzmPoEzzY_!!3862422644.jpg,https://img.alicdn.com/imgextra/i4/3862422644/O1CN01Z4ZXt11VOzk8SJCde_!!3862422644.jpg,https://img.alicdn.com/imgextra/i3/3862422644/O1CN01SG79UT1VOzk4Qh4rT_!!3862422644.jpg,https://img.alicdn.com/imgextra/i2/3862422644/O1CN01uGbH1n1VOzk96NeOb_!!3862422644.jpg,https://img.alicdn.com/imgextra/i3/3862422644/O1CN01n4AO841VOzk6PoXFB_!!3862422644.jpg,https://img.alicdn.com/imgextra/i3/3862422644/O1CN01Dd0QAD1VOzk7LPIYI_!!3862422644.jpg,https://img.alicdn.com/imgextra/i1/3862422644/O1CN01eRlX0B1VOzk5isf5Z_!!3862422644.jpg,https://img.alicdn.com/imgextra/i2/3862422644/O1CN01g98ut61VOzoo2lB1T_!!3862422644.jpg,https://img.alicdn.com/imgextra/i4/3862422644/O1CN016cAmIY1VOzol0uU6p_!!3862422644.jpg,https://img.alicdn.com/imgextra/i4/3862422644/O1CN017Ymhn51VOzorh9zRj_!!3862422644.jpg,https://img.alicdn.com/imgextra/i4/3862422644/O1CN01MiC5vV1VOzk7koXpz_!!3862422644.jpg,https://img.alicdn.com/imgextra/i2/3862422644/O1CN015kTTHz1VOzk9PFXHb_!!3862422644.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":70,"category_code":"","sell_point":""},{"app_food_code":"1007284","box_num":0,"box_price":0,"category_name":"综合营养","is_sold_out":1,"min_order_count":1,"name":"发育宝CG5犬猫用爆毛乐60ml","description":"","unit":"","picture":"http://file.vetscloud.com/474f74d904c2ba1a08b25e5bb95e6727.jpg","sequence":0,"price":0,"tag_id":200002364,"zh_name":"","product_name":"发育宝CG5犬猫用爆毛乐60ml","origin_name":"","flavour":"","common_attr_value":null,"skus":[{"sku_id":"1007284001","spec":"60ml(瓶)/盒","upc":"6970782131589","price":"75","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.06","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"http://file.vetscloud.com/a9e8797510cefbb48f20211f202a2708.jpg,http://file.vetscloud.com/83d45329d6137f6eec060778f5cb024a.jpg,http://file.vetscloud.com/2bf0caafc672a99d3d7d0f9519484e60.jpg,http://file.vetscloud.com/122aa9adf167462f40f56eef6fcd4063.jpg,http://file.vetscloud.com/1715cc6c39e903d2d8ae213d41a2fccd.jpg,http://file.vetscloud.com/6d364848d330fdd73d0be59bfacc0c29.jpg,http://file.vetscloud.com/9b27d88a6b571fde2c721da6ef650765.jpg,http://file.vetscloud.com/726b0a5202dc8acf1a8b614afaefbf18.jpg,http://file.vetscloud.com/3a8156da4fed32d84d4138f007dc269b.jpg,http://file.vetscloud.com/8fcfb4d65bc442b7eeb3384cd66fe636.jpg,http://file.vetscloud.com/79c91716aa19a08e7eb67cd70e5908a8.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":88,"category_code":"","sell_point":""},{"app_food_code":"1005487","box_num":0,"box_price":0,"category_name":"眼部护理","is_sold_out":1,"min_order_count":1,"name":"安琪儿泪痕消咀嚼片600mg","description":"","unit":"","picture":"http://file.vetscloud.com/e582af9c2cca035faaf4a64a649e7589.png","sequence":0,"price":0,"tag_id":400000733,"zh_name":"","product_name":"安琪儿泪痕消咀嚼片600mg","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2420702,"value":"安琪儿"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300016655,"value":"眼部清洁"}]},{"attrId":1200000101,"attrName":"适用对象","valueList":[{"valueId":1300006626,"value":"猫狗通用"}]},{"attrId":1200000463,"attrName":"形态","valueList":[{"valueId":1300006704,"value":"固体"}]},{"attrId":1200000141,"attrName":"用法","valueList":[{"valueId":1300000252,"value":"内用"}]},{"attrId":1200000005,"attrName":"注意事项","valueList":[{"valueId":0,"value":"."}]}],"skus":[{"sku_id":"1005487001","spec":"50粒/600mg","upc":"6971148190486","price":"70","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.03","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":59,"category_code":"","sell_point":""},{"app_food_code":"1001261","box_num":0,"box_price":0,"category_name":"猫罐头","is_sold_out":1,"min_order_count":1,"name":"【猫条】希宝猫条舔食软包罐头海洋鱼虾+鲣鱼三文鱼 12g*4","description":"","unit":"","picture":"http://file.vetscloud.com/3b055d7627261696ae72b5f1ad5585cb.jpg,http://file.vetscloud.com/7a51bd9405f3db0b9e4abfd981146056.jpg,,,","sequence":0,"price":0,"tag_id":200002359,"zh_name":"","product_name":"【猫条】希宝猫条舔食软包罐头海洋鱼虾+鲣鱼三文鱼 12g*4","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":1017754,"value":"希宝"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300015617,"value":"猫条"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000050,"value":"中国"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]}],"skus":[{"sku_id":"1001261001","spec":"48g","upc":"MSXB04","price":"5000","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.1","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":67,"category_code":"","sell_point":""},{"app_food_code":"1001873","box_num":0,"box_price":0,"category_name":"冻干猫粮","is_sold_out":1,"min_order_count":1,"name":"【猫零食】朗诺猫冻干三文鱼300g","description":"","unit":"","picture":"https://file.vetscloud.com/ada8e3a3097e18ec5c659d98e27d87ee.png","sequence":0,"price":0,"tag_id":200002359,"zh_name":"","product_name":"【猫零食】朗诺猫冻干三文鱼300g","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":2303005,"value":"朗诺"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300015618,"value":"冻干"}]},{"attrId":1200000132,"attrName":"国产/进口","valueList":[{"valueId":1300000003,"value":"国产"}]}],"skus":[{"sku_id":"103804005","spec":"300g/300g","upc":"6930693204505","price":"225","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.3","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":74,"category_code":"","sell_point":""},{"app_food_code":"1001595","box_num":0,"box_price":0,"category_name":"猫罐头","is_sold_out":1,"min_order_count":1,"name":"希宝猫条成猫舔食软包罐海鲜+鲣鱼+虾 12g*4","description":"","unit":"","picture":"https://file.vetscloud.com/dd4b7353e4bcabd677996b00a44640c0.jpeg","sequence":0,"price":0,"tag_id":200002359,"zh_name":"","product_name":"希宝猫条成猫舔食软包罐海鲜+鲣鱼+虾 12g*4","origin_name":"","flavour":"","common_attr_value":[{"attrId":1200000088,"attrName":"品牌","valueList":[{"valueId":1017754,"value":"希宝"}]},{"attrId":1200000210,"attrName":"商品类别","valueList":[{"valueId":1300015617,"value":"猫条"}]},{"attrId":1200000094,"attrName":"产地","valueList":[{"valueId":100000050,"value":"中国"}]},{"attrId":1200000134,"attrName":"适用阶段","valueList":[{"valueId":1300000230,"value":"全阶段"}]}],"skus":[{"sku_id":"105512003","spec":"48g","upc":"6942020014107","price":"15","stock":"0","unit":"","min_order_count":1,"available_times":{"monday":"","tuesday":"","wednesday":"","thursday":"","friday":"","saturday":"","sunday":""},"location_code":"","box_num":"","box_price":"","ladder_box_num":"","ladder_box_price":"","weight_for_unit":"0.048","weight_unit":"千克(kg)","openSaleAttrValueList":null}],"picture_contents":"https://oss.upetmart.com/www/shop/store/repgoods/105512/2c07259e35c4464f3fff26bc6a549aa9.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/9ceb26c5332fb57dcceb1fe4b8240173.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/2b75beaa4850d342c59f95b409588058.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/993b0dd3716dcc40e15c9a76927d9247.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/f79789799a09f52801ab35eb5ee7afdb.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/a927ac5522b3c3bd997cac15f30c4951.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/c5fa180d4196aca90454839753c3c905.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/26e68514a6b238dac54907a57ee3392c.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/01141aa0e26221d8277c61b7c7d406f6.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/a4ff872cbefd29f4d13d89756d531c7b.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/8aa98929cc2a75d6948ad6b467b3dbea.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/13f0b90a770a8f0efc3e7137720b6104.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/4f044f4c348373d58423ffcae6d9792d.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/52f837e4007e86caea49e2e21fb5c4b2.jpg,https://oss.upetmart.com/www/shop/store/repgoods/105512/b7423b0d24a97aa84468732f54ef5822.jpg","properties":[],"is_specialty":0,"video_id":0,"limit_sale_info":{"limitSale":false,"type":0,"frequency":0,"begin":"","count":0,"end":""},"category_id":67,"category_code":"","sell_point":""}],"store_master_id":1}`

			var mtRes et.RetailSellStatusResult
			var foodData []*et.RetailBatchinitdata
			json.Unmarshal([]byte(foodDatajson), foodData)
			json.Unmarshal([]byte(mtResjson), mtRes)
			//MtProductThirdId(&mtRes, foodData, "")
		})
	}
}
