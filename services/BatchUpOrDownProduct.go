package services

//
import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/et"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/google/uuid"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

/**
批量上下架的接口整改
*/

// 序列化使用task定时任务
type BatchChannelProductUpDownBase struct {
	// 商品Id列表-必须
	ProductIds []string
	// 渠道-必须
	ChannelId int
	// 需要上架的门店财务代码-必须
	FinanceCodes []string
	// 操作的用户代码
	UserNo   string
	UserName string
	// 是否需要同步价格
	IsSyncPrice bool

	// 是否出现了未捕获的异常
	UnknownError error

	// 上架处理结果
	UpResult []*pc.UpDownERROR

	Ctx context.Context `json:"-"`

	TaskId int

	//是否是切换仓库的操作 true：是 false：否。切换仓库的操作和正常的上架操作不一样
	IsSwitchingWarehouse bool
}

// 上架执行结果
type Batch_ChannelProductUp_Result_Base struct {
	// 门店
	FinanceCode string
	// 是否成功
	IsSuccess bool
	// 信息
	Message string
}

// 商品上架包装器
// A.实例化ChannelProductUpDown
// B.调用UpProduct方法执行上架
// C.(可选)调用Export将错误结果导出到七牛云
type BatchChannelProductUpDown struct {
	// 商品Id列表-必须
	ProductIds []string
	// 渠道-必须
	ChannelId int
	// 需要上架的门店财务代码-必须
	FinanceCode string
	//仓库编码
	Code string
	// 操作的用户代码
	UserNo   string
	UserName string
	// 是否需要同步价格
	IsSyncPrice bool
	// 商品与门店财务代码的关联关系
	baseUpDownProduct []*Batch_ChannelProductUp_StoreProduct
	// 上架处理结果
	UpResult []*pc.UpDownERROR
	// 是否出现了未捕获的异常
	UnknownError error
	Ctx          context.Context `json:"-"`

	TaskId int

	//门店和仓库的关系
	WarehouseMap map[string]*models.ChannelWarehouse

	// 获取第三方平台配置的门店id
	StoreMap         map[string]*dac.StoreInfo // 门店与渠道平台对应关系
	StoreDrugInfoMap map[string]string         //门店在该渠道是否有售药资质

	//是否是切换仓库的操作 true：是 false：否。切换仓库的操作和正常的上架操作不一样
	IsSwitchingWarehouse bool
}

// 上架执行结果
type Batch_ChannelProductUp_Result struct {
	// 商品信息
	StoreProduct *Batch_ChannelProductUp_StoreProduct
	// 是否成功
	IsSuccess bool
	// 信息
	Message string
}

// 商品与店铺关联关系
type Batch_ChannelProductUp_StoreProduct struct {
	// 门店财务编码列表 -- 必填
	StoreFinanceCode string
	// 门店在渠道里的Id
	StoreChannelFinanceCodes string
	// 仓库类型 3 门店仓 4 前置仓
	StoreWarehouseCategory int
	// 门店对应仓库Id
	StoreWarehouseId int
	// 商品Id -- 必填
	ProductId string
	// 商品Sku -- 必填
	ProductSkuId string
	// 商品在渠道的分类Id
	ProductChannelCategoryId int
	// 第三方平台分类id
	ProductChannelTagId int
	// 商品库存数量
	ProductStock int
	// 门店仓价格
	ProductSkuStorePrice int32
	// 前置仓价格
	ProductSkuPreposePrice int32
	// 商品重量为空 -- 必填
	ProductWeight float64
	// 商品A8货号
	ProductA8SkuId string
	// 商品的子龙货号
	ProductZilongId string
	// 1-实物 2-虚拟 3-组合商品
	ProductType int
	// 组合商品应用范围 1 电商 2 前置仓 3 门店仓
	ProductGroupUseRang map[int]bool
	// 组合商品有效期截止日期
	ProductGroupTermEndDate time.Time
	// 商品快照Id
	ProductSnapshotId int

	// 价格关系结构
	PriceSync ChannelProductPriceSync
	// has_stock的关系
	HasStockData *ic.GetStockInfoResponse
}

type ChannelSkus struct {
	BarCode   string
	ProductId int32
}

// 初始化StoreProducts参数
func (this *BatchChannelProductUpDown) init() {
	this.baseUpDownProduct = this.buildStoreProducts(this.ChannelId, this.ProductIds, this.FinanceCode)
}

func (this *BatchChannelProductUpDown) initWarehouseAndFinanceCode(channel_id int) {
	// 获取第三方平台配置的门店id
	financeCodes := []string{this.FinanceCode}

	var StoreMap = make(map[string]*dac.StoreInfo) // 门店与渠道平台对应关系
	datacenterGrpc := GetDataCenterClient()
	defer datacenterGrpc.Close()
	res, err := datacenterGrpc.RPC.QueryStoreInfo(datacenterGrpc.Ctx, &dac.StoreInfoRequest{ChannelId: int32(this.ChannelId), FinanceCode: financeCodes})
	if err != nil {
		glog.Error(err)
	} else {
		for _, detail := range res.Details {
			StoreMap[detail.FinanceCode] = detail
		}
	}

	// 获取仓库
	var WarehouseMap = make(map[string]*models.ChannelWarehouse) // 门店与仓库对应关系

	// 获取渠道关联关系，修改下面的仓库类型，因为以前是更具绑定关系来判断的现在需要按照渠道关系去获取货号和价格等信息
	product := new(Product)

	resp, err := product.GetChannelWarehouses(financeCodes, int32(channel_id), nil, 1)
	if err != nil {
		glog.Error("获取渠道关联关系异常：", err.Error())
	}
	for _, w := range resp {
		WarehouseMap[w.ShopId] = &w
	}

	this.WarehouseMap = WarehouseMap
	this.StoreMap = StoreMap

}

// 获取商品与店铺关联关系列表
// 获取商品与店铺关联关系列表
func (this *BatchChannelProductUpDown) buildStoreProducts(channelId int, productIds []string, finance_code string) []*Batch_ChannelProductUp_StoreProduct {
	prefix := "批量上下架初始化StoreProducts参数: "
	glog.Info(prefix, "buildStoreProducts run  productIds : ", productIds, " channelId ", channelId, "  financeCode ", finance_code)
	var channelStoreProduct []*Batch_ChannelProductUp_StoreProduct

	financeCodes := []string{finance_code}

	// 查询商品快照
	snapshots, channelProductSnapMap, _ := GetSnapInfo(channelId, productIds, financeCodes)

	// 商品的第三方货号
	var channelSkuThrids []*models.ChannelSkuThird
	var channelSkuThirdMap = make(map[string][]*models.ChannelSkuThird) // 商品第三方sku列表
	engine.Where("channel_id=?", channelId).In("product_id", productIds).Find(&channelSkuThrids)

	for _, channelSkuThrid := range channelSkuThrids {
		var key = cast.ToString(channelSkuThrid.ProductId)
		channelSkuThirdMap[key] = append(channelSkuThirdMap[key], channelSkuThrid)
	}

	// 查询渠道商品
	var productMap = make(map[string]*models.ChannelProduct)
	var channelProduct []*models.ChannelProduct
	err := engine.In("id", productIds).Where("channel_id=?", this.ChannelId).Find(&channelProduct)
	if err != nil {
		glog.Error(err)
	}
	for _, product := range channelProduct {
		productMap[strconv.Itoa(int(product.Id))] = product
	}

	// 处理商品
	for _, financeCode := range financeCodes {
		channelStore, isChannelStore := this.StoreMap[financeCode] // 第三方店铺Id
		warehouse, isWarehouse := this.WarehouseMap[financeCode]   // 仓库类型

		for _, productId := range productIds {
			channelProductRequest, isChannelProduct := channelProductSnapMap[fmt.Sprintf("%s%s", financeCode, productId)]
			product, isProduct := productMap[productId]
			// 商品与门店财务代码的关联关系
			var storeProduct = Batch_ChannelProductUp_StoreProduct{}
			storeProduct.ProductId = productId
			// 提取第三方门店Id
			if isChannelStore {
				storeProduct.StoreChannelFinanceCodes = channelStore.ChannelStoreId
			}
			// 提取sku信息
			//兼容多规格处理数据
			if len(snapshots) > 0 && isChannelProduct {
				for _, v1 := range snapshots {
					if v1.FinanceCode == financeCode {
						var jsonData pc.ChannelProductRequest
						err := json.Unmarshal([]byte(v1.JsonData), &jsonData)
						if err != nil {
							msg := "快照为空无法上架，请先编辑商品信息"
							glog.Error(msg, err.Error())
							continue
						}
						// 之前的sku和第三方全部是获取的第一个
						for _, skuVData := range jsonData.SkuInfo {

							st_data := storeProduct
							if st_data.ProductId != cast.ToString(skuVData.ProductId) {
								continue
							}
							st_data.ProductSkuId = cast.ToString(skuVData.SkuId)
							st_data.ProductSkuStorePrice = skuVData.StorePrice
							st_data.ProductSkuPreposePrice = skuVData.PreposePrice
							st_data.ProductWeight = skuVData.WeightForUnit
							st_data.ProductSnapshotId = channelProductRequest.Id
							st_data.ProductChannelCategoryId = int(jsonData.Product.ChannelCategoryId)
							st_data.ProductChannelTagId = int(jsonData.Product.ChannelTagId)
							//}
							// 提取组合商品信息
							if isProduct {
								st_data.ProductType = int(product.ProductType)
								var useRange = strings.Split(product.UseRange, ",")
								st_data.ProductGroupUseRang = make(map[int]bool)
								for _, u := range useRange {
									st_data.ProductGroupUseRang[cast.ToInt(u)] = true
								}
								// 虚拟商品设置过期日期
								if product.ProductType == 2 && product.TermType == 1 {
									st_data.ProductGroupTermEndDate = time.Unix(int64(product.TermValue), 0)
								} else {
									st_data.ProductGroupTermEndDate = time.Now().AddDate(1, 0, 0)
								}
							}
							// 提取仓库类型
							st_data.StoreFinanceCode = financeCode
							if isWarehouse {
								st_data.StoreWarehouseCategory = int(warehouse.Category)
								st_data.StoreWarehouseId = int(warehouse.WarehouseId)
							}

							for _, SkuThirdData := range skuVData.SkuThird {
								if SkuThirdData.ErpId == 4 && st_data.ProductSkuId == cast.ToString(SkuThirdData.SkuId) { // 前置仓
									st_data.ProductZilongId = SkuThirdData.ThirdSkuId
								}
								if SkuThirdData.ErpId == 2 && st_data.ProductSkuId == cast.ToString(SkuThirdData.SkuId) { // 门店仓
									st_data.ProductA8SkuId = SkuThirdData.ThirdSkuId
								}
							}
							// 保存数据

							channelStoreProduct = append(channelStoreProduct, &st_data)
						}
					}
				}
			} else { // 以前的处理逻辑，现在保留主要为了兼容第三方平台的上架

				skuThirds, isSkuThird := channelSkuThirdMap[productId]
				// 商品与门店财务代码的关联关系
				//var storeProduct = &ChannelProductUp_StoreProduct{}
				// 渠道商品
				//var channelProduct models.ChannelProduct
				//engine.Where("channel_id=?", channelId).Where("id=?", productId).Get(&channelProduct)
				if isProduct {
					storeProduct.ProductChannelCategoryId = int(product.CategoryId)
				}
				storeProduct.ProductChannelTagId = int(product.ChannelTagId)
				// 渠道sku
				channelSkuDatas := make([]models.ChannelSku, 0)
				engine.Where("channel_id=?", channelId).Where("product_id=?", product.Id).Find(&channelSkuDatas)
				for _, channelSku := range channelSkuDatas {
					bytes, _ := json.Marshal(storeProduct)
					newStoreProduct := Batch_ChannelProductUp_StoreProduct{}
					json.Unmarshal(bytes, &newStoreProduct)
					newStoreProduct.ProductSkuId = cast.ToString(channelSku.Id)
					newStoreProduct.ProductWeight = channelSku.WeightForUnit
					newStoreProduct.ProductSkuStorePrice = channelSku.StorePrice
					newStoreProduct.ProductSkuPreposePrice = channelSku.PreposePrice

					// 提取组合商品信息
					if isProduct {
						newStoreProduct.ProductType = int(product.ProductType)
						var useRange = strings.Split(product.UseRange, ",")
						newStoreProduct.ProductGroupUseRang = make(map[int]bool)
						for _, u := range useRange {
							newStoreProduct.ProductGroupUseRang[cast.ToInt(u)] = true
						}
						// 虚拟商品设置过期日期
						if product.ProductType == 2 && product.TermType == 1 {
							newStoreProduct.ProductGroupTermEndDate = time.Unix(int64(product.TermValue), 0)
						} else {
							newStoreProduct.ProductGroupTermEndDate = time.Now().AddDate(1, 0, 0)
						}
					}
					// 提取第三方sku信息
					if isSkuThird {
						//newStoreProduct.ProductSkuId = cast.ToString(skuThirds[0].SkuId)
						for _, skuThird := range skuThirds {
							if skuThird.ErpId == 2 && cast.ToString(skuThird.SkuId) == newStoreProduct.ProductSkuId {
								newStoreProduct.ProductA8SkuId = skuThird.ThirdSkuId
							}
							if skuThird.ErpId == 4 && cast.ToString(skuThird.SkuId) == newStoreProduct.ProductSkuId {
								newStoreProduct.ProductZilongId = skuThird.ThirdSkuId
							}
						}

					}
					// 提取仓库类型
					newStoreProduct.StoreFinanceCode = financeCode
					if isWarehouse {
						newStoreProduct.StoreWarehouseCategory = int(warehouse.Category)
						newStoreProduct.StoreWarehouseId = int(warehouse.WarehouseId)
					}
					// 保存数据
					channelStoreProduct = append(channelStoreProduct, &newStoreProduct)

				}
			}
		}
	}

	glog.Info(prefix, "buildStoreProducts exit  productIds : ", productIds, " channelId ", channelId, "  financeCode ", financeCodes)
	return channelStoreProduct
}

// 渠道商品上架，支持单门店多门店 v6.27.2
func (c *Product) BatchUpChannelProduct(ctx context.Context, request *pc.UpDownChannelProductRequest) (*pc.BaseResponse, error) {
	prefix := "渠道商品上架，支持单门店多门店 "
	var response = &pc.BaseResponse{Code: 400}
	// 上下架类封装
	var channelProductUpDown BatchChannelProductUpDownBase
	channelProductUpDown.ProductIds = request.ProductId
	channelProductUpDown.ChannelId = int(request.ChannelId)
	channelProductUpDown.FinanceCodes = request.FinanceCode
	channelProductUpDown.UserNo = request.UserNo
	channelProductUpDown.Ctx = ctx
	userInfo := loadLoginUserInfo(ctx) // 取名字保存
	if userInfo != nil {
		channelProductUpDown.UserName = userInfo.UserName
	}

	// 全部渠道需要同步价格 商品上架需要同步价格
	channelProductUpDown.IsSyncPrice = true
	// 是否且切仓库和不切换仓库的逻辑不一样
	channelProductUpDown.IsSwitchingWarehouse = request.IsSwitchingWarehouse

	// 执行批量上架
	channelProductUpDown.BatchUpProduct()

	response.UpDownDetail = channelProductUpDown.UpResult
	glog.Info(prefix, "批量上架信息返回数量： ", " 参数：", kit.JsonEncode(channelProductUpDown), len(response.UpDownDetail), " 返回的错误信息", kit.JsonEncode(response.UpDownDetail))
	return response, nil

}

// 不可销返回false
func ChekcCanSell(storeProduct *Batch_ChannelProductUp_StoreProduct, ChannelId int) bool {
	ids := storeProduct.ProductId
	if storeProduct.ProductType == 3 {
		ids = CheckGroupProduct(storeProduct.ProductId, ChannelId)
	}
	var SyncZlProductRecord []models.SyncZlProductRecordExtend
	if err := engine.SQL("select r.*,s.product_id,st.finance_code from sync_zl_product_record r inner join dc_product.sku_third s "+
		"on r.item_code = s.third_sku_id and s.erp_id = 4 inner join datacenter.store st on r.zilong_id = st.zilong_id "+
		"where s.product_id in ("+ids+") and st.finance_code = ?;", storeProduct.StoreFinanceCode).Find(&SyncZlProductRecord); err != nil {
	}
	if len(SyncZlProductRecord) > 0 {
		var flag = false
		var rid []string
		for _, v := range SyncZlProductRecord {
			if v.CanSell == 0 {
				flag = true
				rid = append(rid, cast.ToString(v.ProductId))
			}
		}
		if flag {
			return false
		}
	}

	return true

}

// 批量上架方法，支持按照门店批量执行
func (base *BatchChannelProductUpDownBase) BatchUpProduct() {
	prefix := "批量上架方法BatchUpProduct: "
	glog.Info(prefix, "入参:", kit.JsonEncode(base))
	defer func() {
		// 出现异常则记录
		if err := recover(); err != nil {
			base.UnknownError = errors.New("出现了未处理的错误")
			glog.Error(prefix, " 上架panic", err)
		}
	}()

	// 初始化参数
	conn := NewDbConn()
	for i := range base.FinanceCodes {
		financeCodeOne := base.FinanceCodes[i]
		//按照门店来批量初始化
		down := BatchChannelProductUpDown{
			ProductIds:           base.ProductIds,
			ChannelId:            base.ChannelId,
			FinanceCode:          financeCodeOne,
			UserNo:               base.UserNo,
			UserName:             base.UserName,
			IsSyncPrice:          base.IsSyncPrice,
			Ctx:                  base.Ctx,
			TaskId:               base.TaskId,
			IsSwitchingWarehouse: base.IsSwitchingWarehouse,
		}

		if down.IsSwitchingWarehouse { // 切换仓库目前只是支持阿文，美团，饿了么，京东
			if base.ChannelId < 1 || base.ChannelId > 4 {
				glog.Error(prefix, " 切换仓库暂不支持渠道 ", base.ChannelId)
				continue
			}
		}

		// 初始化门店和财务编码信息以及仓库的信息
		down.initWarehouseAndFinanceCode(base.ChannelId)
		down.StoreDrugInfoMap = QueryStoredrugInfo([]string{financeCodeOne}, base.ChannelId)
		glog.Info(prefix, fmt.Sprintf("渠道%d,门店%s 可上架药品：%s", base.ChannelId, financeCodeOne, kit.JsonEncode(down.StoreDrugInfoMap)))
		//获取仓库类型
		var (
			category      int
			WarehouseCode string
		)
		if data, ok := down.WarehouseMap[financeCodeOne]; ok {
			category = data.Category
			WarehouseCode = data.WarehouseCode
			down.Code = data.WarehouseCode
		} else {
			glog.Error(prefix, " 获取仓库对应类型出错 ", base.ChannelId, financeCodeOne)
			continue
		}
		// 错误导出
		// ProductId和skuid的映射关系
		productIdRelationSkuId := make(map[string]string, 0)
		var allProductIds []int32 // 门店下面所有的批量的商品的商品数
		for _, id := range base.ProductIds {
			allProductIds = append(allProductIds, cast.ToInt32(id))
		}

		if len(allProductIds) > 0 {
			product := make([]models.FailSkuProduct, 0)
			conn.Table("sku_third").In("product_id", allProductIds).Find(&product)
			for hi := range product {
				skuProduct := product[hi]
				productIdRelationSkuId[skuProduct.ProductId] = skuProduct.SkuId
			}
		}

		// 前置排除条件，减少初始化流程
		err := down.ExclusionUpProductFirst(allProductIds, productIdRelationSkuId)
		if err != nil {
			glog.Error(prefix, " 排除上架商品异常：", err.Error())
			continue
		}

		var upInfos map[int]*AwenProductUpInfo

		if base.ChannelId != ChannelDigitalHealth && (category == 3 || category == 4) {
			productIds := make([]int, len(allProductIds))
			for k, priceSync := range allProductIds {
				productIds[k] = int(priceSync)
			}
			if upInfos, err = QueryAwenProductUpInfoByIds(productIds, category); err != nil {
				glog.Error("ItemToAuto QueryAwenProductUpInfoByIds 出错：", err.Error())
				return
			}
		}

		// 初始化商品的数据信息
		down.init()

		storeLen := len(down.baseUpDownProduct)
		glog.Info(prefix, " zx打印定时任务当前执行进度任务id:", base.TaskId, " 当前:", storeLen, "门店：", financeCodeOne)

		// 后置条件排除，减少上架流程，这里会首先将所有的错误日志保存起来
		down.ExclusionUpProductSecond()

		// 开始执行上架操作
		switch down.ChannelId {
		case ChannelAwenId:
			// 价格和库存的校验
			down.UpDownSingleExecInit()

			for _, storeProduct := range down.baseUpDownProduct {
				var resultsLast []*Batch_ChannelProductUp_Result
				bmsg := &Batch_ChannelProductUp_Result{
					StoreProduct: storeProduct,
				}
				resultsLast = append(resultsLast, bmsg)
				if category == 3 {
					if upInfo, has := upInfos[cast.ToInt(storeProduct.ProductSkuId)]; has {
						if err := upInfo.Check(category, storeProduct.StoreFinanceCode, down.ChannelId, WarehouseCode); err != nil {
							bmsg.Message = "上架失败" + err.Error()
							bmsg.IsSuccess = false
							//处理错误信息
							down.UpdateLastErrorMsg(resultsLast)
							continue
						}
					}
				}
				//阿闻批量上架
				err = down.upProductToAwen(storeProduct)
				if err != nil {
					bmsg.Message = fmt.Sprintf("上架失败:%s", err.Error())
					bmsg.IsSuccess = false

				} else {
					bmsg.IsSuccess = true
				}
				// 上架后的操作 修改上架表快照表同步三方价格数据
				down.BatchAfterUp(resultsLast)

				//处理错误信息
				down.UpdateLastErrorMsg(resultsLast)
			}

		case ChannelMtId: // 美团

			down.UpDownSingleExecInit()

			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()

			//美团接口限流每次200组同步一次
			var syncMtChannelProduct []*Batch_ChannelProductUp_StoreProduct

			// mt同步回调的结果处理
			var resultsToMt []*Batch_ChannelProductUp_Result

			for ie, storeProduct := range down.baseUpDownProduct {

				bres := Batch_ChannelProductUp_Result{StoreProduct: storeProduct}
				resultsToMt = append(resultsToMt, &bres)

				if category == 3 {
					if upInfo, has := upInfos[cast.ToInt(storeProduct.ProductSkuId)]; has {
						if err := upInfo.Check(category, storeProduct.StoreFinanceCode, down.ChannelId, WarehouseCode); err != nil {
							bres.Message = "上架失败" + err.Error()
							bres.IsSuccess = false
							//处理错误信息
							down.UpdateLastErrorMsg(resultsToMt)
							continue
						}
					}
				}

				syncMtChannelProduct = append(syncMtChannelProduct, storeProduct)

				if len(syncMtChannelProduct) >= 200 || ie == len(down.baseUpDownProduct)-1 {

					result, err := down.upProductToMt(syncMtChannelProduct, grpcClient)
					glog.Info("批量上架到美团返回数据：", kit.JsonEncode(result), " ----------入参：", kit.JsonEncode(syncMtChannelProduct), "err为：", kit.JsonEncode(err))
					if err != nil { // 调用远程第三方下架失败
						for e := range resultsToMt {
							resultsToMt[e].Message = fmt.Sprintf("上架失败:%s", err.Error())
							resultsToMt[e].IsSuccess = false
						}

					} else if len(result.Msg) <= 0 { // 调用成功
						for e := range resultsToMt {
							resultsToMt[e].IsSuccess = true
						}
					} else { // 部分失败部分成功
						mt := []models.MsgMT{}

						mtFailMsg := make(map[string]string, 0)
						// 所有的id
						var allMtIds []int32
						//失败的
						var failMtIds []int32
						// 成功的美团id
						var successMtIds []int32
						if len(result.Msg) > 0 {
							err := json.Unmarshal([]byte(result.Msg), &mt)
							if err != nil {
								glog.Error(prefix, err.Error())
							}
							//失败的
							if len(mt) > 0 {
								for e := range mt {
									failMtIds = append(failMtIds, cast.ToInt32(mt[e].AppFoodCode))
									mtFailMsg[mt[e].AppFoodCode] = mt[e].Msg
								}
							}
							for e := range syncMtChannelProduct {
								allMtIds = append(allMtIds, cast.ToInt32(syncMtChannelProduct[e].ProductId))
							}
							setFails := utils.NewSet(failMtIds...)
							// 同步成功的id
							successMtIds = utils.NewSet(allMtIds...).Minus(setFails).List()

						}

						// 提示错误信息
						for e := range resultsToMt {
							for _, succ := range successMtIds {
								if cast.ToString(succ) == resultsToMt[e].StoreProduct.ProductId {
									resultsToMt[e].IsSuccess = true
									break
								}

							}

							for _, fail := range failMtIds {
								st := cast.ToString(fail)
								storeProductArr := resultsToMt[e].StoreProduct
								if st == storeProductArr.ProductId {
									resultsToMt[e].Message = mtFailMsg[st]
									resultsToMt[e].IsSuccess = false
									//因为切仓是新数据，成功的记录没有错误信息，则不用清空
									UpdateProductThirdSyncErr(cast.ToInt(storeProductArr.ProductId), down.ChannelId, storeProductArr.StoreFinanceCode, mtFailMsg[st])
									break
								}
							}
						}
					}

					// 上架后的操作
					down.BatchAfterUp(resultsToMt)

					// 处理错误信息
					down.UpdateLastErrorMsg(resultsToMt)

					// 清空 继续循环处理
					resultsToMt = make([]*Batch_ChannelProductUp_Result, 0)
					syncMtChannelProduct = make([]*Batch_ChannelProductUp_StoreProduct, 0)

				}

			}

		case ChannelElmId: // 饿了么
			down.UpDownSingleExecInit()
			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()

			//eleMe接口限流每次100组同步一次
			var syncEleMeChannelProduct []*Batch_ChannelProductUp_StoreProduct

			// ele同步回调的结果处理
			var resultsToEle []*Batch_ChannelProductUp_Result
			for ie, storeProduct := range down.baseUpDownProduct {
				bres := Batch_ChannelProductUp_Result{StoreProduct: storeProduct}
				resultsToEle = append(resultsToEle, &bres)
				if category == 3 {

					if upInfo, has := upInfos[cast.ToInt(storeProduct.ProductSkuId)]; has {
						if err := upInfo.Check(category, storeProduct.StoreFinanceCode, down.ChannelId, WarehouseCode); err != nil {
							bres.Message = "上架失败" + err.Error()
							bres.IsSuccess = false
							//处理错误信息
							down.UpdateLastErrorMsg(resultsToEle)
							continue
						}
					}
				}

				syncEleMeChannelProduct = append(syncEleMeChannelProduct, storeProduct)

				//eleMe最更多支持100个商品更新状态
				if len(syncEleMeChannelProduct) >= 100 || ie == len(down.baseUpDownProduct)-1 {

					elmOnlineResponse, err := down.upProductToElm(syncEleMeChannelProduct, grpcClient)
					if err != nil {
						for e := range resultsToEle {
							resultsToEle[e].Message = fmt.Sprintf("上架失败:%s", err.Error())
						}

					} else if elmOnlineResponse.Code == 200 && len(elmOnlineResponse.Error) <= 0 { // 全部成功
						for e := range resultsToEle {
							resultsToEle[e].IsSuccess = true
						}
					} else { // 部分成功部分失败
						if len(elmOnlineResponse.Error) > 0 {
							msgToELE := models.MsgELE{}
							json.Unmarshal([]byte(elmOnlineResponse.Error), &msgToELE)

							// 提示错误信息
							for e := range msgToELE.SuccessList {
								for mi := range resultsToEle {
									if msgToELE.SuccessList[e].CustomSkuId == resultsToEle[mi].StoreProduct.ProductSkuId {
										resultsToEle[mi].IsSuccess = true
										UpdateProductThirdSyncErr(cast.ToInt(resultsToEle[mi].StoreProduct.ProductId), ChannelElmId, resultsToEle[mi].StoreProduct.StoreFinanceCode, "")

										break
									}

								}
							}
							for e := range msgToELE.FailedList {
								for mi := range resultsToEle {
									if msgToELE.FailedList[e].CustomSkuId == resultsToEle[mi].StoreProduct.ProductSkuId {
										resultsToEle[mi].IsSuccess = false
										resultsToEle[mi].Message = msgToELE.FailedList[e].ErrorMsg
										UpdateProductThirdSyncErr(cast.ToInt(resultsToEle[mi].StoreProduct.ProductId), ChannelElmId, resultsToEle[mi].StoreProduct.StoreFinanceCode, msgToELE.FailedList[e].ErrorMsg)

										break
									}
								}
							}
						}
					}

					// 上架后的操作
					down.BatchAfterUp(resultsToEle)

					// 批量修复快照片和上架表对不上的数据
					//var snapProductIds []string
					//for ki := range syncEleMeChannelProduct {
					//	snapProductIds = append(snapProductIds, syncEleMeChannelProduct[ki].ProductId)
					//}
					//BatchRepairSnapToChannelStoreSnapIdRelation(snapProductIds, down.ChannelId, down.FinanceCode)

					// 处理错误信息
					down.UpdateLastErrorMsg(resultsToEle)

					// 清空 继续循环处理
					resultsToEle = make([]*Batch_ChannelProductUp_Result, 0)
					syncEleMeChannelProduct = make([]*Batch_ChannelProductUp_StoreProduct, 0)

				}

			}

		case ChannelJddjId: // 京东
			down.UpDownSingleExecInit()

			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()

			//jd接口限流每次50组同步一次
			var syncJDDJChannelProduct []*Batch_ChannelProductUp_StoreProduct

			// mt同步回调的结果处理
			var resultsToJDDJ []*Batch_ChannelProductUp_Result

			// jd更新可售状态最多一次50个
			for ie, storeProduct := range down.baseUpDownProduct {

				bres := Batch_ChannelProductUp_Result{StoreProduct: storeProduct}
				resultsToJDDJ = append(resultsToJDDJ, &bres)
				if category == 3 {
					if upInfo, has := upInfos[cast.ToInt(storeProduct.ProductSkuId)]; has {
						if err = upInfo.Check(category, storeProduct.StoreFinanceCode, down.ChannelId, WarehouseCode); err != nil {
							bres.Message = "上架失败" + err.Error()
							bres.IsSuccess = false
							//处理错误信息
							down.UpdateLastErrorMsg(resultsToJDDJ)
							continue
						}
					}
				}
				syncJDDJChannelProduct = append(syncJDDJChannelProduct, storeProduct)

				if len(syncJDDJChannelProduct) >= 50 || ie == len(down.baseUpDownProduct)-1 {

					// 批量上架更新库存到jd
					result, err := down.upProductToJd(syncJDDJChannelProduct, grpcClient)
					if err != nil { // 调用远程第三方下架失败
						for e := range resultsToJDDJ {
							resultsToJDDJ[e].Message = fmt.Sprintf("上架失败:%s", err.Error())
						}
					} else if len(result.Data) <= 0 { // 调用成功
						for e := range resultsToJDDJ {
							resultsToJDDJ[e].IsSuccess = true
						}
					} else {
						for ti := range result.Data {
							for mi := range resultsToJDDJ {
								if result.Data[ti].OutSkuId == resultsToJDDJ[mi].StoreProduct.ProductSkuId {
									if result.Data[ti].Code == 0 { // 成功
										resultsToJDDJ[mi].IsSuccess = true
										break
									} else { // 失败
										resultsToJDDJ[mi].IsSuccess = false
										resultsToJDDJ[mi].Message = result.Data[ti].Msg
										break
									}
								}

							}
						}
					}

					// 上架后的操作
					down.BatchAfterUp(resultsToJDDJ)

					// 处理错误信息
					down.UpdateLastErrorMsg(resultsToJDDJ)

					// 清空 继续循环处理
					resultsToJDDJ = make([]*Batch_ChannelProductUp_Result, 0)
					syncJDDJChannelProduct = make([]*Batch_ChannelProductUp_StoreProduct, 0)
				}
			}
		}

		// 收集所有的异常信息返回
		base.UpResult = append(base.UpResult, down.UpResult...)
	}
}

// 单个执行的还能优化
func (this *BatchChannelProductUpDown) UpDownSingleExecInit() []*Batch_ChannelProductUp_Result {

	prefix := "UpDownSingleExecInit方法： "
	checks := make([]*Batch_ChannelProductUp_Result, 0)

	var allInitIds []int32
	for ix, storeProduct := range this.baseUpDownProduct {
		allInitIds = append(allInitIds, cast.ToInt32(storeProduct.ProductId))

		if this.TaskId > 0 {
			glog.Info(prefix, "zx打印定时任务当前执行进度任务id:", this.TaskId, "当前:", ix, "/", "门店：", storeProduct.StoreFinanceCode)
		}

		data := new(ic.GetStockInfoResponse)
		// 校验执行结果
		check := Batch_ChannelProductUp_Result{
			StoreProduct: storeProduct,
		}

		// 查询是否有第三方商品id

		if (this.ChannelId == ChannelMtId || this.ChannelId == ChannelElmId) && !CanCallThirdApi(cast.ToInt(storeProduct.ProductId), this.ChannelId, storeProduct.StoreFinanceCode) {
			desc := "批量上架失败:商品未在第三方创建，请先编辑后再进行操作"
			glog.Error(prefix, "没有第三方商品id====2", "参数为：", kit.JsonEncode(storeProduct), "this数据为：", kit.JsonEncode(this))
			check.Message = desc
			checks = append(checks, &check)
			continue
		}

		// 同步价格的时候提到这里来，价格校验也在这里处理
		// v6.0 上架前需要同步价格到各个渠道 放到这里来进行处理
		glog.Info(prefix, " 上架前需要同步价格到各个渠道 放到这里来进行处理 run  productIds : ", kit.JsonEncode(storeProduct))
		// 同步价格优化性能
		var channelProductPriceSync ChannelProductPriceSync
		if this.IsSyncPrice {
			channelProductPriceSync.ChannelId = this.ChannelId
			channelProductPriceSync.FinanceCode = storeProduct.StoreFinanceCode
			channelProductPriceSync.ChannelFinanaceCode = storeProduct.StoreChannelFinanceCodes
			channelProductPriceSync.WarehouseId = storeProduct.StoreWarehouseId
			channelProductPriceSync.WarehouseCategory = storeProduct.StoreWarehouseCategory
			channelProductPriceSync.ProductId = storeProduct.ProductId
			channelProductPriceSync.ProductSkuId = storeProduct.ProductSkuId
			channelProductPriceSync.WarehouseCode = this.Code

			if storeProduct.StoreWarehouseCategory == 4 || storeProduct.StoreWarehouseCategory == 5 || storeProduct.StoreWarehouseCategory == 1 { // 前置仓
				channelProductPriceSync.ProductThirdSkuId = storeProduct.ProductA8SkuId
			}
			if storeProduct.StoreWarehouseCategory == 3 { // 门店仓
				channelProductPriceSync.ProductThirdSkuId = storeProduct.ProductZilongId
			}

			// 同步价格到第三方渠道
			glog.Infof(prefix, " 批量上架同步价格参数; 商品id: %s, 门店id: %s, 渠道id: %d", channelProductPriceSync.ProductId, channelProductPriceSync.FinanceCode, channelProductPriceSync.ChannelId)
			// 检验参数
			err := channelProductPriceSync.checkParams()
			if err != nil {
				Message := fmt.Sprintf("%s: %s", err.Error(), storeProduct.ProductId)
				check.Message = Message
				checks = append(checks, &check)
				continue
			}
			// 构造单价
			err = channelProductPriceSync.BuildPrice()
			if err != nil {
				Message := fmt.Sprintf("%s: %s", err.Error(), storeProduct.ProductId)
				check.Message = Message
				checks = append(checks, &check)
				continue
			}
			// 保存关联关系
			storeProduct.PriceSync = channelProductPriceSync

		}
		glog.Info(prefix, "上架前校验 run  productIds : ", kit.JsonEncode(storeProduct))
		//A.上架前校验
		err := this.beforeUp(storeProduct, data)
		storeProduct.HasStockData = data
		if err != nil {
			Message := fmt.Sprintf("%s: %s", err.Error(), storeProduct.ProductId)
			check.Message = Message
			checks = append(checks, &check)
		}
	}

	if len(checks) > 0 { // 排除校验失败的
		var excludeIds []int32
		for e := range checks {
			excludeIds = append(excludeIds, cast.ToInt32(checks[e].StoreProduct.ProductId))
		}

		shouldBeInitList := utils.NewSet(allInitIds...).Minus(utils.NewSet(excludeIds...)).List()
		// 删除未查询到的异常
		this.baseUpDownProduct = this.DeleteUpDownProductData(shouldBeInitList)

		//添加异常信息
		this.UpdateLastErrorMsg(checks)

	}

	return checks
}

// 处理异常错误
func (this *BatchChannelProductUpDown) UpdateLastErrorMsg(resultsLast []*Batch_ChannelProductUp_Result) {

	for it := range this.UpResult {
		sId := this.UpResult[it].ProductId
		for ni := range resultsLast {
			product := resultsLast[ni].StoreProduct
			if product.ProductId == sId {
				this.UpResult[it].Message = resultsLast[ni].Message
				this.UpResult[it].IsSuccess = resultsLast[ni].IsSuccess
				break
			}

		}

	}
}

func (this *BatchChannelProductUpDown) BatchAfterUp(resultsData []*Batch_ChannelProductUp_Result) {
	prefix := "上架后的操作: "
	glog.Info(prefix, "BatchAfterUp  run  productIds : ", len(resultsData))

	upDown := ChannelProductUpDown{
		ProductIds:   nil,
		ChannelId:    this.ChannelId,
		FinanceCodes: []string{this.FinanceCode},
		UserNo:       this.UserNo,
		IsSyncPrice:  this.IsSyncPrice,
	}
	for _, storeProductMt := range resultsData {

		storeProduct := storeProductMt.StoreProduct

		product := &ChannelProductUp_StoreProduct{
			StoreFinanceCode:         storeProduct.StoreFinanceCode,
			StoreChannelFinanceCodes: storeProduct.StoreChannelFinanceCodes,
			StoreWarehouseCategory:   storeProduct.StoreWarehouseCategory,
			StoreWarehouseId:         storeProduct.StoreWarehouseId,
			ProductId:                storeProduct.ProductId,
			ProductSkuId:             storeProduct.ProductSkuId,
			ProductChannelCategoryId: storeProduct.ProductChannelCategoryId,
			ProductChannelTagId:      storeProduct.ProductChannelTagId,
			ProductStock:             storeProduct.ProductStock,
			ProductSkuStorePrice:     storeProduct.ProductSkuStorePrice,
			ProductSkuPreposePrice:   storeProduct.ProductSkuPreposePrice,
			ProductWeight:            storeProduct.ProductWeight,
			ProductA8SkuId:           storeProduct.ProductA8SkuId,
			ProductZilongId:          storeProduct.ProductZilongId,
			ProductType:              storeProduct.ProductType,
			ProductGroupUseRang:      storeProduct.ProductGroupUseRang,
			ProductGroupTermEndDate:  storeProduct.ProductGroupTermEndDate,
			ProductSnapshotId:        storeProduct.ProductSnapshotId,
		}
		// 组合商品同步库存到第三方
		go func(data *ChannelProductUp_StoreProduct) {
			upDown.SynchronizeInventoryToThird(data)
		}(product)

		// C.上架后回调
		//修改上架表hasStock的状态
		//glog.Info(prefix, "channelProductPriceSync数据：", kit.JsonEncode(product))
		if storeProductMt.IsSuccess {

			//添加切仓操作日志
			go func() {
				new(Product).SaveChannelProductLogDetail(this.ChannelId, cast.ToInt(storeProduct.ProductId), enum.RecordTypeUpOne,
					storeProduct.StoreFinanceCode, "cutWarehouse", this.UserName)
			}()

			err := this.afterUp(product, storeProduct.PriceSync, storeProduct.HasStockData, int32(this.ChannelId))
			if err != nil {
				// 异常错误记录
				this.UpdateLastErrorMsg([]*Batch_ChannelProductUp_Result{{
					StoreProduct: storeProduct,
					IsSuccess:    false,
					Message:      fmt.Sprintf("上架成功，回调出错:%s", err.Error()),
				}})
			}
		}
	}

}

// 添加之前快照表的id和上架表对应不上的bug_fix修复
func BatchRepairSnapToChannelStoreSnapIdRelation(productIds []string, channel_id int, financode string) {

	type product_snap_model struct {
		StoreId    string `json:"store_id"`
		ProductId  int32  `json:"product_id"`
		SnapshopId int32  `json:"snapshop_id"`
		SnapId     int32  `json:"snap_id"`
	}

	sql := `
	select
	csp.id store_id,
	csp.product_id product_id, 
	csp.snapshot_id snapshop_id,
	cps.id snap_id 
	from dc_product.channel_store_product csp join dc_product.channel_product_snapshot cps 
	on csp.finance_code  = cps.finance_code and csp.channel_id = cps.channel_id and csp.product_id  = cps.product_id 
	where csp.channel_id  = ? and csp.finance_code = ? and csp.snapshot_id != cps.id `

	if len(productIds) > 0 {
		sql = sql + " and  csp.product_id  in ('" + strings.Join(productIds, "','") + "')"
	}

	//engine.ShowSQL(true)
	var data []product_snap_model
	find := engine.SQL(sql, channel_id, financode).Find(&data)
	fmt.Println("find:", find)

	for i := range data {
		model := data[i]
		sqlUp := "update dc_product.channel_store_product sc set snapshot_id = ? where id =?;"
		engine.Exec(sqlUp, model.SnapId, model.StoreId)

	}

}

// 排除校验失败的商品
func (this *BatchChannelProductUpDown) DeleteUpDownProductData(shouldSynchronizedList []int32) []*Batch_ChannelProductUp_StoreProduct {

	products := make([]*Batch_ChannelProductUp_StoreProduct, 0)

	for i := range this.baseUpDownProduct {
		product := this.baseUpDownProduct[i]
		for _, id := range shouldSynchronizedList {
			if product.ProductId == cast.ToString(id) {
				products = append(products, product)
				break
			}

		}

	}

	return products

}

// 上架前的通用操作
func (this *BatchChannelProductUpDown) beforeUp(storeProduct *Batch_ChannelProductUp_StoreProduct, args ...interface{}) error {

	// 校验商品和店铺的组合信息
	err := storeProduct.Valid(this.ChannelId, args[0])
	if err != nil {
		return err
	}

	// 组合商品需要检验子商品是否满足条件
	if storeProduct.ProductType == 3 {
		// 查询组合商品里的子商品
		var groupProductIds []string
		engine.Table(&models.ChannelSkuGroup{}).Where("product_id=?", storeProduct.ProductId).Select("distinct group_product_id").Find(&groupProductIds)
		// 子商品的StoreProducts
		var subChannelStoreProdcts = this.buildStoreProducts(this.ChannelId, groupProductIds, storeProduct.StoreFinanceCode)

		//多于的sku去除
		group := make([]models.ChannelSkuGroup, 0)
		engine.SQL("select * from channel_sku_group where product_id = ? and channel_id = ?", storeProduct.ProductId, this.ChannelId).Find(&group)

		for _, subChannelStoreProdct := range subChannelStoreProdcts {
			// 校验子商品
			for _, v_group := range group {
				if v_group.GroupProductId == cast.ToInt(subChannelStoreProdct.ProductId) &&
					cast.ToInt(subChannelStoreProdct.ProductSkuId) == v_group.GroupSkuId {
					err := subChannelStoreProdct.ValidChild(this.ChannelId, args[0])
					if err != nil {
						return errors.New(fmt.Sprintf("子商品校验异常%s : %s ", subChannelStoreProdct.ProductId, err.Error()))
					}
				}
			}
		}
	}

	return nil
}

// 批量查询库存方法
func BatchGetStockInfoBySkuCodeAndShopId(channelId int32, skuId []string, shopId string, args ...interface{}) (map[string]int32, error) {
	prefix := "批量查询库存方法BatchGetStockInfoBySkuCodeAndShopId: "
	skuCodeInfoSlice := []*ic.SkuCodeInfo{}
	for i := range skuId {
		info := &ic.SkuCodeInfo{
			Sku:         cast.ToString(skuId[i]),
			FinanceCode: shopId,
		}
		skuCodeInfoSlice = append(skuCodeInfoSlice, info)

	}

	glog.Info(prefix, " BatchGetStockInfoBySkuCodeAndShopId channelId：", kit.JsonEncode(args))
	glog.Info(prefix, " skuCodeInfoSlice", kit.JsonEncode(skuCodeInfoSlice))
	if len(args) > 0 {
		stockMap, err := GetStockInfoBySkuCode(0, skuCodeInfoSlice, channelId, 0, args[0])
		if err != nil {
			return stockMap, err
		}
		return stockMap, nil
	}
	stockMap, err := GetStockInfoBySkuCode(0, skuCodeInfoSlice, channelId, 0)
	if err != nil {
		return stockMap, err
	}
	return stockMap, nil
}

// 通用校验逻辑
func (storeProduct *Batch_ChannelProductUp_StoreProduct) Valid(channelId int, args ...interface{}) error {

	// 判断一下仓库类型
	switch storeProduct.StoreWarehouseCategory {
	case 3: // 门店仓
		if channelId != ChannelAwenId && storeProduct.ProductSkuStorePrice == 0 {
			return errors.New(storeProduct.ProductId + " 门店仓价格为必填项")
		}
		if len(storeProduct.ProductZilongId) == 0 {
			return errors.New(storeProduct.ProductId + " 查询不到子龙货号信息，无法上架")
		}
	case 1: // 电商仓
		if channelId != ChannelAwenId && storeProduct.ProductSkuStorePrice == 0 {
			return errors.New(storeProduct.ProductId + " 门店仓价格为必填项")
		}
		if len(storeProduct.ProductA8SkuId) == 0 {
			return errors.New(storeProduct.ProductId + " 查询不到A8货号信息，无法上架")
		}
	case 4, 5: //前置仓
		if channelId != ChannelAwenId && storeProduct.ProductSkuPreposePrice == 0 {
			return errors.New(storeProduct.ProductId + " 前置仓价格为必填项")
		}
		if len(storeProduct.ProductA8SkuId) == 0 {
			return errors.New(storeProduct.ProductId + " 查询不到A8货号信息，无法上架")
		}
	default:
		return errors.New(storeProduct.StoreFinanceCode + " 门店仓库类型不正确")
	}

	if storeProduct.ProductType == 1 { // 实物商品
		// 校验商品库存信息 一次查询所有库存来进行处理
		stock, err := GetStockInfoBySkuCodeAndShopId(int32(channelId), cast.ToInt32(storeProduct.ProductSkuId), storeProduct.StoreFinanceCode, args[0])
		if err != nil {
			return errors.New(fmt.Sprintf("查询库存失败 商品%s, err:%s", storeProduct.ProductId, err.Error()))
		}
		if stock == 0 {
			return errors.New(fmt.Sprintf("商品 %s 库存为0，无法上架", storeProduct.ProductId))
		} else {
			storeProduct.ProductStock = int(stock)
		}
		if storeProduct.ProductWeight == 0 {
			return errors.New("商品重量不能为空")
		}
	} else if storeProduct.ProductType == 2 { // 虚拟商品
		var isBefore = storeProduct.ProductGroupTermEndDate.Before(time.Now())
		if isBefore {
			return errors.New(fmt.Sprintf("商品 %s 已经过期，无法上架", storeProduct.ProductId))
		}
		// 虚拟商品的应用范围 bj接口
		product := ChannelProductUp_StoreProduct{
			ProductSkuId: storeProduct.ProductSkuId,
		}

		// 查询beijing的接口不支持批量
		useRange, err := CheckVirtualProductUseRange(&product, channelId)
		if err != nil {
			return errors.New(fmt.Sprintf("虚拟商品的应用范围 bj接口查询异常：%s", storeProduct.ProductId))
		}
		if _, ok := useRange[storeProduct.StoreFinanceCode]; !ok {
			return errors.New(fmt.Sprintf("虚拟商品的应用范围不在门店的应用范围或者商品在门店已经停售 %s : %s", storeProduct.ProductId, storeProduct.StoreFinanceCode))
		}

	} else if storeProduct.ProductType == 3 { // 组合商品

		if storeProduct.StoreWarehouseCategory == 3 {
			if _, ok := storeProduct.ProductGroupUseRang[3]; !ok {
				return errors.New("组合商品不能上架到门店仓")
			}
		}
		if storeProduct.StoreWarehouseCategory == 4 {
			if _, ok := storeProduct.ProductGroupUseRang[2]; !ok {
				return errors.New("组合商品不能上架到前置仓")
			}
		}

	}
	return nil
}

// 通用校验子商品信息
func (storeProduct *Batch_ChannelProductUp_StoreProduct) ValidChild(channelId int, args ...interface{}) error {

	// 非阿闻、互联网医院渠道
	if !(channelId == ChannelAwenId || channelId == ChannelDigitalHealth) {
		if len(storeProduct.StoreChannelFinanceCodes) == 0 {
			return errors.New("门店未找到渠道门店代码信息")
		}

		if storeProduct.ProductChannelCategoryId == 0 {
			return errors.New("店内分类Id为空，请选择店内分类")
		}
		if storeProduct.ProductChannelTagId == 0 {
			return errors.New("第三方分类Id为空，请选择第三方分类")
		}
	}

	// 添加删除商品的判断，商品删除提示平台已经删除
	isTrue, _ := engine.SQL("select * from  product where is_del = 1  and id = ?  ", storeProduct.ProductId).Exist()
	if isTrue {
		return errors.New(storeProduct.ProductId + "平台商品已经被删除或者停用")
	}

	// 基础验证
	if len(storeProduct.ProductSkuId) == 0 {
		return errors.New("商品没有找到SkuId")
	}
	if len(storeProduct.StoreFinanceCode) == 0 {
		return errors.New("未找到门店财务代码")
	}

	// 判断一下仓库类型
	switch storeProduct.StoreWarehouseCategory {
	case 1: // 电商仓
		if channelId != ChannelAwenId && storeProduct.ProductSkuStorePrice == 0 {
			return errors.New(storeProduct.ProductId + " 门店仓价格为必填项")
		}
		if len(storeProduct.ProductA8SkuId) == 0 {
			return errors.New(storeProduct.ProductId + " 查询不到A8货号信息，无法上架")
		}
	case 3: // 门店仓
		if channelId != ChannelAwenId && storeProduct.ProductSkuStorePrice == 0 {
			return errors.New(storeProduct.ProductId + " 门店仓价格为必填项")
		}
		if len(storeProduct.ProductZilongId) == 0 {
			return errors.New(storeProduct.ProductId + " 查询不到子龙货号信息，无法上架")
		}
	case 4, 5: //前置仓
		if channelId != ChannelAwenId && storeProduct.ProductSkuPreposePrice == 0 {
			return errors.New(storeProduct.ProductId + " 前置仓价格为必填项")
		}
		if len(storeProduct.ProductA8SkuId) == 0 {
			return errors.New(storeProduct.ProductId + " 查询不到A8货号信息，无法上架")
		}
	default:
		return errors.New(storeProduct.StoreFinanceCode + " 门店仓库类型不正确")
	}
	if storeProduct.ProductType == 1 { // 实物商品
		//todo 校验商品库存信息 子商品不需要在查询库存了，组合商品的库存已经全部查询出来了
		stock, err := GetStockInfoBySkuCodeAndShopId(int32(channelId), cast.ToInt32(storeProduct.ProductSkuId), storeProduct.StoreFinanceCode, args[0])
		if err != nil {
			return errors.New(fmt.Sprintf("查询库存失败 商品%s, err:%s", storeProduct.ProductId, err.Error()))
		}
		if stock == 0 {
			return errors.New(fmt.Sprintf("商品 %s 库存为0，无法上架", storeProduct.ProductId))
		} else {
			storeProduct.ProductStock = int(stock)
		}
		if storeProduct.ProductWeight == 0 {
			return errors.New("商品重量不能为空")
		}
	} else if storeProduct.ProductType == 2 { // 虚拟商品
		var isBefore = storeProduct.ProductGroupTermEndDate.Before(time.Now())
		if isBefore {
			return errors.New(fmt.Sprintf("商品 %s 已经过期，无法上架", storeProduct.ProductId))
		}
		// 虚拟商品的应用范围 bj接口
		product := ChannelProductUp_StoreProduct{
			ProductSkuId: storeProduct.ProductSkuId,
		}
		useRange, err := CheckVirtualProductUseRange(&product, channelId)
		if err != nil {
			return errors.New(fmt.Sprintf("虚拟商品的应用范围 bj接口查询异常：%s", storeProduct.ProductId))
		}
		if _, ok := useRange[storeProduct.StoreFinanceCode]; !ok {
			return errors.New(fmt.Sprintf("虚拟商品的应用范围不在门店的应用范围或者商品在门店已经停售 %s : %s", storeProduct.ProductId, storeProduct.StoreFinanceCode))
		}

	} else if storeProduct.ProductType == 3 { // 组合商品

		if storeProduct.StoreWarehouseCategory == 3 {
			if _, ok := storeProduct.ProductGroupUseRang[3]; !ok {
				return errors.New("组合商品不能上架到门店仓")
			}
		}
		if storeProduct.StoreWarehouseCategory == 4 {
			if _, ok := storeProduct.ProductGroupUseRang[2]; !ok {
				return errors.New("组合商品不能上架到前置仓")
			}
		}
	}
	return nil
}

// 上架到阿闻
func (this *BatchChannelProductUpDown) upProductToAwen(storeProduct *Batch_ChannelProductUp_StoreProduct) error {
	var tran = engine.NewSession()
	defer tran.Close()
	// 开始事务
	err := tran.Begin()
	if err != nil {
		return err
	}

	// 查询快照
	var productSnapshot models.ChannelProductSnapshot
	_, err = engine.Where("channel_id=?", this.ChannelId).Where("finance_code=?", storeProduct.StoreFinanceCode).Where("product_id=?", storeProduct.ProductId).Get(&productSnapshot)
	if err != nil {
		return errors.New("获取商品快照失败")
	}
	if productSnapshot.Id == 0 {
		// 保存快好
		productSnapshot.ChannelId = this.ChannelId
		productSnapshot.FinanceCode = storeProduct.StoreFinanceCode
		productSnapshot.ProductId = cast.ToInt32(storeProduct.ProductId)
		productSnapshot.CreateDate = time.Now()
		productSnapshot.UserNo = this.UserNo

		in := &pc.ChannelProductSnapshotRequest{
			FinanceCode: storeProduct.StoreFinanceCode,
			ChannelId:   int32(this.ChannelId),
			ProductId:   []int32{cast.ToInt32(storeProduct.ProductId)},
		}
		var product Product
		result, err := product.QueryChannelProductSnapshot(context.Background(), in)
		if err != nil {
			return errors.New("获取新的商品快照失败")
		} else {
			if len(result.Details) > 0 {
				productSnapshot.JsonData = result.Details[0].JsonData
			}
		}

		// 保存
		_, err = tran.Insert(&productSnapshot)
		if err != nil {
			return errors.New(fmt.Sprintf("保存新的商品快照失败,%s", err.Error()))
		}
	}
	var jsonData pc.ChannelProductRequest
	err = json.Unmarshal([]byte(productSnapshot.JsonData), &jsonData)
	if err != nil {
		return err
	}
	//查询上架信息
	var model models.ChannelStoreProduct
	_, err = engine.Where("channel_id=?", this.ChannelId).And("finance_code=?", storeProduct.StoreFinanceCode).And("product_id=?", storeProduct.ProductId).Get(&model)
	if err != nil {
		return err
	}
	if model.Id > 0 {
		model.UpDownState = 1
	} else {
		model.UpDownState = 1
		model.ChannelId = this.ChannelId
		model.FinanceCode = storeProduct.StoreFinanceCode
		model.ProductId = cast.ToInt(storeProduct.ProductId)
		model.ChannelCategoryId = int(jsonData.Product.ChannelCategoryId)
		model.ChannelCategoryName = jsonData.Product.ChannelCategoryName
		model.CreateDate = time.Now()
		model.UpdateDate = time.Now()
		model.SnapshotId = productSnapshot.Id
		model.IsRecommend = int(jsonData.Product.IsRecommend)
		model.Name = jsonData.Product.Name
		model.SkuId = int(jsonData.SkuInfo[0].SkuId)
		if storeProduct.StoreWarehouseCategory == 3 {
			model.MarketPrice = int(storeProduct.ProductSkuStorePrice)
		}
		if storeProduct.StoreWarehouseCategory == 4 {
			model.MarketPrice = int(storeProduct.ProductSkuPreposePrice)
		}
		_, err := tran.Insert(&model)
		if err != nil {
			return err
		}
	}
	// 提交事物
	err = tran.Commit()

	//更新product_has_stock表 放在外面不然拿不到上架表的id
	err = InsertOrUpdateProductHasStock(model, storeProduct.HasStockData)
	if err != nil {
		return err
	}

	if err != nil {
		return err
	}
	return nil
}

// 上架到美团
func (this *BatchChannelProductUpDown) upProductToMt(storeProducts []*Batch_ChannelProductUp_StoreProduct, client *et.Client) (*et.RetailSellStatusResult, error) {
	logPrefix := fmt.Sprintf("上架到美团，财务编码为%s", this.FinanceCode)
	// 请求参数
	mtReq := &et.RetailSellStatusRequest{
		AppPoiCode: this.StoreMap[this.FinanceCode].ChannelStoreId,
		FoodData:   []*et.AppFoodCode{},
		SellStatus: 0,
	}

	for e := range storeProducts {
		storeProduct := storeProducts[e]
		mtReq.FoodData = append(mtReq.FoodData, &et.AppFoodCode{AppFoodCode: storeProduct.ProductId})
	}

	storeMasterId, err := GetAppChannelByFinanceCode(this.FinanceCode)
	if err != nil {
		glog.Error("upProductToMt", "GetAppChannelByFinanceCode failed,", kit.JsonEncode(this.FinanceCode))
		return nil, errors.New("GetAppChannelByFinanceCode failed")
	}
	mtReq.StoreMasterId = storeMasterId

	var canCallThird bool
	for i := range storeProducts {
		if CanCallThirdApi(cast.ToInt(storeProducts[i].ProductId), ChannelMtId, storeProducts[i].StoreFinanceCode) {
			canCallThird = true
		}
	}
	if !canCallThird {
		glog.Error(logPrefix, "没有第三方商品id====1", "入参为：", kit.JsonEncode(storeProducts), "this的数据为", kit.JsonEncode(this))
		return nil, errors.New("商品未在第三方创建，请先编辑后再进行上架")
	}

	// 调用远程更新状态
	res, err := client.RPC.RetailSellStatus(client.Ctx, mtReq)
	glog.Info("上架-1111上下架到美团返回数据", kit.JsonEncode(res), ",入参：", kit.JsonEncode(mtReq), "错误err：", kit.JsonEncode(err))

	if err != nil {
		return res, errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	}

	if res.Code != 200 {
		return res, errors.New(res.Error.Msg)
	}
	return res, nil
}

// 上架到饿了么
func (this *BatchChannelProductUpDown) upProductToElm(storeProduct []*Batch_ChannelProductUp_StoreProduct, client *et.Client) (*et.ELMBaseResponse, error) {
	// 调用远程接口
	var elmOnlineResponse = &et.ELMBaseResponse{}
	//查询门店的appChannel
	appChannel, err := GetAppChannelByFinanceCode(this.FinanceCode)
	if err != nil {
		return elmOnlineResponse, err
	}

	if appChannel == 0 {
		return elmOnlineResponse, errors.New("饿了么上架获取appChannel值错误")
	}

	vo := &et.UpdateElmShopSkuPriceRequest{
		ShopId:     storeProduct[0].StoreChannelFinanceCodes,
		AppChannel: appChannel,
	}

	var skuStr []string
	var canCallThird bool
	skuProductIds := make(map[int]int)
	for i := range storeProduct {
		skuStr = append(skuStr, storeProduct[i].ProductSkuId)
		skuProductIds[cast.ToInt(storeProduct[i].ProductSkuId)] = cast.ToInt(storeProduct[i].ProductId)
		if CanCallThirdApi(cast.ToInt(storeProduct[i].ProductId), ChannelElmId, storeProduct[i].StoreFinanceCode) {
			canCallThird = true
		}
	}
	if !canCallThird {
		glog.Error("批量上架到饿了么，没有第三方商品id====3,入参为：", kit.JsonEncode(storeProduct), "this数据为：", kit.JsonEncode(this))
		return elmOnlineResponse, errors.New("商品未在第三方创建，无法上架到饿了么")
	}
	vo.CustomSkuId = strings.Join(skuStr, ",")

	elmOnlineResponse, err = client.ELMPRODUCT.OnlineElmShopSku(context.Background(), vo)
	glog.Info("上下架商品到饿了么返回数据：", kit.JsonEncode(elmOnlineResponse), "请求数据为：", kit.JsonEncode(vo), ",err为：", kit.JsonEncode(err))
	if err != nil {
		return elmOnlineResponse, errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	}

	return elmOnlineResponse, nil
}

// 上架到京东
func (this *BatchChannelProductUpDown) upProductToJd(storeProducts []*Batch_ChannelProductUp_StoreProduct, client *et.Client) (*et.BatchUpdateVendibilityResponse, error) {

	var response = &et.BatchUpdateVendibilityResponse{} // 保存同步失败的商品信息

	storeMasterId, err := GetAppChannelByFinanceCode(this.FinanceCode)
	if err != nil {
		glog.Error("upProductToJd,", "GetAppChannelByFinanceCode,", this.FinanceCode, err.Error())
		return response, err
	}

	syncData := &et.BatchUpdateVendibilityRequest{
		StationNo:     storeProducts[0].StoreChannelFinanceCodes, // 第三方门店id
		UserPin:       "xrp",
		StoreMasterId: storeMasterId,
	}

	// productId和skuId的集合
	m := make(map[string]string, len(storeProducts))

	// 需要同步库存的数据
	UpdateListStock := []*et.SkuStockList{}

	for i := range storeProducts {
		storeProduct := storeProducts[i]
		m[storeProduct.ProductId] = storeProduct.ProductSkuId
		syncData.StockVendibilityList = append(syncData.StockVendibilityList, &et.JddjStockVendibility{DoSale: true, OutSkuId: storeProduct.ProductSkuId})

		if storeProduct.ProductStock >= 0 {
			UpdateListStock = append(UpdateListStock, &et.SkuStockList{OutSkuId: storeProduct.ProductSkuId, StockQty: int32(storeProduct.ProductStock)})
		}
	}

	res, err := client.JddjProduct.BatchUpdateVendibility(client.Ctx, syncData)
	if err != nil {
		return response, errors.New(fmt.Sprintf("BatchUpdateVendibility,external通信失败,%s", err.Error()))
	} else if res.RetCode != "0" {
		return response, errors.New(res.RetMsg)
	} else {

		//记录所有的返回
		if len(res.Data) > 0 {
			response.Data = append(response.Data, res.Data...)
		}

		// 同步京东库存
		//构造请求
		updateStockReq := &et.UpdateStockRequest{
			StationNo:     storeProducts[0].StoreChannelFinanceCodes,
			UserPin:       "xrp",
			SkuStockList:  UpdateListStock,
			StoreMasterId: storeMasterId,
		}

		stockRes, err := client.JddjProduct.UpdateStock(client.Ctx, updateStockReq)
		if err != nil {
			return response, errors.New("UpdateStock,上架成功，同步京东库存失败" + err.Error())
		} else if res.RetCode != "0" {
			return response, errors.New(res.RetMsg)
		} else {
			if len(stockRes.Data) > 0 { // 记录所有的返回
				response.Data = append(response.Data, res.Data...)
			}
		}
	}
	return response, nil
}

// 上架后的通用操作
func (this *BatchChannelProductUpDown) afterUp(storeProduct *ChannelProductUp_StoreProduct, channelProductPriceSync ChannelProductPriceSync, in *ic.GetStockInfoResponse, channelId int32) error {
	prefix := "上架后的通用操作afterUp: "
	// 查询快照
	if this.ChannelId != ChannelAwenId && this.ChannelId != ChannelDigitalHealth {
		var channelProductSnapshot models.ChannelProductSnapshot
		_, err := engine.Where("channel_id=?", this.ChannelId).Where("product_id=?", storeProduct.ProductId).Where("finance_code=?", storeProduct.StoreFinanceCode).Get(&channelProductSnapshot)
		if err != nil {
			return err
		}

		if channelProductSnapshot.Id > 0 {
			// 查询渠道商品上架表
			var channelStoreProduct models.ChannelStoreProduct
			_, err := engine.Where("channel_id=?", this.ChannelId).Where("product_id=?", storeProduct.ProductId).Where("finance_code=?", storeProduct.StoreFinanceCode).Get(&channelStoreProduct)
			if err != nil {
				return err
			}
			if channelStoreProduct.Id == 0 { // 不存在上架记录则新增
				// 解析sku信息
				var channelProductRequest pc.ChannelProductRequest
				err = json.Unmarshal([]byte(channelProductSnapshot.JsonData), &channelProductRequest)
				// 新增
				channelStoreProduct.ChannelId = this.ChannelId
				channelStoreProduct.FinanceCode = storeProduct.StoreFinanceCode
				channelStoreProduct.ProductId = cast.ToInt(storeProduct.ProductId)
				channelStoreProduct.ChannelCategoryId = int(channelProductRequest.Product.ChannelCategoryId)
				channelStoreProduct.ChannelCategoryName = channelProductRequest.Product.ChannelCategoryName
				channelStoreProduct.IsRecommend = 0
				channelStoreProduct.UpDownState = 1
				channelStoreProduct.HasStock = 1
				channelStoreProduct.SnapshotId = channelProductSnapshot.Id
				channelStoreProduct.Name = channelProductRequest.Product.Name
				channelStoreProduct.SkuId = int(channelProductRequest.SkuInfo[0].SkuId)
				_, err := engine.Insert(&channelStoreProduct)
				if err != nil {
					return err
				}
			} else { // 已经存在上架记录则更新上架状态
				channelStoreProduct.UpDownState = 1
				channelStoreProduct.HasStock = 1
				// 更新上架状态
				_, err := engine.ID(channelStoreProduct.Id).Cols("up_down_state,has_stock").Update(&channelStoreProduct)
				if err != nil {
					return err
				}
			}

			//更新product_has_stock表
			err = InsertOrUpdateProductHasStock(channelStoreProduct, in)
			if err != nil {
				return err
			}

		}
	}

	// v6.0 上架前需要同步价格到各个渠道 放到前面去处理
	if this.IsSyncPrice {

		if this.IsSwitchingWarehouse && this.ChannelId != ChannelAwenId {
			// 如果是切换仓库的话直接退出，因为切换仓库时在编辑的时候已经同步了价格(除了阿闻渠道)，这里不需要再次调用了
			return nil
		}

		if channelProductPriceSync.ProductSyncPrice == 0 {
			return errors.New("没有获取到价格或者价格为0，无法同步第三方")
		}
		// 同步价格到第三方
		glog.Info(prefix, "同步价格到第三方渠道：", kit.JsonEncode(channelProductPriceSync))
		if channelProductPriceSync.ChannelId == ChannelAwenId { // 阿闻
			return channelProductPriceSync.syncPriceToAwen()
		} else {
			// A . 先更新快照或上架表的价格
			err := channelProductPriceSync.syncPriceToChannelProductSnap()
			if err != nil {
				return errors.New(fmt.Sprintf("更新快照价格失败:%s", err.Error()))
			}
			// B. 更新第三发价格信息
			if channelProductPriceSync.ChannelId == ChannelMtId { // 美团
				return channelProductPriceSync.syncPriceToMt()
			}
			if channelProductPriceSync.ChannelId == ChannelElmId { // 饿了么
				return channelProductPriceSync.syncPriceToElm()
			}
			if channelProductPriceSync.ChannelId == ChannelJddjId { // 京东
				return channelProductPriceSync.syncPriceToJd()
			}
		}
	}

	return nil
}

// 渠道商品批量下架，支持单门店多门店
func (c *Product) BatchDownChannelProduct(ctx context.Context, request *pc.UpDownChannelProductRequest) (*pc.BaseResponse, error) {
	prefix := "渠道商品批量下架BatchDownChannelProduct: "
	var response = &pc.BaseResponse{Code: 400}
	// 上下架类封装
	var channelProductUpDown BatchChannelProductUpDownBase
	channelProductUpDown.ProductIds = request.ProductId
	channelProductUpDown.ChannelId = int(request.ChannelId)
	channelProductUpDown.FinanceCodes = request.FinanceCode
	channelProductUpDown.UserNo = request.UserNo
	userInfo := loadLoginUserInfo(ctx) // 取名字保存
	if userInfo != nil {
		channelProductUpDown.UserName = userInfo.UserName
	}
	// 执行批量上架
	channelProductUpDown.BatchDownProduct()

	response.UpDownDetail = channelProductUpDown.UpResult
	glog.Info(prefix, "批量下架信息返回数量： ", len(response.UpDownDetail))

	return response, nil
}

//批量下架调整，支持按照门店批量执行
/**
v6.6.2目前的这个下架接口只有在阿闻渠道且仓库的时候会调用，其他渠道不会调用这里，如果需要调用这里，需要测试其他渠道的批量下架方法
*/
func (base *BatchChannelProductUpDownBase) BatchDownProduct() {

	prefix := "BatchDownProduct批量下架："
	defer func() {
		// 出现异常则记录
		if err := recover(); err != nil {
			base.UnknownError = errors.New("出现了未处理的错误")
			glog.Error(prefix, "下架panic", err)
		}
	}()
	// 初始化参数
	for i := range base.FinanceCodes {
		//按照门店来批量初始化

		financeCodeOne := base.FinanceCodes[i]
		down := BatchChannelProductUpDown{
			ProductIds:  base.ProductIds,
			ChannelId:   base.ChannelId,
			FinanceCode: financeCodeOne,
			UserNo:      base.UserNo,
			UserName:    base.UserName,
			IsSyncPrice: base.IsSyncPrice,
			Ctx:         base.Ctx,
			TaskId:      base.TaskId,
		}

		if base.ChannelId < 1 && base.ChannelId > 4 {
			glog.Error(prefix, "不支持的渠道Id", base.ChannelId)
			continue
		}

		var (
			allProductIds []int32 // 门店下面所有的批量的商品的商品数

			failPID0 []int32 // 已经下过架的商品

		)
		for _, id := range down.ProductIds {
			allProductIds = append(allProductIds, cast.ToInt32(id))

		}
		//初始化之前先排除一些不符合要求的商品信息
		//1：排除已经下过架的
		engine.ShowSQL(true)

		engine.Table("channel_store_product").Where("up_down_state = ?", 0).
			Where("channel_id=?", down.ChannelId).Where("finance_code=?", down.FinanceCode).
			In("product_id", allProductIds).Select("product_id").Find(&failPID0)

		setAll := utils.NewSet(allProductIds...)
		set0 := utils.NewSet(failPID0...)

		setAll = setAll.Minus(set0)

		//获取失败的商品的skuid，前端展示使用
		var faildSkuIds []int32
		faildSkuIds = append(faildSkuIds, failPID0...)

		productIdRelationSkuId := make(map[string]string, 0)
		if len(faildSkuIds) > 0 {
			product := make([]models.FailSkuProduct, 0)
			engine.Table("sku_third").In("product_id", faildSkuIds).Find(&product)
			for i := range product {
				skuProduct := product[i]
				productIdRelationSkuId[skuProduct.ProductId] = skuProduct.SkuId
			}
		}

		for _, id := range failPID0 {
			//排除已经下过架的商品
			sId := cast.ToString(id)
			skuId := productIdRelationSkuId[sId]
			downERROR := pc.UpDownERROR{
				FinanceCode: down.FinanceCode,
				ProductId:   sId,
				SkuId:       skuId,
				Message:     fmt.Sprintf("排除已经下过架的商品 %d", id),
			}
			down.UpResult = append(down.UpResult, &downERROR)
		}

		// 初始化门店和财务编码信息以及仓库的信息
		down.initWarehouseAndFinanceCode(base.ChannelId)

		// 初始化商品的数据信息
		down.init()

		// 执行下架操作
		switch down.ChannelId {
		case ChannelAwenId:

			//阿文接口限流每次200组同步一次
			var syncAwenChannelProduct []*Batch_ChannelProductUp_StoreProduct

			for ie, storeProduct := range down.baseUpDownProduct {

				bres := pc.UpDownERROR{
					FinanceCode: storeProduct.StoreFinanceCode,
					ProductId:   storeProduct.ProductId,
					SkuId:       storeProduct.ProductSkuId,
				}
				down.UpResult = append(down.UpResult, &bres)

				// 阿闻改成批量下架一次下架200个商品
				if len(syncAwenChannelProduct) >= 200 || ie == len(down.baseUpDownProduct)-1 {

					err := down.downProductToAwen(financeCodeOne, syncAwenChannelProduct)
					if err != nil { // 记录异常信息
						glog.Error(prefix, "downProductToAwen失败:", err.Error())
						bres.Message = fmt.Sprintf("下架失败:%s", err.Error())
					} else {
						bres.IsSuccess = true
					}
					// 清空 继续循环处理
					syncAwenChannelProduct = make([]*Batch_ChannelProductUp_StoreProduct, 0)

				}

			}

		case ChannelMtId: // 美团

			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()

			//美团接口限流每次200组同步一次
			var syncMtChannelProduct []*Batch_ChannelProductUp_StoreProduct

			// mt同步回调的结果处理
			var resultsToMt []*Batch_ChannelProductUp_Result

			for ie, storeProduct := range down.baseUpDownProduct {

				bres := Batch_ChannelProductUp_Result{StoreProduct: storeProduct}
				resultsToMt = append(resultsToMt, &bres)

				upReres := pc.UpDownERROR{
					FinanceCode: storeProduct.StoreFinanceCode,
					ProductId:   storeProduct.ProductId,
					SkuId:       storeProduct.ProductSkuId,
				}
				down.UpResult = append(down.UpResult, &upReres)

				syncMtChannelProduct = append(syncMtChannelProduct, storeProduct)

				if len(syncMtChannelProduct) >= 200 || ie == len(down.baseUpDownProduct)-1 {

					result, err := down.downProductToMt(syncMtChannelProduct, grpcClient)
					if err != nil { // 全部失败
						for e := range resultsToMt {
							resultsToMt[e].Message = fmt.Sprintf("下架失败:%s", err.Error())
						}

					} else if len(result.Msg) <= 0 { // 全部成功
						for e := range resultsToMt {
							resultsToMt[e].IsSuccess = true
						}
					} else { // 部分失败
						mt := []models.MsgMT{}

						mtFailMsg := make(map[string]string, 0)
						// 所有的id
						var allMtIds []int32
						//失败的
						var failMtIds []int32
						// 成功的美团id
						var successMtIds []int32
						if len(result.Msg) > 0 {
							err := json.Unmarshal([]byte(result.Msg), &mt)
							if err != nil {
								glog.Error(prefix, err.Error())
							}
							//失败的
							if len(mt) > 0 {
								for e := range mt {
									failMtIds = append(failMtIds, cast.ToInt32(mt[e].AppFoodCode))
									mtFailMsg[mt[e].AppFoodCode] = mt[e].Msg
									UpdateProductThirdSyncErr(cast.ToInt(mt[e].AppFoodCode), ChannelMtId, storeProduct.StoreFinanceCode, mt[e].Msg)

								}
							}
							for e := range syncMtChannelProduct {
								allMtIds = append(allMtIds, cast.ToInt32(syncMtChannelProduct[e].ProductId))
							}
							glog.Info(prefix, " failMtIds: ", failMtIds)
							setFails := utils.NewSet(failMtIds...)
							// 同步成功的id
							successMtIds = utils.NewSet(allMtIds...).Minus(setFails).List()
							for _, v := range successMtIds {
								UpdateProductThirdSyncErr(int(v), ChannelMtId, storeProduct.StoreFinanceCode, "")
							}
							glog.Info(prefix, " successMtIds:", successMtIds)
						}

						// 提示错误信息
						for e := range resultsToMt {
							for _, succ := range successMtIds {
								if cast.ToString(succ) == resultsToMt[e].StoreProduct.ProductId {
									resultsToMt[e].IsSuccess = true
									break
								}

							}

							for _, fail := range failMtIds {
								st := cast.ToString(fail)
								if st == resultsToMt[e].StoreProduct.ProductId {
									resultsToMt[e].Message = mtFailMsg[st]
									break
								}
							}
						}
					}

					// 下架后的操作
					err = down.BatchAfterDown(resultsToMt)
					if err != nil {
						glog.Error(prefix, " BatchAfterDown: ", err.Error())
					}

					// 全量返回的mt结果信息
					down.UpdateLastErrorMsg(resultsToMt)

					// 清空 继续循环处理
					resultsToMt = make([]*Batch_ChannelProductUp_Result, 0)
					syncMtChannelProduct = make([]*Batch_ChannelProductUp_StoreProduct, 0)

				}

			}

		case ChannelElmId: // 饿了么

			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()

			for ie, storeProduct := range down.baseUpDownProduct {

				data := Batch_ChannelProductUp_Result{StoreProduct: down.baseUpDownProduct[ie]}
				bres := pc.UpDownERROR{
					FinanceCode: storeProduct.StoreFinanceCode,
					ProductId:   storeProduct.ProductId,
					SkuId:       storeProduct.ProductSkuId,
				}

				down.UpResult = append(down.UpResult, &bres)

				err := down.downProductToElm(storeProduct, grpcClient)
				if err != nil {
					glog.Info(prefix, "下架失败", storeProduct.StoreFinanceCode, err.Error())
					down.UpResult[ie].Message = fmt.Sprintf("下架失败:%s", err.Error())
				} else {
					glog.Info(prefix, "下架成功", storeProduct.StoreFinanceCode)
					down.UpResult[ie].IsSuccess = true
				}

				// 下架后的操作
				err = down.BatchAfterDown([]*Batch_ChannelProductUp_Result{&data})
				if err != nil {
					glog.Error(prefix, err.Error())
				}

			}

		case ChannelJddjId: // 京东

			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()

			for ie, storeProduct := range down.baseUpDownProduct {

				bres := pc.UpDownERROR{
					FinanceCode: storeProduct.StoreFinanceCode,
					ProductId:   storeProduct.ProductId,
					SkuId:       storeProduct.ProductSkuId,
				}
				data := Batch_ChannelProductUp_Result{StoreProduct: down.baseUpDownProduct[ie]}

				down.UpResult = append(down.UpResult, &bres)
				err := down.downProductToJd(storeProduct, grpcClient)
				if err != nil {
					down.UpResult[ie].Message = fmt.Sprintf("下架失败:%s", err.Error())
				} else {
					down.UpResult[ie].IsSuccess = true
				}

				// 上架后的操作
				err = down.BatchAfterDown([]*Batch_ChannelProductUp_Result{&data})
				if err != nil {
					glog.Error(prefix, err.Error())
				}

			}
		}

		base.UpResult = append(base.UpResult, down.UpResult...)
	}

}

// 下架到美团
func (this *BatchChannelProductUpDown) downProductToMt(storeProducts []*Batch_ChannelProductUp_StoreProduct, client *et.Client) (*et.RetailSellStatusResult, error) {
	// 请求参数
	mtReq := &et.RetailSellStatusRequest{
		AppPoiCode: this.StoreMap[this.FinanceCode].ChannelStoreId,
		FoodData:   []*et.AppFoodCode{},
		SellStatus: 1,
	}

	for e := range storeProducts {
		storeProduct := storeProducts[e]
		mtReq.FoodData = append(mtReq.FoodData, &et.AppFoodCode{AppFoodCode: storeProduct.ProductId})
	}
	// 调用远程更新状态

	storeMasterId, err := GetAppChannelByFinanceCode(this.FinanceCode)
	if err != nil {
		glog.Error("upProductToMt", "GetAppChannelByFinanceCode failed,", kit.JsonEncode(this.FinanceCode))
		return nil, errors.New("GetAppChannelByFinanceCode failed")
	}
	mtReq.StoreMasterId = storeMasterId

	var canCallThird bool
	for i := range storeProducts {
		if CanCallThirdApi(cast.ToInt(storeProducts[i].ProductId), ChannelMtId, storeProducts[i].StoreFinanceCode) {
			canCallThird = true
		}
	}
	if !canCallThird {
		glog.Error("没有第三方商品id====4,入参为：", kit.JsonEncode(storeProducts), "，this数据为：", kit.JsonEncode(this))
		return nil, errors.New("无法下架到美团:商品未在第三方创建，请先编辑后再进行操作")
	}

	glog.Info("批量下架参数：", kit.JsonEncode(mtReq))
	res, err := client.RPC.RetailSellStatus(client.Ctx, mtReq)
	glog.Info("下架-2222上下架到美团返回数据", kit.JsonEncode(res), ",入参：", kit.JsonEncode(mtReq), "错误err：", kit.JsonEncode(err))

	if err != nil {
		return res, errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	}

	if res.Code != 200 {
		return res, errors.New(res.Msg)
	}
	return res, nil
}

// 下架到阿闻
func (this *BatchChannelProductUpDown) downProductToAwen(financeCode string, storeProducts []*Batch_ChannelProductUp_StoreProduct) error {

	var productIds []int
	for i := range storeProducts {
		productIds = append(productIds, cast.ToInt(storeProducts[i].ProductId))
	}

	// 查询上架记录
	var channelStoreProdct []models.ChannelStoreProduct
	err := engine.Where("channel_id=?", this.ChannelId).And("finance_code=?", financeCode).
		In("product_id", productIds).Find(&channelStoreProdct)
	if err != nil {
		return err
	} else {

		var ids []string
		for i := range channelStoreProdct {
			ids = append(ids, cast.ToString(channelStoreProdct[i].Id))
		}

		sql := "update dc_product.channel_store_product a set up_down_state = 0 , update_date = now() where id in (%s)"
		sprintfSql := fmt.Sprintf(sql, strings.Join(ids, ","))

		if len(ids) > 0 {
			_, err = engine.Exec(sprintfSql)
			if err != nil {
				return err
			}
		}

		go func() {
			for i := range storeProducts {
				product := storeProducts[i]
				new(Product).SaveChannelProductLogDetail(this.ChannelId, cast.ToInt(product.ProductId),
					enum.RecordTypeDownOne, product.StoreFinanceCode, "cutWarehouse", this.UserName)
			}
		}()
	}
	return nil
}

// 下架到京东
func (this *BatchChannelProductUpDown) downProductToJd(storeProduct *Batch_ChannelProductUp_StoreProduct, client *et.Client) error {
	storeMasterId, err := GetAppChannelByFinanceCode(storeProduct.StoreFinanceCode)
	if err != nil {
		glog.Error("downProductToJd,", "GetAppChannelByFinanceCode,", storeProduct.StoreFinanceCode, err)
		return err
	}

	syncData := &et.BatchUpdateVendibilityRequest{
		StationNo:     storeProduct.StoreChannelFinanceCodes,
		UserPin:       "xrp",
		StoreMasterId: storeMasterId,
	}
	syncData.StockVendibilityList = append(syncData.StockVendibilityList, &et.JddjStockVendibility{DoSale: false, OutSkuId: storeProduct.ProductSkuId})

	res, err := client.JddjProduct.BatchUpdateVendibility(client.Ctx, syncData)
	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else if res.RetCode != "0" {
		return errors.New(res.RetMsg)
	}
	return nil
}

// 下架到饿了么
func (this *BatchChannelProductUpDown) downProductToElm(storeProduct *Batch_ChannelProductUp_StoreProduct, client *et.Client) error {

	// 调用远程接口

	appChannel, err := GetAppChannelByFinanceCode(storeProduct.StoreFinanceCode)
	if err != nil {
		return err
	}

	if appChannel == 0 {
		err = errors.New("获取appChannel值错误")
		return err
	}
	elmIn := &et.UpdateElmShopSkuPriceRequest{
		ShopId:      storeProduct.StoreChannelFinanceCodes,
		CustomSkuId: storeProduct.ProductSkuId,
		AppChannel:  appChannel,
	}
	if !CanCallThirdApi(cast.ToInt(storeProduct.ProductId), ChannelElmId, storeProduct.StoreFinanceCode) {
		glog.Error("没有第三方商品id====5,入参为：", kit.JsonEncode(storeProduct), "，this的数据为：", kit.JsonEncode(this))
		return errors.New("商品未在第三方创建，请先编辑后再进行操作")
	}
	if len(storeProduct.StoreChannelFinanceCodes) == 0 {
		glog.Error("饿了么门店id为空2")
		return errors.New("饿了么门店id为空")
	}
	elmOnlineResponse, err := client.ELMPRODUCT.OfflineElmShopSkuOne(client.Ctx, elmIn)
	glog.Info("上下架商品到饿了么返回数据(单个商品饿了么下架)：：：", kit.JsonEncode(elmOnlineResponse), "请求数据为：", kit.JsonEncode(elmIn), ",err为：", kit.JsonEncode(err))

	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	}

	UpdateProductThirdSyncErr(cast.ToInt(storeProduct.ProductId), ChannelElmId, storeProduct.StoreFinanceCode, elmOnlineResponse.Message)
	if elmOnlineResponse.Code != 200 {
		return errors.New(elmOnlineResponse.Message)
	}
	return nil
}

// v6.3.6 批量下架后操作
func (this *BatchChannelProductUpDown) BatchAfterDown(data []*Batch_ChannelProductUp_Result) error {

	var successfulIds []string
	for e := range data {
		if data[e].IsSuccess {
			successfulIds = append(successfulIds, data[e].StoreProduct.ProductId)
			go func() {
				new(Product).SaveChannelProductLogDetail(this.ChannelId, cast.ToInt(data[e].StoreProduct.ProductId),
					enum.RecordTypeDownOne, data[e].StoreProduct.StoreFinanceCode, "cutWarehouse", this.UserName)
			}()
		}
	}

	var channelStoreProductIds []string
	err := engine.Table("channel_store_product").Where("channel_id=?", this.ChannelId).
		Where("finance_code=?", this.FinanceCode).In("product_id", successfulIds).Select("id").
		Find(&channelStoreProductIds)
	if err != nil {
		return err
	} else {
		if len(channelStoreProductIds) > 0 {

			join := strings.Join(channelStoreProductIds, "','")
			glog.Info("join: ", join, " len:", len(channelStoreProductIds))
			engine.Exec("update dc_product.channel_store_product a set a.up_down_state =0 , a.update_date = now() where id in ('" + join + "')")
		}
	}
	return nil
}

// 排除上架前的前置条件
func (this *BatchChannelProductUpDown) ExclusionUpProductFirst(allProductIds []int32, productIdRelationSkuId map[string]string) error {

	prefix := "排除上架前的前置条件ExclusionUpProduct: "
	var (
		failPID0  []int32 // 批量排除虚拟商品
		failPID1  []int32 // 切换仓库排除组合商品
		failPID2  []int32 // 批量排除已经上架过的
		failPID3  []int32 // 切换仓库排除价格表里面没有价格的
		failPID6  []int32 // 批量排除平台已经被删除的商品
		failPID9  []int32 // 批量排除平台和渠道的第三方货号不一致的
		failPID10 []int32 // 美团切仓校验非瑞鹏和代运营配置的商品数据
		failPID11 []int32 // 排除门店仓子龙不可销、无效处方药
		drugIds   []int32 // 排除药品
	)

	setAll := utils.NewSet(allProductIds...)

	//engine.ShowSQL(true)
	conn := NewDbConn()

	// 排除可上架商品
	configProductMap, err := this.ExcludeAgencyConfig()
	if err != nil {
		glog.Error(prefix, " ExcludeAgencyConfig: ", err.Error())
		return err
	}
	if len(configProductMap) > 0 {
		for ip := range allProductIds {
			if _, ok := configProductMap[cast.ToInt(allProductIds[ip])]; !ok {
				failPID10 = append(failPID10, allProductIds[ip])
			}
		}
	}
	set10 := utils.NewSet(failPID10...)
	setAll = setAll.Minus(set10)
	// 将错误保存下来，导出使用
	this.HandlingMsg(failPID10, productIdRelationSkuId, "该商品禁止上架，如需上架请联系总部运营")

	//初始化之前先排除一些不符合要求的商品信息
	//1：批量排除虚拟商品
	conn.Table("product").Select("id").Where("product_type = 2").In("id", setAll.List()).Find(&failPID0)
	set0 := utils.NewSet(failPID0...)
	setAll = setAll.Minus(set0)
	this.HandlingMsg(failPID0, productIdRelationSkuId, "排除虚拟商品上架")

	// 3 校验商品已经上过架了
	conn.Table("channel_store_product").Select("product_id").Where("channel_id=?", this.ChannelId).In("product_id", setAll.List()).
		Where("finance_code=?", this.FinanceCode).Where("up_down_state = ?", 1).Find(&failPID2)

	set2 := utils.NewSet(failPID2...)
	setAll = setAll.Minus(set2)
	this.HandlingMsg(failPID2, productIdRelationSkuId, "批量排除已经上架过的")

	// 4 商品已经在平台删除了
	// 添加删除商品的判断，平台商品已经被删除或者停用
	conn.Table("product").Select("id").Where("is_del=?", 1).In("id", allProductIds).Find(&failPID6)
	set6 := utils.NewSet(failPID6...)
	setAll = setAll.Minus(set6)
	this.HandlingMsg(failPID6, productIdRelationSkuId, "批量排除平台已经被删除的商品")

	//var drugIds []int32
	conn.Table("product").In("id", allProductIds).Where("is_drugs = 1").Select("id").Find(&drugIds)
	//医疗互联网的不允许上架药品
	if this.ChannelId != ChannelDigitalHealth {
		if _, ok := this.StoreDrugInfoMap[this.FinanceCode]; !ok {
			glog.Info("药品不允许上架到渠道-", enum.ChannelMap[this.ChannelId])
			set7 := utils.NewSet(drugIds...)
			setAll = setAll.Minus(set7)
			this.HandlingMsg(drugIds, productIdRelationSkuId, fmt.Sprintf("药品不允许上架到%s", enum.ChannelMap[this.ChannelId]))
		}

	}

	//  批量基础校验 平台和渠道的货号对应不上的
	if data, ok := this.WarehouseMap[this.FinanceCode]; ok {
		if data.Category == 4 || data.Category == 5 || data.Category == 1 { // 前置仓 or 电商仓
			conn.Table("dc_product.channel_sku_third").Alias("a").
				Join("inner", "dc_product.sku_third b ", "a.product_id = b.product_id and a.sku_id = b.sku_id and a.erp_id = b.erp_id ").
				Where("a.third_sku_id != b.third_sku_id ").And("a.erp_id = ?", 2).
				And("a.channel_id = ?", this.ChannelId).In("a.product_id", allProductIds).Select("a.product_id").Find(&failPID9)

			//存在药品
			if len(drugIds) > 0 {
				// 仓库没有售药资质
				// if data.SellDrugs == 0 {
				// 	setAll = setAll.Minus(utils.NewSet(drugIds...))
				// 	this.HandlingMsg(drugIds, productIdRelationSkuId, "批量基础校验 没有售药资质的前置仓不允许上架药品")
				// } else { // 判断处方药
				var ids []int
				for _, id := range drugIds {
					ids = append(ids, cast.ToInt(id))
				}
				if upInfos, err := QueryAwenProductUpInfoByIds(ids, data.Category); err != nil {
					glog.Error("ExclusionUpProductFirst QueryAwenProductUpInfoByIds ", err.Error())
				} else if len(upInfos) > 0 {
					for _, info := range upInfos {
						if err := info.Check(data.Category, "", data.ChannelId, data.WarehouseCode); err != nil {
							failPID11 = append(failPID11, cast.ToInt32(info.ProductId))
						}
					}
				}
				if len(failPID11) > 0 {
					setAll = setAll.Minus(utils.NewSet(failPID11...))
					this.HandlingMsg(failPID11, productIdRelationSkuId, "批量基础校验 处方药校验失败")
				}
				// }
			}
		}
		if data.Category == 3 { // 门店仓
			conn.Table("dc_product.channel_sku_third").Alias("a").
				Join("inner", "dc_product.sku_third b ", "a.product_id = b.product_id and a.sku_id = b.sku_id and a.erp_id = b.erp_id ").
				Where("a.third_sku_id != b.third_sku_id ").And("a.erp_id = ?", 4).
				And("a.channel_id = ?", this.ChannelId).In("a.product_id", allProductIds).Select("a.product_id").Find(&failPID9)

			ids := make([]int, len(allProductIds))
			for i, id := range allProductIds {
				ids[i] = cast.ToInt(id)
			}
			if upInfos, err := QueryAwenProductUpInfoByIds(ids, data.Category); err != nil {
				glog.Error("ExclusionUpProductFirst QueryAwenProductUpInfoByIds ", err.Error())
			} else if len(upInfos) > 0 {
				for _, info := range upInfos {
					if err := info.Check(data.Category, this.FinanceCode, this.ChannelId, data.WarehouseCode); err != nil {
						failPID11 = append(failPID11, cast.ToInt32(info.ProductId))
					}
				}
			}

			if len(failPID11) > 0 {
				setAll = setAll.Minus(utils.NewSet(failPID11...))
				this.HandlingMsg(failPID11, productIdRelationSkuId, "批量基础校验 子龙不可销或处方药校验失败")
			}
		}
	}

	set9 := utils.NewSet(failPID9...)
	setAll = setAll.Minus(set9)
	this.HandlingMsg(failPID9, productIdRelationSkuId, "批量基础校验平台和渠道的货号对应不上的")

	if this.IsSwitchingWarehouse {
		//2：切换仓库排除组合商品
		conn.Table("product").Select("id").Where("product_type = 3").In("id", setAll.List()).Find(&failPID1)
		set1 := utils.NewSet(failPID1...)
		setAll = setAll.Minus(set1)
		this.HandlingMsg(failPID0, productIdRelationSkuId, "切换仓库排除虚拟商品上架")

		// 切换仓库只去取价格表有价格的数据进行上架
		//按照仓库类型取出价格数据
		if data, ok := this.WarehouseMap[this.FinanceCode]; ok {
			nowList := setAll.List()
			// 切换仓库校验价格，在价格表里面没有价格的不让它上架
			failPID3, err = this.CheckPrice(nowList, data)
			if err != nil {
				return err
			}
		}

		// 切换仓库的时候才有这个判断，排除没有价格表的数据
		set3 := utils.NewSet(failPID3...)
		setAll = setAll.Minus(set3)
		this.HandlingMsg(failPID3, productIdRelationSkuId, "切换仓库排除价格表里面没有价格的数据")

		// todo 切仓库的时候只是针对实物商品直接查询库存数据判断库存是否足够。排除部分无库存商品。

	}

	ids := setAll.List()
	glog.Info(prefix, " ExclusionUpProductFirst成功的ids : ", ids)
	var shouldBeInitIds []string
	for _, idString := range ids {
		shouldBeInitIds = append(shouldBeInitIds, cast.ToString(idString))
	}
	// 排除先前失败的商品，需要初始化的商品信息
	this.ProductIds = shouldBeInitIds

	return nil
}

//更具门店判断是否是马氏渴望等渠道
/**
map[int]struct{}： 返回的是可上架商品的id或者sku集合
*/
func (this *BatchChannelProductUpDown) ExcludeAgencyConfig() (map[int]struct{}, error) {

	prefix := "排除马氏代运营商品ExcludeAgencyConfig: "

	res := make(map[int]struct{})
	p := new(Product)
	agencyConfig, err := p.GetAgencyConfig(context.Background(), &empty.Empty{}) // 这里配置的tp代运营和阿闻渠道的
	if err != nil {
		glog.Error(prefix, " 获取代运营配置异常：", err)
		return res, err
	}

	// v6.5.8判断是否是马氏，雀巢等门店，需要排除商品 ,非瑞鹏和代运营门店配置商品
	//获取appchannel
	var appChannel int32
	engine.SQL("select app_channel from datacenter.store s where finance_code = ?;", this.FinanceCode).Get(&appChannel)

	var flag bool
	for iv := range agencyConfig.ConfigData {
		if appChannel == agencyConfig.ConfigData[iv] {
			flag = true
		}
	}
	if !flag {
		res, err = p.IsUpProduct(context.Background(), appChannel, 1)
		if err != nil {
			msg := fmt.Sprintf(" 切仓查询配置非瑞鹏和代运营商品异常financeCode:%sproduct:%d", this.FinanceCode, appChannel)
			glog.Error(prefix, msg)
			return res, err
		}
	}

	return res, nil
}

func (this *BatchChannelProductUpDown) HandlingMsg(FailData []int32, PidSkuIdMap map[string]string, errName string) {
	for _, id := range FailData {
		//批量排除不能上架的商品信息
		sId := cast.ToString(id)
		skuId := PidSkuIdMap[sId]
		downERROR := pc.UpDownERROR{
			FinanceCode: this.FinanceCode,
			ProductId:   sId,
			SkuId:       skuId,
			Message:     errName + fmt.Sprintf(" %s：%d", this.FinanceCode, id),
		}
		this.UpResult = append(this.UpResult, &downERROR)
	}

}

// 排除不符合要求的上架商品
func (this *BatchChannelProductUpDown) ExclusionUpProductSecond() {

	prefix := "排除不符合要求的商品ExclusionUpProductSecond："
	glog.Info(prefix, this.FinanceCode, " channel_id: ", this.ChannelId, kit.JsonEncode(this.baseUpDownProduct))

	var (
		failPID1 []int32 // 批量排除第三方上架没有快照的商品
		failPID3 []int32 // 批量排除第三方上架未找到渠道门店代码信息
		failPID4 []int32 // 批量排除第三方上架未找到请选择店内分类
		failPID5 []int32 // 批量排除第三方上架第三方分类Id为空，请选择第三方分类
		failPID7 []int32 // 批量排除没有获取到skuId的商品
		failPID8 []int32 // 批量排除没有获取到财务编码的商品
	)

	var idsInt32 []int32
	productIds := this.ProductIds
	for bi := range productIds {
		idsInt32 = append(idsInt32, cast.ToInt32(productIds[bi]))
	}

	setAll := utils.NewSet(idsInt32...)

	//批量校验当前门店下的所有商品
	//1: 没有快照的商品
	for _, storeProduct := range this.baseUpDownProduct {

		// todo 所有的错误在这里先保存进去
		bres := pc.UpDownERROR{
			FinanceCode: this.FinanceCode,
			ProductId:   storeProduct.ProductId,
			SkuId:       storeProduct.ProductSkuId,
		}
		this.UpResult = append(this.UpResult, &bres)

		//1.1: 第三方渠道排除没有快照的商品
		if storeProduct.ProductSnapshotId <= 0 && this.ChannelId != ChannelAwenId {
			failPID1 = append(failPID1, cast.ToInt32(storeProduct.ProductId))
			bres.Message = fmt.Sprintf("第三方渠道排除没有快照的商品 productId: %s", storeProduct.ProductId)
			continue
		}

		//1.2: 第三方渠道排除门店未找到渠道门店代码信息
		if len(storeProduct.StoreChannelFinanceCodes) <= 0 && !(this.ChannelId == ChannelAwenId || this.ChannelId == ChannelDigitalHealth) {
			failPID3 = append(failPID3, cast.ToInt32(storeProduct.ProductId))
			bres.Message = fmt.Sprintf("第三方渠道排除门店未找到渠道门店代码信息 productId: %s", storeProduct.ProductId)
			continue
		}

		//1.3: 第三方渠道排除门店请选择店内分类
		if storeProduct.ProductChannelCategoryId <= 0 && this.ChannelId != ChannelAwenId {
			failPID4 = append(failPID4, cast.ToInt32(storeProduct.ProductId))
			bres.Message = fmt.Sprintf("第三方渠道排除门店请选择店内分类 productId: %s", storeProduct.ProductId)
			continue
		}

		// 2.3 第三方分类Id为空，请选择第三方分类
		if storeProduct.ProductChannelTagId == 0 && this.ChannelId != ChannelAwenId {
			failPID5 = append(failPID5, cast.ToInt32(storeProduct.ProductId))
			bres.Message = fmt.Sprintf("第三方分类Id为空，请选择第三方分类 productId: %s", storeProduct.ProductId)
			continue
		}

		if len(storeProduct.ProductSkuId) == 0 {
			failPID7 = append(failPID7, cast.ToInt32(storeProduct.ProductId))
			bres.Message = fmt.Sprintf("没有查询到skuid的商品 productId: %s", storeProduct.ProductId)
			continue
		}

		if len(storeProduct.StoreFinanceCode) == 0 {
			failPID8 = append(failPID8, cast.ToInt32(storeProduct.ProductId))
			bres.Message = fmt.Sprintf("门店财务编码列表为空 productId: %s", storeProduct.ProductId)
			continue
		}

	}

	// 2 第三方渠道排除
	//2.1: 第三方渠道排除没有快照的商品
	set1 := utils.NewSet(failPID1...)
	setAll = setAll.Minus(set1)
	//this.HandlingMsg(failPID1, productIdRelationSkuId, "第三方渠道排除没有快照的商品")

	//2.1: 第三方渠道排除门店未找到渠道门店代码信息
	set3 := utils.NewSet(failPID3...)
	setAll = setAll.Minus(set3)
	//this.HandlingMsg(failPID3, productIdRelationSkuId, "第三方渠道排除门店未找到渠道门店代码信息")

	//2.2: 第三方渠道排除门店请选择店内分类
	set4 := utils.NewSet(failPID4...)
	setAll = setAll.Minus(set4)
	//this.HandlingMsg(failPID4, productIdRelationSkuId, "第三方渠道排除门店请选择店内分类")

	// 2.3 第三方分类Id为空，请选择第三方分类
	set5 := utils.NewSet(failPID5...)
	setAll = setAll.Minus(set5)
	//this.HandlingMsg(failPID5, productIdRelationSkuId, "第三方分类Id为空，请选择第三方分类")

	set7 := utils.NewSet(failPID7...)
	setAll = setAll.Minus(set7)
	//this.HandlingMsg(failPID7, productIdRelationSkuId, "批量排除没有获取到skuId的商品")

	set8 := utils.NewSet(failPID8...)
	setAll = setAll.Minus(set8)
	//this.HandlingMsg(failPID8, productIdRelationSkuId, "批量排除没有获取到财务编码的商品")

	// 筛选出符合要求的进行上架
	this.baseUpDownProduct = this.DeleteUpDownProductData(setAll.List())

	glog.Info(prefix, " 结束： ", this.FinanceCode, " channel_id: ", this.ChannelId, kit.JsonEncode(this.baseUpDownProduct))

}

/*
*
切仓的时候，价格表没有价格数据不会上架，产品需求 v6.6.2
*/
func (this *BatchChannelProductUpDown) CheckPrice(nowList []int32, data *models.ChannelWarehouse) ([]int32, error) {

	prefix := "上架前校验价格表的价格: "
	glog.Info(prefix, " 查询的list：", kit.JsonEncode(nowList), " 仓库： ", kit.JsonEncode(data), this.FinanceCode)
	var failPID3 = make([]int32, 0)

	conn := NewDbConn()
	if len(nowList) > 0 {
		var allPriceId []int32
		if data.Category == FrontWarehouse {
			// 前置仓
			// 查询仓库下所有的价格大于0的数据
			conn.Table("qzc_price_sync").Select("product_id").Where("warehouse_id = ?", data.WarehouseId).
				In("product_id", nowList).And(" price > 0").Find(&allPriceId)
		}
		if data.Category == virtualWarehouse { // 强制使用前置仓去查询价格
			relation := LoadWarehouseRelationCache(this.FinanceCode)
			for i := range relation {
				if relation[i].Category == FrontWarehouse {
					// 查询仓库下所有的价格大于0的数据
					conn.Table("qzc_price_sync").Select("product_id").Where("warehouse_id = ?", relation[i].Id).
						In("product_id", nowList).And(" price > 0").Find(&allPriceId)
					break
				}
			}
		}
		if data.Category == StoreWarehouse { // 门店仓
			//根据门店编码查询仓库编码
			var code string
			if _, err := conn.SQL(`select distinct code from dc_dispatch.warehouse w left join dc_dispatch.warehouse_relation_shop wrs 
    on wrs.warehouse_id = w.id where wrs.shop_id =? and wrs.channel_id =?;`, this.FinanceCode, this.ChannelId).Get(&code); err != nil {
				glog.Errorf("CheckPrice 根据门店编码查询仓库编码失败%s", err.Error())
			}
			if code == "" {
				glog.Errorf("CheckPrice 根据门店编码查询仓库编码无记录:%v,%s", this.ChannelId, this.FinanceCode)
			}
			conn.Table("price_sync").Select("product_id").Where("finance_code = ?", code).
				In("product_id", nowList).And(" price > 0").Find(&allPriceId)
		}
		if data.Category == MallWarehouse { //  电商仓
			var strPrid = []string{}
			for i := range nowList {
				strPrid = append(strPrid, cast.ToString(nowList[i]))
			}
			sql := `select s.product_id from dc_product.mall_product_price mpp join dc_product.sku s on s.id = mpp.sku_id 
						where mpp.warehouse_code = ? and s.product_id in (%s) and mpp.price > 0 ;`

			sprintfSql := fmt.Sprintf(sql, strings.Join(strPrid, ","))
			conn.SQL(sprintfSql, data.WarehouseCode).Find(&allPriceId)
		}

		// 排除不符合要求的数据
		m := make(map[int32]struct{})
		for i := range allPriceId {
			m[allPriceId[i]] = struct{}{}
		}

		for i := range nowList {
			if _, okr := m[nowList[i]]; !okr {
				failPID3 = append(failPID3, nowList[i]) // 价格表没有价格的数据或者价格为0的数据
			}
		}
	}

	return failPID3, nil
}

//todo 美团渠道--批量同步商品-目前只支持切换仓库去调用 这里没有同步库存，应为切换仓库的时候会在最后同步库存
/**
针对 单门店多商品，不支持多门店
切换仓库新建立的美团切换仓库方法，因为之前的BatchToMT也是单个方法执行的，速度比较慢
	1：批量生成快照数据
	2：批量同步到美团或者更新到美团
	3：批量同步价格
*/
func (c *Product) BatchToMTNew(ctx context.Context, in *pc.BatchToMTRequest) (*pc.BaseResponse, error) {

	prefix := "切换仓库批量同步美团商品,"
	glog.Info(prefix, " 入参为:", kit.JsonEncode(in))

	var response = &pc.BaseResponse{}
	var successIds = []int32{}
	var err error
	financeCodeOne := in.FinanceCode

	conn := NewDbConn()

	productIdStrs := strings.Split(in.ProductId, ",")
	var productIds []int32
	for i := range productIdStrs {
		productIds = append(productIds, cast.ToInt32(productIdStrs[i]))
	}

	var skuList []models.Sku
	conn.Table(&models.Sku{}).In("product_id", productIds).Find(&skuList)
	m := make(map[int32]int32, len(skuList)) // product_id和sku_id的集合，错误记录
	for i := range skuList {
		m[skuList[i].ProductId] = skuList[i].Id
	}
	// 通用的错误处理
	response, successIds, err = c.CheckBatchThird(in, response, productIds, m)
	if err != nil {
		return response, err
	}
	if len(successIds) <= 0 { // 没有需要往下处理的商品ID
		return response, nil
	}

	if len(successIds) > 200 {
		return response, errors.New("美团渠道批量每次只能处理200个商品")
	}

	warehouses, err := c.GetChannelWarehouses([]string{financeCodeOne}, cast.ToInt32(ChannelMtId))
	if err != nil {
		glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
		return response, errors.New("查询仓库绑定关系异常" + err.Error())
	}
	if len(warehouses) <= 0 {
		return response, errors.New("没有查询到门店的绑定关联关系" + financeCodeOne)
	}

	// 查询门店的关系和appChannel
	storeMasterId, err := GetAppChannelByFinanceCode(financeCodeOne)
	glog.Info(storeMasterId)
	if err != nil {
		glog.Error("BatchToMT,", "GetAppChannelByFinanceCode failed,", financeCodeOne, err)
		return response, errors.New("查询门店的关系和appChannel异常" + financeCodeOne)
	}
	var finRelation models.StoreRelation
	conn.SQL("select * from datacenter.store_relation sr where finance_code =? and channel_id = ? and length(channel_store_id) > 0;",
		financeCodeOne, ChannelMtId).Get(&finRelation)
	if len(finRelation.ChannelStoreId) <= 0 {
		return response, errors.New("没有查询到第三方门店的channelStoreId:" + financeCodeOne)
	}

	//批量查询有快照的数据 没有快照的数据批量生成
	var failId2s []int32 // 生成快照失败的商品id
	if res, err := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
		ChannelId:   ChannelMtId,
		ProductId:   successIds,
		FinanceCode: financeCodeOne,
	}); err != nil {
		sprintf := fmt.Sprintf("批量查询快照数据异常:门店%s code: %d", financeCodeOne, res.Code)
		glog.Error(sprintf, err.Error())
		return response, errors.New(sprintf)
	} else if res.Code != 200 {
		sprintf := fmt.Sprintf("批量查询快照数据异常:门店%s code: %d", financeCodeOne, res.Code)
		return response, errors.New(sprintf)
	} else {
		for i := range res.Details {
			snapOne := res.Details[i]
			if snapOne.Id <= 0 {
				// 没有快照的生成快照
				if snapRes, err := c.NewChannelProductSnapshot(ctx, &pc.ChannelProductSnapshot{
					Id:          snapOne.Id,
					ChannelId:   ChannelMtId,
					UserNo:      in.UserNo,
					ProductId:   snapOne.ProductId,
					JsonData:    snapOne.JsonData,
					FinanceCode: financeCodeOne}); err != nil {
					response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
						ProductId:   cast.ToString(snapOne.ProductId),
						SkuId:       cast.ToString(m[snapOne.ProductId]),
						IsSuccess:   false,
						Message:     "生成快照失败" + err.Error(),
						FinanceCode: financeCodeOne,
					})
					failId2s = append(failId2s, snapOne.ProductId)
				} else if snapRes.Code != 200 {
					response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
						ProductId:   cast.ToString(snapOne.ProductId),
						SkuId:       cast.ToString(m[snapOne.ProductId]),
						IsSuccess:   false,
						Message:     "生成快照失败" + err.Error(),
						FinanceCode: financeCodeOne,
					})
					failId2s = append(failId2s, snapOne.ProductId)
				}
			}
		}
	}

	// 减去快照生成失败的商品
	setAll := utils.NewSet(successIds...)
	setFailId2 := utils.NewSet(failId2s...)
	setAll.Minus(setFailId2)

	// 初始化美团商品
	gmdr := pc.GetMtProductDataRequest{
		ProductId:   setAll.List(),
		ChannelId:   ChannelMtId,
		FinanceCode: []string{financeCodeOne},
		Type:        1,
	}
	mtProductData, err := c.GetMtProductData(ctx, &gmdr)
	glog.Info(prefix, "格式化获取美团同步数据", kit.JsonEncode(mtProductData), "err:", err)
	if err != nil {
		msg := "处理格式化美团商品信息异常" + err.Error()
		glog.Error(msg)
		return response, errors.New(msg)
	} else if mtProductData.Code != 200 || len(mtProductData.Data) == 0 {
		msg := fmt.Sprintf("处理格式化美团商品信息失败: code%d :length%d", mtProductData.Code, len(mtProductData.Data))
		glog.Error(msg)
		return response, errors.New(msg)
	}
	if len(mtProductData.ErrList) > 0 {
		var errList []*pc.UpDownERROR
		json.Unmarshal([]byte(mtProductData.ErrList), &errList)
		response.UpDownDetail = append(response.UpDownDetail, errList...)
	}

	foodData := []*et.RetailBatchinitdata{}
	if json.Unmarshal([]byte(mtProductData.Data), &foodData) != nil {
		msg := "序列化美团商品信息失败" + err.Error()
		glog.Error(msg)
		return response, errors.New(msg)
	}

	// 校验格式化之后的fooData是否符合规范
	newFooData := c.CheckFooData(foodData, response, financeCodeOne, m)

	//同步到第三方： 判断是创建还是更新美团// 1-创建，2-更新
	var createVo = &et.RetailBatchinitdataRequest{
		AppPoiCode: finRelation.ChannelStoreId,
		//FoodData:   foodData,
		OperateType:   int32(1),
		StoreMasterId: storeMasterId,
	}
	var updateVo = &et.RetailBatchinitdataRequest{
		AppPoiCode:    finRelation.ChannelStoreId,
		OperateType:   int32(2),
		StoreMasterId: storeMasterId,
	}

	existProductIdThird := utils.NewSet(in.ExistProductIdThird...)
	for i := range newFooData {
		id := cast.ToInt32(newFooData[i].AppFoodCode)
		if existProductIdThird.Has(id) { // 更新
			updateVo.FoodData = append(updateVo.FoodData, newFooData[i])
		} else { // 创建
			// 创建的时候需要构建价格
			getPrice, err1 := GetPrice(int(ChannelMtId), cast.ToString(newFooData[i].Skus[0].SkuId), financeCodeOne)
			if err1 != nil {
				glog.Error(prefix, "获取商品价格失败", err1)
			} else {
				newFooData[i].Skus[0].Price = cast.ToString(getPrice)
			}
			createVo.FoodData = append(createVo.FoodData, newFooData[i])
		}
	}

	clientMt := GetMtGlobalProductClient()
	// 创建商品的时候一个参数错误，所有的创建都不成功
	resUuid, _ := uuid.NewUUID()
	for num := 0; num < 3; num++ { // 这里重复创建三次就可，筛选出错误的数据
		glog.Info(prefix, "请求参数,uuid: ", resUuid.String(), " 重试次数： ", num, " 数据量：", len(createVo.FoodData))
		if len(createVo.FoodData) > 0 {
			initRes, err := clientMt.RPC.RetailBatchinitdata(context.Background(), createVo)
			glog.Info(prefix, "返回结果,uuid: ", resUuid.String(), "财务编码为", financeCodeOne, ",10001美团创建/更新商品返回数据为:", kit.JsonEncode(initRes), "----------入参:", kit.JsonEncode(createVo), ",错误为:", kit.JsonEncode(err))

			if err != nil {
				msg := " 创建到美团返回 " + err.Error()
				glog.Error(prefix, "uuid: ", resUuid.String(), msg)
				return response, errors.New(msg)
			}

			// v7.0.11 同步第三方商品ID回来
			MtProductThirdId(enum.UpdateProductThirdIdFrom4, initRes, createVo.FoodData, financeCodeOne, createVo.OperateType)

			if initRes.Code != 200 || len(initRes.ErrorList) > 0 {
				//删除错误的创建数据重新执行
				newDataFoo := []*et.RetailBatchinitdata{}

				//  保存同步失败数据
				var errData = initRes.ErrorList
				mapRemove := make(map[string]struct{}, 0)
				for i := range errData {
					errMsg := errData[i]
					mapRemove[errMsg.AppSpuCode] = struct{}{} // 去除错误
					for ci := range createVo.FoodData {
						failData := createVo.FoodData[ci]
						if errMsg.AppSpuCode == failData.AppFoodCode {
							response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
								ProductId:   cast.ToString(failData.AppFoodCode),
								SkuId:       cast.ToString(m[cast.ToInt32(failData.AppFoodCode)]),
								IsSuccess:   false,
								Message:     "创建到美团失败：" + errMsg.Msg,
								FinanceCode: financeCodeOne,
							})
							break
						}
					}
				}
				for ci := range createVo.FoodData {
					failData := createVo.FoodData[ci]
					if _, ok := mapRemove[failData.AppFoodCode]; !ok { // 排除创建错误的继续创建
						newDataFoo = append(newDataFoo, failData)
					}
				}
				//如果没有排除掉错误直接退出，可以少同步
				if len(newDataFoo) == len(createVo.FoodData) {
					break
				}
				// 赋值新的foodata去创建
				createVo.FoodData = newDataFoo
			} else {
				break
			}
		}

	}

	//// 更新商品到美团，报错处理不一样
	if len(updateVo.FoodData) > 0 {
		initRes, err := clientMt.RPC.RetailBatchinitdata(context.Background(), updateVo)
		glog.Info(prefix, "财务编码为", financeCodeOne, ",10000美团创建/更新商品返回数据为", kit.JsonEncode(initRes), "----------入参", kit.JsonEncode(updateVo), "错误为", kit.JsonEncode(err))

		if err != nil {
			msg := "更新到美团商品返回 " + err.Error()
			glog.Error(msg)
			return response, errors.New(msg)
		}
		MtProductThirdId(enum.UpdateProductThirdIdFrom5, initRes, updateVo.FoodData, financeCodeOne, updateVo.OperateType)

		if initRes.Code != 200 && len(initRes.Message) > 0 {
			//[{"app_food_code":"1032967","blockFlag":1,"error_msg":"已经存在此商品，不能重复创建"},{"app_food_code":"1033667","blockFlag":1,"error_msg":"已经存在此商品，不能重复创建"}]
			var errData []models.InitResErrListMsg
			json.Unmarshal([]byte(initRes.Message), &errData)
			for ei := range errData {
				Errmsg := errData[ei]
				for i := range updateVo.FoodData {
					failData := updateVo.FoodData[i]
					if Errmsg.AppFoodCode == failData.AppFoodCode {
						response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
							ProductId:   cast.ToString(failData.AppFoodCode),
							SkuId:       cast.ToString(m[cast.ToInt32(failData.AppFoodCode)]),
							IsSuccess:   false,
							Message:     "更新到美团商品失败：" + Errmsg.ErrorMsg,
							FinanceCode: financeCodeOne,
						})
						break
					}
				}
			}
		}
	}

	//异步同步价格
	list := setAll.List()
	go func() {
		time.Sleep(10 * time.Second)
		for i := range list {
			proId := list[i]
			var channelProduct ChannelProductPriceSync
			channelProduct.ChannelId = ChannelMtId
			channelProduct.FinanceCode = financeCodeOne
			channelProduct.ProductSkuId = cast.ToString(m[proId])
			channelProduct.ProductId = cast.ToString(proId)
			channelProduct.ChannelFinanaceCode = finRelation.ChannelStoreId
			channelProduct.WarehouseCategory = warehouses[0].Category
			channelProduct.SyncPrice()
		}
	}()

	return response, nil
}

/*
*
切换仓库：同步商品到eleMe渠道
1: 批量生成快照
2：批量同步到eleMe 更新或者创建
3: 同步价格
*/
func (c *Product) BatchToELENew(ctx context.Context, in *pc.BatchToMTRequest) (*pc.BaseResponse, error) {
	prefix := fmt.Sprintf("切换仓库同步商品到eleMe渠道|FinanceCode为%s|", in.FinanceCode)
	glog.Info(prefix, " 入参为:", kit.JsonEncode(in))
	//2、循环商品
	//2.1、取商品价格和库存
	//3、信息同步到美团
	var response = &pc.BaseResponse{}
	var successIds = []int32{}
	var err error
	financeCodeOne := in.FinanceCode

	conn := NewDbConn()

	productIdStrs := strings.Split(in.ProductId, ",")
	var productIds []int32
	for i := range productIdStrs {
		productIds = append(productIds, cast.ToInt32(productIdStrs[i]))
	}

	var skuList []models.Sku
	conn.Table(&models.Sku{}).In("product_id", productIds).Find(&skuList)
	m := make(map[int32]int32, len(skuList))               // product_id和sku_id的集合，错误记录
	sku_product_ids := make(map[int32]int32, len(skuList)) // sku_id和product_id的集合，错误记录
	for i := range skuList {
		m[skuList[i].ProductId] = skuList[i].Id
		sku_product_ids[skuList[i].Id] = skuList[i].ProductId
	}

	// 通用的错误处理
	response, successIds, err = c.CheckBatchThird(in, response, productIds, m)
	if err != nil {
		return response, err
	}
	if len(successIds) <= 0 { // 没有需要往下处理的商品ID
		return response, nil
	}

	if len(successIds) > 50 {
		return response, errors.New("饿了么渠道批量每次只能处理50个商品")
	}

	warehouses, err := c.GetChannelWarehouses([]string{financeCodeOne}, cast.ToInt32(ChannelElmId))
	if err != nil {
		glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
		return response, errors.New("查询仓库绑定关系异常" + err.Error())
	}
	if len(warehouses) <= 0 {
		return response, errors.New("没有查询到门店的绑定关联关系" + financeCodeOne)
	}

	// 查询门店的关系和appChannel
	storeMasterId, err := GetAppChannelByFinanceCode(financeCodeOne)
	glog.Info(storeMasterId)
	if err != nil {
		glog.Error("BatchToMT,", "GetAppChannelByFinanceCode failed,", financeCodeOne, err)
		return response, errors.New("查询门店的关系和appChannel异常" + financeCodeOne)
	}
	var finRelation models.StoreRelation
	conn.SQL("select * from datacenter.store_relation sr where finance_code =? and channel_id = ? and length(channel_store_id) > 0;",
		financeCodeOne, ChannelElmId).Get(&finRelation)
	if len(finRelation.ChannelStoreId) <= 0 {
		return response, errors.New("没有查询到第三方门店的channelStoreId:" + financeCodeOne)
	}

	//批量查询有快照的数据 没有快照的数据批量生成
	var failId2s []int32 // 生成快照失败的商品id
	var succSnap []*pc.ChannelProductSnapshot
	//获取bar_code
	var channelSku []ChannelSkus
	conn.Table(&models.ChannelSku{}).Select("product_id,bar_code").Where("channel_id = ?", ChannelElmId).In("product_id", successIds).Find(&channelSku)
	//channelSku 变成 prodcut_id为键值，bar_code为值
	bar_code_map := make(map[int32]string, len(channelSku))
	for i := range channelSku {
		bar_code_map[channelSku[i].ProductId] = channelSku[i].BarCode
	}

	if res, err := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
		ChannelId:   ChannelElmId,
		ProductId:   successIds,
		FinanceCode: financeCodeOne,
	}); err != nil {
		sprintf := fmt.Sprintf("批量查询快照数据异常:门店%s code: %d", financeCodeOne, res.Code)
		glog.Error(sprintf, err.Error())
		return response, errors.New(sprintf)
	} else if res.Code != 200 {
		sprintf := fmt.Sprintf("批量查询快照数据异常:门店%s code: %d", financeCodeOne, res.Code)
		return response, errors.New(sprintf)
	} else {
		for i := range res.Details {
			snapOne := res.Details[i]
			if snapOne.Id <= 0 {
				// 没有快照的生成快照
				if snapRes, err := c.NewChannelProductSnapshot(ctx, &pc.ChannelProductSnapshot{
					Id:          snapOne.Id,
					ChannelId:   ChannelElmId,
					UserNo:      in.UserNo,
					ProductId:   snapOne.ProductId,
					JsonData:    snapOne.JsonData,
					FinanceCode: financeCodeOne}); err != nil {
					response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
						ProductId:   cast.ToString(snapOne.ProductId),
						SkuId:       cast.ToString(m[snapOne.ProductId]),
						IsSuccess:   false,
						Message:     "生成快照失败" + err.Error(),
						FinanceCode: financeCodeOne,
					})
					failId2s = append(failId2s, snapOne.ProductId)
				} else if snapRes.Code != 200 {
					response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
						ProductId:   cast.ToString(snapOne.ProductId),
						SkuId:       cast.ToString(m[snapOne.ProductId]),
						IsSuccess:   false,
						Message:     "生成快照失败" + err.Error(),
						FinanceCode: financeCodeOne,
					})
					failId2s = append(failId2s, snapOne.ProductId)
				} else {
					succSnap = append(succSnap, snapOne) // 成功的快照
				}
			} else { // 成功的快照
				succSnap = append(succSnap, snapOne)
			}
		}

	}

	// 减去快照生成失败的商品
	setAll := utils.NewSet(successIds...)
	setFailId2 := utils.NewSet(failId2s...)
	setAll.Minus(setFailId2)

	//处理成功的快照:格式化饿了么商品
	footData := c.GetEleMeProductData(ctx, succSnap, storeMasterId, finRelation.ChannelStoreId, bar_code_map)
	glog.Info(prefix, " 格式化饿了么商品为", kit.JsonEncode(footData))
	response.UpDownDetail = append(response.UpDownDetail, footData.UpDownDetail...) // 记录异常

	// 第三方有的商品数据
	var existSkuId []int32
	conn.Table(&models.Sku{}).Select("id").In("product_id", in.ExistProductIdThird).Find(&existSkuId)

	if len(existSkuId) != len(in.ExistProductIdThird) { // 在查询一下渠道表，有一个版本的bug吧gj和平台删除了
		var existChannelSkuid []int32
		conn.Table(&models.ChannelSku{}).Select("id").Where("channel_id = ?", ChannelElmId).In("product_id", in.ExistProductIdThird).Find(&existChannelSkuid)
		existSkuId = append(existSkuId, existChannelSkuid...)
	}

	setSkuIds := utils.NewSet(existSkuId...)

	var updateVo = et.BatchUpdateElmShopSkuRequest{ // 编辑
		ShopId:     finRelation.ChannelStoreId,
		AppChannel: storeMasterId,
	}
	var createVo = et.BatchCreateElmShopSkuRequest{ // 新增
		ShopId:     finRelation.ChannelStoreId,
		AppChannel: storeMasterId,
	}

	var jsonSkuRequest []*et.UpdateElmShopSkuRequest
	if len(footData.Message) > 0 {
		json.Unmarshal([]byte(footData.Message), &jsonSkuRequest)
		for i := range jsonSkuRequest {
			request := jsonSkuRequest[i]
			if setSkuIds.Has(cast.ToInt32(request.CustomSkuId)) {
				updateVo.UpdateList = append(updateVo.UpdateList, request)
			} else {
				createVo.UpdateList = append(createVo.UpdateList, request)
			}
		}
	}

	client := GetMtProductClient()
	defer client.Close()
	// 同步到饿了么创建或者更新
	if len(updateVo.UpdateList) > 0 {
		result, err := client.ELMPRODUCT.BatchUpdateElmShopSku(client.Ctx, &updateVo)
		glog.Info(prefix, "1111饿了么创建/更新商品返回数据为", kit.JsonEncode(result), "||||||||||入参为", kit.JsonEncode(updateVo), " error为", kit.JsonEncode(err))
		ElmProductThirdId(enum.UpdateProductThirdIdFrom1, result.ElmBatchReturn, updateVo.UpdateList, financeCodeOne, sku_product_ids)
		if err != nil {
			for i := range updateVo.UpdateList {
				fail := updateVo.UpdateList[i]
				response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
					ProductId:   cast.ToString(sku_product_ids[cast.ToInt32(fail.CustomSkuId)]),
					SkuId:       fail.CustomSkuId,
					IsSuccess:   false,
					Message:     "更新饿了么商品失败：" + err.Error(),
					FinanceCode: financeCodeOne,
				})
			}
		} else if result.Code != 200 { // 部分成功
			if result.Data != nil && len(result.Error) > 0 {
				for i := range updateVo.UpdateList {
					fail := updateVo.UpdateList[i]
					splitError := strings.Split(result.Error, ";")
					for ei := range splitError {
						sError := splitError[ei]
						if strings.Contains(sError, fail.CustomSkuId) {
							response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
								ProductId:   cast.ToString(sku_product_ids[cast.ToInt32(fail.CustomSkuId)]),
								SkuId:       fail.CustomSkuId,
								IsSuccess:   false,
								Message:     "更新饿了么异常：" + sError,
								FinanceCode: financeCodeOne,
							})
							break
						}
					}
				}
			}
		}

	}

	if len(createVo.UpdateList) > 0 {
		result, err := client.ELMPRODUCT.BatchCreateElmShopSku(client.Ctx, &createVo)
		glog.Info(prefix, "2222饿了么创建/更新商品返回数据为", kit.JsonEncode(result), "||||||||||入参：", kit.JsonEncode(createVo), " error为", kit.JsonEncode(err))
		ElmProductThirdId(enum.UpdateProductThirdIdFrom2, result.ElmBatchReturn, createVo.UpdateList, financeCodeOne, sku_product_ids)
		if err != nil {
			for i := range createVo.UpdateList {
				fail := createVo.UpdateList[i]
				response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
					ProductId:   cast.ToString(sku_product_ids[cast.ToInt32(fail.CustomSkuId)]),
					SkuId:       fail.CustomSkuId,
					IsSuccess:   false,
					Message:     "创建饿了么商品异常：" + err.Error(),
					FinanceCode: financeCodeOne,
				})
			}
		} else if result.Code != 200 {
			if len(result.Error) > 0 { // 部分错误
				for i := range createVo.UpdateList { //
					fail := createVo.UpdateList[i]
					splitError := strings.Split(result.Error, ";")
					for ei := range splitError {
						sError := splitError[ei]
						if strings.Contains(sError, fail.CustomSkuId) || strings.Contains(sError, fail.Upc) {
							response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
								ProductId:   cast.ToString(sku_product_ids[cast.ToInt32(fail.CustomSkuId)]),
								SkuId:       fail.CustomSkuId,
								IsSuccess:   false,
								Message:     "创建饿了么商品异常：" + sError,
								FinanceCode: financeCodeOne,
							})
							break
						}

					}
				}
			}
		}
	}

	//异步同步价格
	list := setAll.List()
	go func() {
		for i := range list {
			proId := list[i]
			var channelProduct ChannelProductPriceSync
			channelProduct.ChannelId = ChannelElmId
			channelProduct.FinanceCode = financeCodeOne
			channelProduct.ProductSkuId = cast.ToString(m[proId])
			channelProduct.ProductId = cast.ToString(proId)
			channelProduct.ChannelFinanaceCode = finRelation.ChannelStoreId
			channelProduct.WarehouseCategory = warehouses[0].Category
			channelProduct.SyncPrice()
		}
	}()

	return response, nil
}

// 组装eleMe的商品数据
func (c *Product) GetEleMeProductData(ctx context.Context, snapShots []*pc.ChannelProductSnapshot, appChannel int32, channelStoreId string, channelSku map[int32]string) (out *pc.BaseResponse) {
	out = new(pc.BaseResponse)
	out.Code = 400

	FoodData := make([]*et.UpdateElmShopSkuRequest, 0)
	//组装单门店多商品数据
	for i := range snapShots {
		snapshotOne := snapShots[i]
		var newSnap pc.ChannelProductRequest
		if err := json.Unmarshal([]byte(snapshotOne.JsonData), &newSnap); err != nil {
			out.UpDownDetail = append(out.UpDownDetail, &pc.UpDownERROR{
				IsSuccess:   false,
				Message:     "反序列化报错：门店id" + snapshotOne.JsonData,
				FinanceCode: snapshotOne.FinanceCode,
			})
			continue
		}
		//获取channelSku的值
		BarCode, ok := channelSku[snapshotOne.ProductId]
		if !ok || len(BarCode) == 0 {
			BarCode = cast.ToString(newSnap.SkuInfo[0].SkuId)
		} else {
			if CheckkChannelBarCode(newSnap.SkuInfo[0].SkuId, 3, snapshotOne.FinanceCode, BarCode) {
				out.UpDownDetail = append(out.UpDownDetail, &pc.UpDownERROR{
					IsSuccess:   false,
					Message:     "bar_code重复" + snapshotOne.JsonData,
					FinanceCode: snapshotOne.FinanceCode,
				})
				continue
			}
		}

		var sku = new(et.UpdateElmShopSkuRequest)
		sku.Upc = BarCode

		sku.ShopId = channelStoreId
		picList, err := ProcessPic(&newSnap)
		if err != nil {
			out.UpDownDetail = append(out.UpDownDetail, &pc.UpDownERROR{
				ProductId:   cast.ToString(newSnap.Product.Id),
				SkuId:       cast.ToString(newSnap.SkuInfo[0].SkuId),
				IsSuccess:   false,
				Message:     "饿了么处理图片异常：" + err.Error(),
				FinanceCode: snapshotOne.FinanceCode,
			})
			continue
		}
		// 图片
		for k, v := range picList {
			photo := et.SkuPhotos{}
			photo.IsMaster = 0
			if k == 0 {
				photo.IsMaster = 1
			}
			photo.Url = v
			sku.Photos = append(sku.Photos, &photo)
		}

		sku.Desc = newSnap.Product.ContentPc
		//应用渠道
		sku.AppChannel = appChannel

		//保存或者校验其他属性
		err = CheckOrUpdateData(newSnap, sku)
		if err != nil {
			out.UpDownDetail = append(out.UpDownDetail, &pc.UpDownERROR{
				ProductId:   cast.ToString(newSnap.Product.Id),
				SkuId:       cast.ToString(newSnap.SkuInfo[0].SkuId),
				IsSuccess:   false,
				Message:     "保存或者校验其他属性失败：" + err.Error(),
				FinanceCode: snapshotOne.FinanceCode,
			})
			continue
		}
		sku.Name = newSnap.Product.Name
		sku.Cat3Id = newSnap.Product.ChannelTagId
		sku.Summary = newSnap.Product.SellingPoint
		sku.Weight = cast.ToFloat32(newSnap.SkuInfo[0].WeightForUnit)
		sku.SaleUnit = newSnap.SkuInfo[0].WeightUnit
		if sku.SalePrice <= 0 {
			if newSnap.SkuInfo[0].MarketPrice > 0 {
				sku.SalePrice = newSnap.SkuInfo[0].MarketPrice
			} else {
				out.UpDownDetail = append(out.UpDownDetail, &pc.UpDownERROR{
					ProductId:   cast.ToString(newSnap.Product.Id),
					SkuId:       cast.ToString(newSnap.SkuInfo[0].SkuId),
					IsSuccess:   false,
					Message:     "商品价格不能为0",
					FinanceCode: snapshotOne.FinanceCode,
				})
				continue
			}
		}

		sku.CustomSkuId = cast.ToString(newSnap.SkuInfo[0].SkuId)

		// 不处理库存这里，需要处理的自己查库存
		sku.LeftNum = 0
		sku.Status = 0 // 下架
		FoodData = append(FoodData, sku)
	}

	marshal, err := json.Marshal(FoodData)
	if err != nil {
		return out
	}

	out.Message = string(marshal)
	return out
}

func CheckOrUpdateData(in pc.ChannelProductRequest, sku *et.UpdateElmShopSkuRequest) error {
	if in.Product.ChannelCategoryId == 0 {
		return errors.New("店内分类为必填项")
	}
	if in.Product.ChannelTagId == 0 {
		return errors.New("饿了么分类为必填项")
	}
	if len(in.Product.Name) == 0 {
		return errors.New("商品名称为必填项")
	}
	if cast.ToFloat32(in.SkuInfo[0].WeightForUnit) <= 0 {
		return errors.New("商品重量为必填项")
	}

	if in.Product.ChannelCategoryId > 0 {
		thirdModel := models.ChannelCategoryThirdid{}
		hasthird, _ := engine.Table("channel_category_thirdid").
			Select("category_id").Where("id=? AND channel_store_id=?",
			in.Product.ChannelCategoryId, sku.ShopId).Get(&thirdModel)
		if !hasthird {
			return errors.New("饿了么第三方分类不存在")
		} else {
			sku.CategoryId = cast.ToInt64(thirdModel.CategoryId)
		}
	}

	return nil

}

// 处理图片
func ProcessPic(product *pc.ChannelProductRequest) ([]string, error) {

	//图片转换
	var picList []string
	photoList := strings.Split(product.Product.Pic, ",")

	client := GetMtProductClient()
	defer client.Close()

	for _, picV := range photoList {
		if strings.TrimSpace(picV) == "" {
			continue
		}
		var pic et.UploadPictureRequest
		pic.Url = picV
		//商品图片的上传直接使用瑞鹏的appChannel就好，该方法仅仅是饿了么上传图片并返回他的图片地址，所以与应用无关
		pic.AppChannel = 1
		res, err := client.ELMPRODUCT.UploadPicture(context.Background(), &pic)
		if err != nil {
			glog.Errorf("饿了么商品图片上传失败，全部门店，productId：%d，ERR：%v, pic: %s", product.Product.Id, err.Error(), picV)
			continue
		}
		if res.Code != 200 {
			glog.Errorf("饿了么商品图片上传失败，全部门店，productId：%d，ERR：%v, pic: %s", product.Product.Id, res.Error, picV)
			continue
		}
		picList = append(picList, res.Data)
	}
	if len(picList) == 0 {
		return picList, errors.New("图片不可为空")
	}
	return picList, nil

}

//// 处理富文本数据
//func ProcessTrf(product *pc.ChannelProductRequest, appChannel int32, channelStoreId string) (string, error) {
//	//富文本处理
//	client := GetMtProductClient()
//	defer client.Close()
//
//	var rtfStr string
//	//if product.Product.ContentPc != "" { // 这里批量的时候不传递这种格式会报错，所以将空的也进行转换
//	var rtf et.UploadPictureRequest
//	rtf.ShopId = channelStoreId
//	rtf.Data = product.Product.ContentPc
//	//此处必须是正确的的appChannel
//	rtf.AppChannel = appChannel
//	res, err := client.ELMPRODUCT.UploadPictureRTF(context.Background(), &rtf)
//	if err != nil {
//		glog.Error("饿了么商品富文本转换失败，全部门店，productId：", product.Product.Id, "，ERR：", err.Error(), ",请求参数", kit.JsonEncode(rtf), channelStoreId)
//		return rtfStr, err
//	}
//	if res.Code != 200 {
//		glog.Error("【饿了么商品富文本转换失败】全部门店，productId：", product.Product.Id, "，", res.Error, ",请求参数", kit.JsonEncode(rtf), channelStoreId)
//		return rtfStr, errors.New("饿了么富文本上传错误" + res.Error)
//	} else if res.Data != "" {
//		rtfStr = res.Data
//	}
//	//}
//
//	return rtfStr, nil
//}

/*
*
切换仓库：批量编辑到jd渠道
1: 批量生成快照
2：jd没有同步到第三方，应为在认领的时候已经过去了
3: 同步价格
*/
func (c *Product) BatchToJDDJNew(ctx context.Context, in *pc.BatchToMTRequest) (*pc.BaseResponse, error) {

	prefix := "切换仓库批量编辑到jd渠道: "
	glog.Info(prefix, " 入参：", kit.JsonEncode(in))
	//2、循环商品
	//2.1、取商品价格和库存
	//3、信息同步到美团
	var response = &pc.BaseResponse{}
	var successIds = []int32{}
	var err error

	financeCodeOne := in.FinanceCode

	conn := NewDbConn()

	productIdStrs := strings.Split(in.ProductId, ",")
	var productIds []int32
	for i := range productIdStrs {
		productIds = append(productIds, cast.ToInt32(productIdStrs[i]))
	}

	var skuList []models.Sku
	conn.Table(&models.Sku{}).In("product_id", productIds).Find(&skuList)
	m := make(map[int32]int32, len(skuList)) // product_id和sku_id的集合，错误记录
	for i := range skuList {
		m[skuList[i].ProductId] = skuList[i].Id
	}

	// 通用的错误处理
	response, successIds, err = c.CheckBatchThird(in, response, productIds, m)
	if err != nil {
		return response, err
	}
	if len(successIds) <= 0 { // 没有需要往下处理的商品ID
		return response, nil
	}

	warehouses, err := c.GetChannelWarehouses([]string{financeCodeOne}, cast.ToInt32(ChannelJddjId))
	if err != nil {
		glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
		return response, errors.New("查询仓库绑定关系异常" + err.Error())
	}
	if len(warehouses) <= 0 {
		return response, errors.New("没有查询到门店的绑定关联关系" + financeCodeOne)
	}

	// 查询门店的关系和appChannel
	storeMasterId, err := GetAppChannelByFinanceCode(financeCodeOne)
	glog.Info(storeMasterId)
	if err != nil {
		glog.Error("BatchToMT,", "GetAppChannelByFinanceCode failed,", financeCodeOne, err)
		return response, errors.New("查询门店的关系和appChannel异常" + financeCodeOne)
	}
	var finRelation models.StoreRelation
	conn.SQL("select * from datacenter.store_relation sr where finance_code =? and channel_id = ? and length(channel_store_id) > 0;",
		financeCodeOne, ChannelJddjId).Get(&finRelation)
	if len(finRelation.ChannelStoreId) <= 0 {
		return response, errors.New("没有查询到第三方门店的channelStoreId:" + financeCodeOne)
	}

	//批量查询有快照的数据 没有快照的数据批量生成
	var failId2s []int32 // 生成快照失败的商品id
	if res, err := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
		ChannelId:   ChannelJddjId,
		ProductId:   successIds,
		FinanceCode: financeCodeOne,
	}); err != nil {
		sprintf := fmt.Sprintf("批量查询快照数据异常:门店%s code: %d", financeCodeOne, res.Code)
		glog.Error(sprintf, err.Error())
		return response, errors.New(sprintf)
	} else if res.Code != 200 {
		sprintf := fmt.Sprintf("批量查询快照数据异常:门店%s code: %d", financeCodeOne, res.Code)
		return response, errors.New(sprintf)
	} else {
		for i := range res.Details {
			snapOne := res.Details[i]
			if snapOne.Id <= 0 {
				// 没有快照的生成快照
				if snapRes, err := c.NewChannelProductSnapshot(ctx, &pc.ChannelProductSnapshot{
					Id:          snapOne.Id,
					ChannelId:   ChannelJddjId,
					UserNo:      in.UserNo,
					ProductId:   snapOne.ProductId,
					JsonData:    snapOne.JsonData,
					FinanceCode: financeCodeOne}); err != nil {
					response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
						ProductId:   cast.ToString(snapOne.ProductId),
						SkuId:       cast.ToString(m[snapOne.ProductId]),
						IsSuccess:   false,
						Message:     "生成快照异常：" + err.Error(),
						FinanceCode: financeCodeOne,
					})
					failId2s = append(failId2s, snapOne.ProductId)
				} else if snapRes.Code != 200 {
					response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
						ProductId:   cast.ToString(snapOne.ProductId),
						SkuId:       cast.ToString(m[snapOne.ProductId]),
						IsSuccess:   false,
						Message:     "生成快照失败" + err.Error(),
						FinanceCode: financeCodeOne,
					})
					failId2s = append(failId2s, snapOne.ProductId)
				}
			}
		}
	}

	// 减去快照生成失败的商品
	setAll := utils.NewSet(successIds...)
	setFailId2 := utils.NewSet(failId2s...)
	setAll.Minus(setFailId2)

	//异步同步价格
	list := setAll.List()
	go func() {
		for i := range list {
			proId := list[i]
			var channelProduct ChannelProductPriceSync
			channelProduct.ChannelId = ChannelJddjId
			channelProduct.FinanceCode = financeCodeOne
			channelProduct.ProductSkuId = cast.ToString(m[proId])
			channelProduct.ProductId = cast.ToString(proId)
			channelProduct.ChannelFinanaceCode = finRelation.ChannelStoreId
			channelProduct.WarehouseCategory = warehouses[0].Category
			channelProduct.SyncPrice()
		}
	}()

	return response, nil
}

// 通用校验同步到第三方
/**
pc.BaseResponse： 返回错误集合response
[]int32： 校验成功的productIds集合
error： err错误
*/
func (c *Product) CheckBatchThird(in *pc.BatchToMTRequest, response *pc.BaseResponse, productIds []int32, m map[int32]int32) (*pc.BaseResponse, []int32, error) {

	conn := NewDbConn()

	var list = []int32{}

	if len(in.FinanceCode) <= 0 || len(in.ProductId) <= 0 {
		return response, list, errors.New("参数FinanceCode和ProductId不能为空")
	}

	financeCodeOne := in.FinanceCode

	setAll := utils.NewSet(productIds...)
	// 排除虚拟同步到美团商品
	var failId0s []int32
	conn.Table(&models.Product{}).Select("id").Where("product_type = ?", 2).In("id", productIds).Find(&failId0s)
	//conn.SQL("select id from dc_product.product p where product_type = 2 ;").Find(&failId0s)
	setFailId0 := utils.NewSet(failId0s...)
	setAll = setAll.Minus(setFailId0)

	// 记录异常
	for i := range failId0s {
		response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
			ProductId:   cast.ToString(failId0s[i]),
			SkuId:       cast.ToString(m[failId0s[i]]),
			IsSuccess:   false,
			Message:     "排除虚拟同步到第三方",
			FinanceCode: financeCodeOne,
		})
	}

	//切换仓库商品的同步和上架不用管组合商品，组合商品不自动同步和上架（并不是只针对美团渠道，所有渠道都如此）
	if in.IsCutStorehouse == 1 {
		var failId1s []int32
		//conn.SQL("select id from dc_product.product p where product_type = 3 ;").Find(&failId1s)
		conn.Table(&models.Product{}).Select("id").Where("product_type = ?", 2).In("id", productIds).Find(&failId1s)
		for i := range failId1s {
			response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
				ProductId:   cast.ToString(failId1s[i]),
				SkuId:       cast.ToString(m[failId0s[i]]),
				IsSuccess:   false,
				Message:     "切换仓库商品的同步和上架不用管组合商品，组合商品不自动同步和上架",
				FinanceCode: financeCodeOne,
			})
		}
		setFailId1 := utils.NewSet(failId1s...)
		setAll = setAll.Minus(setFailId1)
	}

	list = setAll.List()

	return response, list, nil
}
