package services

import (
	"_/models"
	"_/proto/pc"
	"context"
	"errors"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/spf13/cast"
	"net/http"
	"strings"
	"time"
)

//电商仓商品价格导入 在datacenter公共导入接口实现

//电商仓商品价格列表
func (c *Product) MallProductPriceList(ctx context.Context, req *pc.MallProductPriceListReq) (*pc.MallProductPriceListRes, error) {
	out := &pc.MallProductPriceListRes{Code: http.StatusBadRequest}

	Engine := NewDbConn()
	session := Engine.Table("mall_product_price")

	if len(req.Skuid) > 0 {
		session.Where("sku_id=?", req.Skuid)
	}

	countSession := *session
	count, _ := countSession.Count()
	out.Total = int32(count)

	err := session.Select("id, warehouse_code, sku_id, price, create_date, update_date, create_name, update_name").
		Limit(int(req.PageSize), int((req.PageIndex-1)*req.PageSize)).OrderBy("update_date DESC, id DESC").Find(&out.Data)
	if err != nil {
		glog.Error("MallProductPriceList失败, ", err)
		out.Message = err.Error()
		return out, err
	}

	out.Code = http.StatusOK
	return out, nil
}

//电商仓商品价格移除
func (c *Product) MallProductPriceDel(ctx context.Context, req *pc.IdRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: http.StatusBadRequest}

	Engine := NewDbConn()

	//查询详情
	model := []*models.MallProductPrice{}
	Engine.Table("mall_product_price").In("id", strings.Split(req.Id, ",")).Find(&model)
	if len(model) == 0 {
		out.Message = "未找到待删除的记录或记录已删除"
		return out, nil
	}

	session := Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		out.Message = "删除失败"
		return out, nil
	}

	_, err := session.Table("mall_product_price").In("id", strings.Split(req.Id, ",")).Delete(&models.MallProductPrice{})
	if err != nil {
		_ = session.Rollback()
		glog.Error("MallProductPriceDel失败, ", err)
		return out, err
	}

	data := []*models.OperateRecord{}
	for _, v := range model {
		tmp := &models.OperateRecord{
			OperateType:   1,
			OperateDetail: "移除电商仓商品skuid: " + cast.ToString(v.SkuId),
			CreateId:      req.CreateId,
			CreateName:    req.CreateName,
			CreateIp:      req.IpAddr,
			IpLocation:    req.IpLocation,
			CreateTime:    time.Now().Local(),
		}
		data = append(data, tmp)
	}
	_, err = session.Table("operate_record").Insert(&data)
	if err != nil {
		_ = session.Rollback()
		glog.Error("移除电商仓商品价格插入操作记录失败： ", err)
		return out, err
	}
	_ = session.Commit()

	out.Code = http.StatusOK
	return out, nil
}

//电商仓商品价格编辑
func (c *Product) MallProductPriceEdit(ctx context.Context, req *pc.MallProductPriceEditReq) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: http.StatusBadRequest}

	if req.Id <= 0 {
		return out, errors.New("参数错误！")
	}
	if req.Price < 1 {
		return out, errors.New("价格错误！")
	}

	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return nil, errors.New("用户不存在")
	}

	Engine := NewDbConn()

	//查询详情
	model := models.MallProductPrice{}
	has, _ := Engine.Table("mall_product_price").ID(req.Id).Get(&model)
	if !has {
		return nil, errors.New("ID不存在")
	}

	session := Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		out.Message = "编辑失败"
		return out, nil
	}

	productPrice := &models.MallProductPrice{
		Price:      cast.ToInt(req.Price),
		UpdateDate: time.Now().Local(),
		UpdateBy:   userInfo.UserNo,
		UpdateName: userInfo.UserName,
	}
	_, err := session.Table("mall_product_price").Cols("price, update_date, update_by, update_name").ID(req.Id).Update(productPrice)
	if err != nil {
		_ = session.Rollback()
		glog.Error("MallProductPriceEdit失败, ", err)
		return out, err
	}

	detail := "修改电商仓商品skuid: " + cast.ToString(model.SkuId) + "的价格，从" + cast.ToString(kit.FenToYuan(model.Price)) + "元修改为" + cast.ToString(kit.FenToYuan(int32(req.Price))) + "元"
	data := models.OperateRecord{
		OperateType:   2,
		OperateDetail: detail,
		CreateId:      req.CreateId,
		CreateName:    req.CreateName,
		CreateIp:      req.IpAddr,
		IpLocation:    req.IpLocation,
		CreateTime:    time.Now().Local(),
	}
	_, err = session.Table("operate_record").Insert(&data)
	if err != nil {
		_ = session.Rollback()
		glog.Error("编辑电商仓商品价格插入操作记录失败： ", err)
		return out, err
	}
	_ = session.Commit()

	out.Code = http.StatusOK
	return out, nil
}
