package services

import (
	"_/models"
	"_/proto/dac"
	"_/proto/dc"
	"_/proto/et"
	"_/proto/ic"
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

// 循环里面的全局链接
var MtGlobalProductClient *MtProductClientGlb
var GlobalDispatchClient *DispatchClientGlb

func init() {
	MtGlobalProductClient = new(MtProductClientGlb)
	GlobalDispatchClient = new(DispatchClientGlb)
}

type BaseClient struct {
	lock sync.Mutex
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
}
type DispatchClientGlb struct {
	BaseClient
	RPC dc.WarehouseServiceClient
}
type MtProductClient struct {
	BaseClient
	RPC         et.MtProductServiceClient
	ELMPRODUCT  et.ElmProductServiceClient
	JDDJPRODUCT et.JddjProductServiceClient
	ELMSTORE    et.ElmStoreServiceClient
}

func GetMtProductClient() *MtProductClient {
	var client MtProductClient
	var err error
	url := config.GetString("grpc.external")
	if url == "" {
		url = "127.0.0.1:11031"
	}
	//url = "10.1.1.248:11031"
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = et.NewMtProductServiceClient(client.Conn)
		client.ELMSTORE = et.NewElmStoreServiceClient(client.Conn)
		client.ELMPRODUCT = et.NewElmProductServiceClient(client.Conn)
		client.JDDJPRODUCT = et.NewJddjProductServiceClient(client.Conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*60)

		return &client
	}
}

type MtProductClientGlb struct {
	BaseClient
	RPC         et.MtProductServiceClient
	ELMPRODUCT  et.ElmProductServiceClient
	JDDJPRODUCT et.JddjProductServiceClient
	ELMSTORE    et.ElmStoreServiceClient
}

func GetMtGlobalProductClient() *MtProductClientGlb {
	MtGlobalProductClient.Ctx, _ = context.WithTimeout(context.Background(), 60*time.Second)

	if isAlive() {
		return MtGlobalProductClient
	}

	MtGlobalProductClient.lock.Lock()
	defer MtGlobalProductClient.lock.Unlock()

	if isAlive() {
		return MtGlobalProductClient
	}

	return NewMtGlobalProductClient()
}

func GetGlobalDispatchClient() *DispatchClientGlb {
	GlobalDispatchClient.Ctx, _ = context.WithTimeout(context.Background(), 60*time.Second)

	if isAlivedis() {
		return GlobalDispatchClient
	}

	GlobalDispatchClient.lock.Lock()
	defer GlobalDispatchClient.lock.Unlock()

	if isAlivedis() {
		return GlobalDispatchClient
	}

	return NewGlobalDispatchClient()
}

func NewGlobalDispatchClient() *DispatchClientGlb {
	var err error
	url := config.GetString("grpc.dispatch-center")
	if url == "" {
		url = "127.0.0.1:11006"
	}
	//url = "10.1.1.248:11006"
	if GlobalDispatchClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		GlobalDispatchClient.RPC = dc.NewWarehouseServiceClient(GlobalDispatchClient.Conn)
		GlobalDispatchClient.Ctx = context.Background()
		GlobalDispatchClient.Ctx, GlobalDispatchClient.Cf = context.WithTimeout(GlobalDispatchClient.Ctx, time.Second*60)
		return GlobalDispatchClient
	}

}

func NewMtGlobalProductClient() *MtProductClientGlb {
	var err error
	url := config.GetString("grpc.external")
	if url == "" {
		url = "127.0.0.1:11031"
	}
	//url = "10.1.1.248:11031"
	if MtGlobalProductClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("GetMtGlobalProductClient异常：", err)
		return nil
	} else {
		MtGlobalProductClient.RPC = et.NewMtProductServiceClient(MtGlobalProductClient.Conn)
		MtGlobalProductClient.ELMSTORE = et.NewElmStoreServiceClient(MtGlobalProductClient.Conn)
		MtGlobalProductClient.ELMPRODUCT = et.NewElmProductServiceClient(MtGlobalProductClient.Conn)
		MtGlobalProductClient.JDDJPRODUCT = et.NewJddjProductServiceClient(MtGlobalProductClient.Conn)
		//MtGlobalProductClient.Ctx = context.Background()
		//MtGlobalProductClient.Ctx, _ = context.WithTimeout(MtGlobalProductClient.Ctx, time.Second*60)

		glog.Info("GetMtGlobalProductClient成功。。。。")
		return MtGlobalProductClient
	}
}

func isAlive() bool {
	return MtGlobalProductClient != nil && MtGlobalProductClient.Conn != nil && MtGlobalProductClient.Conn.GetState().String() != "SHUTDOWN"
}
func isAlivedis() bool {
	return GlobalDispatchClient != nil && GlobalDispatchClient.Conn != nil && GlobalDispatchClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *MtProductClient) Close() {
	c.Conn.Close()
	c.Cf()
}

type InventoryCenterClient struct {
	BaseClient
	RPC ic.InventoryServiceClient
}

func GetInventoryCenterClient() *InventoryCenterClient {
	var client InventoryCenterClient
	var err error
	url := config.GetString("grpc.inventory-center")
	if url == "" {
		url = "127.0.0.1:11007"
	}
	//url = "10.1.1.248:11007"
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = ic.NewInventoryServiceClient(client.Conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*30)

		return &client
	}
}

func (c *InventoryCenterClient) Close() {
	c.Conn.Close()
	c.Cf()
}

type DataCenterClient struct {
	BaseClient
	RPC dac.DatacenterServiceClient
}

func GetDataCenterClient() *DataCenterClient {
	var client DataCenterClient
	var err error
	url := config.GetString("grpc.datacenter")
	if url == "" {
		url = "127.0.0.1:10032" //改为10032
	}
	//url = "10.1.1.248:10032"
	//url = "47.95.149.67:10032"  //SIT2
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = dac.NewDatacenterServiceClient(client.Conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*60)

		return &client
	}
}

func (c *DataCenterClient) Close() {
	c.Conn.Close()
	c.Cf()
}

func loadLoginUserInfo(ctx context.Context) *models.LoginUserInfo {
	isExist := ctx.Value("user_info")
	if isExist != nil {
		return isExist.(*models.LoginUserInfo)
	}

	var userInfo models.LoginUserInfo
	if md, ok := metadata.FromIncomingContext(ctx); ok && len(md.Get("login_user_info")) > 0 {
		if err := json.Unmarshal([]byte(md.Get("login_user_info")[0]), &userInfo); err != nil {
			glog.Error(err)
		}
	}
	//else {
	//	glog.Error("grpc context 加载用户登录信息失败")
	//}
	// 如果是宠物saas 过来的， userInfo.ScrmId代表的是线下门店端用户id
	if userInfo.ScrmId != "" {
		return &userInfo
	} else {
		if userInfo.UserNo == "" {
			return nil
		} else {
			return &userInfo
		}
	}

}

type DispatchClient struct {
	BaseClient
	RPC dc.WarehouseServiceClient
}

func GetDispatchClient() *DispatchClient {
	var client DispatchClient
	var err error
	url := config.GetString("grpc.dispatch-center")
	if url == "" {
		url = "127.0.0.1:11006"
	}
	//url = "10.1.1.248:11006"
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = dc.NewWarehouseServiceClient(client.Conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*60)
		return &client
	}
}

func (c *DispatchClient) Close() {
	c.Conn.Close()
	c.Cf()
}

type ExternalClient struct {
	lock         sync.Mutex
	Conn         *grpc.ClientConn
	Ctx          context.Context
	Cf           context.CancelFunc
	RPC          et.MtProductServiceClient
	MtOrder      et.MtOrderServiceClient
	MtStore      et.MtStoreServiceClient
	MtReturn     et.MtReturnOrderInfoClient
	ORDER        et.MtReturnOrderInfoClient
	ELMSTORE     et.ElmStoreServiceClient
	ELMPRODUCT   et.ElmProductServiceClient
	ELMORDER     et.ELMOrderServiceClient
	MPServer     et.MpServiceClient
	JddjProduct  et.JddjProductServiceClient
	JddjOrder    et.JddjOrderServiceClient
	JddjPlatform et.JddjPlatformServiceClient
	JddjBuss     et.JddjBussServiceClient
	ShanSong     et.IShanSongServiceClient
	WxVideo      et.WxVideoServiceClient
}

func NewExternalClient() *ExternalClient {
	var err error
	var grpcClient ExternalClient
	url := config.GetString("grpc.external")
	//url = "10.1.1.248:11031"
	if url == "" {
		url = "127.0.0.1:11031"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("external，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = et.NewMtProductServiceClient(grpcClient.Conn)
		grpcClient.MtOrder = et.NewMtOrderServiceClient(grpcClient.Conn)
		grpcClient.MtStore = et.NewMtStoreServiceClient(grpcClient.Conn)
		grpcClient.MtReturn = et.NewMtReturnOrderInfoClient(grpcClient.Conn)
		grpcClient.ELMSTORE = et.NewElmStoreServiceClient(grpcClient.Conn)
		grpcClient.ELMPRODUCT = et.NewElmProductServiceClient(grpcClient.Conn)
		grpcClient.ELMORDER = et.NewELMOrderServiceClient(grpcClient.Conn)
		grpcClient.MPServer = et.NewMpServiceClient(grpcClient.Conn)
		grpcClient.JddjProduct = et.NewJddjProductServiceClient(grpcClient.Conn)
		grpcClient.JddjOrder = et.NewJddjOrderServiceClient(grpcClient.Conn)
		grpcClient.JddjPlatform = et.NewJddjPlatformServiceClient(grpcClient.Conn)
		grpcClient.JddjBuss = et.NewJddjBussServiceClient(grpcClient.Conn)
		grpcClient.ShanSong = et.NewIShanSongServiceClient(grpcClient.Conn)
		grpcClient.WxVideo = et.NewWxVideoServiceClient(grpcClient.Conn)

		grpcClient.Ctx, grpcClient.Cf = context.WithTimeout(context.Background(), 6*time.Hour)

		return &grpcClient
	}
}

func (c *ExternalClient) Close() {
	c.Conn.Close()
	c.Cf()
}
