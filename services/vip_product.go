package services

import (
	"_/models"
	"_/proto/mk"
	"_/proto/pc"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// GoodsClassify 商品分类
type GoodsClassify struct {
	GcOne   int32 `json:"gc_one"`
	GcTwo   int32 `json:"gc_two"`
	GcThree int32 `json:"gc_three"`
}

type GoodsCate struct {
	GcId       int32  // 商品ID
	GcParentId int32  // 父ID
	GcName     string // 商品名称
}

type Member struct {
	MemberId       int32
	NewcomerTag    int32
	MemberDiscount float64
	VipCardState   int32
	UserLevelId    int32
}

// 免费等级价
type UserLevel struct {
	LevelId     int32
	MemberPrice float64
}

// 到家会员商品列表
func (c *Product) AwenVipGoodsList(ctx context.Context, in *pc.VipGoodsRequest) (out *pc.VipGoodsResponse, e error) {
	out = &pc.VipGoodsResponse{Code: 400}
	if in.FinanceCode == "" {
		out.Message = "财务编码不能为空"
		return
	}
	var vipGoods []*models.ChannelVipProduct
	session := NewDbConn().Table("channel_sku").Alias("sku").
		Where("sku.vip_discount > 0 and csp.up_down_state = 1 and csp.finance_code = ?", in.FinanceCode)

	if len(in.GcId) > 0 {
		session.And("csp.channel_category_id IN (?)", in.GcId)
	}
	if in.GoodName != "" {
		session.And("csp.name like ?", "%"+in.GoodName+"%")
	}
	if in.PriceState == 2 {
		session.OrderBy("csp.market_price asc")
	} else {
		session.OrderBy("csp.market_price desc")
	}
	if in.SalesState == 2 {
		session.OrderBy("csp.sales_volume asc")
	} else {
		session.OrderBy("csp.sales_volume desc")
	}
	if err := session.Select("sku.vip_discount, csp.sku_id, csp.product_id, csp.name, csp.market_price, csp.finance_code, cps.json_data").
		Join("left", "channel_store_product csp", "sku.id = csp.sku_id and sku.channel_id = 1").
		Join("left", "channel_product_snapshot cps", "csp.snapshot_id = cps.id").
		Find(&vipGoods); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}

	var strIds []string
	for _, x := range vipGoods {
		strIds = append(strIds, cast.ToString(x.SkuId))
	}

	//查出sku的优惠活动信息
	promotionMap := map[string][]*mk.PromotionSkuDto{}
	client := mk.GetMarketingCenterClient()
	if res, err := client.RPC.QueryPromotionBySkuIds(client.Ctx, &mk.PromotionQueryBySkuIdsRequest{
		ShopId:            in.FinanceCode,
		ChannelId:         1,
		SkuIds:            strIds,
		IsReachReduceInfo: false,
	}); err != nil {
		glog.Error(err)
	} else if res.Code != 200 {
		glog.Error(res.Message)
	} else {
		for _, v := range res.Data {
			promotionMap[v.SkuId] = append(promotionMap[v.SkuId], v)
		}
	}
	member := getUserInfo(in.UserId)
	if len(vipGoods) > 0 {
		for i := range vipGoods {
			date := vipGoods[i]
			MarketPrice := date.MarketPrice
			var GoodsPromotionType int32
			var GoodsPromotionPrice int32
			var MemberPrice1 int32
			var IsMemberPrice int32
			for _, p := range promotionMap[cast.ToString(date.SkuId)] {
				switch p.Types {
				case 2:
					GoodsPromotionType = 2
					switch p.TimeDiscount.DisountType {
					case 0: //按折扣计算
						GoodsPromotionPrice = int32(decimal.NewFromInt32(date.MarketPrice * p.TimeDiscount.DiscountValue).Div(decimal.NewFromInt32(1000)).Ceil().IntPart())
					case 1: //固定价格
						GoodsPromotionPrice = p.TimeDiscount.DiscountValue
					}

					// 折上折
					if p.TimeDiscount.VipDiscount > 0 {
						price := MarketPrice
						if GoodsPromotionPrice > 0 { // 折后折
							price = GoodsPromotionPrice
						}
						MemberPrice1 = int32(decimal.NewFromInt32(p.TimeDiscount.VipDiscount * price).Div(decimal.NewFromInt32(1000)).Ceil().IntPart())
						if member.VipCardState > 0 {
							IsMemberPrice = 1
						}
						GoodsPromotionType = 0
						GoodsPromotionPrice = 0
					}
				case 4:
					GoodsPromotionPrice = 0
					GoodsPromotionType = 0
					if member.VipCardState > 0 {
						IsMemberPrice = 1
					}
					MemberPrice1 = int32(decimal.NewFromInt32(p.VipDiscount.DiscountValue * MarketPrice).Div(decimal.NewFromInt32(1000)).Ceil().IntPart())
				}
			}
			var resData = pc.VipGoodsData{
				SkuId:               date.SkuId,
				ProductId:           date.ProductId,
				Name:                date.Name,
				MarketPrice:         date.MarketPrice,
				MemberPrice_1:       MemberPrice1,
				IsMemberPrice:       IsMemberPrice,
				GoodsPromotionType:  GoodsPromotionType,
				GoodsPromotionPrice: GoodsPromotionPrice,
			}
			out.Data = append(out.Data, &resData)
		}
	}
	out.Code = 200
	return
}

// 电商会员商品列表 vip-3.0
func (c *Product) ShopVipGoodsList(ctx context.Context, in *pc.VipGoodsRequest) (out *pc.VipGoodsResponse, e error) {
	out = &pc.VipGoodsResponse{Code: 400}
	var (
		vipGoods       []*models.VipGoods
		serviceFeeGood models.VipGoods
		member         Member
		equityType10   int32 //健康服务金 权益id
	)
	var CardId int32

	if in.UserId != "" {
		member = getUserInfo(in.UserId)
		//判断用户购买地区卡是否配置了会员商品权益
		if member.VipCardState == 1 {

			type equtityS struct {
				Id         int32 `json:"id"`
				EquityType int32 `json:"equity_type"`
			}
			equtitySli := make([]equtityS, 0)
			if err := NewDatacenterDbConn().SQL(`
select distinct e.id,e.equity_type from vip_card_order o left join vip_card_template vct on o.card_id = vct.id
left join vip_card_equity_config c on vct.or_id = c.or_id
inner join vip_card_equity e on e.id = c.equity_id and (e.equity_type = 4 or e.equity_type = 10)
where o.state = 10 and o.expiry_date > now() and o.user_id = ?;
`, in.UserId).Find(&equtitySli); err != nil {
				out.Message = "查询会员商品权益数据异常" + err.Error()
				return
			}
			//获取 会员价商品权益 id
			var equityType4 int32
			for _, v := range equtitySli {
				if v.EquityType == 4 {
					equityType4 = v.Id
				}
				if v.EquityType == 10 {
					equityType10 = v.Id
				}
			}
			//没有配置 会员价商品权益
			if equityType4 == 0 {
				out.Code = 200
				out.Total = 0
				return
			}
		}

		CardId = GetUserCard(in.UserId)

	}
	db := UpetNewDbConn()
	db.ShowSQL(true)
	session := db.Table("upet_goods").Alias("g")
	if in.Type == 3 && in.GcId != "" && CardId == 1 {
		session.Where("(vip_discount > 0 or vip_state =2) and " +
			"goods_verify = 1 and goods_state = 1 AND store_id=1 and g.goods_id not in(select goods_id from upet_p_time where  promotion_type not in (2,10) and state =1 AND store_id=1 and start_time < unix_timestamp() and end_time > unix_timestamp())")
	} else {
		session.Where("vip_discount > 0 and " +
			"goods_verify=1 and goods_state=1 AND store_id=1 and g.goods_id not in(select goods_id from upet_p_time where  promotion_type not in (2,10) and state =1 AND store_id=1 and start_time < unix_timestamp() and end_time > unix_timestamp())")
	}

	switch in.Type {
	case 1:
		session.And("g.vip_state = 1")
	case 2:
		session.And("g.vip_state = 0")
	case 3:
		session.OrderBy("g.sort asc,g.goods_id")
	}
	ids2 := make([]string, 0)
	if len(in.GcId) > 0 {
		//查询是否有下级分类
		ids2, _ = getChildCate(strings.Split(in.GcId, ","))
		session.In("g.gc_id", ids2)
	}
	if in.GoodName != "" {
		session.And("g.goods_name like ?", "%"+in.GoodName+"%")
	}
	if in.PriceState == 1 {
		session.OrderBy("g.goods_price desc")
	} else if in.PriceState == 2 {
		session.OrderBy("g.goods_price asc")
	}
	if in.SalesState == 1 {
		session.OrderBy("g.goods_salenum desc")
	} else if in.SalesState == 2 {
		session.OrderBy("g.goods_salenum asc")
	}
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	if total, err := session.Clone().Count(); err != nil {
		out.Message = err.Error()
		return
	} else if total > 0 {
		err = session.Select("g.goods_id,g.goods_commonid,g.goods_name,g.goods_image,g.enable_member_price,"+
			"g.goods_price,g.goods_marketprice,g.vip_discount,g.vip_state").
			GroupBy("g.goods_id").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
			Find(&vipGoods)
		if err != nil {
			out.Message = "查询数据异常" + err.Error()
			return
		}
		out.Total = int32(total)
	}

	bbcImgPath := config.GetString("bbc_img_path")
	if len(vipGoods) > 0 {
		for i := range vipGoods {
			date := vipGoods[i]

			MarketPrice := int32(decimal.NewFromFloat(date.GoodsPrice).Mul(decimal.NewFromInt32(100)).Round(0).IntPart())
			var goodsPromotionType int32
			var goodsPromotionPrice int32
			var MemberPrice1 int32
			var IsMemberPrice int32

			//医保价优化判断
			if date.VipState == 2 {
				IsMemberPrice = 1
				MemberPrice1 = int32(decimal.NewFromFloat(date.GoodsPrice).Mul(decimal.NewFromInt32(100)).Round(0).IntPart())
				MarketPrice = int32(decimal.NewFromFloat(date.GoodsMarketprice).Mul(decimal.NewFromInt32(100)).Round(0).IntPart())
			} else {
				//查询限时活动
				var pTime models.PTime
				if has, err := db.SQL(`select goods_id,promotion_type,promotion_price from upet_p_time where 
				start_time < unix_timestamp() and end_time > unix_timestamp() and promotion_type = 2 and goods_id = ?;`, date.GoodsId).
					Get(&pTime); err != nil {
					out.Message = "查询促销活动数据异常" + err.Error()
					return
				} else if has {
					date.PromotionType = pTime.PromotionType
					date.PromotionPrice = pTime.PromotionPrice
				}

				if date.PromotionType > 0 && (date.PromotionType != 7 || member.NewcomerTag == 1) {
					goodsPromotionType = date.PromotionType
					goodsPromotionPrice = int32(decimal.NewFromFloat(date.PromotionPrice).Mul(decimal.NewFromInt32(100)).Round(0).IntPart())
				} else if date.PromotionType == 7 {
					date.PromotionType = 0
				}

				if date.PromotionType == 0 || date.PromotionType == 2 {
					price := MarketPrice
					if goodsPromotionPrice > 0 { // 折后折
						price = goodsPromotionPrice
					}
					// 免费会员价
					if member.MemberDiscount > 0 && date.EnableMemberPrice > 0 {
						freeVip := int32(decimal.NewFromFloat(member.MemberDiscount).Mul(decimal.NewFromInt32(MarketPrice)).Div(decimal.NewFromInt32(10)).Ceil().IntPart())
						if freeVip < price {
							MemberPrice1 = freeVip
							IsMemberPrice = 1
						}
					}
					// 付费会员价
					if date.VipDiscount > 0 {
						vip := int32(decimal.NewFromFloat(date.VipDiscount).Mul(decimal.NewFromInt32(price)).Div(decimal.NewFromInt32(10)).Ceil().IntPart())
						if member.MemberId > 0 && IsMemberPrice == 1 && MemberPrice1 <= vip {
							//去掉免费更底的商品
							continue
						}
						// 付费会员价更低
						if MemberPrice1 == 0 || vip <= MemberPrice1 {
							if member.VipCardState > 0 {
								IsMemberPrice = 1
								MemberPrice1 = vip
								//todo 商品没有参与活动， 则市场价 不会去乘以会员折扣
								if goodsPromotionPrice > 0 { //Mul(decimal.NewFromFloat(v.MemberPrice))
									MarketPrice = int32(decimal.NewFromFloat(date.VipDiscount).Mul(decimal.NewFromInt32(MarketPrice)).Div(decimal.NewFromInt32(10)).Ceil().IntPart())
								}
							} else if member.MemberId > 0 && vip <= MemberPrice1 { // 已登/未购买，当最低购买价是付费会员价时
								MarketPrice = MemberPrice1
								MemberPrice1 = vip
								IsMemberPrice = 0
							} else if member.MemberId > 0 && member.VipCardState == 0 { // 已登/未购买，当最低购买价是限时价格时
								MarketPrice = price
								MemberPrice1 = int32(decimal.NewFromFloat(date.VipDiscount).Mul(decimal.NewFromInt32(price)).Div(decimal.NewFromInt32(10)).Ceil().IntPart())
							} else if MemberPrice1 > 0 { // 没开通付费会员，且免费会员价格低
								continue
								MarketPrice = MemberPrice1
								IsMemberPrice = 0
							} else {
								MemberPrice1 = vip
								MarketPrice = price
							}
						}
					}
					if MemberPrice1 > 0 {
						goodsPromotionType = 0
						goodsPromotionPrice = 0
					}
				}
			}
			GoodsImage := date.GoodsImage
			if !strings.HasPrefix(date.GoodsImage, "http") {
				GoodsImage = bbcImgPath + date.GoodsImage
			}

			if date.PromotionType == 2 && IsMemberPrice > 0 {
				GoodsImage = strings.Split(GoodsImage, "?")[0]
			}
			var resData = pc.VipGoodsData{
				SkuId:               date.GoodsId,
				ProductId:           date.GoodsCommonid,
				Name:                date.GoodsName,
				MarketPrice:         MarketPrice,
				MemberPrice_1:       MemberPrice1,
				GoodsPromotionType:  goodsPromotionType,
				GoodsPromotionPrice: goodsPromotionPrice,
				IsMemberPrice:       IsMemberPrice,
				GoodsImage:          GoodsImage,
			}
			out.Data = append(out.Data, &resData)
		}
	}

	//购买了会员卡，且该会员卡配置了健康服务金 服务， 才混排医保价商品,有分类则不混排
	if member.VipCardState == 1 && equityType10 > 0 && (len(in.GcId) == 0 && in.Type == 3) {
		//只有有会员价商品的时候，才加入一条医保价商品
		if len(out.Data) > 0 {
			_session := db.Table("upet_goods").Select("goods_id, goods_commonid,goods_name,goods_image,enable_member_price,goods_price,goods_marketprice,vip_discount").
				Where("vip_state =? and goods_verify=? and goods_state=? AND store_id=1", 2, 1, 1)
			if len(in.GcId) > 0 {
				_session.In("gc_id", ids2)
			}
			if in.GoodName != "" {
				_session.And("goods_name like ?", "%"+in.GoodName+"%")
			}
			has, err := _session.Limit(1, int(in.PageIndex*1)-int(1)).Get(&serviceFeeGood)
			if err != nil {
				glog.Errorf("ShopVipGoodsList===入参：%s,获取医保价商品失败：%s", kit.JsonEncode(in), err.Error())
			}
			if has {
				GoodsImage := serviceFeeGood.GoodsImage
				if !strings.HasPrefix(serviceFeeGood.GoodsImage, "http") {
					GoodsImage = bbcImgPath + serviceFeeGood.GoodsImage
				}
				resData := pc.VipGoodsData{
					SkuId:               serviceFeeGood.GoodsId,
					ProductId:           serviceFeeGood.GoodsCommonid,
					Name:                serviceFeeGood.GoodsName,
					MarketPrice:         int32(decimal.NewFromFloat(serviceFeeGood.GoodsMarketprice).Mul(decimal.NewFromInt32(100)).Round(0).IntPart()),
					MemberPrice_1:       int32(decimal.NewFromFloat(serviceFeeGood.GoodsPrice).Mul(decimal.NewFromInt32(100)).Round(0).IntPart()),
					GoodsPromotionType:  0,
					GoodsPromotionPrice: 0,
					IsMemberPrice:       1,
					GoodsImage:          GoodsImage,
				}
				out.Data = append(out.Data, &resData)
				out.Total += 1
			}
		}

	}
	out.Code = 200
	return
}

func getUserInfo(userID string) (member Member) {
	if _, err := UpetNewDbConn().Table("upet_member").Alias("m").
		Join("left", "datacenter.user_level l", "l.level_id = m.user_level_id").
		Where("m.scrm_user_id = ?", userID).
		Select("m.member_id,m.newcomer_tag,l.member_price as member_discount,m.vip_card_state,m.user_level_id").Get(&member); err != nil {
		glog.Error("getUserInfo " + err.Error())
	}
	return member
}

// 电商会员商品分类
func (c *Product) ShopCateList(ctx context.Context, in *pc.ShopCateReq) (out *pc.ShopCateListResponse, e error) {
	out = &pc.ShopCateListResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("ShopCateList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	var (
		CardId int32
		stuArr []*GoodsClassify
	)

	if in.UserId != "" {
		CardId = GetUserCard(in.UserId)
	}

	db := UpetNewDbConn()
	session := db.Table("upet_goods").Select("gc_id_1 as gc_one,gc_id_2 as gc_two,gc_id_3 as gc_three").
		Where("goods_verify=1 and goods_state=1 AND store_id=?", in.OrgId)
	if CardId == 1 { //有全国卡返回会员价跟医保价的分类
		session.Where("vip_state > 0")
	} else { //否则只返回会员价的分类
		session.Where("vip_discount > 0")
	}
	if err := session.GroupBy("gc_id").Find(&stuArr); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}

	var ids []int32
	for _, v := range stuArr {
		ids = append(ids, v.GcOne)
		ids = append(ids, v.GcTwo)
		ids = append(ids, v.GcThree)
	}
	var goodsCateArr []*GoodsCate
	if err := db.Table("upet_goods_class").Select("gc_id,gc_name,gc_parent_id").In("gc_id", ids).
		Find(&goodsCateArr); err != nil {
		return nil, err
	}
	if res, err := getListGoods(goodsCateArr, 0, 0); err != nil {
		out.Message = "获取分类异常" + err.Error()
		return
	} else {
		out.Code = 200
		out.Data = res
	}
	return
}

// ShopCateListByClassify 电商会员商品分类 vip-3.0
func (c *Product) ShopCateListByClassify(ctx context.Context, in *pc.ShopCateListReq) (out *pc.ShopCateListByClassifyResponse, e error) {
	out = &pc.ShopCateListByClassifyResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("ShopCateList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	classify := in.Classify
	var selectSql string
	if classify == 0 {
		selectSql = "CONCAT_WS(',',gc_id_1,gc_id_2,gc_id_3)"
	} else {
		selectSql = fmt.Sprintf("gc_id_%d", classify)
	}
	//1、未登录时//已登录未买全国卡，---- 底部的会员价商品--分类，只查  会员商品分类
	//2、已登录，且已开通全国卡，---- 底部的会员价商品--分类，查医保价+会员价商品 分类
	var CardId int32
	if in.UserId != "" {
		CardId = GetUserCard(in.UserId)
	}

	where := "vip_discount > 0 AND"
	if CardId == 1 && in.Type == 1 {
		where = "(vip_discount > 0 or vip_state = 2) AND"
	} else if in.Type == 2 {
		where = "vip_state = 2 AND"
	}
	var gcIdStrArr []string
	if err := UpetNewDbConn().SQL(fmt.Sprintf("SELECT %s FROM upet_goods WHERE  %s goods_verify=1 AND goods_state=1 AND store_id=%d GROUP BY gc_id;", selectSql, where, in.OrgId)).
		Find(&gcIdStrArr); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}
	var ids []string
	idsMap := make(map[string]int)
	for _, gcIdStr := range gcIdStrArr {
		gcIdArr := strings.Split(gcIdStr, ",")
		for _, id := range gcIdArr {
			//去掉重复的分类id
			idsMap[id] = 0
		}

	}

	for k := range idsMap {
		ids = append(ids, k)
	}

	var goodsCateArr []*pc.ShopCateData
	if err := UpetNewDbConn().Table("upet_goods_class").Select("GROUP_CONCAT(gc_id) as gc_ids,gc_name").In("gc_id", ids).
		GroupBy("gc_name").
		OrderBy("gc_sort desc").
		Find(&goodsCateArr); err != nil {
		return nil, err
	}
	out.Code = 200
	out.Data = goodsCateArr
	return
}

// 1、未登录时//已登录未买全国卡，---- 底部的会员价商品--分类，只查  会员商品分类
// 2、已登录，且已开通全国卡，---- 底部的会员价商品--分类，查医保价+会员价商品 分类
func GetUserCard(UserId string) (CardId int32) {
	var id int32
	if has, err := NewDatacenterDbConn().SQL("select id from datacenter.vip_card_order where card_id =1 and state=10 and "+
		"expiry_date>current_date() and user_id = ?", UserId).Get(&id); err != nil {
		return 0
	} else if has {
		return 1
	}
	return
}

// 到家会员商品分类
func (c *Product) AwenCateList(ctx context.Context, in *pc.AwenCateListReq) (out *pc.ShopCateListResponse, e error) {
	out = &pc.ShopCateListResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("AwenCateList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	Db := NewDbConn()
	var ids []*int32
	if err := Db.Table("channel_sku").Alias("sku").Select("csp.channel_category_id").
		Join("left", "channel_store_product as csp", "sku.id = csp.sku_id").
		Where("sku.vip_discount >0 and sku.channel_id=1 and csp.up_down_state =1 and csp.finance_code=?", in.FinanceCode).
		GroupBy("csp.channel_category_id").
		Find(&ids); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}
	idArr, err := getParentCate(ids)
	if err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}
	ids = append(ids, idArr...)
	var goodsCateArr []*GoodsCate
	if err := Db.Table("channel_category").Select("id as gc_id,name as gc_name,parent_id as gc_parent_id").In("id", ids).
		Find(&goodsCateArr); err != nil {
		return nil, err
	}
	if res, err := getListGoods(goodsCateArr, 0, 0); err != nil {
		out.Message = "获取分类异常" + err.Error()
	} else {
		out.Code = 200
		out.Data = res
	}
	return
}

// 获取到家父级分类
func getParentCate(ids []*int32) (id []*int32, e error) {
	var goodsCateArr []*GoodsCate
	if err := NewDbConn().Table("channel_category").Select("id as gc_id,name as gc_name,parent_id as gc_parent_id").
		Where("parent_id > 0").In("id", ids).
		Find(&goodsCateArr); err != nil {
		return nil, err
	}
	if len(goodsCateArr) > 0 {
		for _, v := range goodsCateArr {
			id = append(id, &v.GcParentId)
		}
		getParentCate(id)
	}

	return id, nil
}

// 获得列出商品分类
func getListGoods(stuAll []*GoodsCate, pid, lev int32) ([]*pc.GoodCateData, error) {
	var goodArr []*pc.GoodCateData
	for _, v := range stuAll {
		if v.GcParentId == pid {
			children, _ := getListGoods(stuAll, v.GcId, lev+1)
			node := &pc.GoodCateData{
				GcId:       v.GcId,
				GcParentId: v.GcParentId,
				GcName:     v.GcName,
				Level:      lev,
				Children:   children,
			}
			goodArr = append(goodArr, node)
		}
	}
	return goodArr, nil
}

// 获取电商子类
func getChildCate(id []string) (ids []string, e error) {
	var goodsCateArr []*GoodsCate
	if err := UpetNewDbConn().Table("upet_goods_class").Select("gc_id,gc_name,gc_parent_id").
		In("gc_parent_id", id).
		Find(&goodsCateArr); err != nil {
		return nil, err
	}
	if len(goodsCateArr) > 0 {
		for _, v := range goodsCateArr {
			ids = append(ids, strconv.Itoa(int(v.GcId)))
		}
		getChildCate(ids)
	}

	return ids, nil
}

// 会员商品明细
func (c *Product) ShopVipCardGoodsList(ctx context.Context, in *pc.ShopVipCardGoodsRequest) (out *pc.ShopVipCardGoodsResponse, e error) {
	out = &pc.ShopVipCardGoodsResponse{Code: 400}

	db := UpetNewDbConn()
	session := db.Table("upet_goods").Alias("g").Where("g.vip_discount > 0 AND store_id=?", in.OrgId)

	switch in.IsFree {
	case 1:
		session.And("g.enable_member_price =1")
	case 2:
		session.And("g.enable_member_price =0")
	}

	if in.SkuId > 0 {
		session.And("g.goods_id = ?", in.SkuId)
	}

	if in.SpuId > 0 {
		session.And("g.goods_commonid = ?", in.SpuId)
	}
	if in.GoodsName != "" {
		session.And("g.goods_name like ?", "%"+in.GoodsName+"%")
	}

	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	if in.Export == 0 {
		session.Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize))
	}
	var Goods []*models.VipGoodsExtend
	total, err := session.Select("g.goods_id,g.goods_commonid,g.goods_name,g.goods_image,g.enable_member_price,"+
		"g.goods_price,g.goods_marketprice,g.vip_discount,g.have_gift,p.promotion_type,p.promotion_price").
		Join("left", fmt.Sprintf("(select goods_id,promotion_type, promotion_price from upet_p_time where state = 1 AND store_id=%d and end_time > unix_timestamp() group by goods_id) p", in.OrgId), "g.goods_id = p.goods_id").
		FindAndCount(&Goods)
	if err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}

	if total > 0 {
		var PromotionTypeMap = map[int32]string{2: "限时折扣", 5: "拼团", 6: "周期购", 7: "新人专享", 8: "预售", 9: "新秒杀", 99: "助力"}
		var userLevel []UserLevel
		err = NewDatacenterDbConn().Table("user_level").Where("member_price > 0").Find(&userLevel)
		if err != nil {
			out.Message = "查询数据异常" + err.Error()
			return
		}

		for i := range Goods {
			date := Goods[i]
			vipDiscount, _ := decimal.NewFromFloat(date.VipDiscount).Div(decimal.NewFromInt32(10)).Round(2).Float64()
			var resData = pc.VipGoodsList{
				Id:                  cast.ToInt32(i) + 1,
				SkuId:               date.GoodsId,
				SpuId:               date.GoodsCommonid,
				GoodsName:           date.GoodsName,
				MarketPrice:         date.GoodsMarketprice,
				GoodsPrice:          date.GoodsPrice,
				GoodsPromotionType:  date.PromotionType,
				GoodsPromotionPrice: date.PromotionPrice,
				IsMemberText:        "否",
				VipDiscount:         vipDiscount,
				VipDiscountPrice:    date.GoodsPrice,
			}
			if date.EnableMemberPrice == 1 {
				resData.IsMemberText = "是"
			}

			//促销类型： 5-拼团 8-预售 99助力 参与付费会员折扣的价格、付费会员价 显示 /
			//付费会员价：6-周期购 7-新人专享 9-新秒杀 买一赠一 显示：销售价*付费折扣
			PromotionPrice, _ := decimal.NewFromFloat(date.GoodsPrice).Mul(decimal.NewFromFloat(date.VipDiscount)).Div(decimal.NewFromInt32(10)).Round(2).Float64()
			if date.HaveGift == 1 {
				resData.GoodsPromotionName = "买一赠一"
				resData.GoodsPromotionPrice = 0
			}
			resData.VipMemberPrice = PromotionPrice
			//付费会员价：限时折扣：商城活动价*付费折扣
			switch date.PromotionType {
			case 2:
				memberPrice1, _ := decimal.NewFromFloat(date.PromotionPrice).Mul(decimal.NewFromFloat(date.VipDiscount)).Div(decimal.NewFromInt32(10)).Round(2).Float64()
				resData.VipMemberPrice = memberPrice1
				resData.VipDiscountPrice = date.PromotionPrice
				if resData.GoodsPromotionName != "" {
					resData.GoodsPromotionName = fmt.Sprintf("%s,%s", resData.GoodsPromotionName, PromotionTypeMap[date.PromotionType])
				} else {
					resData.GoodsPromotionName = PromotionTypeMap[date.PromotionType]
				}
				resData.GoodsPromotionPrice = date.PromotionPrice
			case 6:
				resData.GoodsPromotionPrice = 0
				resData.GoodsPromotionName = PromotionTypeMap[date.PromotionType]
			case 7, 9:
				resData.GoodsPromotionName = PromotionTypeMap[date.PromotionType]
			case 5, 8, 99:
				resData.VipDiscountPrice = 0
				resData.VipMemberPrice = 0
				resData.GoodsPromotionName = PromotionTypeMap[date.PromotionType]
			}

			// 免费会员价
			if len(userLevel) > 0 && date.EnableMemberPrice == 1 {
				var leverPriceText []*pc.MemberLeverPrice
				for i := 0; i < len(userLevel); i++ {
					v := userLevel[i]
					price, _ := decimal.NewFromFloat(date.GoodsPrice).Mul(decimal.NewFromFloat(v.MemberPrice)).Div(decimal.NewFromInt32(10)).Round(2).Float64()
					text := fmt.Sprintf("V%d:%.2f", v.LevelId, price)
					leverPriceText = append(leverPriceText, &pc.MemberLeverPrice{
						LeverPriceText: text,
					})
					//最高等级的价格
					if i+1 == len(userLevel) {
						resData.MemberPrice = price
					}
				}
				resData.LeverPrice = leverPriceText
			}

			out.Data = append(out.Data, &resData)
		}
	}
	out.Total = int32(total)
	out.Code = 200
	return
}

// DelShopVipCardGoods 删除会员商品
func (c *Product) DelShopVipCardGoods(ctx context.Context, in *pc.DelShopVipCardGoodsReq) (out *pc.ProductBaseResponse, e error) {
	out = &pc.ProductBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("DeleteGift 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.SkuId <= 0 {
		out.Message = "请求参数异常"
		return
	}
	db := UpetNewDbConn()
	session := db.NewSession()
	type goods struct {
		GoodsId     int32
		GoodsName   string
		VipDiscount float64
	}
	var goodsMod goods
	if has, err := db.SQL("select goods_id,goods_name,vip_discount from upet_goods where goods_id = ? AND store_id=?;", in.SkuId, in.OrgId).Get(&goodsMod); err != nil {
		out.Message = "操作数据库异常"
		return
	} else if !has {
		out.Message = "查询不到商品信息"
		return
	}
	if goodsMod.VipDiscount-0.001 <= 0 {
		out.Message = "无更新记录"
		return
	}

	session.Begin()
	if _, err := session.Exec("update upet_goods set vip_discount=0,vip_state=0 where goods_id = ? AND store_id=?", in.SkuId, in.OrgId); err != nil {
		out.Message = "操作数据库异常"
		session.Rollback()
		return
	}

	nowTime := time.Now().Unix()
	model := &models.UpetGoodsHandlelog{
		GhGoodIds: cast.ToString(in.SkuId),
		GhNotes:   "删除VIP商品",
		GhAddtime: nowTime,
		GhState:   0,
	}
	if _, err := session.Insert(model); err != nil {
		out.Message = "插入更新ES出错" + err.Error()
		session.Rollback()
		return
	}
	session.Commit()
	//添加日志
	desc := fmt.Sprintf("%v，%s", in.SkuId, goodsMod.GoodsName)
	//记录日志
	NewDatacenterDbConn().Insert(models.StoreOperateLog{
		Type:       4,
		Desc:       desc,
		UserName:   in.UserName,
		UserNo:     in.UserId,
		BeforeJson: kit.JsonEncode(goodsMod.VipDiscount),
	})

	out.Code = 200
	return
}

// ShopGoodsList 电商竖屏首页实物商品列表
func (c *Product) ShopGoodsList(ctx context.Context, in *pc.GoodsRequest) (out *pc.GoodsResponse, e error) {
	out = &pc.GoodsResponse{Code: 400}
	var vipGoods []*models.VipGoods
	db := UpetNewDbConn()
	session := db.Table("upet_goods").Alias("g").Where("g.is_virtual=0 and g.goods_verify=1 and g.goods_state=1 and g.vip_state <> 2 and g.g_search_status =0 AND store_id=? and "+
		"g.goods_id not in(select goods_id from upet_p_time where  promotion_type != 2 and state =1 and store_id=? and "+
		"start_time < unix_timestamp() and end_time > unix_timestamp())", in.OrgId, in.OrgId)
	if in.GoodsId > 0 {
		session.And("g.goods_id = ?", in.GoodsId)
	}
	if in.Search == 1 && in.GoodName == "" {
		out.Code = 200
		return
	}
	if in.GoodName != "" {
		session.And("g.goods_name like ?", "%"+in.GoodName+"%")
	}
	session.OrderBy("g.goods_salenum desc")

	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	if total, err := session.Clone().Count(); err != nil {
		out.Message = err.Error()
		return
	} else if total > 0 {
		err = session.Select("g.goods_id,g.goods_commonid,g.goods_name,g.goods_image,g.enable_member_price,"+
			"g.goods_price,g.goods_marketprice,g.vip_discount").
			GroupBy("g.goods_id").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
			Find(&vipGoods)
		if err != nil {
			out.Message = "查询数据异常" + err.Error()
			return
		}
		out.Total = int32(total)
	}

	var member Member
	if in.UserId != "" {
		member = getUserInfo(in.UserId)
	}
	bbcImgPath := config.GetString("bbc_img_path")
	if len(vipGoods) > 0 {
		for i := range vipGoods {
			var (
				goodsPromotionType  int32
				goodsPromotionPrice int32
				MemberPrice1        int32
				IsMemberPrice       int32 //显示有更低的免费会员价或付费会员价
				UserLevelId         int32 //当登录有免费更低价时，才返回等级；否则重置为0，前端以此来判断显示文案
			)
			date := vipGoods[i]
			//查询限时活动
			var pTime models.PTime
			if has, err := db.SQL(`select goods_id,promotion_type,promotion_price from upet_p_time where 
				start_time < unix_timestamp() and end_time > unix_timestamp() and promotion_type = 2 and goods_id = ?;`, date.GoodsId).
				Get(&pTime); err != nil {
				out.Message = "查询促销活动数据异常" + err.Error()
				return
			} else if has {
				date.PromotionType = pTime.PromotionType
				date.PromotionPrice = pTime.PromotionPrice
			}

			MarketPrice := int32(decimal.NewFromFloat(date.GoodsPrice).Mul(decimal.NewFromInt32(100)).Round(0).IntPart())

			if date.PromotionType > 0 && (date.PromotionType != 7 || member.NewcomerTag == 1) {
				goodsPromotionType = date.PromotionType
				goodsPromotionPrice = int32(decimal.NewFromFloat(date.PromotionPrice).Mul(decimal.NewFromInt32(100)).Round(0).IntPart())
			} else if date.PromotionType == 7 {
				date.PromotionType = 0
			}

			if date.PromotionType == 0 || date.PromotionType == 2 {
				price := MarketPrice
				if goodsPromotionPrice > 0 { // 折后折
					price = goodsPromotionPrice
				}
				// 免费会员价
				if member.MemberDiscount > 0 && date.EnableMemberPrice > 0 {
					freeVip := int32(decimal.NewFromFloat(member.MemberDiscount).Mul(decimal.NewFromInt32(MarketPrice)).Div(decimal.NewFromInt32(10)).Ceil().IntPart())
					if freeVip <= price {
						MemberPrice1 = freeVip
						IsMemberPrice = 1
						UserLevelId = member.UserLevelId
					}
				}
				// 付费会员价
				if date.VipDiscount > 0 {
					vip := int32(decimal.NewFromFloat(date.VipDiscount).Mul(decimal.NewFromInt32(price)).Div(decimal.NewFromInt32(10)).Ceil().IntPart())
					// 付费会员价更低
					if MemberPrice1 == 0 || vip < MemberPrice1 {
						if member.VipCardState > 0 {
							UserLevelId = 0
							IsMemberPrice = 1
							MemberPrice1 = vip
							if goodsPromotionPrice > 0 { //Mul(decimal.NewFromFloat(v.MemberPrice))
								MarketPrice = int32(decimal.NewFromFloat(date.VipDiscount).Mul(decimal.NewFromInt32(MarketPrice)).Div(decimal.NewFromInt32(10)).Ceil().IntPart())
							}
						} else if MemberPrice1 > 0 { // 没开通付费会员，且免费会员价格低
							UserLevelId = member.UserLevelId
						} else {
							MemberPrice1 = vip
							MarketPrice = price
						}
					}
				}
				if MemberPrice1 > 0 {
					goodsPromotionType = 0
					goodsPromotionPrice = 0
				}
			}
			GoodsImage := date.GoodsImage
			if !strings.HasPrefix(date.GoodsImage, "http") {
				GoodsImage = bbcImgPath + date.GoodsImage
			}

			var resData = pc.GoodsData{
				SkuId:               date.GoodsId,
				ProductId:           date.GoodsCommonid,
				Name:                date.GoodsName,
				MarketPrice:         kit.FenToYuan(MarketPrice),
				MemberPrice_1:       kit.FenToYuan(MemberPrice1),
				GoodsPromotionType:  goodsPromotionType,
				GoodsPromotionPrice: kit.FenToYuan(goodsPromotionPrice),
				IsMemberPrice:       IsMemberPrice,
				GoodsImage:          GoodsImage,
				UserLevelId:         UserLevelId,
			}
			out.Data = append(out.Data, &resData)
		}
	}
	out.Code = 200
	return
}
