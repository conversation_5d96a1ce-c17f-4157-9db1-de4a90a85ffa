package services

import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/es"
	"_/proto/et"
	"_/proto/pc"
	"_/utils"
	"context"
	"errors"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

/**
查询记录信息
*/

//删除商品
/**
公共部分
	删除商品信息 channel_product,channel_sku_group
	删除channel_sku, channel_sku_third, ChannelSkuValue
	删除快照上架信息channel_store_product，channel_product_snapshot
阿闻，清除es数据 ,清理channel_store_product_has_stock
第三方渠道：调用三方接口直接删除


注意点：
	1：阿闻和美团因为是不同的商品库，所以是按照货号来删除的，如果传递的是子龙货号就删除所有的门店仓，如果是A8就删除关联的前置仓
	2：京东和饿了么是不区分门店的，直接删除的品牌库
*/
func (c *Product) DeleteChannelProduct(ctx context.Context, in *pc.DeleteProductVo) (*pc.BaseResponse, error) {
	prefix := "删除商品日志记录："
	glog.Info(prefix, " 参数信息：", kit.JsonEncode(in))

	var res = &pc.BaseResponse{Code: 400}
	conn := NewDbConn()
	var financeCodes []string // 查询所有门店记录日志
	conn.SQL(`select distinct finance_code  from datacenter.store s where length(finance_code ) >0;`).Find(&financeCodes)
	// 先查询商品货号信息
	var err error
	var channelSkuData []*models.ChannelSkuThird
	err = conn.SQL(`select *  from dc_product.channel_sku_third cst 
				where product_id = ? and channel_id =?  and erp_id in (2,4);`, in.ProductId, in.ChannelId).Find(&channelSkuData)
	if err != nil {
		glog.Error(prefix, "查询商品总计异常 ", err.Error())
		return res, err
	}

	var mapChannelSku = make(map[string]int32, len(channelSkuData))
	for i := range channelSkuData {
		third := channelSkuData[i]
		mapChannelSku[third.ThirdSkuId] = third.ErpId
	}
	if _, ok := mapChannelSku[in.ThirdSkuId]; !ok {
		return res, errors.New("货号已经被删除了")
	}

	var num = len(channelSkuData) // 如果两个货号的话需要判断是全部删除认领还是 只删除channel_sku部分删除，重新认领

	var recordData models.RecordData
	ok, err := conn.SQL(`select cp.name, cst.product_id, cst.sku_id,
	MAX( case WHEN erp_id=2 THEN third_sku_id ELSE '' END) AS a8,
 	MAX( case WHEN erp_id=4 THEN third_sku_id ELSE '' END) AS zi_long
	from dc_product.channel_product cp
	left join dc_product.channel_sku_third cst on cp.id  = cst.product_id
	where cp.channel_id = ? and cst.channel_id = ? and cp.id =?   and cst.product_id = ? group by cp.id ;`,
		in.ChannelId, in.ChannelId, in.ProductId, in.ProductId).Get(&recordData)

	if err != nil {
		glog.Error(prefix, " 查询商品详情信息异常 ", err.Error())
		return res, err
	}
	if !ok {
		return res, errors.New("未查询到商品详情信息")
	}

	session := conn.NewSession()
	defer session.Close()
	session.Begin()

	// 需要删除的门店
	var shopIds []string
	var isDeleteAll bool
	if num <= 1 || in.ChannelId == ChannelElmId || in.ChannelId == ChannelJddjId {
		// 此商品只有一个货号，直接删除即可,饿了么和京东到家渠道直接删除（共用个的一个品牌库）
		isDeleteAll = true
		//删除channel_product
		_, err = session.Table(&models.ChannelProduct{}).Where(" channel_id = ? and id = ? ", in.ChannelId, in.ProductId).Delete(&models.ChannelProduct{})
		if err != nil {
			glog.Info(prefix, "删除channel_product ", err.Error())
			res.Message = "删除channel_product数据失败"
			return res, err
		}

		// 删除channel_sku_group
		_, err = session.Table(&models.ChannelSkuGroup{}).Where("product_id = ? and channel_id = ? ", in.ProductId, in.ChannelId).Delete(&models.ChannelSkuGroup{})
		if err != nil {
			glog.Info(prefix, "删除channel_sku_group ", err.Error())
			res.Message = "删除channel_sku_group数据失败"
			return res, err
		}

		// 删除上架表数据
		_, err = session.Table(&models.ChannelStoreProduct{}).Where("channel_id = ? and product_id = ? ", in.ChannelId, in.ProductId).Delete(&models.ChannelStoreProduct{})
		if err != nil {
			session.Rollback()
			glog.Info(prefix, "删除上架表数据 ", err.Error())
			res.Message = "删除上架表数据失败"
			return res, err
		}

		// 删除快照数据
		_, err = session.Table(&models.ChannelProductSnapshot{}).Where("channel_id = ? and product_id = ? ", in.ChannelId, in.ProductId).Delete(&models.ChannelProductSnapshot{})
		if err != nil {
			session.Rollback()
			glog.Info(prefix, "删除快照数据 ", err.Error())
			res.Message = "删除快照数据失败"
			return res, err
		}
		//删除channel_sku表
		_, err = session.Table(&models.ChannelSku{}).Where("channel_id = ? and product_id = ? ", in.ChannelId, in.ProductId).Delete(&models.ChannelSku{})
		if err != nil {
			session.Rollback()
			glog.Info(prefix, "删除channel_sku表 ", err.Error())
			res.Message = "删除channel_sku表失败"
			return res, err
		}

		// 删除channel_sku_third表
		_, err = session.Table(&models.ChannelSkuThird{}).Where("channel_id = ? and product_id = ?", in.ChannelId, in.ProductId).Delete(&models.ChannelSkuThird{})
		if err != nil {
			session.Rollback()
			glog.Info(prefix, "删除channel_sku_third表 ", err.Error())
			res.Message = "删除channel_sku_third表失败"
			return res, err
		}
		_, err = session.Table(&models.ChannelSkuValue{}).Where("channel_id = ? and product_id = ? ", in.ChannelId, in.ProductId).Delete(&models.ChannelSkuValue{})
		if err != nil {
			session.Rollback()
			glog.Info(prefix, "删除ChannelSkuValue表失败 ", err.Error())
			res.Message = "删除ChannelSkuValue表失败"
			return res, err
		}

		// 重新认领状态标识重置
		var gjProduct models.GjProduct
		isOk, err := session.Table(&models.GjProduct{}).Where("id = ? ", in.ProductId).Get(&gjProduct)
		if err != nil {
			session.Rollback()
			glog.Info(prefix, " 查询gj商品异常 ", err.Error())
			res.Message = "查询gj商品异常"
			return res, err
		}
		if !isOk {
			session.Rollback()
			glog.Info(prefix, " 未查询到gj商品异常 ", err.Error())
			res.Message = "未查询到gj商品异常"
			return res, err
		}
		channelIds := gjProduct.ChannelId
		var newChannelIds []string
		if len(channelIds) > 0 {
			split := strings.Split(channelIds, ",")
			for i := range split {
				channelId := split[i]
				if cast.ToInt32(channelId) != in.ChannelId {
					newChannelIds = append(newChannelIds, channelId)
				}
			}
		}
		//认领更新
		gjProduct.ChannelId = strings.Join(newChannelIds, ",")
		_, err = session.Table(&models.GjProduct{}).Cols("channel_id").Where("id = ? ", in.ProductId).Update(gjProduct)
		if err != nil {
			session.Rollback()
			return res, err
		}
	} else { // 商品有多个货号，只能删除channel_sku_third， 快照，上架表， gj只能走重新认领操作
		// 通过货号判断出只绑定子龙或者a8渠道的门店，然后在删除相对应的表

		var shopWarehouseData []models.WarehouseRelationShopRedis
		if in.ErpId == 2 { // a8,前置仓
			err = session.SQL(`select wrs.shop_id, wrs.warehouse_id, wrs.channel_id, w.category  from dc_dispatch.warehouse_relation_shop wrs 
											join dc_dispatch.warehouse w on w.id = wrs.warehouse_id 
											where channel_id  = ? and w.category  in (4,5);`, in.ChannelId).Find(&shopWarehouseData)
			if err != nil {
				glog.Error(prefix, num, " 查询前置仓绑定关系异常：", err.Error())
				session.Rollback()
				return res, err
			}
		} else {
			err = session.SQL(`select wrs.shop_id, wrs.warehouse_id, wrs.channel_id, w.category  from dc_dispatch.warehouse_relation_shop wrs 
											join dc_dispatch.warehouse w on w.id = wrs.warehouse_id 
											where channel_id  = ? and w.category  in (3);`, in.ChannelId).Find(&shopWarehouseData)
			if err != nil {
				glog.Error(prefix, num, " 查询门店仓绑定关系异常：", err.Error())
				session.Rollback()
				return res, err
			}
		}

		for i := range shopWarehouseData {
			shopIds = append(shopIds, shopWarehouseData[i].ShopId)
		}

		// 删除channel_sku_third表
		_, err = session.Table(&models.ChannelSkuThird{}).Where("channel_id = ? and product_id = ? and third_sku_id = ? and erp_id = ? ",
			in.ChannelId, in.ProductId, in.ThirdSkuId, in.ErpId).Delete(&models.ChannelSkuThird{})
		if err != nil {
			session.Rollback()
			glog.Info(prefix, num, " 删除channel_sku_third表 ", err.Error())
			res.Message = "删除channel_sku_third表失败"
			return res, err
		}

		// 删除相关联的表
		if len(shopIds) > 0 {
			// 删除上架表数据
			_, err = session.Table(&models.ChannelStoreProduct{}).Where("channel_id = ? and product_id = ? ",
				in.ChannelId, in.ProductId).In("finance_code", shopIds).Delete(&models.ChannelStoreProduct{})
			if err != nil {
				session.Rollback()
				glog.Info(prefix, num, " 删除上架表数据 ", err.Error())
				res.Message = "删除上架表数据失败"
				return res, err
			}

			// 删除快照数据
			_, err = session.Table(&models.ChannelProductSnapshot{}).Where("channel_id = ? and product_id = ? ", in.ChannelId, in.ProductId).
				In("finance_code", shopIds).Delete(&models.ChannelProductSnapshot{})
			if err != nil {
				session.Rollback()
				glog.Info(prefix, num, " 删除快照数据 ", err.Error())
				res.Message = "删除快照数据失败"
				return res, err
			}
		}
	}

	glog.Info(prefix, num, " 第三方删除参数： ", kit.JsonEncode(recordData), " 入参：", kit.JsonEncode(in), " 删除的门店： ", len(shopIds))
	err = session.Commit()
	if err != nil {
		res.Message = "提交事务异常"
		return res, err
	}

	//记录日志信息,已经算删除了,删除是删除所有门店直接标记all即可
	var records = []*models.RecordChannelProduct{}
	if isDeleteAll {
		for i := range financeCodes {
			record := models.RecordChannelProduct{
				ProductId:   recordData.ProductId,
				ChannelId:   cast.ToInt64(in.ChannelId),
				SkuId:       recordData.SkuId,
				ProductName: recordData.Name,
				Zilong:      recordData.ZiLong,
				A8:          recordData.A8,
				RecordType:  enum.RecordTypeDelete,
				RecordTime:  time.Now(),
				FinanceCode: financeCodes[i],
				UserNo:      in.CreateId,
				UserName:    in.CreateName,
				CreateDate:  time.Now(),
				UpdateDate:  time.Now(),
			}
			records = append(records, &record)
		}
	} else {
		for i := range shopIds {
			record := models.RecordChannelProduct{
				ProductId:   recordData.ProductId,
				ChannelId:   cast.ToInt64(in.ChannelId),
				SkuId:       recordData.SkuId,
				ProductName: recordData.Name,
				Zilong:      recordData.ZiLong,
				A8:          recordData.A8,
				RecordType:  enum.RecordTypeDelete,
				RecordTime:  time.Now(),
				FinanceCode: shopIds[i],
				UserNo:      in.CreateId,
				UserName:    in.CreateName,
				CreateDate:  time.Now(),
				UpdateDate:  time.Now(),
			}
			records = append(records, &record)
		}
	}

	go c.SaveChannelProductLog(records)

	// 处理渠道任务
	if in.ChannelId == ChannelAwenId {
		err = c.DeleteChannelAwenProduct(in, shopIds, isDeleteAll)
		if err != nil {
			glog.Error(prefix, "阿闻商品删除异常：", err.Error())
			res.Message = "阿闻商品删除异常"
			return res, err
		}
	}

	if in.ChannelId == ChannelMtId {
		err = c.DeleteChannelMtProduct(recordData, shopIds, isDeleteAll)
		if err != nil {
			glog.Error(prefix, "美团商品删除异常：", err.Error())
			res.Message = "美团商第三方品删除异常"
			return res, err
		}
	}

	// 京东到家和饿了么共用的同一个品牌库，只能全部删除了，区分不了
	if in.ChannelId == ChannelElmId {
		err = c.DeleteChannelEleProduct(recordData)
		if err != nil {
			glog.Error(prefix, "饿了么商品删除异常：", err.Error())
			res.Message = "饿了么第三方商品删除异常"
			return res, err
		}
	}
	if in.ChannelId == ChannelJddjId {
		err = c.DeleteChannelJddjProduct(recordData)
		if err != nil {
			glog.Error(prefix, "京东到家商品删除异常：", err.Error())
			res.Message = "京东到家第三方商品删除异常"
			return res, err
		}
	}
	if in.ChannelId == ChannelDigitalHealth {
		// 医疗互联网不需要处理
	}

	res.Code = 200
	return res, nil
}

// 删除阿闻商品信息
func (c *Product) DeleteChannelAwenProduct(in *pc.DeleteProductVo, shopIds []string, isDeleteAll bool) error {
	// 删除hasstock表
	conn := NewDbConn()

	// 清理es的数据
	client := es.NewEsClient()
	boolQuery := elastic.NewBoolQuery()

	if isDeleteAll {
		_, err := conn.Table(&models.ChannelStoreProductHasStock{}).Where("channel_id = ? and product_id = ? ", in.ChannelId, in.ProductId).Delete(&models.ChannelStoreProductHasStock{})
		if err != nil {
			return err
		}

		query := boolQuery.Filter(elastic.NewTermsQuery("product.channel_id", ChannelAwenId), elastic.NewTermQuery("product.id", in.ProductId))
		_, err = client.DeleteByQuery(es.IndexChannelStoreProduct).Query(query).Do(context.Background())
		if err != nil {
			return err
		}

	} else {
		_, err := conn.Table(&models.ChannelStoreProductHasStock{}).Where("channel_id = ? and product_id = ? ",
			in.ChannelId, in.ProductId).In("finance_code", shopIds).Delete(&models.ChannelStoreProductHasStock{})
		if err != nil {
			return err
		}

		var deleteFinanceCode []interface{}
		for i := range shopIds {
			deleteFinanceCode = append(deleteFinanceCode, shopIds[i])
		}
		query := boolQuery.Filter(elastic.NewTermsQuery("product.channel_id", ChannelAwenId),
			elastic.NewTermsQuery("product.id", in.ProductId), elastic.NewTermsQuery("finance_code.keyword", deleteFinanceCode...))
		_, err = client.DeleteByQuery(es.IndexChannelStoreProduct).Query(query).Do(context.Background())
		if err != nil {
			glog.Error("删除es数据异常：", err.Error())
			return err
		}

	}

	return nil
}

func (c *Product) DeleteChannelMtProduct(recordData models.RecordData, financeCodes []string, isDeleteAll bool) error {

	// 查询有配置的第三方门店
	storeMaster, err := c.GetStoreMasterResult(ChannelMtId)
	if err != nil {
		resultError := &MoveCategoryResultError{}
		resultError.Msg = "查询第三方门店异常" + err.Error()
		return err
	}

	var mapFinanceCode = make(map[string]struct{}, len(financeCodes))
	for i := range financeCodes {
		mapFinanceCode[financeCodes[i]] = struct{}{}
	}

	var newStoreMaters []*dac.StoreInfo
	if !isDeleteAll {
		for i := range storeMaster {
			info := storeMaster[i]
			if _, ok := mapFinanceCode[info.FinanceCode]; ok {
				newStoreMaters = append(newStoreMaters, info)
			}
		}
	} else {
		newStoreMaters = storeMaster
	}

	clientMt := GetMtGlobalProductClient()
	// 异步处理美团删除接口
	if recordData.SkuId > 0 && recordData.ProductId > 0 {
		go func() {
			for i := range newStoreMaters {
				info := newStoreMaters[i]
				request := et.RetailSkuDeleteRequest{
					AppPoiCode:        info.ChannelStoreId,
					AppFoodCode:       cast.ToString(recordData.ProductId),
					SkuId:             cast.ToString(recordData.SkuId),
					IsDeleteRetailCat: 2,
					StoreMasterId:     info.AppChannel,
				}
				if !CanCallThirdApi(cast.ToInt(recordData.ProductId), ChannelMtId, info.FinanceCode) {
					glog.Error("没有第三方商品id====17,商品id:", recordData.ProductId, "财务编码：", info.FinanceCode)
					return
				}
				skuDelete, err := clientMt.RPC.RetailSkuDelete(context.Background(), &request)
				glog.Info("删除美团商品： ", " 入参： ", kit.JsonEncode(request), " 返回：", kit.JsonEncode(skuDelete))

				if err != nil {
					glog.Error("删除美团商品异常： ", kit.JsonEncode(request), " err: ", err.Error())
				}

				if skuDelete.Message != "成功" && skuDelete.Code != 200 {
					UpdateProductThirdSyncErr(recordData.ProductId, ChannelMtId, info.FinanceCode, skuDelete.Message)

				}
			}
		}()
	} else {
		return errors.New("DeleteChannelMtProduct-未查询到sku信息 " + kit.JsonEncode(recordData))
	}
	return nil
}
func (c *Product) DeleteChannelEleProduct(recordData models.RecordData) error {
	// 查询有配置的第三方门店
	storeMaster, err := c.GetStoreMasterResult(ChannelElmId)
	if err != nil {
		resultError := &MoveCategoryResultError{}
		resultError.Msg = "查询第三方门店异常" + err.Error()
		return err
	}

	clientEle := GetMtGlobalProductClient()
	// 异步处理美团删除接口
	if recordData.SkuId > 0 {
		go func() {
			for i := range storeMaster {
				info := storeMaster[i]
				params := et.UpdateElmShopSkuPriceRequest{
					ShopId: info.ChannelStoreId,
					//CustomSkuId: "1000968001,1004153001",
					CustomSkuId: cast.ToString(recordData.SkuId),
					AppChannel:  info.AppChannel}
				if !CanCallThirdApi(recordData.ProductId, ChannelElmId, info.FinanceCode) {
					glog.Error("没有第三方商品id====18,商品id:", recordData.ProductId, "财务编码：", info.FinanceCode)

					return
				}
				skuDelete, err := clientEle.ELMPRODUCT.DeleteElmShopSku(context.Background(), &params)
				glog.Info("删除饿了么商品： ", " 入参： ", kit.JsonEncode(params), " 返回：", kit.JsonEncode(skuDelete))
				if err != nil {
					glog.Error("删除饿了么商品异常： ", kit.JsonEncode(params), " err: ", err.Error())
				}
				if skuDelete.Message != "成功" && skuDelete.Code != 200 {
					UpdateProductThirdSyncErr(recordData.ProductId, ChannelElmId, info.FinanceCode, skuDelete.Message)

				}

			}
		}()
	} else {
		return errors.New("DeleteChannelEleProduct-未查询到sku信息 " + kit.JsonEncode(recordData))
	}
	return nil
}

func (c *Product) DeleteChannelJddjProduct(recordData models.RecordData) error {
	// 查询有配置的第三方门店
	storeMaster, err := c.GetStoreMasterResult(ChannelJddjId)
	if err != nil {
		resultError := &MoveCategoryResultError{}
		resultError.Msg = "查询第三方门店异常" + err.Error()
		return err
	}

	clientEle := GetMtGlobalProductClient()
	// 异步处理美团删除接口
	if recordData.SkuId > 0 {
		go func() {
			for i := range storeMaster {
				info := storeMaster[i]
				params := et.JddjUpdateGoodsListRequest{TraceId: uuid.New().String(),
					OutSkuId:      cast.ToString(recordData.SkuId),
					FixedStatus:   4, // 删除
					StoreMasterId: info.AppChannel,
					BrandId:       35247, // 测试更新必须要带上
					Weight:        1,     // 测试更新必须要带上重量参数，否则会更新失败
					CategoryId:    24479, // 测试更新必须要带上
				}
				skuDelete, err := clientEle.JDDJPRODUCT.JddjUpdateGoodsList(context.Background(), &params)
				glog.Info("删除京东到家商品： ", " 入参： ", kit.JsonEncode(params), " 返回：", kit.JsonEncode(skuDelete))
				if err != nil {
					glog.Error("删除京东到家商品异常： ", kit.JsonEncode(params), " err: ", err.Error())
				}
			}
		}()
	} else {
		return errors.New("DeleteChannelJddjProduct-未查询到sku信息 " + kit.JsonEncode(recordData))
	}
	return nil
}

//保存日志的公共入口
/**
ChannelId: 渠道id
productId： 商品id
recordType：日志类型
financeCode： 门店财务编码
userNo：
userName
*/
func (c *Product) SaveChannelProductLogDetail(ChannelId, productId, recordType int, financeCode, userNo, userName string) {

	conn := NewDbConn()

	var recordData models.RecordData
	conn.SQL(`select cp.name, cst.product_id, cst.sku_id,
	MAX( case WHEN erp_id=2 THEN third_sku_id ELSE '' END) AS a8,
 	MAX( case WHEN erp_id=4 THEN third_sku_id ELSE '' END) AS zi_long
	from dc_product.channel_product cp
	left join dc_product.channel_sku_third cst on cp.id  = cst.product_id
	where cp.channel_id = ? and cst.channel_id = ? and cp.id =?  and cst.product_id = ? group by cp.id ;`,
		ChannelId, ChannelId, productId, productId).Get(&recordData)

	channelProduct := models.RecordChannelProduct{
		ProductId:   cast.ToInt(recordData.ProductId),
		ChannelId:   cast.ToInt64(ChannelId),
		SkuId:       cast.ToInt(recordData.SkuId),
		ProductName: recordData.Name,
		Zilong:      recordData.ZiLong,
		A8:          recordData.A8,
		RecordTime:  time.Now(),
		FinanceCode: financeCode,
		UserNo:      userNo,
		UserName:    userName,
		RecordType:  cast.ToInt64(recordType),
		CreateDate:  time.Now(),
		UpdateDate:  time.Now(),
	}
	var records = []*models.RecordChannelProduct{&channelProduct}

	c.SaveChannelProductLog(records)

}

// 保存日志的公共接口
func (c *Product) SaveChannelProductLog(records []*models.RecordChannelProduct) {

	conn := NewDbConn()

	// 每次处理500条数据
	var records200 []*models.RecordChannelProduct
	for i := range records {
		records200 = append(records200, records[i])

		if len(records200) >= 500 || i == len(records)-1 {
			_, err := conn.Insert(records200)
			if err != nil {
				glog.Error("保存日志的公共接口插入记录异常 ", err.Error())
			}
			records200 = []*models.RecordChannelProduct{} // 清空数据
		}
	}
}

// 查询渠道商品的操作记录
func (c *Product) ChannelProductRecords(ctx context.Context, in *pc.ChannelProductRecordsVo) (*pc.RecordChannelProductRes, error) {

	var res = &pc.RecordChannelProductRes{Code: 400}

	prefix := "查询渠道商品的操作记录: "
	glog.Info(prefix, " 入参：", kit.JsonEncode(in))

	conn := NewDbConn()
	session := conn.Table(&models.RecordChannelProduct{}).Where(" finance_code = ?  ", in.FinanceCode)
	if in.ChannelId > 0 {
		session.Where("channel_id = ? ", in.ChannelId)
	}
	if in.RecordType > 0 {
		session.Where("record_type = ? ", in.RecordType)
	}
	if len(in.StartTime) > 0 {
		session.Where("record_time >=? ", in.StartTime)
	}
	if len(in.EndTime) > 0 {
		session.Where("record_time <= ?", in.EndTime)
	}
	if len(in.Where) > 0 && len(strings.TrimSpace(in.Value)) > 0 {
		if in.Where == "sku_id" {
			session.Where("sku_id = ?", in.Value)
		}
		if in.Where == "product_name" {
			session.Where("product_name = ?", in.Value)
		}
		if in.Where == "third_spu_sku_id" {
			session.Where("(a8 = ? or zilong = ? )", in.Value, in.Value)
		}
	}
	// 老版本的xorm的FindAndCount不能用proto
	var data []*models.RecordChannelProduct
	count, err := session.Select("*").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("create_date desc").FindAndCount(&data)
	if err != nil {
		return res, err
	}

	var tranData []*pc.RecordChannelProduct
	for i := range data {
		product := data[i]
		createTimeStr := product.CreateDate.Format(utils.DATE_TIME_LAYOUT)
		updateTimeStr := product.UpdateDate.Format(utils.DATE_TIME_LAYOUT)
		recordTimeStr := product.UpdateDate.Format(utils.DATE_TIME_LAYOUT)
		tranData = append(tranData, &pc.RecordChannelProduct{
			Id:          int64(product.Id),
			ProductId:   int64(product.ProductId),
			ChannelId:   product.ChannelId,
			SkuId:       int64(product.SkuId),
			ProductName: product.ProductName,
			Zilong:      product.Zilong,
			A8:          product.A8,
			RecordType:  product.RecordType,
			RecordTime:  recordTimeStr,
			FinanceCode: product.FinanceCode,
			UserNo:      product.UserNo,
			UserName:    product.UserName,
			CreateDate:  createTimeStr,
			UpdateDate:  updateTimeStr,
		})

	}

	res.Data = tranData
	res.Total = count
	res.Code = 200
	return res, nil
}

// 查询平台的商品库的修改货号的记录
func (c *Product) ProductRecords(ctx context.Context, in *pc.ProductRecordsVo) (*pc.RecordProductRes, error) {
	prefix := "查询平台的商品库的修改货号的记录: "
	glog.Info(prefix, " 入参：", kit.JsonEncode(in))

	var res = &pc.RecordProductRes{
		Code: 400,
	}
	conn := NewDbConn()
	session := conn.Table(&models.RecordProduct{})

	if in.RecordType > 0 {
		session.Where("record_type = ? ", in.RecordType)
	}

	if len(in.StartTime) > 0 {
		session.Where("record_time >= ? ", in.StartTime)
	}
	if len(in.EndTime) > 0 {
		session.Where("record_time <= ?", in.EndTime)
	}
	if len(in.Where) > 0 && len(in.Value) > 0 {
		if in.Where == "sku_id" {
			session.Where("sku_id = ?", in.Value)
		}
		if in.Where == "product_id" {
			session.Where("product_id = ?", in.Value)
		}
		if in.Where == "upc" {
			session.Where("upc = ?", in.Value)
		}
		if in.Where == "product_name" {
			session.Where("product_name = ?", in.Value)
		}
		if in.Where == "third_spu_sku_id" {
			session.Where("(a8_new = ? or zilong_new = ? or zilong_old = ? or a8_old = ? )", in.Value, in.Value, in.Value, in.Value)
		}
	}

	// 老版本的xorm的FindAndCount不能用proto
	var data []*models.RecordProduct
	conn.ShowSQL()
	count, err := session.Select("*").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("create_date desc").FindAndCount(&data)
	if err != nil {
		return res, err
	}
	var tranData []*pc.RecordProduct
	for i := range data {
		product := data[i]
		createTimeStr := product.CreateDate.Format(utils.DATE_TIME_LAYOUT)
		updateTimeStr := product.UpdateDate.Format(utils.DATE_TIME_LAYOUT)
		recordTimeStr := product.UpdateDate.Format(utils.DATE_TIME_LAYOUT)
		tranData = append(tranData, &pc.RecordProduct{
			Id:          int32(product.Id),
			RecordType:  int32(product.RecordType),
			RecordTime:  recordTimeStr,
			RecordData:  product.RecordData,
			UserNo:      product.UserNo,
			UserName:    product.UserName,
			Upc:         product.Upc,
			ProductName: product.ProductName,
			ProductId:   int64(product.ProductId),
			SkuId:       int64(product.SkuId),
			ZilongNew:   product.ZilongNew,
			A8New:       product.A8New,
			ZilongOld:   product.ZilongOld,
			A8Old:       product.A8Old,
			CreateDate:  createTimeStr,
			UpdateDate:  updateTimeStr,
		})
	}

	res.Data = tranData
	res.Total = count
	res.Code = 200

	return res, nil
}
