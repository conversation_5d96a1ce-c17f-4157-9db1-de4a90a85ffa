package services

import (
	"_/proto/pc"
	"context"
	"testing"
)

//AddTaskProductFromOms
func TestProduct_AddTaskProductFromOms(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.ProductRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.ChildProductsResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				in: &pc.ProductRequest{
					Product: &pc.Product{
						Id:                   0,
						CategoryId:           0,
						BrandId:              0,
						Name:                 "test-name",
						Code:                 "",
						BarCode:              "",
						CreateDate:           "",
						UpdateDate:           "",
						IsDel:                0,
						IsGroup:              0,
						Pic:                  "",
						SellingPoint:         "",
						Video:                "",
						ContentPc:            "",
						ContentMobile:        "",
						IsDiscount:           0,
						ProductType:          0,
						IsUse:                0,
						CategoryName:         "",
						Sku:                  nil,
						Attr:                 nil,
						ChannelId:            "",
						BrandName:            "",
						JdCategoryId:         0,
						IsDrugs:              0,
						UseRange:             "",
						TermType:             0,
						TermValue:            0,
						GroupType:            0,
						VirtualInvalidRefund: 0,
						WarehouseType:        0,
						IsIntelGoods:         0,
					},
					SkuInfo:     []*pc.SkuInfo{},
					ProductAttr: nil,
					ProductTags: nil,
				},
			},
		},
	}

	SetupDB()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//c := Product{
			//	categoryNames: tt.fields.categoryNames,
			//}
			//got, err := c.AddTaskProductFromOms(tt.args.ctx, tt.args.in)
			//fmt.Println(got, err)
		})
	}
}
