package services

import (
	"_/proto/pc"
	"context"
	"encoding/json"
	"reflect"
	"testing"
)

func TestProduct_EditGjProduct(t *testing.T) {
	str := `{"product":{"id":1018189,"category_id":1140,"brand_id":256,"name":"测试商品22000","code":"","bar_code":"","create_date":"2020-08-20 14:28:21","update_date":"2020-08-20 14:28:23","is_del":0,"is_group":0,"pic":"http://file.vetscloud.com/1390de573bb5e762988abb9ffdabcf02,http://file.vetscloud.com/1390de573bb5e762988abb9ffdabcf02,,,","selling_point":"商品卖点000","video":"http://file.vetscloud.com/a9ca48e1809f1dbf6d6fbe8895c4f2cf.mp4","content_pc":"<p>电脑端富文本内容3331111111111111111</p>","content_mobile":"<p>手机端富文本内容</p>","is_discount":0,"product_type":1,"is_use":0,"category_name":"猫站>猫咪专区>猫咪玩具>猫抓板","sku":null,"attr":null,"channel_id":""},"product_attr":[],"sku_info":[{"sku_third":[{"id":22774,"sku_id":1018189001,"third_sku_id":"2bdaf5c2983b432ca78ca2ea7f151e34","erp_id":4,"product_id":1018189,"erp_name":"子龙货号","third_spu_id":"1f6411c1eaf049f9a70872d96ba19ec33333","channel_id":0,"is_use":0}],"skuv":[{"id":19800,"spec_id":1,"spec_value_id":1,"sku_id":1018189001,"product_id":1018189,"pic":"xxx222.jpg","spec_name":"种类","spec_value_value":"小型幼犬1.5KG","details":null,"channel_id":0}],"retail_price":150,"sku_id":1018189001,"product_id":1018189,"market_price":199,"sku_group":null,"bar_code":"95842620ac7e4ad9b4b82e6ba90080f799","channel_id":0,"is_use":0,"weight_for_unit":111,"weight_unit":"千克(kg)","min_order_count":0,"price_unit":"","prepose_price":111,"store_price":111}],"sku_group":[]}`
	var req pc.ProductRequest
	if err := json.Unmarshal([]byte(str), &req); err != nil {
		t.Error(err)
	}

	type args struct {
		ctx context.Context
		in  *pc.ProductRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "编辑商品",
			c:    &Product{},
			args: args{ctx: nil, in: &req},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.EditProduct(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.EditProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.EditProduct() = %v, want %v", got, tt.want)
			}
		})
	}
}
