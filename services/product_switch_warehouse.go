package services

import (
	"_/enum"
	"_/models"
	"_/proto/pc"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/maybgit/glog"
	"github.com/ppkg/distributed-worker/dto"
	kit "github.com/tricobbler/rp-kit"
)

// 取消异步任务,只支持taskContent=67
func (c *Product) CancelTask(ctx context.Context, req *pc.CancelTaskRequest) (*pc.CancelTaskResponse, error) {
	if req.Id == 0 {
		return nil, errors.New("参数ID不能为空")
	}
	taskResp, err := c.GetTaskList(ctx, &pc.GetTaskListRequest{
		Id:          int32(req.Id),
		TaskContent: 67,
	})
	if err != nil {
		glog.Errorf("Product/CancelTask 查询任务异常,请求参数:%s,%+v", kit.JsonEncode(req), err)
		return nil, err
	}
	resp := &pc.CancelTaskResponse{
		Code: http.StatusBadRequest,
	}
	if len(taskResp.TaskList) == 0 {
		resp.Message = "当前任务不可以取消"
		return resp, nil
	}
	// 任务处于未开始或进行中则可以进行取消，否则不可以
	// 1:未开始;2:进行中;
	task := taskResp.TaskList[0]
	if task.TaskStatus == 3 {
		resp.Message = "当前任务已完成，无法取消"
		return resp, nil
	}
	if task.TaskStatus == 4 {
		resp.Message = "当前任务已取消，不用重复提交"
		return resp, nil
	}
	if task.ContextData != "" {
		var contextData SwitchWarehouseContext
		err = json.Unmarshal([]byte(task.ContextData), &contextData)
		if err != nil {
			glog.Errorf("Product/CancelTask 反序列化异常,参数:%s,%+v", task.ContextData, err)
			resp.Message = "反序列化异常"
			resp.Error = err.Error()
			return resp, nil
		}
		if contextData.ScheduleId != 0 {
			err = workerCtx.ManualCancelJob(dto.ManualCancelRequest{
				Id:     int64(contextData.ScheduleId),
				Reason: "用户手动取消任务",
			})
			if err != nil {
				glog.Errorf("Product/CancelTask 取消任务失败,参数:%d,%+v", contextData.ScheduleId, err)
				resp.Message = "取消任务失败"
				resp.Error = err.Error()
				return resp, nil
			}
		}
	}

	updateModel := models.TaskList{
		TaskStatus: 4,
		TaskDetail: "该任务已取消",
		ModifyTime: time.Now(),
	}
	_, err = engine.Where("id=?", task.Id).Update(updateModel)
	if err != nil {
		resp.Message = "更新任务失败"
		resp.Error = err.Error()
		return resp, nil
	}
	resp.Code = http.StatusOK
	resp.Message = "取消成功"
	return resp, nil
}

// 分布式调度器回调通知,更新任务状态及计算结果
func (c *Product) SwitchWarehouseScheduleCallback(ctx context.Context, req *pc.SwitchWarehouseScheduleCallbackRequest) (*empty.Empty, error) {
	glog.Infof("Product/SwitchWarehouseScheduleCallback 收到切仓回调通知,入参:%s", kit.JsonEncode(req))
	if req.TaskId == 0 {
		glog.Errorf("Product/SwitchWarehouseScheduleCallback 参数taskId不能为空")
		return &empty.Empty{}, nil
	}

	taskResp, err := c.GetTaskList(ctx, &pc.GetTaskListRequest{
		Id:          int32(req.TaskId),
		TaskContent: 67,
	})
	if err != nil {
		glog.Errorf("Product/SwitchWarehouseScheduleCallback 查询任务详情失败,taskId:%d,%+v", req.TaskId, err)
		return nil, fmt.Errorf("查询任务详情失败,%+v", err)
	}
	if len(taskResp.TaskList) == 0 {
		glog.Errorf("Product/SwitchWarehouseScheduleCallback 查询不到任务信息,taskId:%d", req.TaskId)
		return &empty.Empty{}, nil
	}

	task := taskResp.TaskList[0]
	var contextData SwitchWarehouseContext
	err = json.Unmarshal([]byte(task.ContextData), &contextData)
	if err != nil {
		glog.Errorf("Product/SwitchWarehouseScheduleCallback 反序列化异常,taskId:%d,参数:%s,%+v", req.TaskId, task.ContextData, err)
		return &empty.Empty{}, nil
	}

	// 合并数据
	c.mergeSwitchWarehouseData(&contextData, req.StoreList)

	// 更新任务信息
	updateModel := models.TaskList{
		TaskStatus:     3,
		ResulteFileUrl: exportSwitchWarehouseError(&contextData),
		ModifyTime:     time.Now(),
	}

	_, err = engine.Where("id=?", task.Id).Update(updateModel)
	if err != nil {
		glog.Errorf("Product/SwitchWarehouseScheduleCallback 更新任务状态异常,taskId:%d,%+v", req.TaskId, err)
		return nil, fmt.Errorf("更新任务状态异常,%+v", err)
	}
	return &empty.Empty{}, nil
}

// 合并切仓结果数据
func (c *Product) mergeSwitchWarehouseData(ctx *SwitchWarehouseContext, storeList []*pc.SimpleStoreInfo) {
	successMap := make(map[string]StoreInfo, len(ctx.SuccessList))
	for _, v := range ctx.SuccessList {
		successMap[v.FinanceCode] = v
	}

	for _, v := range storeList {
		storeInfo, ok := successMap[v.FinanceCode]
		if !ok {
			glog.Errorf("Product/mergeSwitchWarehouseData 找不到店铺信息,数据不合法:%s,taskId:%d", kit.JsonEncode(v), ctx.TaskId)
			continue
		}
		if v.SystemError != "" {
			storeInfo.Message = fmt.Sprintf("%s渠道已绑定%s,执行切换仓库失败：%s", enum.ChannelMap[int(ctx.ChannelId)], enum.WarehouseCategoryMap[int(ctx.TargetCategory)], v.SystemError)
			ctx.FailList = append(ctx.FailList, storeInfo)
			delete(successMap, v.FinanceCode)
			continue
		}
		for _, item := range v.FailList {
			storeInfo.FailProductList = append(storeInfo.FailProductList, ProductInfo{
				Id:      item.Id,
				SkuId:   item.SkuId,
				Name:    item.Name,
				Type:    item.Type,
				Message: item.Message,
			})
		}
		successMap[v.FinanceCode] = storeInfo
	}

	successList := make([]StoreInfo, 0, len(successMap))
	for _, v := range successMap {
		successList = append(successList, v)
	}
	ctx.SuccessList = successList
}

// 切仓日志列表
func (c *Product) ListSwitchWarehouseLog(ctx context.Context, req *pc.ListSwitchWarehouseLogRequest) (*pc.ListSwitchWarehouseLogResponse, error) {
	session := engine.NewSession()
	defer session.Close()
	if req.ChannelId > 0 {
		session = session.Where("channel_id=?", req.ChannelId)
	}
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		session = session.Where("shop_name like ? or finance_code like ?", keyword, keyword)
	}
	if req.CreateId != "" {
		session = session.Where("create_id=?", req.CreateId)
	}
	countSession := session.Clone()
	defer countSession.Close()
	count, err := countSession.Table("switch_warehouse_log").Count()
	if err != nil {
		glog.Errorf("Product/ListSwitchWarehouseLog 查询总记录数异常:%+v %s", err, kit.JsonEncode(req))
		return nil, err
	}
	resp := &pc.ListSwitchWarehouseLogResponse{
		List:  []*pc.SwitchWarehouseLog{},
		Total: int32(count),
	}
	if count == 0 {
		return resp, nil
	}
	var list []models.SwitchWarehouseLog
	err = session.Limit(int(req.PageSize), int((req.Page-1)*req.PageSize)).OrderBy("id desc").Find(&list)
	if err != nil {
		glog.Errorf("Product/ListSwitchWarehouseLog 查询分页数据异常:%+v %s", err, kit.JsonEncode(req))
		return nil, err
	}
	for _, v := range list {
		resp.List = append(resp.List, &pc.SwitchWarehouseLog{
			Id:                   v.Id,
			ChannelId:            v.ChannelId,
			Action:               v.Action,
			FinanceCode:          v.FinanceCode,
			ShopName:             v.ShopName,
			SrcWarehouseCode:     v.SrcWarehouseCode,
			SrcWarehouseName:     v.SrcWarehouseName,
			SrcWarehouseCategory: v.SrcWarehouseCategory,
			DstWarehouseCode:     v.DstWarehouseCode,
			DstWarehouseName:     v.DstWarehouseName,
			DstWarehouseCategory: v.DstWarehouseCategory,
			Content:              v.Content,
			CreateId:             v.CreateId,
			CreateTime:           v.CreateTime,
			CreateName:           v.CreateName,
			CreateIp:             v.CreateIp,
			IpLocation:           v.IpLocation,
		})
	}
	return resp, nil
}

// 切仓日志列表
func (c *Product) AddSwitchWarehouseLog(ctx context.Context, req *pc.AddSwitchWarehouseLogRequest) (*empty.Empty, error) {
	if len(req.List) == 0 {
		return &empty.Empty{}, nil
	}

	var list []models.SwitchWarehouseLog
	for _, v := range req.List {
		list = append(list, models.SwitchWarehouseLog{
			ChannelId:            v.ChannelId,
			Action:               v.Action,
			FinanceCode:          v.FinanceCode,
			ShopName:             v.ShopName,
			SrcWarehouseCode:     v.SrcWarehouseCode,
			SrcWarehouseName:     v.SrcWarehouseName,
			SrcWarehouseCategory: v.SrcWarehouseCategory,
			DstWarehouseCode:     v.DstWarehouseCode,
			DstWarehouseName:     v.DstWarehouseName,
			DstWarehouseCategory: v.DstWarehouseCategory,
			Content:              v.Content,
			CreateId:             v.CreateId,
			CreateTime:           v.CreateTime,
			CreateName:           v.CreateName,
			CreateIp:             v.CreateIp,
			IpLocation:           v.IpLocation,
		})
	}
	_, err := engine.Insert(list)
	if err != nil {
		glog.Errorf("Product/AddSwitchWarehouseLog 新增切仓日志异常:%+v %s", err, kit.JsonEncode(req))
		return nil, err
	}
	return &empty.Empty{}, nil
}
