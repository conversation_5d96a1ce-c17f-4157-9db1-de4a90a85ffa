package services

import (
	"_/models"
	"_/proto/pc"
	"_/utils"
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/spf13/cast"
)

//获取瑞鹏和代运营的配置id
func (p *Product) GetAgencyConfig(ctx context.Context, empty *empty.Empty) (*pc.GetAgencyConfigResp, error) {

	var res = pc.GetAgencyConfigResp{
		Code:       0,
		Message:    "",
		ConfigData: []int32{},
	}
	getString := config.GetString("id.config.agency.operation")

	//if len(getString) <= 0 {
	//	return configData, errors.New("没有配置代运营和瑞鹏的appChannel")
	//}

	split := strings.Split(getString, ",")
	for i := range split {
		res.ConfigData = append(res.GetConfigData(), cast.ToInt32(split[i]))
	}

	return &res, nil
}

// 导入商品数据
func (p *Product) ImportAgencyProduct(ctx context.Context, in *pc.ImportAgencyProductVo) (*pc.ImportAgencyProductResponse, error) {

	var resp pc.ImportAgencyProductResponse
	// 下载excel
	req, err := http.NewRequest("POST", in.OpenFileUrl, nil)
	if err != nil {
		return &resp, errors.New("下载excel失败")
	}
	do, err := utils.Client60Second.Do(req)
	if err != nil {
		return nil, err
	}

	if err != nil {
		return &resp, errors.New("下载excel失败")
	}
	defer do.Body.Close()

	f, err := excelize.OpenReader(do.Body)
	if err != nil {
		return &resp, errors.New("下载excel失败")
	}
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows := f.GetRows(sheetName)

	if len(rows) <= 1 {
		return &resp, errors.New("请导入数据")
	}

	var data []models.AgencyProduct
	for i := 1; i < len(rows); i++ {
		d := models.AgencyProduct{
			AppChannel:  int(in.AppId),
			SkuId:       cast.ToInt(strings.TrimSpace(rows[i][0])),
			ProductName: rows[i][1],
			CreateBy:    in.CreateBy,
			CreateTime:  time.Now(),
		}

		if d.SkuId != 0 {
			data = append(data, d)
		}
	}

	db := NewDbConn()

	sql := "INSERT INTO agency_product (app_channel, sku_id, product_name, create_by,create_time)" +
		"VALUES (?,?,?,?,?) ON DUPLICATE KEY UPDATE  product_name= ?, create_by=?"

	var failed []string
	for _, v := range data {
		_, err = db.Exec(sql, v.AppChannel, v.SkuId, v.ProductName, v.CreateBy, v.CreateTime, v.ProductName, v.CreateBy)
		if err != nil {
			failed = append(failed, cast.ToString(v.SkuId))
		}
	}

	resp.Message = strings.Join(failed, ",")
	return &resp, nil
}

/**
 判断是否是马氏雀巢等，按照app_channel返回配置商品的map
typeInfo = 0 返回sku的map集合
typeInfo = 1 返回product的map集合
*/
func (p *Product) IsUpProduct(ctx context.Context, appChannel int32, typeInfo int32) (map[int]struct{}, error) {

	glog.Info("查询配置商品：appChannel: ", appChannel)
	var productMap = make(map[int]struct{}, 0)

	conn := NewDbConn()

	var data []models.AgencyProductDto

	err := conn.SQL("select ap.sku_id, s.product_id, ap.product_name, ap.app_channel   from dc_product.agency_product ap "+
		"join dc_product.sku s on ap.sku_id  = s.id where ap.app_channel = ?; ", appChannel).Find(&data)

	if err != nil {
		glog.Error("查询配置商品异常：", err.Error())
		return productMap, err
	}

	for i := range data {
		if typeInfo == 1 {
			productMap[data[i].ProductId] = struct{}{}
		} else {
			productMap[data[i].SkuId] = struct{}{}
		}
	}

	return productMap, nil

}

// 获取对应店铺可上架商品列表
func (p *Product) GetUploadSkuList(ctx context.Context, in *pc.UploadSkuListRequest) (*pc.UploadSkuListResponse, error) {
	res := new(pc.UploadSkuListResponse)
	res.Code = 400

	db := NewDbConn()

	session := db.Table("agency_product").
		Where("app_channel = ?", in.AppId)

	if len(in.Key) > 0 {
		session.And("sku_id = ? or product_name like ?", in.Key, "%"+in.Key+"%")
	}

	sessionClone := session.Clone()
	totalCount, err := sessionClone.Count()
	if err != nil {
		res.Message = err.Error()
		return res, err
	}
	res.Total = int32(totalCount)

	err = session.Select("agency_product.*").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
		OrderBy("create_time DESC").Find(&res.Data)

	if err != nil {
		res.Message = err.Error()
		return res, err
	}

	res.Code = 200
	return res, nil
}

// 删除店铺可上架商品
func (p *Product) RemoveUploadSku(ctx context.Context, in *pc.UploadSkuDeleteRequest) (*pc.UploadSkuDeleteResponse, error) {
	glog.Info("RemoveUploadSku:", kit.JsonEncode(in))
	res := new(pc.UploadSkuDeleteResponse)
	res.Code = 400

	db := NewDbConn()

	_, err := db.Exec(fmt.Sprintf("delete from agency_product where id = %d", in.Id))

	if err != nil {
		res.Message = err.Error()
		return res, err
	}
	res.Code = 200
	res.Message = "移除成功"
	return res, nil
}
