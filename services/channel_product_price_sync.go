package services

import (
	"_/models"
	"_/proto/dac"
	"_/proto/et"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	kit "github.com/tricobbler/rp-kit"

	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

////   渠道商品价格

// 渠道商品价格
type ChannelProductPriceSync struct {
	//渠道Id-必须
	ChannelId int
	// 商品货号-必须
	ProductId string
	//财务编码-必须
	FinanceCode string
	//门店对应仓库Id-可选，传递了则计算价格更快
	WarehouseId int
	// 仓库编码
	WarehouseCode string
	//门店对应仓库类型  1电商仓 3 门店仓 4 前置仓  -可选，传递了则计算价格更快
	WarehouseCategory int
	//渠道门店Id-同步到非阿闻渠道必填 -可选，传递了则计算价格更快
	ChannelFinanaceCode string
	// 商品Sku  -可选，传递了则计算价格更快
	ProductSkuId string
	// 商品第三方货号 -可选，传递了则计算价格更快
	ProductThirdSkuId string
	// 需要同步的价格
	ProductSyncPrice int
	// 组合商品明细信息
	SkuGroup []*models.ChannelSkuGroup
	JsonData string
	// 第三方商品id
	ProductThirdId string
	// 历史的商品价格
	oldJsonData string
	// 第三方商品id是否存在的阻断 是否忽略
	IgnoreThirdProductCheck bool
}

func GetPrice(channelId int, skuId, financeCode string) (price string, err error) {
	// 创建的时候需要构建价格
	var channelProduct1 ChannelProductPriceSync
	channelProduct1.ChannelId = int(channelId)
	channelProduct1.FinanceCode = financeCode
	channelProduct1.ProductSkuId = cast.ToString(skuId)
	channelProduct1.IgnoreThirdProductCheck = true
	logPrefix := fmt.Sprintf("同步价格参数; 商品id: %s, 门店id: %s, 渠道id: %d", channelProduct1.ProductId, channelProduct1.FinanceCode, channelProduct1.ChannelId)
	glog.Info(logPrefix, "入参为：", kit.JsonEncode(channelProduct1))
	// 检验参数
	if err = channelProduct1.checkParams(); err != nil {
		glog.Error(logPrefix, "检验参数失败", err)
		return
	}
	// 构造单价
	if err = channelProduct1.BuildPrice(); err != nil {
		glog.Error(logPrefix, "构造单价失败", err)
		return
	}

	// A . 先更新快照或上架表的价格
	go func() {
		err = channelProduct1.syncPriceToChannelProductSnap()
		if err != nil {
			glog.Error(logPrefix, "更新快照价格失败", err)

		}
	}()

	skuPrice, _ := decimal.NewFromInt(int64(channelProduct1.ProductSyncPrice)).DivRound(decimal.NewFromInt(100), 2).Float64()
	return cast.ToString(skuPrice), nil
}

// 通用校验方法
func (channelProductPriceSync *ChannelProductPriceSync) checkParams() error {

	log_prefix := "通用校验方法checkParams  " + kit.JsonEncode(channelProductPriceSync)
	glog.Info(log_prefix, "入参为：", kit.JsonEncode(channelProductPriceSync))
	if len(channelProductPriceSync.FinanceCode) == 0 {
		return errors.New("财务编码 不能为空")
	}
	if channelProductPriceSync.ChannelId == 0 {
		return errors.New("上架渠道 不能为空")
	}
	if len(channelProductPriceSync.ProductId) == 0 && len(channelProductPriceSync.ProductSkuId) == 0 {
		return errors.New("商品Spu或Sku 两者之一不能为空")
	}

	// 如果是美团或饿了么， 并且第三方商品id是否存在的阻断 不忽略，则查找第三方商品id是否存在
	if (channelProductPriceSync.ChannelId == ChannelMtId || channelProductPriceSync.ChannelId == ChannelElmId) && !channelProductPriceSync.IgnoreThirdProductCheck {
		if !CanCallThirdApi(cast.ToInt(channelProductPriceSync.ProductId), channelProductPriceSync.ChannelId, channelProductPriceSync.FinanceCode) {
			glog.Error(log_prefix, "没有第三方商品id====7， 阻断后面操作,商品id:", cast.ToInt(channelProductPriceSync.ProductId), ",财务编码：", channelProductPriceSync.FinanceCode)
			return errors.New("商品未在第三方创建，请先编辑后再进行操作")
		}
	}

	if len(channelProductPriceSync.ChannelFinanaceCode) == 0 && channelProductPriceSync.ChannelId != 9 {
		// 获取渠道门店
		client := GetDataCenterClient()
		defer client.Close()
		out, err := client.RPC.QueryStoresChannelId(client.Ctx, &dac.StoreRelationUserRequest{
			FinanceCode: []string{channelProductPriceSync.FinanceCode},
			Psize:       1000,
			ChannelId:   int32(channelProductPriceSync.ChannelId),
		})
		if err != nil {
			return err
		} else {
			if len(out.Data) == 0 {
				return errors.New("渠道门店 不能为空")
			} else {
				channelProductPriceSync.ChannelFinanaceCode = out.Data[0].ChannelStoreId
			}
		}
	}

	if channelProductPriceSync.WarehouseId == 0 {
		// 获取仓库
		product := new(Product)
		resp, err := product.GetChannelWarehouses([]string{channelProductPriceSync.FinanceCode}, cast.ToInt32(channelProductPriceSync.ChannelId))
		if err != nil {
			glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，"+log_prefix, err)
			return errors.New(log_prefix + "获取渠道仓库信息失败")
		}
		if len(resp) <= 0 && cast.ToInt32(channelProductPriceSync.ChannelId) == ChannelAwenId {
			resp, err = product.GetChannelWarehouses([]string{channelProductPriceSync.FinanceCode}, ChannelAwenPickUpId)
			if err != nil {
				glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，"+log_prefix, err)
				return errors.New(log_prefix + "获取渠道仓库信息失败")
			}
		}

		if len(resp) <= 0 {
			return errors.New(log_prefix + "未获取到渠道的仓库信息设置")
		}

		for i := range resp {
			warehouse := resp[i]
			if warehouse.Category == 3 || warehouse.Category == 4 || warehouse.Category == 1 {
				channelProductPriceSync.WarehouseId = int(warehouse.WarehouseId)
				channelProductPriceSync.WarehouseCategory = int(warehouse.Category)
			} else if warehouse.Category == 5 {
				warehouses := LoadWarehouseRelationCache(channelProductPriceSync.FinanceCode)
				if err != nil {
					glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
					return errors.New("获取仓库绑定信息失败")
				}
				for _, v := range warehouses {
					if v.Category == 4 && v.Status == 1 {
						channelProductPriceSync.WarehouseId = int(v.Id)
						channelProductPriceSync.WarehouseCategory = int(v.Category)
					}
				}
			}
		}
	}
	// 商品id为空
	if len(channelProductPriceSync.ProductId) == 0 {
		var skuThird models.ChannelSkuValue
		engine.Where("channel_id=?", channelProductPriceSync.ChannelId).Where("sku_id=?", channelProductPriceSync.ProductSkuId).Get(&skuThird)
		if skuThird.Id > 0 {
			channelProductPriceSync.ProductId = cast.ToString(skuThird.ProductId)
		}
	}
	// 商品skuId为空
	if len(channelProductPriceSync.ProductSkuId) == 0 || len(channelProductPriceSync.ProductThirdSkuId) == 0 {
		var erpId int
		// 子龙
		if channelProductPriceSync.WarehouseCategory == 3 {
			erpId = 4
		}
		// a8
		if channelProductPriceSync.WarehouseCategory == 4 || channelProductPriceSync.WarehouseCategory == 5 ||
			channelProductPriceSync.WarehouseCategory == 1 {
			erpId = 2
		}
		// 获取sku与第三方渠道sku
		var sku_third models.SkuThird
		engine.Where("erp_id=?", erpId).And("product_id=? and sku_id = ? ", channelProductPriceSync.ProductId, channelProductPriceSync.ProductThirdSkuId).Get(&sku_third)
		if sku_third.Id <= 0 {
			engine.Where("erp_id=?", erpId).And("product_id=?", channelProductPriceSync.ProductId).Get(&sku_third)
		}
		if sku_third.Id > 0 {
			if len(channelProductPriceSync.ProductSkuId) == 0 {
				channelProductPriceSync.ProductSkuId = cast.ToString(sku_third.SkuId)
			}
			channelProductPriceSync.ProductThirdSkuId = sku_third.ThirdSkuId
		} else {
			return errors.New("第三方货号或商品Sku 不能为空")
		}
	}
	return nil
}

// 批量更新线上的价格同步 批量跑前置仓价格用zx
func (channelProductPriceSync *ChannelProductPriceSync) QzcUpPrice() error {
	if channelProductPriceSync.ChannelId == ChannelAwenId { // 阿闻
		return channelProductPriceSync.syncPriceToAwenQzc()
	} else {
		// A . 先更新快照或上架表的价格
		err := channelProductPriceSync.syncPriceToChannelProductSnapByQzc()
		if err != nil {
			return errors.New(fmt.Sprintf("更新快照价格失败:%s", err.Error()))
		}
		// B. 更新第三发价格信息
		if channelProductPriceSync.ChannelId == ChannelMtId { // 美团
			return channelProductPriceSync.syncPriceToMt()
		}
		if channelProductPriceSync.ChannelId == ChannelElmId { // 饿了么
			return channelProductPriceSync.syncPriceToElm()
		}
		if channelProductPriceSync.ChannelId == ChannelJddjId { // 京东
			return channelProductPriceSync.syncPriceToJd()
		}
	}
	return nil
}

// 同步商品价格
func (channelProductPriceSync *ChannelProductPriceSync) SyncPrice() error {
	logPrefix := fmt.Sprintf("同步价格参数; 商品id: %s, 门店id: %s, 渠道id: %d", channelProductPriceSync.ProductId, channelProductPriceSync.FinanceCode, channelProductPriceSync.ChannelId)
	glog.Info(logPrefix, "入参为：", kit.JsonEncode(channelProductPriceSync))
	// 检验参数
	err := channelProductPriceSync.checkParams()
	if err != nil {
		glog.Error(logPrefix, "检验参数失败", err)
		return err
	}
	// 构造单价
	err = channelProductPriceSync.BuildPrice()
	if err != nil {
		glog.Error(logPrefix, "构造单价失败", err)
		return err
	}

	if channelProductPriceSync.ChannelId == ChannelAwenId || channelProductPriceSync.ChannelId == ChannelAwenPickUpId { // 阿闻
		return channelProductPriceSync.syncPriceToAwen()
	} else {
		// A . 先更新快照或上架表的价格
		err = channelProductPriceSync.syncPriceToChannelProductSnap()
		if err != nil {
			glog.Error(logPrefix, "更新快照价格失败", err)
			return fmt.Errorf("更新快照价格失败:%s", err.Error())
		}
		// B. 更新第三发价格信息
		if channelProductPriceSync.ChannelId == ChannelMtId { // 美团
			err = channelProductPriceSync.syncPriceToMt()
			if err != nil {
				glog.Error(logPrefix, "同步美团价格失败", err)
				return fmt.Errorf("同步美团价格失败:%s", err.Error())
			}
		}
		if channelProductPriceSync.ChannelId == ChannelElmId { // 饿了么
			err = channelProductPriceSync.syncPriceToElm()
			if err != nil {
				glog.Error(logPrefix, "同步饿了么价格失败", err)
				return fmt.Errorf("同步饿了么价格失败:%s", err.Error())
			}
		}
		if channelProductPriceSync.ChannelId == ChannelJddjId { // 京东
			err = channelProductPriceSync.syncPriceToJd()
			if err != nil {
				glog.Error(logPrefix, "同步京东价格失败", err)
				return fmt.Errorf("同步京东价格失败:%s", err.Error())
			}
		}
	}
	return nil
}

// 获取单价
func (this *ChannelProductPriceSync) BuildPrice() error {
	if this.ProductSyncPrice > 0 {
		return nil
	}
	// 是否是组合商品
	var productType int
	engine.Table(&models.Product{}).Where("id=?", this.ProductId).Select("product_type").Get(&productType)

	if productType == 3 { // 组合商品
		var subProducts []*models.ChannelSkuGroup
		engine.Where("product_id=?", this.ProductId).Where("channel_id=?", this.ChannelId).Find(&subProducts)
		var totalMoney = 0
		// 依次处理子商品

		sku_group_price_map := make(map[string]int64, 0)
		for _, v := range subProducts {
			// 实例化新的价格同步组件
			var subProductPriceSync ChannelProductPriceSync
			subProductPriceSync.ChannelId = this.ChannelId
			subProductPriceSync.FinanceCode = this.FinanceCode
			subProductPriceSync.ChannelFinanaceCode = this.ChannelFinanaceCode
			subProductPriceSync.WarehouseCode = this.WarehouseCode
			subProductPriceSync.WarehouseId = this.WarehouseId
			subProductPriceSync.WarehouseCategory = this.WarehouseCategory
			subProductPriceSync.ProductId = cast.ToString(v.GroupProductId)
			subProductPriceSync.ProductSkuId = cast.ToString(v.GroupSkuId)

			var third_sku_id string
			if this.WarehouseCategory == 3 {
				engine.SQL("select third_sku_id from channel_sku_third where product_id = ? and sku_id = ? and erp_id = ?",
					subProductPriceSync.ProductId, subProductPriceSync.ProductSkuId, 4).Get(&third_sku_id)
				subProductPriceSync.ProductThirdSkuId = third_sku_id
			} else if this.WarehouseCategory == 4 || this.WarehouseCategory == 5 || this.WarehouseCategory == 1 {
				engine.SQL("select third_sku_id from channel_sku_third where product_id = ? and sku_id = ? and erp_id = ?",
					subProductPriceSync.ProductId, subProductPriceSync.ProductSkuId, 2).Get(&third_sku_id)
				subProductPriceSync.ProductThirdSkuId = third_sku_id
			}
			err := subProductPriceSync.checkParams()
			if err != nil {
				return errors.New(fmt.Sprintf("子商品检验失败checkParams：%s", err.Error()))
			}
			err = subProductPriceSync.BuildPrice()
			if err != nil {
				return errors.New(fmt.Sprintf("子商品检验失败BuildPrice：%s", err.Error()))
			}
			if subProductPriceSync.ProductSyncPrice == 0 || subProductPriceSync.ProductSyncPrice == 500000 {
				if v.ProductType == 1 {
					return errors.New(fmt.Sprintf("实物商品子商品 %s的价格不能为0或者5000", subProductPriceSync.ProductId))
				}
			}
			if subProductPriceSync.ProductSyncPrice == 0 {
				return errors.New(fmt.Sprintf("子商品 %s 未找到合适的价格信息", subProductPriceSync.ProductId))
			}

			var subPrice int64
			if v.DiscountType == 1 { // 百分比
				// 将单价四舍五入
				var discountPrice = decimal.NewFromInt(int64(subProductPriceSync.ProductSyncPrice)).
					Mul(decimal.NewFromInt(int64(v.DiscountValue))).
					DivRound(decimal.NewFromInt(100), 0).IntPart()
				// 子商品的价格综合 单价*数量
				totalMoney = totalMoney + int(discountPrice)*v.Count
				subPrice = discountPrice
			} else { // 固定价格
				totalMoney = totalMoney + v.DiscountValue*v.Count
				subPrice = cast.ToInt64(v.DiscountValue)
			}

			// 获取子商品的价格然后更新快照的sku_group里面的价格
			sku_group_price_map[subProductPriceSync.ProductSkuId] = subPrice

			v.MarketPrice = subProductPriceSync.ProductSyncPrice
			this.SkuGroup = subProducts
		}
		// 最终价格
		this.ProductSyncPrice = totalMoney

	} else {
		// 同步价格优先获取第三方价格
		if this.WarehouseCategory == 4 || this.WarehouseCategory == 5 || this.WarehouseCategory == 1 { // 前置仓

			if this.ChannelId == ChannelDigitalHealth { // 互联网渠道的价格取的是编辑页面的
				err := this.getChannelDigitalHealthPrice(productType)
				if err != nil {
					return err
				}
			} else {
				price, _ := this.getA8Price()

				if price == 0 || price == 500000 {
					if productType == 1 {
						return errors.New(fmt.Sprintf("实物商品 %s 的价格不能为0或者5000", this.ProductId))
					}
				}

				if price > 0 {
					this.ProductSyncPrice = price
				} else {
					return errors.New(fmt.Sprintf("商品 %s 未找到合适的价格信息", this.ProductId))
				}
			}
		}
		if this.WarehouseCategory == 3 { // 门店仓
			if this.ChannelId == ChannelDigitalHealth {
				err := this.getChannelDigitalHealthPrice(productType)
				if err != nil {
					return err
				}
			} else {
				price, _ := this.getZilongPrice()
				if price == 0 || price == 500000 {
					if productType == 1 {
						return errors.New(fmt.Sprintf("实物商品 %s 的价格不能为0或者5000", this.ProductId))
					}
				}
				if price > 0 {
					this.ProductSyncPrice = price
				} else {
					return errors.New(fmt.Sprintf("商品 %s 未找到合适的价格信息", this.ProductId))
				}
			}
		}
	}
	return nil
}

// 医疗互联网的价格只取编辑页面的
func (this *ChannelProductPriceSync) getChannelDigitalHealthPrice(productType int) error {
	//先取互联网医疗价格，没有再去渠道商品价格
	var digitalHealthPrice models.HospitalProductPrice
	_, err := engine.Where("sku_id=?", this.ProductSkuId).Get(&digitalHealthPrice)
	if err != nil {
		return errors.New("互联网渠道的价格获取异常")
	}
	if digitalHealthPrice.Id > 0 {
		this.ProductSyncPrice = digitalHealthPrice.Price
	} else {
		var channelProductSku models.ChannelSku
		_, err := engine.Where("id=?", this.ProductSkuId).Where("channel_id=?", this.ChannelId).Get(&channelProductSku)
		if err != nil {
			return errors.New("互联网渠道的价格获取异常")
		}
		if channelProductSku.Id > 0 {
			if this.WarehouseCategory == 3 {
				if int(channelProductSku.StorePrice) == 0 || int(channelProductSku.StorePrice) == 500000 {
					if productType == 1 {
						return errors.New(fmt.Sprintf("实物商品 %s 的价格不能为0或者5000", this.ProductId))
					}
				}
				this.ProductSyncPrice = int(channelProductSku.StorePrice)
			}
			if this.WarehouseCategory == 4 || this.WarehouseCategory == 5 {
				if int(channelProductSku.PreposePrice) == 0 || int(channelProductSku.PreposePrice) == 500000 {
					if productType == 1 {
						return errors.New(fmt.Sprintf("实物商品 %s 的价格不能为0或者5000", this.ProductId))
					}
				}
				this.ProductSyncPrice = int(channelProductSku.PreposePrice)
			}
		}
	}

	return nil
}

// 获取商品在子龙的价格
func (channelProductPriceSync *ChannelProductPriceSync) getZilongPrice() (price int, err error) {
	// 检验参数
	err = channelProductPriceSync.checkParams()
	if err != nil {
		return
	}
	var priceSync models.PriceSync
	//根据门店编码查询仓库编码
	if channelProductPriceSync.WarehouseCode == "" {
		if _, err = engine.SQL(`select distinct code from dc_dispatch.warehouse w left join dc_dispatch.warehouse_relation_shop wrs 
    on wrs.warehouse_id = w.id where wrs.shop_id =? and wrs.channel_id =?;`, channelProductPriceSync.FinanceCode, channelProductPriceSync.ChannelId).Get(&channelProductPriceSync.WarehouseCode); err != nil {
			glog.Errorf("getZilongPrice 根据门店编码查询仓库编码失败%s", err.Error())
			return
		}
	}
	if channelProductPriceSync.WarehouseCode == "" {
		glog.Errorf("getZilongPrice 根据门店编码查询仓库编码无记录:%s,%s", channelProductPriceSync.ProductSkuId, channelProductPriceSync.FinanceCode)
		return
	}

	//取本地同步价格
	_, err = engine.Where("sku=?", channelProductPriceSync.ProductSkuId).And("finance_code = ?", channelProductPriceSync.WarehouseCode).Get(&priceSync)
	if err != nil {
		return
	} else {
		if priceSync.Id > 0 {
			price = priceSync.Price
		} else {
			// 取子龙价格
			request := new(pc.GetProductPriceByBJRequest)
			request.StructCode = []string{channelProductPriceSync.WarehouseCode}
			request.ProductCode = []string{channelProductPriceSync.ProductThirdSkuId}
			// 调用北京接口
			var product Product
			res, priceerr := product.GetProductPriceByBJ(context.Background(), request)
			if priceerr != nil {
				err = priceerr
				return
			}
			if res.Code == 200 && len(res.Data) > 0 {
				for _, data := range res.Data {
					for _, productInfo := range data.Product_Info {
						sellprice, err := decimal.NewFromString(productInfo.Sell_Price)
						if err == nil {
							price = int(sellprice.Mul(decimal.NewFromInt(100)).IntPart())
						} else {
							return price, err
						}
					}
				}
			}
		}
	}
	if price == 0 {
		price, err = channelProductPriceSync.getPriceFromChannelProduct()
	}
	return
}

// 获取A8的价格
func (channelProductPriceSync *ChannelProductPriceSync) getA8Price() (price int, err error) {
	// 电商仓
	if channelProductPriceSync.WarehouseCategory == 1 {
		_, err = engine.Table("mall_product_price").Alias("mpp").
			Join("inner", "dc_dispatch.warehouse w", "w.code = mpp.warehouse_code and w.category = 1").
			Where("w.id = ? and mpp.sku_id = ?", channelProductPriceSync.WarehouseId, channelProductPriceSync.ProductSkuId).
			Select("price").Get(&price)
	} else {
		_, err = engine.Table("qzc_price_sync").Where("warehouse_id=?", channelProductPriceSync.WarehouseId).
			And("sku_id=?", channelProductPriceSync.ProductSkuId).Select("price").Get(&price)
	}

	if err != nil {
		return
	} else {
		if price == 0 {
			price, err = channelProductPriceSync.getPriceFromChannelProduct()
		}
	}
	return
}

// 从商品快照或商品渠道表获取价格信息
func (channelProductPriceSync *ChannelProductPriceSync) getPriceFromChannelProduct() (price int, err error) {
	// 走渠道快照表查询价格
	var channelProductSnap models.ChannelProductSnapshot
	_, err = engine.Where("channel_id=?", channelProductPriceSync.ChannelId).Where("product_id=?", channelProductPriceSync.ProductId).Where("finance_code=?", channelProductPriceSync.FinanceCode).Get(&channelProductSnap)
	if err != nil {
		return
	}
	if channelProductSnap.Id > 0 {
		// 序列化
		var request = new(pc.ChannelProductRequest)
		err = json.Unmarshal([]byte(channelProductSnap.JsonData), request)
		if err != nil {
			return
		}
		if request != nil && len(request.SkuInfo) > 0 {

			for _, v := range request.SkuInfo {
				if cast.ToString(v.SkuId) == channelProductPriceSync.ProductSkuId {
					if channelProductPriceSync.WarehouseCategory == 3 || channelProductPriceSync.WarehouseCategory == 1 {
						price = int(v.StorePrice)
					}
					if channelProductPriceSync.WarehouseCategory == 4 || channelProductPriceSync.WarehouseCategory == 5 {
						price = int(v.PreposePrice)
					}
				}
			}

		}
	} else {
		var channelProductSku models.ChannelSku
		//_, err = engine.Where("product_id=?", channelProductPriceSync.ProductId).Where("channel_id=?", channelProductPriceSync.ChannelId).Get(&channelProductSku)
		_, err = engine.Where("id=?", channelProductPriceSync.ProductSkuId).Where("channel_id=?", channelProductPriceSync.ChannelId).Get(&channelProductSku)
		if err != nil {
			return
		}
		if channelProductSku.Id > 0 {
			if channelProductPriceSync.WarehouseCategory == 3 || channelProductPriceSync.WarehouseCategory == 1 {
				price = int(channelProductSku.StorePrice)
			}
			if channelProductPriceSync.WarehouseCategory == 4 || channelProductPriceSync.WarehouseCategory == 5 {
				price = int(channelProductSku.PreposePrice)
			}
		}
	}
	return
}

// 同步价格到快照和上架表批量跑前置仓价格用zx
func (channelProductPriceSync *ChannelProductPriceSync) syncPriceToChannelProductSnapByQzc() error {
	var channelProductSanp models.ChannelProductSnapshot

	err := json.Unmarshal([]byte(channelProductPriceSync.JsonData), &channelProductSanp)
	if err != nil {
		return err
	}

	if channelProductSanp.Id > 0 {
		// 解析json数据
		var request pc.ChannelProductRequest
		err := json.Unmarshal([]byte(channelProductSanp.JsonData), &request)
		if err != nil {
			return err
		}
		if len(request.SkuInfo) > 0 {
			if channelProductPriceSync.WarehouseCategory == 4 {
				request.SkuInfo[0].PreposePrice = int32(channelProductPriceSync.ProductSyncPrice)
				request.SkuInfo[0].MarketPrice = int32(channelProductPriceSync.ProductSyncPrice)
				if channelProductPriceSync.ChannelId == 9 {
					request.SkuInfo[0].RetailPrice = int32(channelProductPriceSync.ProductSyncPrice)
				}
			}
			if channelProductPriceSync.WarehouseCategory == 3 || channelProductPriceSync.WarehouseCategory == 1 {
				request.SkuInfo[0].StorePrice = int32(channelProductPriceSync.ProductSyncPrice)
				request.SkuInfo[0].MarketPrice = int32(channelProductPriceSync.ProductSyncPrice)
				if channelProductPriceSync.ChannelId == 9 {
					request.SkuInfo[0].RetailPrice = int32(channelProductPriceSync.ProductSyncPrice)
				}
			}
			var skuGroupMap = make(map[int32]int32)
			for _, v := range channelProductPriceSync.SkuGroup {
				skuGroupMap[int32(v.GroupProductId)] = int32(v.MarketPrice)
			}
			for _, v := range request.SkuInfo[0].SkuGroup {
				if value, ok := skuGroupMap[v.GroupProductId]; ok {
					v.MarketPrice = value
				}
			}
			channelProductSanp.JsonData = utils.ObjectToJsonString(request)
			engine.ID(channelProductSanp.Id).Cols("json_data").Update(&channelProductSanp)
			// 是否有上架记录
			var channelStoreProduct models.ChannelStoreProduct
			engine.Where("snapshot_id=?", channelProductSanp.Id).Get(&channelStoreProduct)
			if channelStoreProduct.Id > 0 {
				channelStoreProduct.MarketPrice = channelProductPriceSync.ProductSyncPrice
				engine.ID(channelStoreProduct.Id).Cols("market_price").Update(&channelStoreProduct)
			}

		}
	}
	return nil
}

// 同步价格到快照和上架表
func (channelProductPriceSync *ChannelProductPriceSync) syncPriceToChannelProductSnap() error {
	var channelProductSanp models.ChannelProductSnapshot
	var channelProductSanpCopy models.ChannelProductSnapshot

	if _, err := engine.Where("channel_id=?", channelProductPriceSync.ChannelId).
		And("finance_code=?", channelProductPriceSync.FinanceCode).
		And("product_id=?", channelProductPriceSync.ProductId).
		Get(&channelProductSanp); err != nil {
		return err
	} else if channelProductSanp.Id > 0 {

		// 解析json数据
		var request pc.ChannelProductRequest
		err := json.Unmarshal([]byte(channelProductSanp.JsonData), &request)
		if err != nil {
			return err
		}
		if len(request.SkuInfo) > 0 { // v6.0多规格
			if channelProductPriceSync.WarehouseCategory == 4 || channelProductPriceSync.WarehouseCategory == 5 {
				for _, v := range request.SkuInfo {
					if cast.ToString(v.SkuId) == channelProductPriceSync.ProductSkuId {
						v.PreposePrice = int32(channelProductPriceSync.ProductSyncPrice)
						v.MarketPrice = int32(channelProductPriceSync.ProductSyncPrice)
						if channelProductPriceSync.ChannelId == 9 {
							v.RetailPrice = int32(channelProductPriceSync.ProductSyncPrice)
						}
					}
				}

			}
			if channelProductPriceSync.WarehouseCategory == 3 || channelProductPriceSync.WarehouseCategory == 1 {
				for _, v := range request.SkuInfo {
					if cast.ToString(v.SkuId) == channelProductPriceSync.ProductSkuId {
						v.StorePrice = int32(channelProductPriceSync.ProductSyncPrice)
						v.MarketPrice = int32(channelProductPriceSync.ProductSyncPrice)
						if channelProductPriceSync.ChannelId == 9 {
							v.RetailPrice = int32(channelProductPriceSync.ProductSyncPrice)
						}
					}
				}
			}
			var skuGroupMap = make(map[int32]int32)
			for _, v := range channelProductPriceSync.SkuGroup {
				skuGroupMap[int32(v.GroupSkuId)] = int32(v.MarketPrice)
			}
			// // v6.0多规格
			for _, v_sku := range request.SkuInfo {
				for _, v_group := range v_sku.SkuGroup {
					if value, ok := skuGroupMap[v_group.GroupSkuId]; ok {
						v_group.MarketPrice = value
					}
				}
			}
			channelProductSanp.JsonData = utils.ObjectToJsonString(request)
			engine.ID(channelProductSanp.Id).Cols("json_data").Update(&channelProductSanp)

			// 是否有上架记录
			var channelStoreProduct models.ChannelStoreProduct
			engine.Where("snapshot_id=?", channelProductSanp.Id).Get(&channelStoreProduct)
			if channelStoreProduct.Id > 0 {
				channelStoreProduct.MarketPrice = channelProductPriceSync.ProductSyncPrice
				engine.ID(channelStoreProduct.Id).Cols("market_price").Update(&channelStoreProduct)
			}
		}
	}

	// 异步处理子商品关联组合商品太多同步的话会一直等待
	//性能优化子商品和快照价格对比,没有变化不做下面的更新组合商品的操作
	glog.Info("原始价格信息：", kit.JsonEncode(channelProductSanp), "channelProductPriceSync:", kit.JsonEncode(channelProductPriceSync))
	var flag = false
	if len(channelProductPriceSync.oldJsonData) > 0 {
		err := json.Unmarshal([]byte(channelProductPriceSync.oldJsonData), &channelProductSanpCopy)
		if err != nil {
			glog.Info("序列化异常：", err.Error())
		}
	}

	if channelProductSanpCopy.Id > 0 {
		var request pc.ChannelProductRequest
		json.Unmarshal([]byte(channelProductSanp.JsonData), &request)
		for _, v_children := range request.SkuInfo {
			if cast.ToString(v_children.SkuId) == channelProductPriceSync.ProductSkuId {
				if v_children.MarketPrice != int32(channelProductPriceSync.ProductSyncPrice) {
					flag = true
				}
			}
		}
	} else {
		flag = true
	}

	if flag {
		go UpdateGroupProductPrice(
			channelProductPriceSync.FinanceCode,
			cast.ToInt32(channelProductPriceSync.ChannelId),
			int32(channelProductPriceSync.WarehouseCategory),
			cast.ToInt32(channelProductPriceSync.ProductId),
			int32(channelProductPriceSync.ProductSyncPrice),
			nil,
			cast.ToInt32(channelProductPriceSync.ProductSkuId),
		)
	}

	return nil
}

// 同步到阿闻 批量跑前置仓价格用zx
func (channelProductPriceSync *ChannelProductPriceSync) syncPriceToAwenQzc() error {
	err := channelProductPriceSync.syncPriceToChannelProductSnapByQzc()
	return err
}

// 同步到阿闻
func (channelProductPriceSync *ChannelProductPriceSync) syncPriceToAwen() error {
	err := channelProductPriceSync.syncPriceToChannelProductSnap()
	return err
}

// 同步商品价格到美团
func (channelProductPriceSync *ChannelProductPriceSync) syncPriceToMt() error {

	// 拼接美团参数
	var food_data et.FoodDataMt
	food_data.AppFoodCode = channelProductPriceSync.ProductId
	var skus et.FoodSku
	skus.SkuId = channelProductPriceSync.ProductSkuId
	skuPrice, _ := decimal.NewFromInt(int64(channelProductPriceSync.ProductSyncPrice)).DivRound(decimal.NewFromInt(100), 2).Float64()
	skus.Price = cast.ToString(skuPrice)
	food_data.Skus = append(food_data.Skus, &skus)
	var food_data_list []et.FoodDataMt
	food_data_list = append(food_data_list, food_data)
	str, err := json.Marshal(food_data_list)
	if err != nil {
		return err
	}
	// 请求参数
	foodData := et.MtRetailSkuPriceRequest{}

	//storeMasterId := GetAppChannelByStoreId(channelProductPriceSync.ChannelFinanaceCode)
	storeMasterId, err := GetAppChannelByFinanceCode(channelProductPriceSync.FinanceCode)
	if err != nil {
		glog.Error("syncPriceToMt.", "GetAppChannelByFinanceCode failed：", channelProductPriceSync.FinanceCode, err)
		return errors.New("GetAppChannelByFinanceCode failed")
	}
	foodData.StoreMasterId = storeMasterId

	foodData.AppPoiCode = channelProductPriceSync.ChannelFinanaceCode
	foodData.FoodData = string(str)
	clientMt := channelProductPriceSync.getGrpc()
	//defer clientMt.Conn.Close()
	if !CanCallThirdApi(cast.ToInt(channelProductPriceSync.ProductId), ChannelMtId, channelProductPriceSync.FinanceCode) {
		glog.Error("没有第三方商品id====8,商品id:", cast.ToInt(channelProductPriceSync.ProductId), "财务编码：", channelProductPriceSync.FinanceCode)
		return errors.New("商品未在第三方创建，请先编辑后再进行操作")
	}

	res, err := clientMt.RPC.MtRetailSkuPrice(clientMt.Ctx, &foodData)
	glog.Info("同步价格到第三方返回数据：", kit.JsonEncode(res), "入参：", kit.JsonEncode(foodData), "err为：", kit.JsonEncode(err))
	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else {
		errMsg := ""
		if res.Code != 200 {
			errMsg = res.Message
		}
		UpdateProductThirdSyncErr(cast.ToInt(channelProductPriceSync.ProductId), ChannelMtId, channelProductPriceSync.FinanceCode, errMsg)
		if res.Code != 200 {
			// v7.0.11 同步第三方返回的错误信息
			return errors.New(res.Message)
		}

	}
	return nil
}

// 同步商品价格到饿了么
func (channelProductPriceSync *ChannelProductPriceSync) syncPriceToElm() error {

	var elm_params et.SkuPriceUpdateOneRequest
	elm_params.SkuidPrice = fmt.Sprintf("%s:%d,%d", channelProductPriceSync.ProductSkuId, channelProductPriceSync.ProductSyncPrice, channelProductPriceSync.ProductSyncPrice)
	elm_params.ShopId = channelProductPriceSync.ChannelFinanaceCode
	clientElm := channelProductPriceSync.getGrpc()

	if channelProductPriceSync.FinanceCode == "" {
		glog.Info("syncPriceToElm-FinanceCode-empty", kit.JsonEncode(*channelProductPriceSync))
	}

	appChannel, err := GetAppChannelByFinanceCode(channelProductPriceSync.FinanceCode)

	if err != nil {
		glog.Error("syncPriceToElm 获取appChannel值错误", ",err:", err.Error(), ",param:", kit.JsonEncode(channelProductPriceSync))
		return errors.New("获取appChannel值错误" + err.Error())
	}
	if appChannel == 0 {
		glog.Error("syncPriceToElm 获取appChannel值失败", ",param:", kit.JsonEncode(channelProductPriceSync))
		err = errors.New("获取appChannel值失败")
		return err
	}
	elm_params.AppChannel = appChannel

	//defer clientElm.Conn.Close()
	if !CanCallThirdApi(cast.ToInt(channelProductPriceSync.ProductId), ChannelElmId, channelProductPriceSync.FinanceCode) {
		glog.Error("没有第三方商品id====9,商品id:", cast.ToInt(channelProductPriceSync.ProductId), "财务编码：", channelProductPriceSync.FinanceCode)
		return errors.New("商品未在第三方创建，请先编辑后再进行操作")

	}
	res, err := clientElm.ELMPRODUCT.SkuPriceUpdateOne(context.Background(), &elm_params)
	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else {
		// v7.0.11 同步第三方返回的错误信息
		UpdateProductThirdSyncErr(cast.ToInt(channelProductPriceSync.ProductId), ChannelElmId, channelProductPriceSync.FinanceCode, res.Error)
		if res.Code != 200 {

			return errors.New(res.Message)
		}
	}
	return nil
}

func (channelProductPriceSync *ChannelProductPriceSync) getGrpc() *et.Client {
	client := et.GetExternalClient()
	return client
}

// 同步商品价格到京东
func (channelProductPriceSync *ChannelProductPriceSync) syncPriceToJd() error {

	etReq := new(et.UpdateStationPriceRequest)
	etReq.StationNo = channelProductPriceSync.ChannelFinanaceCode
	etReq.OutStationNo = channelProductPriceSync.FinanceCode

	//更新的sku信息
	var skuPriceInfo et.JddjSkuPriceInfo
	skuPriceInfo.OutSkuId = channelProductPriceSync.ProductSkuId
	skuPriceInfo.Price = int64(channelProductPriceSync.ProductSyncPrice)
	// 添加到请求信息
	etReq.SkuPriceInfoList = append(etReq.SkuPriceInfoList, &skuPriceInfo)
	storeMasterId, err := GetAppChannelByFinanceCode(channelProductPriceSync.FinanceCode)
	if err != nil {
		glog.Error("syncPriceToJd,", "GetAppChannelByFinanceCode,", channelProductPriceSync.FinanceCode, err)
		return errors.New("获取店铺主体信息失败，" + err.Error())
	}

	etReq.StoreMasterId = storeMasterId

	clientJd := channelProductPriceSync.getGrpc()
	//defer clientJd.Conn.Close()

	res, err := clientJd.JddjProduct.UpdateStationPrice(clientJd.Ctx, etReq)
	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else {
		if res.Code != "0" {
			return errors.New(res.Msg)
		}
	}
	return nil
}
