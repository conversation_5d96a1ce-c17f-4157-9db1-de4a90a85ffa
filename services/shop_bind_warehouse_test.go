package services

import (
	"_/proto/pc"
	"context"
	"testing"

	"github.com/limitedlee/microservice/example/proto"
	kit "github.com/tricobbler/rp-kit"
)

func TestProduct_EditShopBindWarehouse(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.ShopBindingWarehouseReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *proto.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "EditShopBindWarehouse",
			args: args{
				ctx: context.Background(),
				in: &pc.ShopBindingWarehouseReq{
					Data: []*pc.BindData{
						{
							ChannelId:   1,
							ShopId:      "CX0013",
							ShopName:    "宠颐生北京爱佳",
							WarehouseId: 233,
							RunType:     1,
						},
						{
							ChannelId:   2,
							ShopId:      "CX0013",
							ShopName:    "宠颐生北京爱佳",
							WarehouseId: 1010191,
							RunType:     1,
						},
						{
							ChannelId:   3,
							ShopId:      "CX0013",
							ShopName:    "宠颐生北京爱佳",
							WarehouseId: 1010191,
							RunType:     1,
						},
						{
							ChannelId:   4,
							ShopId:      "CX0013",
							ShopName:    "宠颐生北京爱佳",
							WarehouseId: 1010191,
							RunType:     1,
						},
						{
							ChannelId:   5,
							ShopId:      "CX0013",
							ShopName:    "宠颐生北京爱佳",
							WarehouseId: 1010191,
							RunType:     1,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.EditShopBindWarehouse(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("EditShopBindWarehouse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_ShopBindWarehouse(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.BindShopWarehouse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BindShopWarehouseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &pc.BindShopWarehouse{
					ChannelId: 1,
					QiniuUrl:  "https://file.vetscloud.com/91f2d5afb2dd3cd8404a070e85261121.xlsx",
					BindType:  3,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := c.ShopBindWarehouse(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("ShopBindWarehouse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

		})
	}
}
