package services

import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/dc"
	"_/proto/et"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 批量新建更新同步至饿了么
// 当选择商品及门店后点击“批量同步”时，如果商品在对应的门店饿了么商品库里不存在则进行新建，商品信息同步，建立快照;
// 如果商品在对应的门店已经存在，则按饿了么渠道商品库商品信息对门店饿了么商品库进行更新，同时更新快照
// 批量更新
func (c *Product) BatchToElmNew(ctx context.Context, in *pc.BatchToMTRequest) (*pc.BatchBaseResponse, error) {
	out := &pc.BatchBaseResponse{
		Code: 400,
	}
	glog.Info("BatchToElmNew请求参数==", in)
	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	var userno string
	var finance_code_arr []string
	//判断是全部门店
	if in.IsAll == 1 {
		if len(in.UserNo) <= 0 {
			out.Message = "用户编码不能为空"
			out.Code = 400
			return out, nil
		}
		client := GetDataCenterClient()
		defer client.Close()

		var params dac.GetHospitalListByUserNoRequest
		userno = in.UserNo
		params.UserNo = in.UserNo
		//用户校验
		userInfo := loadLoginUserInfo(ctx)
		if userInfo.IsGeneralAccount {
			params.IsLogo = 1
		} else {
			params.IsLogo = 0
		}
		params.ChannelId = in.ChannelId
		params.Category = in.Category
		out_result, err := client.RPC.GetHospitalListByUserNo(client.Ctx, &params)
		if err != nil {
			out.Message = "请求GetHospitalListByUserNo失败"
			out.Error = err.Error()
			out.Code = 400
			return out, nil
		}
		for _, v := range out_result.Data {
			finance_code_arr = append(finance_code_arr, v.StructOuterCode)
		}
	} else {
		if len(in.FinanceCode) > 0 {
			finance_code_arr = strings.Split(in.FinanceCode, ",")
		} else {
			out.Message = "财务编码或标识门店不能为空"
			return out, nil
		}
	}

	session := NewDbConn().NewSession()
	defer session.Close()
	var productIdSlice []int32
	// 如果产品id为空，则查更新所有的，如果不为空，则更新所选的产品id
	if len(in.ProductId) > 0 {
		for _, v := range strings.Split(in.ProductId, ",") {
			productIdSlice = append(productIdSlice, cast.ToInt32(v))
		}
	} else {
		out.Message = "商品ID不能为空"
		return out, nil
	}

	//通过门店财务编码查询渠道门店id
	appPoiCodeMap := GetAppPoiCodeByFinanceCode(finance_code_arr, in.ChannelId)
	appPoiCodeSlice := []string{}
	for k := range appPoiCodeMap {
		if len(k) > 0 {
			appPoiCodeSlice = append(appPoiCodeSlice, k)
		}
	}
	if len(appPoiCodeSlice) == 0 {
		out.Message = "没有可用的渠道门店"
		return out, nil
	}

	disclient := GetDispatchClient()
	defer disclient.Close()
	resList := make([][]string, 0)
	res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(context.Background(), &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: finance_code_arr})
	if err != nil {
		glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
		return out, nil
	}
	warehouseMap := make(map[string]*dc.WarehouseList, len(res.Data))
	for _, datum := range res.Data {
		warehouseMap[datum.Code] = datum
	}

	productToSku := make(map[int32]int32)
	for _, appPoiCodeEle := range appPoiCodeSlice {
		financeCode := appPoiCodeMap[appPoiCodeEle]
		for _, productId := range productIdSlice {
			clientElm := GetMtGlobalProductClient()
			//取快照信息
			var snapShots *pc.ChannelProductSnapshot
			if res, err := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
				ChannelId:   in.ChannelId,
				ProductId:   []int32{productId},
				FinanceCode: financeCode,
			}); err != nil {
				glog.Error(err)
				out.Message = err.Error()
				return out, nil
			} else if res.Code != 200 || len(res.Details) == 0 {
				b2, _ := json.Marshal(res)
				glog.Info("b2 ", string(b2))
				out.Message = "商品快照不存在，请先编辑商品"
				return out, nil
			} else {
				snapShots = res.Details[0]
			}

			var newSnap pc.ChannelProductRequest
			if err := json.Unmarshal([]byte(snapShots.JsonData), &newSnap); err != nil {
				glog.Errorf("反序列化报错：门店id: %s, product: %d, err: %s", financeCode, productId, err.Error())
				out.Message = "商品快照错误，请先编辑商品"
				return out, nil
			}
			productToSku[productId] = newSnap.SkuInfo[0].SkuId
			var sku et.UpdateElmShopSkuRequest

			appChannel, err := GetAppChannelByFinanceCode(financeCode)
			if err != nil {
				return out, err
			}
			if appChannel == 0 {
				err = errors.New("获取appChannel值错误")
				return out, err
			}

			resPro, _ := clientElm.ELMPRODUCT.GetElmProductList(context.Background(), &et.ElmGetProductListRequest{
				ShopId:          appPoiCodeEle,
				Page:            1,
				Pagesize:        1,
				Upc:             newSnap.SkuInfo[0].BarCode,
				IncludeCateInfo: 1,
				AppChannel:      appChannel,
			})

			if resPro.Code != 200 {
				glog.Error("查询饿了么商品失败, err: ", resPro.Error)
				return out, nil
			} else if resPro.Data != nil && len(resPro.Data.List) > 0 {
				if resPro.Data.List[0].CustomSkuId == cast.ToString(newSnap.SkuInfo[0].SkuId) {
					sku.SkuId = cast.ToString(resPro.Data.List[0].SkuId)
				} else {
					resList = append(resList, []string{financeCode, cast.ToString(productId), "该商品条码已存在，编辑失败"})
					continue
				}
			}

			if res, err := c.NewChannelProductSnapshot(ctx, &pc.ChannelProductSnapshot{
				Id:          snapShots.Id,
				ChannelId:   in.ChannelId,
				UserNo:      userno,
				ProductId:   snapShots.ProductId,
				JsonData:    snapShots.JsonData,
				FinanceCode: financeCode,
			}); err != nil {
				glog.Error(err)
				out.Message = err.Error()
				return out, nil
			} else if res.Code != 200 {
				b2, _ := json.Marshal(res)
				glog.Info("b2 ", string(b2))
				out.Message = "无法创建快照"
				return out, nil
			}

			sku.ShopId = appPoiCodeEle
			if newSnap.Product.ChannelCategoryId == 0 {
				resList = append(resList, []string{financeCode, cast.ToString(productId), "店内分类为必填项"})
				continue
			}
			//if newSnap.Product.ChannelTagId == 0 {
			//	resList = append(resList, []string{financeCode, cast.ToString(productId), "饿了么分类为必填项"})
			//	continue
			//}
			if len(newSnap.Product.Name) == 0 {
				resList = append(resList, []string{financeCode, cast.ToString(productId), "商品名称为必填项"})
				continue
			}
			if cast.ToFloat32(newSnap.SkuInfo[0].WeightForUnit) <= 0 {
				resList = append(resList, []string{financeCode, cast.ToString(productId), "商品重量为必填项"})
				continue
			}
			if warehouseMap[financeCode].Category == 3 && newSnap.SkuInfo[0].StorePrice <= 0 {
				resList = append(resList, []string{financeCode, cast.ToString(productId), "门店仓价格为必填项"})
				continue
			} else if warehouseMap[financeCode].Category == 4 && newSnap.SkuInfo[0].PreposePrice <= 0 {
				resList = append(resList, []string{financeCode, cast.ToString(productId), "前置仓价格为必填项"})
				continue
			}
			//图片转换
			var picList []string
			photoList := strings.Split(newSnap.Product.Pic, ",")
			for picK, picV := range photoList {
				if strings.TrimSpace(picV) == "" {
					continue
				}
				var pic et.UploadPictureRequest
				pic.Url = picV
				pic.AppChannel = appChannel

				res, _ := clientElm.ELMPRODUCT.UploadPicture(context.Background(), &pic)
				if res.Code != 200 {
					glog.Errorf("饿了么商品图片上传失败，financeCode：%s，productId：%d，ERR：%v, pic: %s", financeCode, productId, res.Error, picV)
					continue
				}
				photo := et.SkuPhotos{}
				photo.IsMaster = 0
				if picK == 0 {
					photo.IsMaster = 1
				}
				photo.Url = res.Data
				picList = append(picList, photo.Url)
				sku.Photos = append(sku.Photos, &photo)
			}
			if len(sku.Photos) == 0 {
				resList = append(resList, []string{financeCode, cast.ToString(productId), "图片不可为空"})
				continue
			}

			//富文本处理
			if newSnap.Product.ContentPc != "" {
				sku.Desc = newSnap.Product.ContentPc
			}

			if newSnap.Product.ChannelCategoryId > 0 {
				thirdModel := models.ChannelCategoryThirdid{}
				hasthird, err := session.Table("channel_category_thirdid").Select("category_id").Where("id=? AND channel_id=? AND channel_store_id=?", newSnap.Product.ChannelCategoryId, 1, appPoiCodeEle).Get(&thirdModel)
				if !hasthird {
					glog.Error("【饿了么第三方分类不存在】financeCodeSlice: ", financeCode, "，productId：", productId, "，", err)
					resList = append(resList, []string{financeCode, cast.ToString(productId), "饿了么第三方分类不存在"})
					continue
				} else {
					sku.CategoryId = cast.ToInt64(thirdModel.CategoryId)
				}
			}
			sku.Name = newSnap.Product.Name
			sku.Cat3Id = newSnap.Product.ChannelTagId
			sku.Summary = newSnap.Product.SellingPoint
			sku.Weight = cast.ToFloat32(newSnap.SkuInfo[0].WeightForUnit)
			sku.SaleUnit = newSnap.SkuInfo[0].WeightUnit
			if newSnap.SkuInfo[0].MarketPrice > 0 {
				sku.SalePrice = newSnap.SkuInfo[0].MarketPrice
			} else {
				glog.Errorf("商品价格为0，financeCode：%v，productIdMap：%v", financeCode, productId)
				resList = append(resList, []string{financeCode, cast.ToString(productId), "价格为必填项"})
				continue
			}
			sku.Upc = newSnap.SkuInfo[0].BarCode
			sku.CustomSkuId = cast.ToString(newSnap.SkuInfo[0].SkuId)
			//查询库存
			skuCodes := []*ic.SkuCodeInfo{}
			for _, s := range newSnap.SkuInfo {
				skuCode := &ic.SkuCodeInfo{}
				skuCode.FinanceCode = financeCode
				skuCode.Sku = cast.ToString(s.SkuId)
				skuCodes = append(skuCodes, skuCode)
			}
			//stockMap, _ := GetStockInfoBySkuCode(skuCodes, 1) //查询本地的库存
			stock, err := GetStockInfoBySkuCodeAndShopId(ChannelElmId, newSnap.SkuInfo[0].SkuId, financeCode) //查询本地的库存
			if err != nil {
				//return errors.New(fmt.Sprintf("查询库存失败 商品%s, err:%s", newSnap.SkuInfo[0].SkuId, err.Error()))
				glog.Error(fmt.Sprintf("查询库存失败 商品%d, err:%s", newSnap.SkuInfo[0].SkuId, err.Error()))
				continue
			}

			//sku.LeftNum = stockMap[financeCode+":"+cast.ToString(newSnap.SkuInfo[0].SkuId)]
			sku.LeftNum = stock
			sku.Status = 0

			sku.AppChannel = appChannel

			res, err := clientElm.ELMPRODUCT.UpdateElmShopSku(context.Background(), &sku)
			glog.Info("4444饿了么创建/更新商品返回数据为", kit.JsonEncode(res), "请求数据为", kit.JsonEncode(sku))
			errMsg := ""
			if res.Code != 200 {
				errMsg = res.Error
			}
			UpdateProductThirdIdBySpu(enum.UpdateProductThirdIdFrom15, int(productId), ChannelElmId, financeCode, errMsg)
			if err != nil {
				glog.Error("饿了么接口失败，失败原因："+err.Error()+"门店id：", financeCode)
				resList = append(resList, []string{financeCode, cast.ToString(productId), "更新饿了么商品失败"})
				continue
			} else if res.Code != 200 {
				jsons, _ := json.Marshal(sku)
				glog.Error("【调用接口UpdateElmShopSku失败】", string(jsons), ",饿了么接口失败，失败原因："+res.Error+"门店id：", financeCode)
				resList = append(resList, []string{financeCode, cast.ToString(productId), res.Error})

				continue
			}

			//clientElm.Close()
		}
	}

	go func(finance_code_arr []string) {
		//价格同步---饿了么
		for _, f := range finance_code_arr {
			for _, v := range productIdSlice {
				if value, ok := warehouseMap[f]; ok && value.Category == 3 {
					var sku_third models.SkuThird
					engine.Where("erp_id=4").And("product_id=?", v).Get(&sku_third)
					if sku_third.Id > 0 {
						UpdateZlPrice(f, sku_third.SkuId, ChannelMtId)
					}
				} else if value.Category == 4 {
					var sku_third models.SkuThird
					engine.Where("erp_id=2").And("product_id=?", v).Get(&sku_third)
					if sku_third.Id > 0 {
						UpdateQzPrice(f, value.Id, sku_third.SkuId, ChannelMtId)
					}
				}
			}
		}
	}(finance_code_arr)

	//更新任务列表信息
	if len(resList) > 0 {
		headRow := append([]string{}, "财务编码", "平台商品ID", "失败原因")
		errList := append([][]string{}, headRow)
		errList = append(errList, resList...)
		excelUrl, err := ExportProductErr(errList)
		out.QiniuUrl = excelUrl
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, nil
		}
		//取第一条数据的错误信息
		out.Message = resList[0][2]
	}
	if out.Message == "" {
		out.Message = "操作成功"
	}
	out.Code = 200
	return out, nil
}

// 饿了么单门店单商品商品价格修改
// todo 什么地方调用的
// 1 调用的地方： 批量更新商品到饿了么 如果不是总账号 只能更新价格
func (c *Product) SingleUpdatePriceToElm(ctx context.Context, in *pc.ChannelProductRequest) (*pc.BatchToMTResponse, error) {
	logPrefix := fmt.Sprintf("SingleUpdatePriceToElm饿了么单门店单商品商品价格修改,入参：%s", kit.JsonEncode(in))
	glog.Info(logPrefix)
	out := new(pc.BatchToMTResponse)
	out.Code = 400

	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		out.Error = "用户不存在"
		return out, nil
	}

	clientElm := GetMtProductClient()
	defer clientElm.Close()

	//通过门店财务编码查询渠道门店id
	appPoiCodeMap := GetAppPoiCodeByFinanceCode([]string{in.FinanceCode}, 3)
	appPoiCodeSlice := []string{}
	for k := range appPoiCodeMap {
		if len(k) > 0 {
			appPoiCodeSlice = append(appPoiCodeSlice, k)
		}
	}
	if len(appPoiCodeSlice) == 0 {
		glog.Error(logPrefix, "没有可用的渠道门店")
		out.Message = "没有可用的渠道门店"
		return out, nil
	}

	product := new(Product)
	warehouse, err := product.GetChannelWarehouses([]string{in.FinanceCode}, ChannelElmId)
	if err != nil {
		glog.Error(logPrefix, err)
		out.Message = "获取饿了么渠道配置异常"
		return out, err
	}
	if len(warehouse) <= 0 {
		glog.Error(logPrefix, "编辑同步eleme商品 ：")
		out.Message = "没有获取到饿了么渠道配置"
		return out, err
	}

	var skuThirdMap = make(map[int32]bool)
	for _, i := range in.SkuInfo[0].SkuThird {
		if i.ErpId == 2 {
			skuThirdMap[4] = true
			skuThirdMap[5] = true
		}
		if i.ErpId == 4 {
			skuThirdMap[3] = true
		}
	}
	if _, ok := skuThirdMap[int32(warehouse[0].Category)]; !ok {
		if warehouse[0].Category == 3 {
			glog.Error(logPrefix, "缺少子龙货号")
			out.Message = "缺少子龙货号"
			return out, nil
		} else if warehouse[0].Category == 4 || warehouse[0].Category == 5 {
			glog.Error(logPrefix, "缺少A8货号")
			out.Message = "缺少A8货号"
			return out, nil
		}
	}

	switch warehouse[0].Category {
	case 3:
		for _, vSku := range in.SkuInfo {
			vSku.MarketPrice = vSku.StorePrice
		}
	case 4, 5:
		for _, vSku := range in.SkuInfo {
			vSku.MarketPrice = vSku.PreposePrice
		}
	}

	// 若条码为空则用skuid代替条码
	if len(in.SkuInfo[0].BarCode) == 0 {
		in.SkuInfo[0].BarCode = cast.ToString(in.SkuInfo[0].SkuId)
	}

	appChannel, err := GetAppChannelByFinanceCode(in.FinanceCode)
	if err != nil {
		out.Message = "获取门店的appChannel出错"
		return out, err
	}
	if appChannel == 0 {
		err = errors.New("获取appChannel值错误")
		return out, err
	}
	db := NewDbConn()
	var snap = models.ChannelProductSnapshot{}
	_, err = db.Where("channel_id = 3").
		And("finance_code = ?", in.FinanceCode).
		And("product_id = ?", in.Product.Id).
		Get(&snap)
	if err != nil {
		out.Message = "查询快照失败: " + err.Error()
		return out, nil
	}
	if snap.Id <= 0 {
		var newSnap models.ChannelProductSnapshot
		newSnap.FinanceCode = in.FinanceCode
		newSnap.ChannelId = cast.ToInt(in.Product.ChannelId)
		newSnap.UserNo = userInfo.UserNo
		newSnap.ProductId = in.Product.Id
		jsonData, _ := json.Marshal(in)
		newSnap.JsonData = string(jsonData)
		_, err = db.Insert(&newSnap)
		if err != nil {
			out.Message = "新增饿了么快照失败: " + err.Error()
			return out, nil
		}
	} else {
		jsonData, _ := json.Marshal(in)
		_, err = db.Id(snap.Id).Update(&models.ChannelProductSnapshot{
			JsonData: string(jsonData),
		})
		if err != nil {
			out.Message = "新增饿了么快照失败: " + err.Error()
			return out, nil
		}
	}

	// 查询商品在饿了么平台是否已存在
	var sku et.UpdateElmShopSkuRequest
	sku.ShopId = appPoiCodeSlice[0]
	sku.Upc = in.SkuInfo[0].BarCode

	sku.AppChannel = appChannel

	resPro, err := clientElm.ELMPRODUCT.GetElmProductList(context.Background(), &et.ElmGetProductListRequest{
		ShopId:          appPoiCodeSlice[0],
		Page:            1,
		Pagesize:        1,
		Upc:             in.SkuInfo[0].BarCode,
		IncludeCateInfo: 1,
		AppChannel:      appChannel,
	})
	if err != nil {
		glog.Error("查询饿了么商品失败, err: ", err.Error())
		out.Message = "查询饿了么商品失败, err: " + err.Error()
		return out, nil
	}
	if resPro.Code != 200 {
		glog.Error("查询饿了么商品失败, err: ", resPro.Error)
		out.Message = resPro.Error
		return out, nil
	}
	if resPro.Data != nil {
		if len(resPro.Data.List) > 0 {
			// 饿了么已存在该upc, 但sku不符
			if resPro.Data.List[0].CustomSkuId != cast.ToString(in.SkuInfo[0].SkuId) {
				out.Message = "编辑失败, UPC已存在"
				return out, nil
			}
			//饿了么无此商品
			sku.SkuId = cast.ToString(resPro.Data.List[0].SkuId)
		} else {
			//饿了么不存在此商品,且商品没有第三方id则不调用接口
			if CanCallThirdApi(cast.ToInt(in.Product.Id), ChannelElmId, in.FinanceCode) {
				glog.Error(logPrefix, "skuid已上架渠道，但商品条码在阿闻与渠道不一致，请注意,商品id:", in.Product.Id, ",财务编码：", in.FinanceCode)
				out.Message = "skuid已上架渠道，但商品条码在阿闻与渠道不一致，请注意"
				go UpdateProductThirdIdBySpu(enum.UpdateProductThirdIdFrom18, int(in.Product.Id), ChannelElmId, in.FinanceCode, "skuid已上架渠道，但商品条码在阿闻与渠道不一致，请注意")
				return out, nil
			}
		}
		res, err := c.UpdateElmProduct(in, sku)
		if err != nil {
			out.Message = "更新饿了么商品失败"
			return out, nil
		}
		if res.SyncError != "初始值" {
			UpdateProductThirdIdBySpu(enum.UpdateProductThirdIdFrom18, int(in.Product.Id), ChannelElmId, in.FinanceCode, res.SyncError)
		}
		if res.Code != 200 {
			out.Message = res.Message
			return out, nil
		}
	}

	var elmParams et.SkuPriceUpdateOneRequest
	if warehouse[0].Category == 3 {
		//价格同步门店仓
		price := UpdateZlPrice(in.FinanceCode, in.SkuInfo[0].SkuId, ChannelElmId)
		if price > 0 {
			out.Code = 200
			return out, err
		}
		if in.SkuInfo[0].StorePrice <= 0 {
			out.Message = "价格不能为0"
			return out, err
		}
		elmParams.SkuidPrice = fmt.Sprintf("%d:%d,%d", in.SkuInfo[0].SkuId, in.SkuInfo[0].StorePrice, in.SkuInfo[0].StorePrice)
	} else if warehouse[0].Category == 4 || warehouse[0].Category == 5 {
		//价格同步前置仓
		price := UpdateQzPrice(in.FinanceCode, int32(warehouse[0].WarehouseId), in.SkuInfo[0].SkuId, ChannelElmId)
		if price > 0 {
			out.Code = 200
			return out, err
		}
		if in.SkuInfo[0].PreposePrice <= 0 {
			out.Message = "价格不能为0"
			return out, err
		}
		elmParams.SkuidPrice = fmt.Sprintf("%d:%d,%d", in.SkuInfo[0].SkuId, in.SkuInfo[0].PreposePrice, in.SkuInfo[0].PreposePrice)
	}
	elmParams.ShopId = appPoiCodeSlice[0]
	elmParams.AppChannel = appChannel

	glog.Info("饿了么更新价格参数：", elmParams)
	if !CanCallThirdApi(cast.ToInt(in.Product.Id), ChannelElmId, in.FinanceCode) {
		glog.Error("没有第三方商品id====6,商品id:", in.Product.Id, ",财务编码：", in.FinanceCode)
		out.Message = "商品未在第三方创建，请先编辑后再进行操作"
		return out, errors.New("商品未在第三方创建，请先编辑后再进行操作")
	}
	res, err := clientElm.ELMPRODUCT.SkuPriceUpdateOne(context.Background(), &elmParams)
	glog.Info("饿了么更新价格参数", elmParams, "饿了么更新价格响应结果：", res, "err为:", kit.JsonEncode(err))
	UpdateProductThirdSyncErr(cast.ToInt(in.Product.Id), ChannelElmId, in.FinanceCode, res.Error)
	if err != nil {
		glog.Error("饿了么价格同步失败err:", err, "数据为:", elmParams)
		out.Message = "饿了么价格同步失败: " + err.Error()
		return out, err
	}
	if res.Code != 200 {
		glog.Error("饿了么价格同步失败err:", res.Error, "数据为：", elmParams)
		out.Message = "饿了么价格同步失败: " + res.Error
		return out, err
	}

	jsonSnap, _ := json.Marshal(in)
	snap.JsonData = string(jsonSnap)
	_, err = db.Id(snap.Id).Update(&snap)

	if err != nil {
		glog.Error(err)
		out.Message = "更新饿了么门店快照价格失败"
		return out, err
	}
	out.Code = 200
	return out, err
}

// 饿了么创建/更新
func (c *Product) UpdateElmProduct(in *pc.ChannelProductRequest, sku et.UpdateElmShopSkuRequest) (*pc.BatchToMTResponse, error) {
	prefix := "饿了么创建/更新,"
	out := new(pc.BatchToMTResponse)
	out.Code = 400
	out.SyncError = "初始值"

	clientElm := GetMtProductClient()
	defer clientElm.Close()

	if len(sku.ShopId) == 0 {
		out.Message = "饿了么门店id不存在"
		return out, nil
	}
	if len(sku.Upc) == 0 {
		out.Message = "UPC不存在"
		return out, nil
	}
	if in.Product.ChannelCategoryId == 0 {
		out.Message = "店内分类为必填项"
		return out, nil
	}
	if in.Product.ChannelTagId == 0 {
		out.Message = "饿了么分类为必填项"
		return out, nil
	}
	if len(in.Product.Name) == 0 {
		out.Message = "商品名称为必填项"
		return out, nil
	}
	if cast.ToFloat32(in.SkuInfo[0].WeightForUnit) <= 0 {
		out.Message = "商品重量为必填项"
		return out, nil
	}

	if in.Product.ChannelCategoryId > 0 {
		thirdModel := models.ChannelCategoryThirdid{}
		hasthird, _ := engine.Table("channel_category_thirdid").
			Select("category_id").Where("id=? AND channel_store_id=?",
			in.Product.ChannelCategoryId, sku.ShopId).Get(&thirdModel)
		if !hasthird {
			glog.Error(prefix, "【饿了么第三方分类不存在】financeCodeSlice: ", in.FinanceCode, "，productId：", in.Product.Id, "，分类id:", in.Product.ChannelCategoryId)
			out.Message = "饿了么第三方分类不存在"
			return out, nil
		} else {
			sku.CategoryId = cast.ToInt64(thirdModel.CategoryId)
		}
	}

	//图片转换
	if len(sku.Photos) <= 0 {
		var picList []string
		photoList := strings.Split(in.Product.Pic, ",")
		for picK, picV := range photoList {
			if strings.TrimSpace(picV) == "" {
				continue
			}
			var pic et.UploadPictureRequest
			pic.Url = picV
			//商品图片的上传直接使用瑞鹏的appChannel就好，该方法仅仅是饿了么上传图片并返回他的图片地址，所以与应用无关
			pic.AppChannel = 1

			res, err := clientElm.ELMPRODUCT.UploadPicture(context.Background(), &pic)
			if err != nil {
				glog.Errorf("饿了么商品图片上传失败，全部门店，productId：%d，ERR：%v, pic: %s", in.Product.Id, err.Error(), picV)
				continue
			}
			if res.Code != 200 {
				glog.Errorf("饿了么商品图片上传失败，全部门店，productId：%d，ERR：%v, pic: %s", in.Product.Id, res.Error, picV)
				continue
			}
			photo := et.SkuPhotos{}
			photo.IsMaster = 0
			if picK == 0 {
				photo.IsMaster = 1
			}
			photo.Url = res.Data
			sku.Photos = append(sku.Photos, &photo)
			picList = append(picList, res.Data)
		}
		if len(sku.Photos) == 0 {
			out.Message = "图片不可为空"
			return out, nil
		}
	}

	//富文本处理
	if len(sku.Desc) == 0 && in.Product.ContentPc != "" {
		sku.Desc = in.Product.ContentPc
	}

	sku.Name = in.Product.Name
	sku.Cat3Id = in.Product.ChannelTagId
	sku.Summary = in.Product.SellingPoint
	sku.Weight = cast.ToFloat32(in.SkuInfo[0].WeightForUnit)
	sku.SaleUnit = in.SkuInfo[0].WeightUnit
	if sku.SalePrice <= 0 {
		if in.SkuInfo[0].MarketPrice > 0 {
			sku.SalePrice = in.SkuInfo[0].MarketPrice
		} else {
			out.Message = "商品价格不能为0"
			return out, nil
		}
	}
	sku.CustomSkuId = cast.ToString(in.SkuInfo[0].SkuId)
	if sku.LeftNum <= 0 {
		if len(sku.SkuId) <= 0 {
			stock, _ := GetStockInfoBySkuCodeAndShopId(ChannelElmId, in.SkuInfo[0].SkuId, in.FinanceCode) //查询本地的库存
			sku.LeftNum = stock
		} else {
			sku.LeftNum = -1
		}
	}
	if sku.Status != 1 {
		var storeProduct = models.ChannelStoreProduct{}
		engine.Where("channel_id = 3 and finance_code = ?", in.FinanceCode).
			And("product_id = ?", in.Product.Id).
			Get(&storeProduct)
		if storeProduct.Id <= 0 {
			sku.Status = 0
		} else {
			sku.Status = int32(storeProduct.UpDownState)
		}
	}
	skuJson, _ := json.Marshal(sku)

	glog.Info(prefix, "门店id: ", in.FinanceCode, ", 请求参数: ", string(skuJson))
	res, err := clientElm.ELMPRODUCT.UpdateElmShopSku(context.Background(), &sku)
	glog.Info(prefix, "门店id: ", in.FinanceCode, ",请求参数: ", string(skuJson), ",返回数据为：", kit.JsonEncode(res))

	if err != nil {
		glog.Error(prefix, "饿了么接口失败，失败原因："+err.Error()+"门店id：", in.FinanceCode)
		out.Message = "更新饿了么商品失败"
		return out, err
	} else {
		out.SyncError = res.Error
		if res.Code != 200 {
			jsons, _ := json.Marshal(sku)
			glog.Error(prefix, "【调用接口UpdateElmShopSku失败】", string(jsons), ",饿了么接口失败，失败原因："+res.Error+"门店id：", in.FinanceCode)
			out.Message = res.Error
			return out, nil
		}
	}
	out.Code = 200
	return out, nil
}

// 批量新建更新同步至饿了么
func (c *Product) BatchToElm(ctx context.Context, in *pc.BatchToMTRequest) (*pc.BatchToMTResponse, error) {
	out := &pc.BatchToMTResponse{
		Code: 400,
	}
	// 序列化请求参数
	paramByte, err := json.Marshal(in)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, err
	}

	// 保存到任务列表
	var tasklist models.TaskList
	tasklist.ChannelId = in.ChannelId
	tasklist.CreateId = in.UserNo
	tasklist.ModifyId = in.UserNo
	tasklist.ModifyTime = time.Now()
	tasklist.CreateTime = time.Now()
	tasklist.TaskContent = 4
	tasklist.TaskStatus = 1
	tasklist.Status = 1
	tasklist.OperationFileUrl = string(paramByte)

	// 保存任务信息
	_, err = NewDbConn().Insert(&tasklist)
	if err != nil {
		glog.Error(err)
		out.Message = "保存批量上架任务信息失败"
		return out, err
	}

	out.Code = 200
	out.Message = "操作已成功提交，请在‘批量任务查看’中查看相关结果"
	return out, nil
}

// 查询饿了么类目列表
func (c *Product) GetElmCategoryList(ctx context.Context, in *pc.CategoryListRequest) (*pc.ElmCategoryListResponse, error) {
	out := &pc.ElmCategoryListResponse{
		Code: 400,
	}
	conn := NewDbConn()
	var categoryList []models.ChannelCategory
	err := conn.Where("channel_id = 3").Find(&categoryList)
	if err != nil {
		return out, err
	}
	var categoryElmList []*pc.ElmStoreCategoryList
	var categoryElmMap = make(map[int32][]*pc.ElmStoreCategoryList)
	for _, v := range categoryList {
		var categoryElm pc.ElmStoreCategoryList
		categoryElm.CatId = cast.ToString(v.Id)
		categoryElm.CatName = v.Name
		categoryElm.ParentId = v.ParentId
		if v.ParentId <= 0 {
			categoryElmList = append(categoryElmList, &categoryElm)
		} else {
			categoryElmMap[v.ParentId] = append(categoryElmMap[v.ParentId], &categoryElm)
		}
	}
	for _, v := range categoryElmList {
		v.Child = categoryElmMap[cast.ToInt32(v.CatId)]
	}
	out.Code = 200
	out.Data = categoryElmList
	return out, nil
}

// 更新饿了么类目列表
func (c *Product) UpdateElmCategoryList(ctx context.Context, in *pc.CategoryListRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{
		Code: 400,
	}
	glog.Info("更新饿了么类目列表..")
	clientElm := GetMtProductClient()
	defer clientElm.Close()

	res, err := clientElm.ELMSTORE.GetElmCategoryList(ctx, &et.CategoryListRequest{
		Keyword:    in.Keyword,
		ParentId:   in.ParentId,
		AppChannel: 1, //多应用 分类应该都是一样的 此处写死瑞鹏
	})

	if err != nil {
		glog.Error("查询饿了么类目列表失败: " + err.Error())
		out.Message = "查询饿了么类目列表失败: " + err.Error()
		return out, err
	} else if res.Data == nil {
		glog.Error("查询饿了么类目列表为空")
		out.Message = "查询饿了么类目列表为空"
		return out, err
	}

	conn := NewDbConn()
	session := conn.NewSession()
	defer session.Close()

	session.Begin()

	_, err = session.Where("channel_id = 3").Delete(&models.ChannelCategory{})
	if err != nil {
		session.Rollback()
		glog.Error("删除饿了么分类失败: ", err)
		return out, err
	}
	err = insertElmCategory(res.Data, session)
	if err != nil {
		session.Rollback()
		glog.Error("写入饿了么分类失败: ", err)
		return out, err
	}
	err = session.Commit()
	if err != nil {
		glog.Error("写入饿了么分类事务提交失败: ", err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

func insertElmCategory(node *et.ElmStoreCategoryList, session *xorm.Session) error {
	for _, child := range node.Child {
		var channelCategory models.ChannelCategory
		channelCategory.Id = cast.ToInt32(child.CatId)
		channelCategory.ChannelId = ChannelElmId
		channelCategory.Name = child.CatName
		channelCategory.ParentId = cast.ToInt32(node.CatId)
		if node.ParentId == 0 {
			channelCategory.ParentId = 0
		}
		_, err := session.Insert(&channelCategory)
		if err != nil {
			session.Rollback()
			return err
		}
		insertElmCategory(child, session)
	}
	return nil
}
