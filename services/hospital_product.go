package services

import (
	"_/models"
	"_/proto/pc"
	"_/utils"
	"context"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"net/http"
	"strings"
	"time"
)

//互联网医院商品价格导入
func (c *Product) HospitalProductPriceImport(ctx context.Context, req *pc.HospitalProductPriceImportReq) (*pc.BaseResponse, error) {
	resp := &pc.BaseResponse{
		Code: http.StatusBadRequest,
	}
	// 验证Excel地址是否为空
	if req.ExcelUrl == "" {
		resp.Message = "Excel文档地址不能为空"
		return resp, nil
	}

	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return nil, errors.New("用户不存在")
	}

	// 下载excel
	request, err := http.NewRequest("GET", req.ExcelUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("构建下载Excel文档(%s)请求异常,%+v", req.ExcelUrl, err)
	}

	response, err := utils.Client60Second.Do(request)
	if err != nil {
		return nil, fmt.Errorf("执行下载Excel文档(%s)请求异常,%+v", req.ExcelUrl, err)
	}
	defer response.Body.Close()

	f, err := excelize.OpenReader(response.Body)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文档(%s)异常,%+v", req.ExcelUrl, err)
	}

	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows := f.GetRows(sheetName)

	Engine := NewDbConn()

	for i, item := range rows {
		if i == 0 {
			continue
		}

		skuid := cast.ToInt(strings.TrimSpace(item[0]))
		price := cast.ToFloat32(strings.TrimSpace(item[1]))

		//入库
		var ProductPrice models.HospitalProductPrice
		if hasPrice, err := Engine.Select("id").Where("sku_id = ?", skuid).Get(&ProductPrice); err != nil {
			glog.Errorf("HospitalProductPriceImport 第%d条 互联网医院商品价格导入异常1:%+v,请求参数:%d", i+1, err, skuid)
		} else if hasPrice {
			_, err := Engine.Where("id = ?", ProductPrice.Id).Cols("price, update_date, update_by").Update(&models.HospitalProductPrice{
				Price:      int(decimal.NewFromFloat32(price * 100).Round(0).IntPart()),
				UpdateDate: time.Now().Local(),
				UpdateBy:   userInfo.UserNo,
			})
			if err != nil {
				glog.Errorf("HospitalProductPriceImport 第%d条 互联网医院商品价格导入异常2:%+v,请求参数:%d", i+1, err, skuid)
			}
		} else {
			_, err := Engine.Insert(&models.HospitalProductPrice{
				SkuId:      skuid,
				Price:      int(decimal.NewFromFloat32(price * 100).Round(0).IntPart()),
				CreateBy:   userInfo.UserNo,
				CreateDate: time.Now().Local(),
				UpdateDate: time.Now().Local(),
			})
			if err != nil {
				glog.Errorf("HospitalProductPriceImport 第%d条 互联网医院商品价格导入异常3:%+v,请求参数:%d", i+1, err, skuid)
			}
		}
	}

	resp.Code = http.StatusOK
	return resp, nil
}

//互联网医院商品价格列表
func (c *Product) HospitalProductPriceList(ctx context.Context, req *pc.HospitalProductPriceListReq) (*pc.HospitalProductPriceListRes, error) {
	out := &pc.HospitalProductPriceListRes{Code: http.StatusBadRequest}

	Engine := NewDbConn()
	session := Engine.Table("hospital_product_price").Where("1 = 1")
	if req.Skuid != "" {
		session.Where("sku_id=?", req.Skuid)
	}

	countSession := *session
	count, _ := countSession.Count()
	out.Total = int32(count)

	err := session.Select("id, sku_id, price, create_date, update_date, create_name as create_by, update_name as update_by").
		Limit(int(req.PageSize), int((req.PageIndex-1)*req.PageSize)).OrderBy("update_date DESC, id DESC").Find(&out.Data)
	if err != nil {
		glog.Error("HospitalProductPriceList失败, ", err)
		out.Message = err.Error()
		return out, err
	}

	out.Code = http.StatusOK
	return out, nil
}

//互联网医院商品价格移除
func (c *Product) HospitalProductPriceDel(ctx context.Context, req *pc.IdRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: http.StatusBadRequest}

	Engine := NewDbConn()
	//查询详情
	model := []*models.HospitalProductPrice{}
	Engine.Table("hospital_product_price").In("id", strings.Split(req.Id, ",")).Find(&model)

	_, err := Engine.Table("hospital_product_price").In("id", strings.Split(req.Id, ",")).Delete(&models.HospitalProductPrice{})
	if err != nil {
		glog.Error("HospitalProductPriceDel失败, ", err)
		return out, err
	}
	//增加一条操作记录
	if len(model) > 0 {
		data := []*models.OperateRecord{}
		for _, v := range model {
			tmp := &models.OperateRecord{
				OperateType:   1,
				OperateDetail: "移除商品skuid: " + cast.ToString(v.SkuId),
				CreateId:      req.CreateId,
				CreateName:    req.CreateName,
				CreateIp:      req.IpAddr,
				IpLocation:    req.IpLocation,
				CreateTime:    time.Now().Local(),
			}
			data = append(data, tmp)
		}
		_, err := Engine.Table("operate_record").Insert(&data)
		if err != nil {
			glog.Error("移除互联网商品价格插入操作记录失败： ", err)
			return out, err
		}
	}

	out.Code = http.StatusOK
	return out, nil
}

//互联网医院商品价格编辑
func (c *Product) HospitalProductPriceEdit(ctx context.Context, req *pc.HospitalProductPriceEditReq) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: http.StatusBadRequest}

	if req.Id <= 0 {
		return out, errors.New("参数错误！")
	}

	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return nil, errors.New("用户不存在")
	}

	Engine := NewDbConn()
	//查询详情
	model := models.HospitalProductPrice{}
	has, _ := Engine.Table("hospital_product_price").ID(req.Id).Get(&model)
	if !has {
		return nil, errors.New("ID不存在")
	}

	productPrice := &models.HospitalProductPrice{
		Price:      cast.ToInt(req.Price),
		UpdateDate: time.Now().Local(),
		UpdateBy:   userInfo.UserNo,
		UpdateName: userInfo.UserName,
	}
	_, err := Engine.Table("hospital_product_price").Cols("price, update_date, update_by, update_name").ID(req.Id).Update(productPrice)

	if err != nil {
		glog.Error("HospitalProductPriceEdit失败, ", err)
		return out, err
	}

	//增加一条操作记录
	detail := "修改商品skuid: " + cast.ToString(model.SkuId) + "的价格，从" + cast.ToString(kit.FenToYuan(int32(model.Price))) + "元修改为" + cast.ToString(kit.FenToYuan(int32(req.Price))) + "元"
	data := models.OperateRecord{
		OperateType:   2,
		OperateDetail: detail,
		CreateId:      req.CreateId,
		CreateName:    req.CreateName,
		CreateIp:      req.IpAddr,
		IpLocation:    req.IpLocation,
		CreateTime:    time.Now().Local(),
	}

	_, err = Engine.Table("operate_record").Insert(&data)
	if err != nil {
		glog.Error("编辑互联网商品价格插入操作记录失败： ", err)
		return out, err
	}

	out.Code = http.StatusOK
	return out, nil
}

//操作记录
func (c *Product) OperateRecord(ctx context.Context, req *pc.OperateRecordReq) (*pc.OperateRecordRes, error) {
	out := &pc.OperateRecordRes{}

	Engine := NewDbConn()
	session := Engine.Table("operate_record").Where("1 = 1")
	if req.Type == 1 {
		session.Where("create_id=?", req.CreateId)
	}

	if req.TaskContent == 35 {
		session.In("operate_type", []int32{1, 2})
	} else if req.TaskContent == 37 {
		session.Where("operate_type=?", 3)
	}

	countSession := *session
	count, _ := countSession.Count()
	out.Total = int32(count)

	err := session.Select("id, operate_type, operate_detail, create_name, create_ip, ip_location, create_time").
		Limit(int(req.PageSize), int((req.PageIndex-1)*req.PageSize)).OrderBy("create_time DESC").Find(&out.Data)
	if err != nil {
		out.Msg = err.Error()
		return out, err
	}

	return out, nil
}
