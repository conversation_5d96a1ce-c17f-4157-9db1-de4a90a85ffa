package services

import (
	"_/models"
	"_/proto/pc"
	"context"
	"fmt"
	"reflect"
	"testing"
)

func Test_GetFinanceCodeWarehouseRelation(t *testing.T) {
	engine = NewDbConn()
	// engine.ShowSQL(true)
	type args struct {
		id          int32
		stock       int32
		financeCode string
	}
	tests := []struct {
		name    string
		args    args
		want    int
		wantErr bool
	}{
		{
			name: "库存通知",
			args: args{
				id:          1,
				stock:       0,
				financeCode: "#11",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			product := new(Product)
			vo := pc.GetFinanceCodeWarehouseRelationVO{
				FinanceCode: "CX0013",
				ChannelId:   1,
			}

			got, err := product.GetFinanceCodeWarehouseRelation(context.Background(), &vo)

			fmt.Println(got, err)

		})
	}
}

func TestGetChannelWarehouseByCategory(t *testing.T) {
	type args struct {
		channelId int32
		category  int32
	}
	tests := []struct {
		name    string
		args    args
		wantCws []models.ChannelWarehouse
		wantErr bool
	}{
		{
			name: "",
			args: args{
				channelId: 1,
				category:  1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotCws, err := GetChannelWarehouseByCategory(tt.args.channelId, tt.args.category)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelWarehouseByCategory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotCws, tt.wantCws) {
				t.Errorf("GetChannelWarehouseByCategory() gotCws = %v, want %v", gotCws, tt.wantCws)
			}
		})
	}
}

func TestProduct_GetChannelWarehouses(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		finance_code []string
		channel_id   int32
		args         []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantCws []models.ChannelWarehouse
		wantErr bool
	}{
		{
			args: args{
				channel_id: 10, finance_code: []string{"SW0008", "RP0492"}, args: []interface{}{[]int32{4, 5}, 1},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotCws, err := c.GetChannelWarehouses(tt.args.finance_code, tt.args.channel_id, tt.args.args...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelWarehouses() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotCws, tt.wantCws) {
				t.Errorf("GetChannelWarehouses() gotCws = %v, want %v", gotCws, tt.wantCws)
			}
		})
	}
}
