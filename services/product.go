package services

import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/dc"
	"_/proto/es"
	"_/proto/et"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"mime/multipart"
	"net/http"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/panjf2000/ants"

	"github.com/go-redis/redis"

	"github.com/go-xorm/xorm"
	"github.com/golang/protobuf/ptypes/empty"
	structpb "github.com/golang/protobuf/ptypes/struct"
	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/google/uuid"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"github.com/tricobbler/mqgo"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
)

type Product struct {
	categoryNames []string
	oldSnap       string
}

var (
	UpLok              = new(sync.Mutex)
	UpMoveErr          = new(sync.Mutex)
	MoveMtProductAnt   *ants.Pool
	MovePEleProductAnt *ants.Pool
)

func init() {
	MoveMtProductAnt, _ = ants.NewPool(10)
	MovePEleProductAnt, _ = ants.NewPool(10)
	//defer MoveMTProductAnt.Release()
	//defer MovePELEroductAnt.Release()
}

// 根据product_id查询第三方分类id
func (c *Product) GetThirdIdByPid(ctx context.Context, in *pc.GetThirdIdByPidRequest) (*pc.GetThirdIdByPidResponse, error) {
	out := &pc.GetThirdIdByPidResponse{}
	var thirdid string
	has, err := NewDbConn().SQL("select category_id from channel_category_thirdid where id = " + in.CategoryId + " and channel_id = " + in.ChannelId).Get(&thirdid)
	if err != nil {
		glog.Error(err)
		return nil, err
	}
	if !has {
		out.Code = 400
		out.Message = "找不到分类信息"
	} else {
		out.Code = 200
		out.ThirdId = thirdid
	}
	return out, nil
}

// 京东gRPC上架方法
func (c *Product) BatchOnTheShelfToJddj(ctx context.Context, in *pc.BatchOnTheShelfToJddjRequest) (*pc.BatchOnTheShelfToJddjResponse, error) {
	glog.Info("BatchOnTheShelfToJddj请求参数=", in)
	out := new(pc.BatchOnTheShelfToJddjResponse)
	out.Code = 200
	session := NewDbConn().NewSession()
	defer session.Close()
	session.Begin()
	result_bool, _, _, err := c.BatchOnTheShelf(in.FinanceCode, cast.ToInt32(in.ProductId), in.ChannelId, in.UpDownState, session, ctx)
	if !result_bool {
		session.Rollback()
		out.Code = 400
		if err != nil {
			out.Message = err.Error()
			out.Error = err.Error()
		}
		return out, nil
	}
	err = session.Commit()
	if err != nil {
		session.Rollback()
		out.Code = 400
		out.Message = "提交事务失败"
		out.Error = err.Error()
		return out, nil
	}
	return out, nil
}

type uploadResult struct {
	Url string `json:"url"`
	Err string `json:"error"`
}

// 新增/编辑规格
func (c *Product) NewSpec(ctx context.Context, in *pc.Spec) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200

	Engine := NewDbConn()
	model := models.Spec{
		Name:   in.Name,
		Sort:   in.Sort,
		HasPic: in.HasPic,
	}

	if in.Id <= 0 {
		reg := regexp.MustCompile("\u3000|\\s+")
		result, _ := Engine.Table("spec").Where("replace(replace(`name`, '　', ''), ' ', '') = ?", reg.ReplaceAllString(in.Name, "")).Count()
		if result > 0 {
			out.Code = 400
			out.Message = "规格已存在！"
			return out, nil
		}

		if _, err := Engine.Insert(&model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
	} else {
		reg := regexp.MustCompile("\u3000|\\s+")
		result, _ := Engine.Table("spec").Where("replace(replace(`name`, '　', ''), ' ', '') = ?", reg.ReplaceAllString(in.Name, "")).And("id <> ?", in.Id).Count()
		if result > 0 {
			out.Code = 400
			out.Message = "规格已存在！"
			return out, nil
		}

		if _, err := Engine.Id(in.Id).Cols("name", "sort", "has_pic", "category_id").Update(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
		model.Id = in.Id
	}

	//推送到MQ
	bt, _ := json.Marshal(model)
	m := mqgo.SyncMqInfo{
		Exchange: exchange,
		Queue:    queueSpec,
		RouteKey: queueSpec,
		Request:  string(bt),
	}
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
	}
	return out, nil
}

// 删除规格（支持批量删除，用逗号分隔）
func (c *Product) DelSpec(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	result, _ := Engine.Table("spec").In("id", ids).Where("has_delete = 0").Count()
	if result > 0 {
		out.Code = 400
		out.Message = "该规格不允许删除！"
		return out, nil
	}

	result, _ = Engine.Table("sku_value").In("spec_id", ids).Count()
	if result > 0 {
		out.Code = 400
		out.Message = "规格已被商品关联，不允许删除！"
		return out, nil
	}

	result, _ = Engine.Table("spec_value").In("spec_id", ids).Count()
	if result > 0 {
		out.Code = 400
		out.Message = "规格下存在规格值，不允许删除！"
		return out, nil
	}

	result, _ = Engine.Table("type_spec").In("spec_id", ids).Count()
	if result > 0 {
		out.Code = 400
		out.Message = "规格已关联类型，不允许删除！"
		return out, nil
	}

	model := new(models.Spec)
	if _, err := Engine.Table("spec").In("id", ids).Delete(model); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 根据ID查询规格，支持多ID逗号分隔
func (c *Product) QuerySpecSingle(ctx context.Context, in *pc.IdRequest) (*pc.SpecResponse, error) {
	out := new(pc.SpecResponse)
	out.Code = 400

	session := NewDbConn().NewSession()
	defer session.Close()

	if in.Id != "" {
		checkHas := make(map[string]struct{})
		var arrID []int
		for _, v := range strings.Split(in.Id, ",") {
			if _, ok := checkHas[v]; !ok {
				i, _ := strconv.Atoi(v)
				arrID = append(arrID, i)
				checkHas[v] = struct{}{}
			}
		}
		session.In("id", arrID)
	}
	if err := session.Table("spec").
		Select("`id`, `name`, `sort`, `has_pic`, `category_id`").
		Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 根据ID查询规格，支持多ID逗号分隔,返回map
func (c *Product) QuerySpecMap(ctx context.Context, in *pc.IdRequest) (*pc.SpecResponse, error) {
	out := new(pc.SpecResponse)
	out.Code = 400

	if res, err := c.QuerySpecSingle(ctx, in); err != nil {
		glog.Error(err)
		return out, err
	} else {
		out.Spec = make(map[int32]*pc.Spec)
		for _, v := range res.Details {
			out.Spec[v.Id] = v
		}
		out.Code = 200
		return out, nil
	}

}

// 查询规格列表
func (c *Product) QuerySpec(ctx context.Context, in *pc.SpecRequest) (*pc.SpecResponse, error) {
	out := new(pc.SpecResponse)
	out.Code = 400
	Engine := NewDbConn()
	session := Engine.Table("spec").Where("1=1")
	if in.Where.Id > 0 {
		session.And("`id` = ?", in.Where.Id)
	}
	if in.Where.Name != "" {
		session.And("`name` like ?", "%"+in.Where.Name+"%")
	}

	countSession := *session

	count, _ := countSession.Count()
	out.TotalCount = int32(count)

	if err := session.Select("`id`, `name`, `sort`, `has_pic`").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("sort ASC").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 新增/编辑规格值
func (c *Product) NewSpecValue(ctx context.Context, in *pc.SpecValue) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200

	model := models.SpecValue{
		Value:  in.Value,
		SpecId: in.SpecId,
	}

	if in.SpecId <= 0 {
		out.Message = "规格值ID不允许为空！"
		out.Code = 400
		return out, nil
	}

	Engine := NewDbConn()
	if in.Id <= 0 {
		if _, err := Engine.Insert(&model); err != nil {
			glog.Error(err)
			out.Code = 400
		} else {
			out.Message = strconv.Itoa(int(model.Id))
		}
	} else {
		if _, err := Engine.Id(in.Id).Update(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
		model.Id = in.Id
	}

	//推送到MQ
	bt, _ := json.Marshal(model)
	m := mqgo.SyncMqInfo{
		Exchange: exchange,
		Queue:    queueSpecValue,
		RouteKey: queueSpecValue,
		Request:  string(bt),
	}
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
	}
	return out, nil
}

// 删除规格值（支持批量删除，用逗号分隔）
func (c *Product) DelSpecValue(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	result, _ := Engine.Table("sku_value").Where("spec_value_id IN(?)", strings.Join(ids, "','")).Count()
	if result > 0 {
		out.Code = 400
		out.Message = "规格值已被商品关联，不允许删除！"
		return out, nil
	}

	model := new(models.SpecValue)
	if _, err := Engine.Table("spec_value").In("id", ids).Delete(model); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 查询规格值列表
func (c *Product) QuerySpecValue(ctx context.Context, in *pc.IdRequest) (*pc.SpecValueResponse, error) {
	out := new(pc.SpecValueResponse)
	out.Code = 400

	session := NewDbConn().NewSession()
	defer session.Close()

	if in.Id != "" {
		checkHas := make(map[string]struct{})
		var arrID []int
		for _, v := range strings.Split(in.Id, ",") {
			if _, ok := checkHas[v]; !ok {
				i, _ := strconv.Atoi(v)
				arrID = append(arrID, i)
				checkHas[v] = struct{}{}
			}
		}
		session.In("id", arrID)
	}

	if err := session.Table("spec_value").Select("`id`, `spec_id`, `value`").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 根据ID查询规格值，支持多ID逗号分隔,返回map
func (c *Product) QuerySpecValueMap(ctx context.Context, in *pc.IdRequest) (*pc.SpecValueResponse, error) {
	out := new(pc.SpecValueResponse)
	out.Code = 400

	if res, err := c.QuerySpecValue(ctx, in); err != nil {
		glog.Error(err)
		return out, err
	} else {
		out.SpecValue = make(map[int32]*pc.SpecValue)
		for _, v := range res.Details {
			out.SpecValue[v.Id] = v
		}
		out.Code = 200
		return out, nil
	}
}

// 新增/编辑属性
func (c *Product) NewAttr(ctx context.Context, in *pc.Attr) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200

	model := models.Attr{
		Name: in.Name,
		Sort: in.Sort,
	}

	Engine := NewDbConn()
	if in.Id <= 0 {
		if _, err := Engine.Insert(&model); err != nil {
			glog.Error(err)
			out.Code = 400
		} else {
			out.Message = strconv.Itoa(int(model.Id))
		}
	} else {
		if _, err := Engine.Id(in.Id).Cols("name", "sort").Update(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
		model.Id = in.Id
	}

	//推送到MQ
	bt, _ := json.Marshal(model)
	m := mqgo.SyncMqInfo{
		Exchange: exchange,
		Queue:    queueAttr,
		RouteKey: queueAttr,
		Request:  string(bt),
	}
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
	}
	return out, nil
}

// 删除属性（支持批量删除，用逗号分隔）
func (c *Product) DelAttr(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	result, _ := Engine.Table("product_attr").Where("attr_id IN(?)", strings.Join(ids, "','")).Count()
	if result > 0 {
		out.Code = 400
		out.Message = "属性已被商品关联，不允许删除！"
		return out, nil
	}

	result, _ = Engine.Table("attr_value").In("attr_id", ids).Count()
	if result > 0 {
		out.Code = 400
		out.Message = "属性下存在属性值，不允许删除！"
		return out, nil
	}

	model := new(models.Attr)
	if _, err := Engine.Table("attr").In("id", ids).Delete(model); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 查询商品属性，多个ID逗号分隔
func (c *Product) QueryAttr(ctx context.Context, in *pc.IdRequest) (*pc.AttrResponse, error) {
	out := new(pc.AttrResponse)
	out.Code = 400

	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	if err := Engine.Table("attr").Select("`id`, `name`, `sort`, `is_show`").Where("id IN('" + strings.Join(ids, "','") + "')").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 新增/编辑属性值
func (c *Product) NewAttrValue(ctx context.Context, in *pc.AttrValue) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200

	model := models.AttrValue{
		Value:  in.Value,
		AttrId: in.AttrId,
	}

	if in.AttrId <= 0 {
		out.Message = "属性ID不允许为空！"
		out.Code = 400
		return out, nil
	}

	Engine := NewDbConn()
	if in.Id <= 0 {
		if _, err := Engine.Insert(&model); err != nil {
			glog.Error(err)
			out.Code = 400
		} else {
			out.Message = strconv.Itoa(int(model.Id))
		}
	} else {
		if _, err := Engine.Id(in.Id).Update(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
		model.Id = in.Id
	}

	//推送到MQ
	bt, _ := json.Marshal(model)
	m := mqgo.SyncMqInfo{
		Exchange: exchange,
		Queue:    queueAttrValue,
		RouteKey: queueAttrValue,
		Request:  string(bt),
	}
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
	}
	return out, nil
}

// 删除属性值（支持批量删除，用逗号分隔）
func (c *Product) DelAttrValue(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	result, _ := Engine.Table("product_attr").Where("attr_value_id IN(?)", strings.Join(ids, "','")).Count()
	if result > 0 {
		out.Code = 400
		out.Message = "属性值已被商品关联，不允许删除！"
		return out, nil
	}

	model := new(models.AttrValue)
	if _, err := Engine.Table("attr_value").In("id", ids).Delete(model); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 查询属性值列表
func (c *Product) QueryAttrValue(ctx context.Context, in *pc.IdRequest) (*pc.AttrValueResponse, error) {
	out := new(pc.AttrValueResponse)
	out.Code = 400
	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	if err := Engine.Table("attr_value").Select("`id`, `value`, `attr_id`").Where("id IN('" + strings.Join(ids, "','") + "')").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 新增/编辑类型
func (p *Product) NewType(ctx context.Context, in *pc.Type) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200

	model := models.Type{
		Name: in.Name,
		Sort: in.Sort,
	}

	Engine := NewDbConn()
	session := Engine.NewSession()
	defer session.Close()
	session.Begin()

	if in.Id <= 0 {
		if _, err := session.Insert(&model); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Code = 400
			return out, nil
		}
		in.Id = int32(model.Id)
		//规格
		if len(in.Spec) > 0 {
			modelSpec := make([]*models.TypeSpec, len(in.Spec))
			for k, v := range in.Spec {
				modelSpec[k] = new(models.TypeSpec)
				modelSpec[k].TypeId = model.Id
				modelSpec[k].SpecId = v.SpecId
				in.Spec[k].TypeId = int32(model.Id)
			}
			if _, err := session.Insert(modelSpec); err != nil {
				glog.Error(err)
				session.Rollback()
				out.Code = 400
				return out, nil
			}

		}
		//品牌
		if len(in.Brand) > 0 {
			modelBrand := make([]*models.TypeBrand, len(in.Brand))
			for k, v := range in.Brand {
				modelBrand[k] = new(models.TypeBrand)
				modelBrand[k].TypeId = model.Id
				modelBrand[k].BrandId = v.BrandId
				in.Brand[k].TypeId = int32(model.Id)
			}
			if _, err := session.Insert(modelBrand); err != nil {
				glog.Error(err)
				session.Rollback()
				out.Code = 400
				return out, nil
			}
		}
		//属性
		if len(in.Attr) > 0 {
			modelTypeAttr := make([]*models.TypeAttr, len(in.Attr))
			for k, v := range in.Attr {
				//写入属性表attr
				modelAttr := models.Attr{
					Name: v.Name,
					//modelAttr[k].Value = v.Value
					Sort:   v.Sort,
					IsShow: v.IsShow,
				}

				if _, err := session.Insert(&modelAttr); err != nil {
					glog.Error(err)
					session.Rollback()
					out.Code = 400
					return out, nil
				}
				in.Attr[k].TypeId = int32(model.Id)
				in.Attr[k].AttrId = int32(modelAttr.Id)
				modelTypeAttr[k] = new(models.TypeAttr)
				modelTypeAttr[k].TypeId = model.Id
				modelTypeAttr[k].AttrId = modelAttr.Id
				for key, val := range v.Value {
					modelAttrValue := models.AttrValue{
						AttrId: modelAttr.Id,
						Value:  val.Value,
					}

					//写入属性表attr_value
					if _, err := session.Insert(&modelAttrValue); err != nil {
						glog.Error(err)
						session.Rollback()
						out.Code = 400
						return out, nil
					}
					in.Attr[k].Value[key].AttrId = int32(modelAttr.Id)
					in.Attr[k].Value[key].Id = int32(modelAttrValue.Id)
				}
			}
			//写入type_attr
			if _, err := session.Insert(modelTypeAttr); err != nil {
				glog.Error(err)
				session.Rollback()
				out.Code = 400
				return out, nil
			}

		}

	} else {
		if _, err := session.Id(in.Id).Cols("name", "sort").Update(model); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Code = 400
			return out, nil
		}
		//对应分类的规格
		if _, err := session.Exec("DELETE FROM type_spec WHERE type_id = ?", in.Id); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Code = 400
			return out, err
		}
		//对应分类的品牌
		if _, err := session.Exec("DELETE FROM type_brand WHERE type_id = ?", in.Id); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Code = 400
			return out, err
		}
		//对应分类的属性值
		if _, err := session.Exec("DELETE FROM attr_value WHERE attr_id in(SELECT attr_id FROM `type_attr` WHERE type_id = ?)", in.Id); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Code = 400
			return out, err
		}
		//对应分类的属性
		if _, err := session.Exec("DELETE FROM type_attr WHERE type_id = ?", in.Id); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Code = 400
			return out, err
		}
		//规格
		if len(in.Spec) > 0 {
			modelSpec := make([]*models.TypeSpec, len(in.Spec))
			for k, v := range in.Spec {
				modelSpec[k] = new(models.TypeSpec)
				modelSpec[k].TypeId = in.Id
				modelSpec[k].SpecId = v.SpecId
			}
			if _, err := session.Insert(modelSpec); err != nil {
				glog.Error(err)
				session.Rollback()
				out.Code = 400
				return out, nil
			}
		}
		//品牌
		if len(in.Brand) > 0 {
			modelBrand := make([]*models.TypeBrand, len(in.Brand))
			for k, v := range in.Brand {
				modelBrand[k] = new(models.TypeBrand)
				modelBrand[k].TypeId = in.Id
				modelBrand[k].BrandId = v.BrandId
			}
			if _, err := session.Insert(modelBrand); err != nil {
				glog.Error(err)
				session.Rollback()
				out.Code = 400
				return out, nil
			}
		}
		//属性
		if len(in.Attr) > 0 {
			modelTypeAttr := make([]*models.TypeAttr, len(in.Attr))
			for k, v := range in.Attr {
				var attrId int32
				modelTypeAttr[k] = new(models.TypeAttr)
				modelTypeAttr[k].TypeId = in.Id
				//modelAttr[k].Value = v.Value
				//写入属性表attr
				modelAttr := models.Attr{
					Name:   v.Name,
					Sort:   v.Sort,
					IsShow: v.IsShow,
				}

				if v.AttrId > 0 {
					attrId = v.AttrId
					if _, err := session.Id(v.AttrId).Update(modelAttr); err != nil {
						glog.Error(err)
						session.Rollback()
						out.Code = 400
						return out, nil
					}
				} else {
					if _, err := session.Insert(&modelAttr); err != nil {
						glog.Error(err)
						session.Rollback()
						out.Code = 400
						return out, nil
					}
					attrId = modelAttr.Id
					in.Attr[k].AttrId = int32(modelAttr.Id)
				}

				//写入属性值表attr_value
				for key, val := range v.Value {
					modelAttrValue := models.AttrValue{
						AttrId: attrId,
						Value:  val.Value,
					}

					if val.Id > 0 {
						if _, err := session.Id(val.Id).Update(modelAttrValue); err != nil {
							glog.Error(err)
							session.Rollback()
							out.Code = 400
							return out, nil
						}
					} else {
						if _, err := session.Insert(&modelAttrValue); err != nil {
							glog.Error(err)
							session.Rollback()
							out.Code = 400
							return out, nil
						}
					}
					in.Attr[k].Value[key].Id = int32(modelAttrValue.Id)
					in.Attr[k].Value[key].AttrId = int32(attrId)
				}
				modelTypeAttr[k].AttrId = attrId
			}
			if _, err := session.Insert(modelTypeAttr); err != nil {
				glog.Error(err)
				session.Rollback()
				out.Code = 400
				return out, nil
			}

		}
	}
	session.Commit()

	//推送到MQ
	bt, _ := json.Marshal(in)
	m := mqgo.SyncMqInfo{
		Exchange: exchange,
		Queue:    queueType,
		RouteKey: queueType,
		Request:  string(bt),
	}
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
	}
	return out, nil
}

// 删除类型（支持批量删除，用逗号分隔）
func (p *Product) DelType(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	result, _ := Engine.Table("category").In("type_id", ids).Count()
	if result > 0 {
		out.Code = 400
		out.Message = "类型已被分类关联，不允许删除！"
		return out, nil
	}

	session := Engine.NewSession()
	defer session.Close()
	session.Begin()
	model := new(models.Type)
	if _, err := session.Table("type").In("id", ids).Delete(model); err != nil {
		glog.Error(err)
		out.Code = 400
		session.Rollback()
		return out, err
	}
	//对应分类的规格
	modelspec := new(models.TypeSpec)
	if _, err := session.Table("type_spec").In("type_id", ids).Delete(modelspec); err != nil {
		glog.Error(err)
		out.Code = 400
		session.Rollback()
		return out, err
	}
	//对应分类的品牌
	modelbrand := new(models.TypeBrand)
	if _, err := session.Table("type_brand").In("type_id", ids).Delete(modelbrand); err != nil {
		glog.Error(err)
		out.Code = 400
		session.Rollback()
		return out, err
	}
	//对应分类的属性
	modelattr := new(models.TypeAttr)
	if _, err := session.Table("type_attr").In("type_id", ids).Delete(modelattr); err != nil {
		glog.Error(err)
		out.Code = 400
		session.Rollback()
		return out, err
	}
	out.Code = 200
	session.Commit()
	return out, nil
}

// 查询类型列表，可按分类查询
func (p *Product) QueryType(ctx context.Context, in *pc.TypeRequest) (*pc.TypeResponse, error) {
	glog.Info("QueryType : ", kit.JsonEncode(in))
	out := new(pc.TypeResponse)
	out.Code = 400

	Engine := NewDbConn()
	session := Engine.Table("type").Where("1=1")
	if in.Where.Id != 0 {
		session.And("`id` = ?", in.Where.Id)
	}
	if in.Where.Name != "" {
		session.And("`name` like ?", "%"+in.Where.Name+"%")
	}
	if in.Where.CategoryId > 0 {
		cate := new(pc.CategoryResponse)
		if err := Engine.Table("category").Select("type_id").Where("id = ?", in.Where.CategoryId).Find(&cate.Details); err != nil {
			glog.Error(err)
			return out, err
		}
		if len(cate.Details) > 0 {
			session.And("`id` = ?", cate.Details[0].TypeId)
		} else {
			out.Code = 200
			return out, nil
		}
	}

	countSession := *session

	count, _ := countSession.Count()
	out.TotalCount = int32(count)

	//if err := session.Select("`id`, `name`, `sort`").OrderBy("sort asc, id desc").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).Find(&out.Details); err != nil {
	//	glog.Error(err)
	//	return out, err
	//}

	if in.Where.CategoryId > 0 {
		if err := session.Select("`id`, `name`, `sort`").OrderBy("sort asc, id desc").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).Find(&out.Details); err != nil {
			glog.Error(err)
			return out, err
		}
	} else { // 否则返回默认类型
		if err := NewDbConn().Select("`id`, `name`, `sort`").Where("id = ?", 1).OrderBy("sort asc, id desc").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).Find(&out.Details); err != nil {
			glog.Error(err)
			return out, err
		}
		out.TotalCount = 1
	}

	for key, value := range out.Details {
		//规格 5.6.3默认返回所有的规格不区分分类

		sessionConn := Engine.Table("type_spec").Select("distinct `spec_id`,`spec`.`name` as `spec_name`").
			Join("LEFT", "spec", "type_spec.spec_id = spec.id").OrderBy("sort asc")

		if in.SpecId > 0 {
			sessionConn.Where("type_spec.spec_id = ? ", in.SpecId)
		}
		sessionConn.Find(&out.Details[key].Spec)
		////规格值
		specConn := Engine.Table("spec_value")

		if len(in.KeyName) > 0 {
			keyName := "%" + in.KeyName + "%"
			specConn.Where("spec_value.value like ?", keyName)
		}
		for k, v := range out.Details[key].Spec {
			specConn.Select("max(id) id, `value`").Where("spec_id = ?", v.SpecId).GroupBy("value").Find(&out.Details[key].Spec[k].SpecValue)
		}
		//品牌
		if in.Where.CategoryId > 0 || in.Where.Id > 0 {
			Engine.Table("type_brand").Select("`brand_id`,`brand`.`name` as `brand_name`").Join("LEFT", "brand", "type_brand.brand_id = brand.id").Where("type_id = ?", value.Id).Find(&out.Details[key].Brand)
			//属性
			Engine.Table("type_attr").Select("`attr_id`,`attr`.`name`,`attr`.`sort`,`attr`.`is_show`").Join("LEFT", "attr", "type_attr.attr_id = attr.id").Where("type_id = ?", value.Id).Find(&out.Details[key].Attr)
			//属性值
			for k, v := range out.Details[key].Attr {
				Engine.Table("attr_value").Select("`id`, `value`").Where("attr_id = ?", v.AttrId).Find(&out.Details[key].Attr[k].Value)
			}
		} else {
			Engine.Table("brand").Select("id `brand_id`,`brand`.`name` as `brand_name`").Find(&out.Details[key].Brand)

			Engine.Table("attr").Select("id `attr_id`,`attr`.`name`,`attr`.`sort`,`attr`.`is_show`").Find(&out.Details[key].Attr)
			for k, v := range out.Details[key].Attr {
				Engine.Table("attr_value").Select("`id`, `value`").Where("attr_id = ?", v.AttrId).Find(&out.Details[key].Attr[k].Value)
			}
		}
	}
	out.Code = 200
	return out, nil
}

func getUuid() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

// 新增品牌
func (c *Product) NewBrand(ctx context.Context, in *pc.Brand) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200

	model := models.Brand{
		Name:            in.Name,
		Sort:            in.Sort,
		Logo:            in.Logo,
		Description:     in.Description,
		IsRecommend:     in.IsRecommend,
		ShowType:        in.ShowType,
		Initial:         in.Initial,
		BrandCategoryId: in.BrandCategoryId,
		CompanyId:       in.CompanyId,
	}

	Engine := NewDbConn()
	if in.Id <= 0 {
		if _, err := Engine.Insert(&model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
	} else {
		if _, err := Engine.Id(in.Id).Cols("name", "sort", "logo", "description", "is_recommend", "show_type", "initial", "company_id", "brand_category_id").Update(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
		model.Id = in.Id
	}

	//推送到MQ
	bt, _ := json.Marshal(model)
	m := mqgo.SyncMqInfo{
		Exchange: exchange,
		Queue:    queueBrand,
		RouteKey: queueBrand,
		Request:  string(bt),
	}
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
	}
	return out, nil
}

// 删除品牌（支持批量删除，用逗号分隔）
func (c *Product) DelBrand(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	result, _ := Engine.Table("type_brand").In("brand_id", ids).Count()
	if result > 0 {
		out.Code = 400
		out.Message = "品牌已关联类型，不允许删除！"
		return out, nil
	}

	model := new(models.Brand)
	if _, err := Engine.Table("brand").In("id", ids).Delete(model); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 品牌列表
func (c *Product) QueryBrand(ctx context.Context, in *pc.BrandRequest) (*pc.BrandResponse, error) {
	out := new(pc.BrandResponse)
	out.Code = 400

	Engine := NewDbConn()
	session := Engine.Table("brand").Where("1=1")
	if in.Where.Id > 0 {
		session.And("`brand`.`id` = ?", in.Where.Id)
	}
	if in.Where.Name != "" {
		session.And("`brand`.`name` like ?", "%"+in.Where.Name+"%")
	}
	if in.Where.Initial != "" {
		session.And("`brand`.`initial` = ?", in.Where.Initial)
	}
	if in.Where.BrandCategoryId > 0 {
		session.And("`brand`.`brand_category_id` = ?", in.Where.BrandCategoryId)
	}

	countSession := *session

	count, _ := countSession.Count()
	out.TotalCount = int32(count)

	if err := session.Select("`brand`.`id`, `brand`.`name`, `brand`.`sort`, `brand`.`logo`, `brand`.`description`, `brand`.`create_date`, `brand`.`is_recommend`, `brand`.`show_type`, `brand`.`initial`, `brand`.`brand_category_id`, `brand`.`company_id`, `category`.`name`").Join("LEFT", "category", "brand.brand_category_id = category.id").OrderBy("sort asc").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 新增/编辑类别
func (c *Product) NewCategory(ctx context.Context, in *pc.Category) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	tag := ""
	var category_list []*models.CategoryList

	if len(in.Name) == 0 {
		out.Code = 400
		out.Message = "名称不能为空"
		return out, nil
	}

	if in.Id > 0 && in.Id == in.ParentId {
		out.Code = 400
		out.Message = "自身不可作父类"
		return out, nil
	}

	if in.Sort < 0 {
		out.Code = 400
		out.Message = "排序值不能小于0"
		return out, nil
	}

	Engine := NewDbConn()
	if in.ParentId > 0 {
		Engine.Table("category").Select("`id`, `name`, `parent_id`").Find(&category_list)
		if len(in.Tag) > 0 {
			tag = in.Tag
		} else {
			tag_nav := GetNavByParentId(int(in.ParentId), ",", category_list)
			if len(tag_nav) > 0 {
				tag = tag_nav + "," + in.Name
			}
		}
	} else {
		in.ParentId = in.CategoryArrId
	}

	model := models.Category{
		Name:            in.Name,
		ParentId:        in.ParentId,
		CategoryArrId:   in.CategoryArrId,
		Sort:            in.Sort,
		IsInvented:      in.IsInvented,
		IsVerify:        in.IsVerify,
		IsNoverify:      in.IsNoverify,
		ProductShowType: in.ProductShowType,
		TypeId:          in.TypeId,
		Tag:             tag,
		Img:             in.Img,
	}
	if in.IsNoverify == 1 {
		model.IsInvented = in.IsNoverify
	}
	if in.Id > 0 {
		var sub_model models.Category
		update_clos := ""
		if in.ConnectType == 1 {
			update_clos = update_clos + "type_id,"
			sub_model.TypeId = in.TypeId
		}
		if in.ConnectInvent == 1 {
			update_clos = update_clos + "is_invented,"
			sub_model.IsInvented = model.IsInvented
		}
		if in.ConnectShowType == 1 {
			update_clos = update_clos + "product_show_type,"
			sub_model.ProductShowType = model.ProductShowType
		}
		if in.ConnectVerify == 1 {
			update_clos = update_clos + "is_verify,"
			sub_model.IsVerify = model.IsVerify
		}
		if in.ConnectNoverify == 1 {
			update_clos = update_clos + "is_noverify,is_invented"
			sub_model.IsNoverify = model.IsNoverify
			if model.IsNoverify == 1 {
				sub_model.IsInvented = model.IsNoverify
			}

		}
		if update_clos != "" {
			//查询所有子分类
			var sublist_model []models.Category
			if err := Engine.SQL("WITH RECURSIVE result(`id`, `name`, `parent_id`, `sort`, `is_invented`, `is_verify`, `is_noverify`, `product_show_type`, `type_id`) AS (SELECT `id`, `name`, `parent_id`, `sort`, `is_invented`, `is_verify`, `is_noverify`, `product_show_type`, `type_id` FROM category WHERE id = ? UNION ALL SELECT i.`id`, i.`name`, i.`parent_id`, i.`sort`, i.`is_invented`, i.`is_verify`, i.`is_noverify`, i.`product_show_type`, i.`type_id` FROM category i JOIN result ON i.parent_id = result.id) SELECT * FROM result", in.Id).Find(&sublist_model); err != nil {
				glog.Error(err)
				out.Code = 400
				return out, nil
			}
			for _, v := range sublist_model {
				if _, err := Engine.Where("id = ?", v.Id).Cols(strings.TrimRight(update_clos, ",")).Update(sub_model); err != nil {
					glog.Error(err)
					out.Code = 400
					return out, nil
				}
				//推送到mq
				modelMQ := models.Category{
					Id:              v.Id,
					Name:            v.Name,
					ParentId:        v.ParentId,
					Sort:            v.Sort,
					IsInvented:      v.IsInvented,
					IsVerify:        v.IsVerify,
					IsNoverify:      v.IsNoverify,
					ProductShowType: v.ProductShowType,
					TypeId:          v.TypeId,
				}
				bt, _ := json.Marshal(modelMQ)
				m := mqgo.SyncMqInfo{
					Exchange: exchange,
					RouteKey: queueCategory,
					Request:  string(bt),
					Queue:    queueCategory,
				}
				if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
					glog.Error(err)
				}
			}

		}

		if _, err := Engine.Id(in.Id).Cols("name,parent_id,category_arr_id,sort,create_date,is_invented,is_verify,is_noverify,product_show_type,img,type_id,tag").Update(model); err != nil {
			glog.Error(err)
			out.Code = 400
			return out, nil
		}
		model.Id = in.Id
	} else {
		if _, err := Engine.Insert(&model); err != nil {
			glog.Error(err)
			out.Code = 400
			return out, nil
		}
	}
	//推送到mq
	bt, _ := json.Marshal(model)
	m := mqgo.SyncMqInfo{
		Exchange: exchange,
		RouteKey: queueCategory,
		Request:  string(bt),
		Queue:    queueCategory,
	}
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
	}
	return out, nil
}

// 因为程序假死导致所有接口都不响应，测试一个不查询数据库的GRPC
func (c *Product) NewTestService(ctx context.Context, in *pc.Category) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	out.Message = "测试不查询数据库"
	return out, nil
}

// 删除类别
func (c *Product) DelCategory(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	if len(in.Id) == 0 {
		out.Code = 400
		out.Message = "ID不能为空"
		return out, nil
	}
	ids := strings.Split(in.Id, ",")
	sub_ids := []string{}
	//删除归属
	Engine := NewDbConn()
	count_arr, err := Engine.Table("category").Where("parent_id = 0 and category_arr_id = 0 and id = " + in.Id).Count()
	if count_arr > 0 {
		session := Engine.NewSession()
		defer session.Close()
		session.Begin()
		if _, err := session.Exec("update category set category_arr_id = 0 where category_arr_id = ?", in.Id); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Code = 400
			return out, err
		}
		if _, err := session.Exec("DELETE FROM category WHERE id = " + in.Id); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Code = 400
			return out, err
		}
		if err := session.Commit(); err != nil {
			session.Rollback()
			out.Code = 400
			return out, err
		}

		return out, err
	}

	//删除分类
	if err := Engine.SQL("WITH RECURSIVE result (`id`,`parent_id`) AS (SELECT`id`,`parent_id` FROM category WHERE id in (" + strings.Join(ids, ",") + ") UNION ALL SELECT i.`id`, i.parent_id FROM category i JOIN result ON result.id = i.parent_id) SELECT id FROM result").Find(&sub_ids); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}

	count, err := Engine.Table("product").Where("category_id in (" + strings.Join(sub_ids, ",") + ")").Count()

	if count > 0 {
		out.Code = 400
		out.Message = "已经关联商品的子类不能删除"
		return out, err
	}

	if _, err := Engine.Exec("DELETE FROM category WHERE id IN(" + strings.Join(sub_ids, ",") + ")"); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 根据ID递归查询分类
func (c *Product) QueryCategoryRecursion(ctx context.Context, in *pc.IdRequest) (*pc.CategoryResponse, error) {
	out := new(pc.CategoryResponse)
	out.Code = 400

	Engine := NewDbConn()
	if err := Engine.SQL("WITH RECURSIVE result(`id`, `name`, `parent_id`, `category_arr_id`, `sort`, `create_date`, `is_invented`, `product_show_type`, `type_id`, `tag`) AS (SELECT `id`, `name`, `parent_id`, `category_arr_id`, `sort`, `create_date`, `is_invented`, `product_show_type`, `type_id`, `tag` FROM category WHERE id = ? UNION ALL SELECT i.`id`,  i.`name`,  i.`parent_id`,  i.`category_arr_id`,  i.`sort`,  i.`create_date`,  i.`is_invented`,  i.`product_show_type`,  i.`type_id`,  i.`tag` FROM category i JOIN result ON result.parent_id = i.id) SELECT * FROM result", in.Id).Find(&out.Details); err != nil {
		glog.Error(err)
	}

	out.Code = 200
	return out, nil
}

// 根据ID递归查询分类（渠道）
func (c *Product) QueryChannelCategoryRecursion(ctx context.Context, in *pc.IdRequest) (*pc.CategoryResponse, error) {
	out := new(pc.CategoryResponse)
	out.Code = 400

	Engine := NewDbConn()
	if err := Engine.SQL("WITH RECURSIVE result(`id`, `channel_id`, `name`, `parent_id`, `sort`, `create_date`) AS (SELECT `id`, `channel_id`, `name`, `parent_id`, `sort`, `create_date` FROM channel_category WHERE id = ? UNION ALL SELECT i.`id`, i.`channel_id`, i.`name`, i.`parent_id`, i.`sort`, i.`create_date` FROM channel_category i JOIN result ON result.parent_id = i.id) SELECT * FROM result", in.Id).Find(&out.Details); err != nil {
		glog.Error(err)
	}

	out.Code = 200
	return out, nil
}

// 查询类别
func (c *Product) QueryCategory(ctx context.Context, in *pc.CategoryRequest) (*pc.CategoryResponse, error) {
	out := new(pc.CategoryResponse)
	var category_list []*models.CategoryList

	Engine := NewDbConn()
	Engine.Table("category").Select("`id`, `name`, `parent_id`").Find(&category_list)

	out.Code = 400
	session := Engine.Table("category").Alias("a").Join("LEFT", "type as b", "a.type_id = b.id").Where("1=1")
	if in.Where.Name != "" {
		session.And("a.name like ?", "%"+in.Where.Name+"%")
	}

	if in.DataType == "category_arr" {
		session.And("a.parent_id = 0")
	} else if in.DataType == "category_sub" {
		//session.And("a.parent_id <> '0'")
		if in.Where.ParentId == -1 {
			parentids := ""
			if len(category_list) > 0 {
				for _, v := range category_list {
					if v.ParentId == 0 {
						parentids = parentids + strconv.Itoa(v.Id) + ","
					}
				}
			}
			if parentids != "" {
				parent_arr := strings.Split(strings.TrimRight(parentids, ","), ",")
				session.And("a.parent_id in (" + strings.Join(parent_arr, ",") + ")")
			}
		}
	} else if in.DataType == "tag" {
		session.And("a.tag <> ''")
	}

	if in.Where.ParentId >= 0 {
		session.And("a.parent_id = ?", in.Where.ParentId)
	}

	countSession := *session

	count, _ := countSession.Count()
	out.TotalCount = int32(count)

	if err := session.Select("a.`id`, a.`name`, a.`parent_id`, a.`category_arr_id`, a.`sort`, a.`create_date`, a.`is_invented`, a.`is_noverify`, a.`product_show_type`, a.`type_id`, a.`tag`, b.`name` as type_name, a.`img`").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("sort asc").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	for index := range out.Details {
		if out.Details[index].IsNoverify == 1 {
			out.Details[index].IsInvented = 0
		}
		out.Details[index].TagName = GetNavByParentId(int(out.Details[index].ParentId), ">", category_list) + ">" + out.Details[index].Name
		out.Details[index].HasSub = HasSubMenu(int(out.Details[index].Id), category_list)
	}
	return out, nil
}

// 新增/编辑ERP
func (c *Product) NewErp(ctx context.Context, in *pc.Erp) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200

	model := models.Erp{
		Name:  in.Name,
		IsUse: in.IsUse,
	}

	Engine := NewDbConn()
	if in.Id > 0 {
		if _, err := Engine.Id(in.Id).Cols("name,is_use").Update(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
	} else {
		if _, err := Engine.Insert(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
	}

	return out, nil
}

// 删除ERP
func (c *Product) DelErp(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	if len(in.Id) == 0 {
		out.Code = 400
		out.Message = "ID不能为空"
		return out, nil
	}
	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	//判断是否已有关联
	count, err := Engine.Table("sku_third").Where("erp_id in (" + strings.Join(ids, ",") + ")").Count()

	if count > 0 {
		out.Code = 400
		out.Message = "已经关联商品的ERP货号不能删除"
		return out, err
	}

	if _, err := Engine.Exec("DELETE FROM erp WHERE id IN(" + strings.Join(ids, ",") + ")"); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	return out, nil
}

// 停用/启用ERP
func (c *Product) ActiveErp(ctx context.Context, in *pc.ActiveErpRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	is_use := "0"
	if in.Status == "enabled" {
		is_use = "1"
	}

	Engine := NewDbConn()
	ids := strings.Split(in.Id, ",")
	if _, err := Engine.Exec("UPDATE erp SET is_use = " + is_use + " WHERE id IN('" + strings.Join(ids, "','") + "')"); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 查询ERP
func (c *Product) QueryErp(ctx context.Context, in *pc.ErpRequest) (*pc.ErpResponse, error) {
	out := new(pc.ErpResponse)
	out.Code = 400

	Engine := NewDbConn()
	session := Engine.Table("erp").Where("1=1")
	if in.Where.Name != "" {
		session.And("name like ?", "%"+in.Where.Name+"%")
	}

	countSession := *session

	count, _ := countSession.Count()
	out.TotalCount = int32(count)

	if err := session.Select("`id`, `name`, `is_use`").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 新增/编辑Tag
func (c *Product) NewTag(ctx context.Context, in *pc.Tag) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	model := models.Tag{
		Name:   in.Name,
		Sort:   in.Sort,
		Type:   in.Type,
		IsShow: in.IsShow,
	}

	Engine := NewDbConn()
	if in.Id > 0 {
		if _, err := Engine.Id(in.Id).Cols("name,value,sort,type,is_show").Update(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
		model.Id = in.Id
	} else {
		if _, err := Engine.Insert(&model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
	}

	//推送到mq
	bt, _ := json.Marshal(model)
	m := mqgo.SyncMqInfo{
		Exchange: exchange,
		Queue:    queueTag,
		RouteKey: queueTag,
		Request:  string(bt),
	}
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
	}

	return out, nil
}

// 删除Tag
func (c *Product) DelTag(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	if len(in.Id) == 0 {
		out.Code = 400
		out.Message = "ID不能为空"
		return out, nil
	}
	ids := strings.Split(in.Id, ",")
	Engine := NewDbConn()
	if _, err := Engine.Exec("DELETE FROM tag WHERE id IN(" + strings.Join(ids, ",") + ")"); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	return out, nil
}

// 查询Tag
func (c *Product) QueryTag(ctx context.Context, in *pc.TagRequest) (*pc.TagResponse, error) {
	out := new(pc.TagResponse)
	out.Code = 400

	Engine := NewDbConn()
	session := Engine.Table("tag").Where("1=1")
	if in.Where.Name != "" {
		session.And("`tag`.`" + in.TypeName + "` like '%" + in.Where.Name + "%'")
	}

	countSession := *session
	count, _ := countSession.Count()
	out.TotalCount = int32(count)

	if err := session.Select("`tag`.`id`, `tag`.`name`, `tag`.`sort`,`tag`.`type`,`tag`.`is_show`").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("id desc").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 查看商品标签
func (c *Product) GetProductTags(ctx context.Context, in *pc.ProductTagsRequest) (*pc.ProductTagsResponse, error) {
	out := new(pc.ProductTagsResponse)
	out.Code = 400

	Engine := NewDbConn()
	session := Engine.Table("product_tag").Where("1=1")
	if in.SkuId != 0 {
		session.And("sku_id = ?", in.SkuId)
	}

	if err := session.Select("`id`, `sku_id`, `product_type`, `species`, `varieties`, `sex`, `shape`, `age`, `special_stage`, `is_sterilization`, `content_type`, `status`").Find(&out.Data); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

/*
*

	限制组合商品只有虚虚组合
*/
func CheckIsAllvirtual(in *pc.ProductRequest) error {

	engin := NewDbConn()

	productIds := make([]int32, 0)
	if in.Product.ProductType == 3 {
		skuInfo := in.SkuInfo
		for _, v := range skuInfo {
			skuGroup := v.SkuGroup
			for _, groupIds := range skuGroup {
				productIds = append(productIds, groupIds.GroupProductId)
			}
		}

		productTypes := make([]int32, 0)
		if len(productIds) > 0 {
			glog.Info("組合商品的子商品productIds: ", kit.JsonEncode(productIds))
			engin.Table("product").In("id", productIds).Cols("product_type").Find(&productTypes)
		}
		if len(productTypes) == 0 {
			return errors.New("组合商品查询子商品类型为空")
		}
		sets := utils.NewSet(productTypes...)
		has := sets.Has(1)
		if !has {
			return errors.New("组合商品不能全部是虛拟商品")
		}

	}

	return nil
}

// 添加商品库商品
// 包括实物商品，虚拟商品，组合商品
func (c *Product) NewProduct(ctx context.Context, in *pc.ProductRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: 400}
	glog.Info("NewProduct请求参数", in)
	if len(in.Product.Name) == 0 {
		out.Message = "商品名称不能为空"
		return out, nil
	}
	if in.Product.CategoryId == 0 {
		out.Message = "商品分类不能为空"
		return out, nil
	}
	if len(in.Product.Pic) == 0 {
		out.Message = "商品图片不能为空"
		return out, nil
	}
	if len(in.SkuInfo) == 0 {
		out.Message = "SKU规格信息为空"
		return out, nil
	}
	if len(in.SkuInfo[0].SkuThird) == 0 && in.Product.ProductType != 3 {
		out.Message = "第三方货号信息不能为空"
		return out, nil
	}
	var skuThirdBool = true
	for _, v := range in.SkuInfo[0].SkuThird {
		if len(v.ThirdSkuId) > 0 {
			skuThirdBool = false
		}
	}
	if skuThirdBool && in.Product.ProductType == 1 {
		out.Message = "第三方货号不能为空"
		return out, nil
	}
	if in.SkuInfo[0].WeightForUnit <= 0 && in.Product.ProductType == 3 {
		out.Message = "组合商品重量不能为0"
		return out, nil
	}
	if len(in.Product.UseRange) == 0 && in.Product.ProductType == 3 {
		out.Message = "组合商品应用范围不能为空"
		return out, nil
	}
	if in.Product.ProductType == 2 && (in.Product.TermType <= 0 || in.Product.TermValue <= 0) {
		out.Message = "虚拟商品有效期不能为空"
		return out, nil
	}

	if in.Product.TermType == 1 && cast.ToInt64(in.Product.TermValue) < time.Now().Unix() {
		out.Message = "虚拟商品有效期不能小于当前时间"
		return out, nil
	}

	if in.Product.ProductType == 2 {
		for _, v := range in.SkuInfo {

			market_price := v.MarketPrice
			retail_price := v.RetailPrice
			if market_price <= 0 || retail_price <= 0 {
				out.Message = "所有的市场价和零售价都必须大于0"
				return out, nil
			}
		}
	}

	allvirtual := CheckIsAllvirtual(in)
	if allvirtual != nil {
		out.Message = allvirtual.Error()
		return out, nil
	}
	source := rand.NewSource(time.Now().UnixNano())
	//处理平台分类
	if res, err := c.QueryCategoryRecursion(ctx, &pc.IdRequest{Id: strconv.Itoa(int(in.Product.CategoryId))}); err != nil {
		glog.Error(err)
	} else {
		in.Product.CategoryName = c.getCategoryName(0, res.Details)
		c.categoryNames = c.categoryNames[0:0]
	}

	// 处理虚拟商品过期时间为当天的23:59:59
	if in.Product.TermType == 1 && in.Product.IsUse == 0 {
		termStr := time.Unix(int64(in.Product.TermValue), 0).Format("2006-01-02")
		t, _ := time.Parse("2006-01-02", termStr)
		in.Product.TermValue = cast.ToInt32(t.AddDate(0, 0, 1).Unix()) - 28801
	}
	redis := GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}

	for _, v := range in.SkuInfo {
		// 虚拟商品或组合商品自动生成条码
		if in.Product.ProductType == 2 || in.Product.ProductType == 3 {
			//key := "productcenter:auto:barcode:"

			barcodeStr := time.Now().Format("0102150405")

			var barcodeNum int32
			for true {
				barcodeNum = rand.New(source).Int31n(1000000)
				if barcodeNum > 0 {
					break
				}
			}

			randNum := cast.ToString(fmt.Sprintf("%06v", barcodeNum+1))
			glog.Info("randNum ; ", randNum)
			v.BarCode = fmt.Sprintf("%s%08v", barcodeStr, randNum)
			if in.Product.ProductType == 3 {
				in.Product.BarCode = fmt.Sprintf("%s%08v", barcodeStr, randNum)
			}

		}
	}

	//商品主表
	product := models.Product{
		//Id:         int(in.Product.Id),
		CategoryId: int(in.Product.CategoryId),
		BrandId:    int(in.Product.BrandId),
		Name:       in.Product.Name,
		ShortName:  in.Product.ShortName,
		BarCode:    in.Product.BarCode,
		//IsDel:         int(in.Product.IsDel),
		IsGroup:              int(in.Product.IsGroup),
		CreateDate:           time.Now(),
		UpdateDate:           time.Now(),
		Pic:                  in.Product.Pic,
		SellingPoint:         in.Product.SellingPoint,
		Video:                in.Product.Video,
		ContentPc:            in.Product.ContentPc,
		ContentMobile:        in.Product.ContentMobile,
		IsDiscount:           int(in.Product.IsDiscount),
		ProductType:          int(in.Product.ProductType),
		IsUse:                int(in.Product.IsUse),
		ChannelId:            in.Product.ChannelId,
		CategoryName:         in.Product.CategoryName,
		IsDrugs:              int(in.Product.IsDrugs),
		UseRange:             in.Product.UseRange,
		TermType:             int(in.Product.TermType),
		TermValue:            int(in.Product.TermValue),
		GroupType:            int(in.Product.GroupType),
		VirtualInvalidRefund: int(in.Product.VirtualInvalidRefund),
		WarehouseType:        int(in.Product.WarehouseType),
		IsIntelGoods:         int(in.Product.IsIntelGoods), // 是否医疗互联网商品
		SourceType:           cast.ToInt(in.Product.SourceType),
	}

	Engine := NewDbConn()
	session := Engine.NewSession()
	defer session.Close()
	session.Begin()

	if product.ProductType == 1 {
		product.IsPrescribedDrug = int(in.Product.IsPrescribedDrug)
		product.DosingDays = int(in.Product.DosingDays)
		product.Disease = in.Product.Disease
		product.DrugDosage = in.Product.DrugDosage
	}

	//商品主表
	if _, err := session.Insert(&product); err != nil {
		glog.Error(err)
		session.Rollback()
		return out, err
	}

	//管家商品主表
	if _, err := session.Table("gj_product").Insert(&product); err != nil {
		glog.Error(err)
		session.Rollback()
		return out, err
	}
	out.Details = append(out.Details, fmt.Sprintf("%d", product.Id))

	var sku []models.Sku           //商品SKU
	var gjSku []models.GjSku       //商品SKU
	var skuThird []models.SkuThird //第三方SKU货号
	var skuValue []models.SkuValue //商品SKU规格组合
	//var skuGroup []models.SkuGroup //组合商品
	var attr []models.ProductAttr //商品自定义属性
	sort := 1                     //排序

	//验证第三方货号不能重复
	thirdID := make(map[string]string)

	var group_skuId int32

	for i, v := range in.SkuInfo {
		var skuId int32
		if product.ProductType != 3 {
			if v.SkuId == 0 {
				sID, _ := strconv.Atoi(fmt.Sprintf("%d%03d", product.Id, i+1))
				skuId = int32(sID)
			} else {
				skuId = v.SkuId
			}
		} else {
			sID, _ := strconv.Atoi(fmt.Sprintf("%d099", product.Id))
			skuId = int32(sID)
			group_skuId = skuId
		}
		v.SkuId = skuId

		// 虚拟商品或组合商品自动生成条码
		//if in.Product.ProductType == 2 || in.Product.ProductType == 3 {
		//	key := "productcenter:auto:barcode:"
		//	redis := GetRedisConn()
		//	barcodeStr := time.Now().Format("20060102")
		//	t, _ := time.Parse("20060102", barcodeStr)
		//	endExp := t.Unix() + 57600
		//	expInt := time.Duration(endExp - time.Now().Unix())
		//	for {
		//		if redisLock := redis.SetNX(key + "lock", barcodeStr, 10*time.Second).Val(); !redisLock {
		//			continue
		//		}
		//		barcodeNumStr := redis.Get(key + barcodeStr).Val()
		//		barcodeNum := cast.ToInt32(barcodeNumStr)
		//		v.BarCode = fmt.Sprintf("%s%05d", barcodeStr, barcodeNum + 1)
		//		redis.Set(key + barcodeStr, barcodeNum + 1, expInt*time.Second)
		//		break
		//	}
		//	redis.Del(key + "lock")
		//}

		sku = append(sku, models.Sku{
			Id:            skuId,
			ProductId:     int32(product.Id),
			MarketPrice:   v.MarketPrice,
			RetailPrice:   v.RetailPrice,
			BarCode:       v.BarCode,
			WeightForUnit: v.WeightForUnit,
		})

		gjSkuModel := models.GjSku{
			Id:            skuId,
			ProductId:     int32(product.Id),
			MarketPrice:   v.MarketPrice,
			RetailPrice:   v.RetailPrice,
			BarCode:       v.BarCode,
			StorePrice:    v.MarketPrice,
			PreposePrice:  v.MarketPrice,
			WeightForUnit: v.WeightForUnit,
		}
		//虚拟商品的重量在管家商品库默认填入0.01
		if in.Product.ProductType == 2 {
			gjSkuModel.WeightForUnit = 0.01
			gjSkuModel.MarketPrice = v.RetailPrice
			gjSkuModel.StorePrice = v.RetailPrice
			gjSkuModel.PreposePrice = v.RetailPrice
		}
		gjSku = append(gjSku, gjSkuModel)

		//第三方货号
		if product.ProductType == 1 {
			for _, t := range v.SkuThird {
				third := models.SkuThird{
					SkuId:      skuId,
					ThirdSkuId: t.ThirdSkuId,
					ThirdSpuId: t.ThirdSpuId,
					ErpId:      t.ErpId,
					ProductId:  int32(product.Id),
				}

				if t.ThirdSkuId != "" || t.ThirdSpuId != "" {
					third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", t.ThirdSpuId, t.ThirdSkuId)
				} else {
					continue
				}

				if _, ok := thirdID[third.ThirdSpuSkuId]; !ok {
					thirdID[third.ThirdSpuSkuId] = ""
				} else {
					out.Message = "第三方货号不能重复"
					return out, nil
				}

				if has, err := Engine.Table("sku_third").Where("third_spu_sku_id=?", third.ThirdSpuSkuId).Exist(); err != nil {
					glog.Error(err)
				} else if has {
					out.Message = fmt.Sprintf("第三方货号[%s]在系统已经存在", strings.Trim(third.ThirdSpuSkuId, ","))
					return out, nil
				}

				skuThird = append(skuThird, third)
			}
		} else if in.Product.ProductType == 2 {
			third := models.SkuThird{
				SkuId:      skuId,
				ThirdSpuId: "",
				ProductId:  int32(product.Id),
			}

			third.ErpId = 2
			third.ThirdSkuId = "A8HH" + cast.ToString(skuId)
			third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", third.ThirdSpuId, third.ThirdSkuId)
			skuThird = append(skuThird, third)

			third.ErpId = 4
			third.ThirdSkuId = "ZLHH" + cast.ToString(skuId)
			third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", third.ThirdSpuId, third.ThirdSkuId)
			skuThird = append(skuThird, third)
		} else if in.Product.ProductType == 3 {
			// 组合商品货号为skuId
			third := models.SkuThird{
				SkuId:      skuId,
				ThirdSkuId: cast.ToString(skuId),
				ThirdSpuId: "",
				ProductId:  int32(product.Id),
			}
			if strings.Contains(product.UseRange, "2") || strings.Contains(product.UseRange, "1") {
				third.ErpId = 2
				third.ThirdSpuSkuId = fmt.Sprintf("2,%s", third.ThirdSkuId)
				skuThird = append(skuThird, third)
			}
			if strings.Contains(product.UseRange, "3") {
				third.ErpId = 4
				third.ThirdSpuSkuId = fmt.Sprintf("4,%s", third.ThirdSkuId)
				skuThird = append(skuThird, third)
			}
		}

		//SKU规格组合
		for _, s := range v.Skuv {
			skuValue = append(skuValue, models.SkuValue{
				SpecId:      s.SpecId,
				SpecValueId: s.SpecValueId,
				SkuId:       skuId,
				ProductId:   int32(product.Id),
				Pic:         s.Pic,
				Sort:        int32(sort),
			})
			sort++
		}
	}

	//商品自定义属性
	for _, v := range in.ProductAttr {
		attr = append(attr, models.ProductAttr{
			ProductId:   int32(product.Id),
			AttrId:      v.AttrId,
			AttrValueId: v.AttrValueId,
		})
	}

	//SKU
	if _, err := session.Insert(&sku); err != nil {
		glog.Error(err)
		session.Rollback()
		return out, err
	}
	//管家sku
	if _, err := session.Insert(gjSku); err != nil {
		glog.Error(err)
		session.Rollback()
		return out, err
	}

	for _, v := range skuThird {

		//第三方SKU货号
		if _, err := session.Insert(&v); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
		//管家第三方SKU货号
		if _, err := session.Table("gj_sku_third").Insert(&v); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
		//更新价格同步表子龙的对应关系
		if v.ErpId == 4 && product.ProductType != 3 {
			//新的子龙货号，如果已存在，则更新对应关系
			var newSkuThird models.SkuThird
			if _, err := session.Table("sku_third").Where("erp_id = ?", 4).And("sku_id=?", v.ThirdSkuId).Get(&newSkuThird); err != nil {
				session.Rollback()
				glog.Error(err)
				return out, err
			}
			if len(newSkuThird.ThirdSkuId) > 0 { //新的子龙货号之前被使用
				if _, err := session.Table("price_sync").Where("zl_productid = ?", v.ThirdSkuId).Cols("product_id,sku").Update(&models.PriceSync{ProductId: int(v.ProductId), Sku: int(v.SkuId)}); err != nil {
					session.Rollback()
					glog.Error(err)
					return out, err
				}
			}
		}
	}

	if in.Product.ProductType != 3 {
		for _, v := range skuValue {

			//商品SKU规格组合
			if _, err := session.Insert(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			//管家商品SKU规格组合
			if _, err := session.Table("gj_sku_value").Insert(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
		}
	}

	var groupType int
	// 写入组合商品
	if in.Product.ProductType == 3 {
		if len(in.SkuInfo[0].SkuGroup) == 0 {
			out.Message = "组合商品信息不能为空"
			return out, nil
		}
		// 写入组合商品
		isGroup, isDrugs, isPrescribedDrug, price, err := updateGroupProduct(session, int32(product.Id), sku[0].Id, in.SkuInfo[0].SkuGroup)
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}
		groupType = isGroup
		_, err = session.ID(product.Id).Update(&models.Product{
			GroupType:        isGroup,
			IsDrugs:          isDrugs,
			IsPrescribedDrug: isPrescribedDrug,
		})
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}
		_, err = session.ID(product.Id).Update(&models.GjProduct{
			GroupType:        isGroup,
			IsDrugs:          isDrugs,
			IsPrescribedDrug: isPrescribedDrug,
		})
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}
		//组合商品写入默认的sku_value
		pic := strings.Split(product.Pic, ",")[0]

		sku_valuemode := models.SkuValue{
			SpecId:      1,
			SpecValueId: cast.ToInt32(config.GetString("spec_value_id")),
			SkuId:       group_skuId,
			ProductId:   int32(product.Id),
			Pic:         pic,
			Sort:        int32(sort),
		}

		glog.Info("读取规格信息：", kit.JsonEncode(sku_valuemode), "  获取配置：", config.GetString("spec_value_id"))
		_, err = session.Insert(&sku_valuemode)
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}

		_, err = session.Insert(&models.GjSkuValue{
			Id:          int(sku_valuemode.Id),
			SpecId:      1,
			SpecValueId: cast.ToInt(config.GetString("spec_value_id")),
			SkuId:       int(group_skuId),
			ProductId:   product.Id,
			Pic:         pic,
			Sort:        sort,
		})
		if err != nil {
			out.Message = err.Error()
			session.Rollback()
			return out, nil
		}

		if price > 0 {
			_, err = session.ID(sku[0].Id).Update(&models.Sku{MarketPrice: int32(price)})
			if err != nil {
				out.Message = err.Error()
				session.Rollback()
				return out, nil
			}
			_, err = session.ID(sku[0].Id).Update(&models.GjSku{
				MarketPrice:  int32(price),
				StorePrice:   int32(price),
				PreposePrice: int32(price),
			})
			if err != nil {
				out.Message = err.Error()
				session.Rollback()
				return out, nil
			}
		}
	}

	//商品自定义属性
	if len(attr) > 0 && in.Product.ProductType != 3 {
		if _, err := session.Insert(&attr); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
	}

	if in.ProductTags != nil && in.Product.ProductType != 3 {
		//组装商品标签数据
		produtcTagModel := models.ProductTag{
			SkuId:           int(sku[0].Id),
			ProductId:       int(sku[0].ProductId),
			ProductType:     int(in.ProductTags.ProductType),
			Species:         in.ProductTags.Species,
			Varieties:       in.ProductTags.Varieties,
			Sex:             in.ProductTags.Sex,
			Shape:           in.ProductTags.Shape,
			Age:             in.ProductTags.Age,
			SpecialStage:    in.ProductTags.SpecialStage,
			IsSterilization: in.ProductTags.IsSterilization,
			ContentType:     in.ProductTags.ContentType,
			Status:          in.ProductTags.Status,
		}
		//创建商品标签数据
		if _, err := session.Insert(&produtcTagModel); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
	}
	if err := session.Commit(); err != nil {
		session.Rollback()
		glog.Error(err)
		return out, err
	}

	go func() {
		// 实物商品、实实组合同步R1采购价格
		if in.Product.ProductType == 1 || groupType == 1 {
			var skuIds []string
			for _, info := range in.SkuInfo {
				skuIds = append(skuIds, cast.ToString(info.SkuId))
			}
			syncReq := &pc.R1PriceSyncSkuReq{Search: strings.Join(skuIds, ","), Type: 1}
			if groupType == 1 {
				syncReq.Type = 3
			}
			c.R1PriceSyncSku(context.Background(), syncReq)
		}
	}()

	out.Code = 200
	return out, nil
}

// 编辑商品
func (c *Product) EditProduct(ctx context.Context, in *pc.ProductRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: 400}
	glog.Info("EditProduct请求参数", in)
	if len(in.Product.Name) == 0 {
		out.Message = "商品名称不能为空"
		return out, nil
	}
	if in.Product.CategoryId == 0 {
		out.Message = "商品分类不能为空"
		return out, nil
	}
	if len(in.Product.Pic) == 0 {
		out.Message = "商品图片不能为空"
		return out, nil
	}
	if len(in.SkuInfo) == 0 {
		out.Message = "SKU规格信息为空"
		return out, nil
	}
	if len(in.SkuInfo[0].SkuThird) == 0 && in.Product.ProductType != 3 {
		out.Message = "第三方货号信息不能为空"
		return out, nil
	}
	var skuThirdBool = true
	for _, v := range in.SkuInfo[0].SkuThird {
		if len(v.ThirdSkuId) > 0 {
			skuThirdBool = false
		}
	}
	if skuThirdBool && in.Product.ProductType != 3 {
		out.Message = "第三方货号不能为空"
		return out, nil
	}
	if in.SkuInfo[0].WeightForUnit <= 0 && in.Product.ProductType == 3 {
		out.Message = "组合商品重量不能为0"
		return out, nil
	}
	if len(in.Product.UseRange) == 0 && in.Product.ProductType == 3 {
		out.Message = "组合商品应用范围不能为空"
		return out, nil
	}
	if in.Product.ProductType == 2 && (in.Product.TermType <= 0 || in.Product.TermValue <= 0) {
		out.Message = "虚拟商品有效期不能为空"
		return out, nil
	}

	if in.Product.TermType == 1 && cast.ToInt64(in.Product.TermValue) < time.Now().Unix() {
		out.Message = "虚拟商品有效期不能小于当前时间"
		return out, nil
	}

	if in.Product.ProductType == 2 {
		for _, v := range in.SkuInfo {

			market_price := v.MarketPrice
			retail_price := v.RetailPrice
			if market_price <= 0 || retail_price <= 0 {
				out.Message = "所有的市场价和零售价都必须大于0"
				return out, nil
			}
		}
	}

	allvirtual := CheckIsAllvirtual(in)
	if allvirtual != nil {
		out.Message = allvirtual.Error()
		return out, nil
	}

	//处理平台分类
	if res, err := c.QueryCategoryRecursion(ctx, &pc.IdRequest{Id: strconv.Itoa(int(in.Product.CategoryId))}); err != nil {
		glog.Error(err)
	} else {
		in.Product.CategoryName = c.getCategoryName(0, res.Details)
		c.categoryNames = c.categoryNames[0:0]
	}

	Engine := NewDbConn()

	product := &models.Product{}
	if has, err := Engine.Where("id=?", in.Product.Id).Select("is_use,channel_id,is_prescribed_drug,is_drugs").Get(product); err != nil {
		out.Message = err.Error()
		return out, nil
	} else if !has {
		out.Message = "商品不存在"
		return out, nil
	}

	session := Engine.NewSession()
	defer session.Close()
	session.Begin()

	isUse := product.IsUse == 1
	// 是否变更了药品、处方药标识
	isChangeDrug := false
	if int32(product.IsPrescribedDrug) != in.Product.IsPrescribedDrug || int32(product.IsDrugs) != in.Product.IsDrugs {
		isChangeDrug = true
	}

	mapSku := make(map[int32]*pc.Sku)

	//sku编号从1开始 格式为 商品ID+001/002/003
	skuNo := 1

	//如果是编辑已经使用的商品，则编辑的商品的原有skuid需要存在，可以多，但不能少
	if isUse {
		if res, err := c.QuerySku(ctx, &wrappers.Int32Value{Value: in.Product.Id}); err != nil {
			glog.Error(err)
		} else {
			//已经被使用的商品，sku编号为现有的sku数量+1
			skuNo = len(res.Details) + 1

			var c int
			for _, v := range res.Details {
				mapSku[v.Id] = v
				for _, v2 := range in.SkuInfo {
					if v.Id == v2.SkuId {
						c++
						break
					}
				}
			}
			//println(c,len(res.Details))
			if c < len(res.Details) {
				out.Message = "当前商品处于使用状态，SKU数据只允许新增，不允许删除"
				return out, nil
			}
		}
	}

	var sku []models.Sku           //商品SKU
	var skuThird []models.SkuThird //第三方SKU货号
	var skuValue []models.SkuValue //商品SKU规格组合
	//var skuGroup []models.SkuGroup //组合商品
	var attr []models.ProductAttr //商品自定义属性
	var ziOldSkuThird string      //旧的子龙货号，清空对应关系
	sort := 1

	redis := GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	source := rand.NewSource(time.Now().UnixNano())
	//验证第三方货号不能重复
	thirdID := make(map[string]string)

	for _, v := range in.SkuInfo {
		var skuId int32
		if in.Product.ProductType != 3 {
			if isUse && v.SkuId != 0 {
				skuId = v.SkuId
			} else {
				sid, _ := strconv.Atoi(fmt.Sprintf("%d%03d", in.Product.Id, skuNo))
				skuId = int32(sid)
				if in.Product.ProductType == 2 && (skuId != v.SkuId || len(v.BarCode) == 0) {

					barcodeStr := time.Now().Format("0102150405")

					var barcodeNum int32
					for true {
						barcodeNum = rand.New(source).Int31n(1000000)
						if barcodeNum > 0 {
							break
						}
					}
					randNum := cast.ToString(fmt.Sprintf("%06v", barcodeNum+1))
					glog.Info("randNum ; ", randNum)
					v.BarCode = fmt.Sprintf("%s%08v", barcodeStr, randNum)
					if in.Product.ProductType == 3 {
						in.Product.BarCode = fmt.Sprintf("%s%08v", barcodeStr, randNum)
					}
				}
				skuNo++
			}
		} else {
			if isUse && v.SkuId != 0 {
				skuId = v.SkuId
			} else {
				sid, _ := strconv.Atoi(fmt.Sprintf("%d099", in.Product.Id))
				skuId = int32(sid)
				if skuId != v.SkuId || len(v.BarCode) == 0 {
					barcodeStr := time.Now().Format("0102150405")

					var barcodeNum int32
					for true {
						barcodeNum = rand.New(source).Int31n(1000000)
						if barcodeNum > 0 {
							break
						}
					}
					randNum := cast.ToString(fmt.Sprintf("%06v", barcodeNum+1))
					glog.Info("randNum ; ", randNum)
					v.BarCode = fmt.Sprintf("%s%08v", barcodeStr, randNum)
					if in.Product.ProductType == 3 {
						in.Product.BarCode = fmt.Sprintf("%s%08v", barcodeStr, randNum)
					}
				}
				skuNo++
			}
		}

		sku = append(sku, models.Sku{
			Id:            skuId,
			ProductId:     in.Product.Id,
			MarketPrice:   v.MarketPrice,
			RetailPrice:   v.RetailPrice,
			BarCode:       v.BarCode,
			WeightForUnit: v.WeightForUnit,
		})

		//第三方货号
		if in.Product.ProductType == 1 {
			for _, t := range v.SkuThird {
				third := models.SkuThird{
					Id:         t.Id,
					SkuId:      skuId,
					ThirdSkuId: t.ThirdSkuId,
					ThirdSpuId: t.ThirdSpuId,
					ErpId:      t.ErpId,
					ProductId:  in.Product.Id,
				}

				if t.ThirdSkuId != "" || t.ThirdSpuId != "" {
					third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", t.ThirdSpuId, t.ThirdSkuId)
				} else {
					if t.Id == 0 {
						continue
					}
				}

				if _, ok := thirdID[third.ThirdSpuSkuId]; !ok {
					thirdID[third.ThirdSpuSkuId] = ""
				} else {
					out.Message = "第三方货号不能重复"
					return out, nil
				}

				session := Engine.Table("sku_third").Where("third_spu_sku_id=?", third.ThirdSpuSkuId)
				if third.Id > 0 {
					session.And("id != ?", third.Id)
				}

				//当商品没有使用的时候，不对本商品下的货号做是否存在的验证，因为没有使用的状态会删除重建
				if !isUse {
					session.And("product_id != ?", in.Product.Id)
				}

				var tempSkuThird []models.SkuThird
				if err := session.Find(&tempSkuThird); err != nil {
					glog.Error(err)
				}

				if len(tempSkuThird) > 0 {
					out.Message = fmt.Sprintf("第三方货号[%s]已经被商品[ID:%d]使用", strings.Trim(third.ThirdSpuSkuId, ","), tempSkuThird[0].ProductId)
					return out, nil
				}

				skuThird = append(skuThird, third)

				if t.ErpId == 4 || t.ErpId == 2 {
					{
						var oldSkuThird models.SkuThird
						if _, err := Engine.Table("sku_third").Where("erp_id = ?", t.ErpId).And("sku_id=?", v.SkuId).Get(&oldSkuThird); err != nil {
							glog.Error(err)
						}
						ziOldSkuThird = oldSkuThird.ThirdSkuId
					}
				}
			}
		} else if in.Product.ProductType == 2 {
			third := models.SkuThird{
				SkuId:      skuId,
				ThirdSpuId: "",
				ProductId:  in.Product.Id,
			}

			third.ErpId = 2
			third.ThirdSkuId = "A8HH" + cast.ToString(skuId)
			third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", third.ThirdSpuId, third.ThirdSkuId)
			var dataThird2 models.SkuThird
			engine.SQL("select * from dc_product.gj_sku_third where third_sku_id =? and erp_id = ? and sku_id = ?;", third.ThirdSkuId, 2, third.SkuId).Get(&dataThird2)
			if dataThird2.Id > 0 {
				third.Id = dataThird2.Id
			}
			skuThird = append(skuThird, third)

			third.ErpId = 4
			third.ThirdSkuId = "ZLHH" + cast.ToString(skuId)
			third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", third.ThirdSpuId, third.ThirdSkuId)
			var dataThird4 models.SkuThird
			engine.SQL("select * from dc_product.gj_sku_third where third_sku_id =? and erp_id = ? and sku_id = ?;", third.ThirdSkuId, 4, third.SkuId).Get(&dataThird4)
			if dataThird4.Id > 0 {
				third.Id = dataThird4.Id
			}
			skuThird = append(skuThird, third)

		} else if in.Product.ProductType == 3 {
			for _, t := range v.SkuThird {
				third := models.SkuThird{
					Id:         t.Id,
					SkuId:      skuId,
					ThirdSkuId: t.ThirdSkuId,
					ThirdSpuId: t.ThirdSpuId,
					ErpId:      t.ErpId,
					ProductId:  in.Product.Id,
				}
				third.ThirdSpuSkuId = fmt.Sprintf("%d,%s", third.ErpId, third.ThirdSkuId)

				skuThird = append(skuThird, third)
			}
			if len(skuThird) < 2 {
				if len(skuThird) == 0 {
					third := models.SkuThird{
						SkuId:      skuId,
						ThirdSkuId: cast.ToString(skuId),
						ThirdSpuId: "",
						ProductId:  in.Product.Id,
					}
					if strings.Contains(in.Product.UseRange, "2") || strings.Contains(in.Product.UseRange, "1") {
						third.ErpId = 2
						third.ThirdSpuSkuId = fmt.Sprintf("2,%s", third.ThirdSkuId)
						skuThird = append(skuThird, third)
					}
					if strings.Contains(in.Product.UseRange, "3") {
						third.ErpId = 4
						third.ThirdSpuSkuId = fmt.Sprintf("4,%s", third.ThirdSkuId)
						skuThird = append(skuThird, third)
					}
				} else {
					if strings.Contains(in.Product.UseRange, "2") || strings.Contains(in.Product.UseRange, "1") {
						if skuThird[0].ErpId != 2 {
							third := models.SkuThird{
								SkuId:         skuId,
								ThirdSkuId:    cast.ToString(skuId),
								ThirdSpuId:    "",
								ProductId:     in.Product.Id,
								ErpId:         2,
								ThirdSpuSkuId: fmt.Sprintf("2,%s", cast.ToString(skuId)),
							}
							skuThird = append(skuThird, third)
						}
					}
					if strings.Contains(in.Product.UseRange, "3") {
						if skuThird[0].ErpId != 4 {
							third := models.SkuThird{
								SkuId:         skuId,
								ThirdSkuId:    cast.ToString(skuId),
								ThirdSpuId:    "",
								ProductId:     in.Product.Id,
								ErpId:         4,
								ThirdSpuSkuId: fmt.Sprintf("4,%s", cast.ToString(skuId)),
							}
							skuThird = append(skuThird, third)
						}
					}
				}
			}
		}

		//SKU规格组合
		for _, s := range v.Skuv {
			skuValue = append(skuValue, models.SkuValue{
				Id:          s.Id,
				SpecId:      s.SpecId,
				SpecValueId: s.SpecValueId,
				SkuId:       skuId,
				ProductId:   in.Product.Id,
				Pic:         s.Pic,
				Sort:        int32(sort),
			})
			sort++
		}
	}

	//商品自定义属性
	for _, v := range in.ProductAttr {
		attr = append(attr, models.ProductAttr{
			Id:          v.Id,
			ProductId:   in.Product.Id,
			AttrId:      v.AttrId,
			AttrValueId: v.AttrValueId,
		})
	}

	var (
		gj_sku       = make([]models.GjSku, 0)
		gj_sku_third = make([]models.GjSkuThird, 0)
		gj_sku_value = make([]models.GjSkuValue, 0)
	)
	session.SQL("select * from gj_sku WHERE product_id = ?", in.Product.Id).Find(&gj_sku)
	session.SQL("select * from gj_sku_third WHERE product_id = ?", in.Product.Id).Find(&gj_sku_third)
	session.SQL("select * from gj_sku_value WHERE product_id = ?", in.Product.Id).Find(&gj_sku_value)

	//未被使用的商品，删除sku及相关和属性【同时删除管家库相关表的数据】
	if !isUse {
		//删除商品相关属性
		if _, err := session.Exec("DELETE FROM product_attr WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}
		if _, err := session.Exec("DELETE FROM gj_product_attr WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}

		//删除商品SKU
		if _, err := session.Exec("DELETE FROM sku WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}
		if _, err := session.Exec("DELETE FROM gj_sku WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}

		//删除组合商品
		if _, err := session.Exec("DELETE FROM sku_group WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}
		//删除管家组合商品
		if _, err := session.Exec("DELETE FROM gj_sku_group WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}

		//删除关联的第三方SKU货号
		if _, err := session.Exec("DELETE FROM sku_third WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}
		if _, err := session.Exec("DELETE FROM gj_sku_third WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}

		//删除SKU规格值
		if _, err := session.Exec("DELETE FROM sku_value WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}
		if _, err := session.Exec("DELETE FROM gj_sku_value WHERE product_id = ?", in.Product.Id); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}

	}

	if in.Product.ProductType == 3 {
		if !isUse {
			if len(in.SkuInfo[0].SkuGroup) == 0 {
				out.Message = "组合商品信息不能为空"
				return out, nil
			}
			// 写入组合商品
			isGroup, isDrugs, isPrescribedDrug, price, err := updateGroupProduct(session, in.Product.Id, in.SkuInfo[0].SkuId, in.SkuInfo[0].SkuGroup)
			if err != nil {
				out.Message = err.Error()
				session.Rollback()
				return out, nil
			}
			in.Product.IsDrugs = int32(isDrugs)
			in.Product.IsPrescribedDrug = int32(isPrescribedDrug)
			in.Product.GroupType = int32(isGroup)
			if price > 0 {
				sku[0].MarketPrice = int32(price)
			}
		}

		upProduct := models.Product{
			CategoryId:           int(in.Product.CategoryId),
			BrandId:              int(in.Product.BrandId),
			Name:                 in.Product.Name,
			ShortName:            in.Product.ShortName,
			BarCode:              in.Product.BarCode,
			IsGroup:              int(in.Product.IsGroup),
			UpdateDate:           time.Now(),
			Pic:                  in.Product.Pic,
			SellingPoint:         in.Product.SellingPoint,
			Video:                in.Product.Video,
			ContentPc:            in.Product.ContentPc,
			ContentMobile:        in.Product.ContentMobile,
			IsDiscount:           int(in.Product.IsDiscount),
			ProductType:          int(in.Product.ProductType),
			CategoryName:         in.Product.CategoryName,
			IsDrugs:              int(in.Product.IsDrugs),
			IsPrescribedDrug:     int(in.Product.IsPrescribedDrug),
			UseRange:             in.Product.UseRange,
			TermType:             int(in.Product.TermType),
			TermValue:            int(in.Product.TermValue),
			VirtualInvalidRefund: int(in.Product.VirtualInvalidRefund),
			WarehouseType:        int(in.Product.WarehouseType),
			IsIntelGoods:         int(in.Product.IsIntelGoods),
		}
		if in.Product.GroupType > 0 {
			upProduct.GroupType = int(in.Product.GroupType)
		}
		if _, err := session.ID(in.Product.Id).MustCols("is_drugs,is_prescribed_drug").Update(&upProduct); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}

		//管家商品库主表
		if _, err := session.Table("gj_product").Cols("brand_id,name,short_name,pic,bar_code,selling_point,video,content_pc,content_mobile, use_range, is_intel_goods,is_drugs,is_prescribed_drug").ID(in.Product.Id).Update(&models.GjProduct{
			BrandId:          upProduct.BrandId,
			Name:             upProduct.Name,
			ShortName:        upProduct.ShortName,
			Pic:              upProduct.Pic,
			BarCode:          upProduct.BarCode,
			SellingPoint:     upProduct.SellingPoint,
			Video:            upProduct.Video,
			ContentPc:        upProduct.ContentPc,
			ContentMobile:    upProduct.ContentMobile,
			UseRange:         upProduct.UseRange,
			WarehouseType:    upProduct.WarehouseType,
			IsIntelGoods:     upProduct.IsIntelGoods,
			IsDrugs:          upProduct.IsDrugs,
			IsPrescribedDrug: upProduct.IsPrescribedDrug,
		}); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}
	} else {
		// 处理虚拟商品过期时间为当天的23:59:59
		if in.Product.TermType == 1 && !isUse {
			termStr := time.Unix(int64(in.Product.TermValue), 0).Format("2006-01-02")
			t, _ := time.Parse("2006-01-02", termStr)
			in.Product.TermValue = int32(t.AddDate(0, 0, 1).Unix()) - 28801
		}
		//平台商品主表
		if _, err := session.ID(in.Product.Id).Cols("category_id,category_name,brand_id,name,short_name,update_date,pic,bar_code,selling_point,video,content_pc,content_mobile,is_drugs,is_discount,term_type,term_value,virtual_invalid_refund,warehouse_type,is_intel_goods,is_prescribed_drug,disease,drug_dosage,dosing_days").Update(models.Product{
			CategoryId:           int(in.Product.CategoryId),
			CategoryName:         in.Product.CategoryName,
			BrandId:              int(in.Product.BrandId),
			Name:                 in.Product.Name,
			ShortName:            in.Product.ShortName,
			BarCode:              in.Product.BarCode,
			Pic:                  in.Product.Pic,
			SellingPoint:         in.Product.SellingPoint,
			Video:                in.Product.Video,
			ContentPc:            in.Product.ContentPc,
			ContentMobile:        in.Product.ContentMobile,
			IsDiscount:           int(in.Product.IsDiscount),
			IsDrugs:              int(in.Product.IsDrugs),
			TermType:             int(in.Product.TermType),
			TermValue:            int(in.Product.TermValue),
			VirtualInvalidRefund: int(in.Product.VirtualInvalidRefund),
			WarehouseType:        int(in.Product.WarehouseType),
			IsIntelGoods:         int(in.Product.IsIntelGoods),
			IsPrescribedDrug:     int(in.Product.IsPrescribedDrug),
			DosingDays:           int(in.Product.DosingDays),
			Disease:              in.Product.Disease,
			DrugDosage:           in.Product.DrugDosage,
		}); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}

		//管家商品库主表
		//【阿闻管家同步更新】
		//规格、 5张主图、详情图、条形码、商品货号 同步更新到管家商品库
		if _, err := session.ID(in.Product.Id).Cols("name,short_name,selling_point,pic,content_pc,bar_code,term_type,term_value,virtual_invalid_refund,warehouse_type,is_drugs, is_intel_goods,is_prescribed_drug").
			Update(&models.GjProduct{Name: in.Product.Name, ShortName: in.Product.ShortName, SellingPoint: in.Product.SellingPoint, Pic: in.Product.Pic, ContentPc: in.Product.ContentPc,
				BarCode:              in.Product.BarCode,
				TermType:             int(in.Product.TermType),
				TermValue:            int(in.Product.TermValue),
				VirtualInvalidRefund: in.Product.VirtualInvalidRefund,
				WarehouseType:        int(in.Product.WarehouseType),
				IsIntelGoods:         int(in.Product.IsIntelGoods),
				IsPrescribedDrug:     int(in.Product.IsPrescribedDrug),
				IsDrugs:              int(in.Product.IsDrugs),
			}); err != nil {
			session.Rollback()
			glog.Error(err)
			return out, err
		}

		// 实物商品药品属性变更了，要同步到组合商品
		if in.Product.ProductType == 1 && isChangeDrug && len(sku) > 0 {
			if _, err := session.Exec(`update dc_product.product p
left join dc_product.gj_product gp on gp.id = p.id
join (select sg.product_id,max(p.is_drugs) as is_drugs,max(is_prescribed_drug) as is_prescribed_drug from dc_product.sku_group sg
         join dc_product.product p on sg.group_product_id = p.id
         where sg.sku_id in (select distinct sku_id from dc_product.sku_group where group_sku_id =?)
group by sg.product_id) t on t.product_id = p.id
set p.is_drugs = t.is_drugs,gp.is_drugs = t.is_drugs,
p.is_prescribed_drug = t.is_prescribed_drug,gp.is_prescribed_drug = t.is_prescribed_drug;`, sku[0].Id); err != nil {
				return out, err
			}
		}
	}

	//SKU
	for _, v := range sku {

		//商品被使用的时候，做更新操作
		if _, has := mapSku[v.Id]; has {
			if _, err := session.ID(v.Id).Update(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			if in.Product.ProductType != 3 {
				//更新管家sku条码
				if _, err := session.ID(v.Id).Cols("bar_code").Update(&models.GjSku{BarCode: v.BarCode}); err != nil {
					session.Rollback()
					glog.Error(err)
					return out, err
				}
			} else {
				//更新管家sku条码
				if in.Product.ProductType == 2 {
					if _, err := session.ID(v.Id).Update(&models.GjSku{
						MarketPrice:   v.RetailPrice,
						PreposePrice:  v.RetailPrice,
						StorePrice:    v.RetailPrice,
						RetailPrice:   v.RetailPrice,
						BarCode:       v.BarCode,
						WeightForUnit: v.WeightForUnit,
					}); err != nil {
						session.Rollback()
						glog.Error(err)
						return out, err
					}
				} else {
					if _, err := session.ID(v.Id).Update(&models.GjSku{
						MarketPrice:   v.MarketPrice,
						PreposePrice:  v.RetailPrice,
						StorePrice:    v.MarketPrice,
						RetailPrice:   v.MarketPrice,
						BarCode:       v.BarCode,
						WeightForUnit: v.WeightForUnit,
					}); err != nil {
						session.Rollback()
						glog.Error(err)
						return out, err
					}
				}
			}
		} else {
			//平台sku新增
			if _, err := session.Insert(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			//管家sku新增
			gjSkuNew := &models.GjSku{
				Id:            v.Id,
				ProductId:     v.ProductId,
				MarketPrice:   v.MarketPrice,
				RetailPrice:   v.RetailPrice,
				BarCode:       v.BarCode,
				StorePrice:    v.MarketPrice,
				PreposePrice:  v.MarketPrice,
				WeightForUnit: v.WeightForUnit,
			}
			if in.Product.ProductType == 2 {
				gjSkuNew.WeightForUnit = 0.01
				gjSkuNew.MarketPrice = v.RetailPrice
				gjSkuNew.StorePrice = v.RetailPrice
				gjSkuNew.PreposePrice = v.RetailPrice
			}
			if _, err := session.Insert(gjSkuNew); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
		}

		for _, v2 := range gj_sku {
			if v.Id == v2.Id {
				if in.Product.ProductType == 3 {
					v2.WeightForUnit = v.WeightForUnit
				}
				session.ID(v.Id).Cols("weight_for_unit").Update(&v2)
			}
		}
	}

	//第三方SKU货号
	for _, v := range skuThird {
		//商品被使用的时候，做更新操作
		if isUse && v.Id != 0 {
			//编辑的时候，如果货号清空了，删除平台库的这条数据
			if v.ThirdSpuSkuId == "" {
				if _, err := session.Delete(v); err != nil {
					glog.Error(err)
					session.Rollback()
					return out, err
				}
				//管家库也同步删除
				if _, err := session.Table("gj_sku_third").Delete(v); err != nil {
					glog.Error(err)
					session.Rollback()
					return out, err
				}
				continue
			}

			//平台库更新
			if _, err := session.ID(v.Id).Update(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			//管家库更新货号
			if _, err := session.ID(v.Id).Cols("third_spu_id,third_sku_id,third_spu_sku_id").Update(&models.GjSkuThird{ThirdSkuId: v.ThirdSkuId, ThirdSpuId: v.ThirdSpuId, ThirdSpuSkuId: v.ThirdSpuSkuId}); err != nil {
				session.Rollback()
				glog.Error(err)
				return out, err
			}
		} else {
			//货号都是空的，不写
			if v.ThirdSpuSkuId == "" {
				continue
			}
			//新增到平台库
			if _, err := session.Insert(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			//新增到管家库
			if _, err := session.Table("gj_sku_third").Insert(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
		}
		//更新价格同步表子龙的对应关系
		if v.ErpId == 4 && in.Product.ProductType != 3 {
			//旧的子龙货号，清空对应关系
			if len(ziOldSkuThird) > 0 {
				if _, err := session.Table("price_sync").Where("zl_productid = ?", ziOldSkuThird).Cols("product_id,sku").Update(&models.PriceSync{ProductId: 0, Sku: 0}); err != nil {
					session.Rollback()
					glog.Error(err)
					return out, err
				}
			}
			if len(v.ThirdSkuId) > 0 {
				if _, err := session.Table("price_sync").Where("zl_productid = ?", v.ThirdSkuId).Cols("product_id,sku").Update(&models.PriceSync{ProductId: int(v.ProductId), Sku: int(v.SkuId)}); err != nil {
					session.Rollback()
					glog.Error(err)
					return out, err
				}
			}
		} else if v.ErpId == 2 && in.Product.ProductType != 3 {
			//更新价格同步a8-前置仓的对应关系
			if len(ziOldSkuThird) > 0 {
				if _, err := session.Table("qzc_price_sync").Where("third_sku_id = ?", ziOldSkuThird).Cols("product_id,sku_id").
					Update(&models.QzcPriceSync{ProductId: 0, SkuId: 0}); err != nil {
					session.Rollback()
					glog.Error(err)
					return out, err
				}
			}
			if len(v.ThirdSkuId) > 0 { //新的a8货号之前被使用
				if _, err := session.Table("qzc_price_sync").Where("third_sku_id = ?", v.ThirdSkuId).Cols("product_id,sku_id").
					Update(&models.QzcPriceSync{ProductId: int(v.ProductId), SkuId: int(v.SkuId)}); err != nil {
					session.Rollback()
					glog.Error(err)
					return out, err
				}
			}
		}
	}

	//商品SKU规格组合
	for _, v := range skuValue {
		//商品被使用的时候，做更新操作
		if isUse && v.Id != 0 {
			//更新平台库规格信息
			if _, err := session.ID(v.Id).Update(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			//更新管家库规格图片
			if _, err := session.ID(v.Id).Cols("pic").Update(&models.GjSkuValue{Pic: v.Pic}); err != nil {
				session.Rollback()
				glog.Error(err)
				return out, err
			}
		} else {
			//平台库规格组合
			if _, err := session.Insert(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			//管家库规格组合
			if _, err := session.Table("gj_sku_value").Insert(&v); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
		}
	}

	//商品自定义属性
	if len(attr) > 0 {
		for _, v := range attr {
			//商品被使用的时候，做更新操作
			if isUse {
				if _, err := session.ID(v.Id).Update(&v); err != nil {
					glog.Error(err)
					session.Rollback()
					return out, err
				}
			} else {
				if _, err := session.Insert(&v); err != nil {
					glog.Error(err)
					session.Rollback()
					return out, err
				}
			}
		}
	}

	if in.ProductTags != nil {
		//组装商品标签数据
		produtcTagModel := models.ProductTag{
			Id:              int(in.ProductTags.Id),
			SkuId:           int(in.ProductTags.SkuId),
			ProductId:       int(in.ProductTags.ProductId),
			ProductType:     int(in.ProductTags.ProductType),
			Species:         in.ProductTags.Species,
			Varieties:       in.ProductTags.Varieties,
			Sex:             in.ProductTags.Sex,
			Shape:           in.ProductTags.Shape,
			Age:             in.ProductTags.Age,
			SpecialStage:    in.ProductTags.SpecialStage,
			IsSterilization: in.ProductTags.IsSterilization,
			ContentType:     in.ProductTags.ContentType,
			Status:          in.ProductTags.Status,
		}
		//更新商品标签数据
		if _, err := session.ID(in.ProductTags.Id).Update(&produtcTagModel); err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
	}

	//添加如果修改药品仓属性的时候清除库存数据
	//是药品仓清除在非药品仓的数据

	warehouseId := 0

	if in.Product.WarehouseType == 1 {
		warehouseId = cast.ToInt(config.GetString("jhcId"))
	} else {
		warehouseId = cast.ToInt(config.GetString("ypcId"))
	}
	_, err := session.Exec("delete from `dc_order`.`warehouse_goods` where warehouse_id=? and goodsid=?", warehouseId, cast.ToString(in.SkuInfo[0].SkuId))
	if err != nil {
		glog.Error("删除药品或者非药品库存失败" + err.Error())
		session.Rollback()
		return out, err
	}

	//先查询原来的货号，然后和现在的对比，看是否发生了变化，如果发生了变化，就记录货号变更的日志
	oldSkuThird := make([]models.SkuThird, 0)
	Engine.Where("product_id=?", in.Product.Id).Find(&oldSkuThird)
	olda8 := ""
	oldzolong := ""
	newa8 := ""
	newzolong := ""
	if len(oldSkuThird) > 0 {
		for _, x := range oldSkuThird {
			if x.ErpId == 2 {
				olda8 = x.ThirdSkuId
			} else if x.ErpId == 4 {
				oldzolong = x.ThirdSkuId
			}
		}
	}

	if err := session.Commit(); err != nil {
		session.Rollback()
		glog.Error(err)
		return out, err
	}
	//redis := GetRedisConn()
	//if kit.EnvCanCron() {
	//	defer redis.Close()
	//}
	//删除redis的缓存库存
	redis.HDel(fmt.Sprintf("stock:%d", in.SkuInfo[0].SkuId), "DSC001")

	//实物商品编辑时，如果A8货号、子龙货号发生了改变，需要重新认领到对应的渠道
	if in.Product.ProductType == 1 && len(product.ChannelId) > 0 {
		req := &pc.CopyGjProductToChannelProductRequest{
			ProductId:    []int32{in.Product.Id},
			UpdateFields: "a8,zilong,bar_code",
			IsBatch:      false,
		}
		for _, v := range strings.Split(product.ChannelId, ",") {
			req.ChannelId = append(req.ChannelId, cast.ToInt32(v))
		}
		go func() {
			rsp, err := c.CopyGjProductToChannelProduct(ctx, req)
			if err != nil {
				glog.Error(" EditProduct 后重新认领 CopyGjProductToChannelProduct err：", err.Error(), "product：", in.Product.Id)
			} else if rsp.Code != 200 {
				glog.Error(" EditProduct 后重新认领 CopyGjProductToChannelProduct err：", rsp.Message, "product：", in.Product.Id)
			}
		}()
	}
	//写入修改日志
	go func() {
		// 实物商品、实实组合同步R1采购价格
		if in.Product.ProductType == 1 || in.Product.GroupType == 1 {
			var skuIds []string
			for _, info := range in.SkuInfo {
				skuIds = append(skuIds, cast.ToString(info.SkuId))
			}
			syncReq := &pc.R1PriceSyncSkuReq{Search: strings.Join(skuIds, ","), Type: 1}
			if in.Product.GroupType == 1 {
				syncReq.Type = 3
			}
			c.R1PriceSyncSku(context.Background(), syncReq)
		}

		if len(skuThird) > 0 {
			for _, x := range skuThird {
				if x.ErpId == 2 {
					newa8 = x.ThirdSkuId
				} else if x.ErpId == 4 {
					newzolong = x.ThirdSkuId
				}
			}
		}
		//判断是否有修改货号
		if olda8 != newa8 || oldzolong != newzolong {
			RecordProduct := models.RecordProduct{}
			RecordProduct.ProductName = in.Product.Name
			RecordProduct.ProductId = int(in.Product.Id)
			RecordProduct.RecordType = 1
			if len(in.SkuInfo) > 0 {
				RecordProduct.SkuId = int(in.SkuInfo[0].SkuId)
			}
			RecordProduct.A8New = newa8
			RecordProduct.A8Old = olda8
			RecordProduct.ZilongNew = newzolong
			RecordProduct.ZilongOld = oldzolong
			RecordProduct.UpdateDate = time.Now()
			RecordProduct.CreateDate = time.Now()
			RecordProduct.Upc = in.Product.BarCode
			RecordProduct.RecordTime = time.Now()
			userInfo := loadLoginUserInfo(ctx)
			if userInfo != nil {
				RecordProduct.UserName = userInfo.UserName
				RecordProduct.UserNo = userInfo.UserNo
			}
			_, err = Engine.Insert(&RecordProduct)
			if err != nil {
				glog.Error("插入平台修改货号日志报错", err.Error(), RecordProduct)
			}

			//判断更改的货号,并删除对就的仓库
			var eType int
			if olda8 != newa8 && oldzolong != newzolong {
				eType = 3
			} else if olda8 != newa8 {
				eType = 1
			} else if oldzolong != newzolong {
				eType = 2
			}
			go c.GetUpProductsWarehouseIds(in.SkuInfo[0].SkuId, eType)
		}

	}()
	out.Code = 200
	return out, nil
}

// 编辑组合商品
func updateGroupProduct(session *xorm.Session, productId, skuId int32, skuGroupInfo []*pc.SkuGroup) (isGroup, isDrugs, isPrescribedDrug, price int, err error) {
	var proIdList []int32
	var skuGroupMap = make(map[int32]*pc.SkuGroup)
	for _, v := range skuGroupInfo {
		proIdList = append(proIdList, v.GroupProductId)
		skuGroupMap[v.GroupSkuId] = v
	}
	var productList []models.Product
	err = session.Where("is_del = 0").In("id", proIdList).Find(&productList)
	if err != nil {
		glog.Error(err)
		return
	}
	var skuList []models.Sku
	err = session.In("product_id", proIdList).Find(&skuList)
	if err != nil {
		glog.Error(err)
		return
	}
	if len(utils.NewSet(proIdList...).List()) != len(productList) {
		return 0, 0, 0, 0, errors.New("组合商品必须全部可用")
	}
	var skuMap = make(map[int32]models.Sku)
	for _, sku := range skuList {
		skuMap[sku.Id] = sku
	}

	proMap := make(map[int]models.Product, 0)
	for _, pro := range productList {
		proMap[pro.Id] = pro
	}

	var groupMap = make(map[int]int)
	for _, v := range skuGroupInfo {
		if pro, ok := proMap[int(v.GroupProductId)]; ok {
			if pro.IsDrugs == 1 {
				isDrugs = 1
			}
			if pro.IsPrescribedDrug == 1 {
				isPrescribedDrug = 1
			}
			groupMap[pro.ProductType] = 1
			// 写入组合商品表
			skuGroup := models.SkuGroup{}
			skuGroup.ProductId = int(productId)
			skuGroup.SkuId = int(skuId)
			skuGroup.GroupProductId = pro.Id
			skuGroup.GroupSkuId = int(skuMap[int32(v.GroupSkuId)].Id)
			skuGroup.Count = int(skuGroupMap[int32(v.GroupSkuId)].Count)
			skuGroup.DiscountType = int(skuGroupMap[int32(v.GroupSkuId)].DiscountType)
			skuGroup.DiscountValue = int(skuGroupMap[int32(v.GroupSkuId)].DiscountValue)
			skuGroup.MarketPrice = int(skuMap[int32(v.GroupSkuId)].MarketPrice)
			if pro.ProductType == 2 {
				skuGroup.MarketPrice = int(skuMap[int32(v.GroupSkuId)].RetailPrice)
			}

			skuGroup.ProductType = pro.ProductType
			if skuGroup.DiscountType == 1 {
				//price = price + int(math.Floor((float64(skuGroup.DiscountValue)/100)*float64(skuGroup.MarketPrice) + 0.5))*skuGroup.Count
				price = 500000
			} else {
				price = price + skuGroup.DiscountValue*skuGroup.Count
			}
			if _, err = session.Insert(&skuGroup); err != nil {
				glog.Error(err)
				return
			}
			if _, err = session.Table("gj_sku_group").Insert(&skuGroup); err != nil {
				glog.Error(err)
				return
			}

		}

	}

	// 分析组合类型,只有一个key时为实实/虚虚组合，两个key时为虚实组合
	if len(groupMap) == 1 {
		for k, _ := range groupMap {
			isGroup = k
		}
	} else {
		isGroup = 3
	}
	if price <= 0 {
		session.Rollback()
		return 0, 0, 0, 0, errors.New("组合商品价格过低")
	}
	return
}

// 启用/停用或删除/恢复商品
func (c *Product) DelProduct(ctx context.Context, in *pc.ArrayIntValue) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	redisHandle := GetRedisConn()
	if !kit.EnvCanCron() {
		defer redisHandle.Close()
	}

	Engine := NewDbConn()
	session := Engine.NewSession()
	defer session.Close()
	session.Begin()

	for _, id := range in.Value {
		//查询商品是否已经被删除，做恢复删除操作
		product := models.Product{}
		has := false
		var err error
		if has, err = Engine.Table("product").Where("id = ?", id).Get(&product); err != nil {
			glog.Error(err)
			return out, err
		} else if has && product.IsDel == 1 {
			if _, err := session.ID(id).Cols("is_del").Update(&models.Product{IsDel: 0}); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			if _, err := session.ID(id).Cols("is_del").Update(&models.GjProduct{IsDel: 0}); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			if product.ProductType == 3 {
				if _, err := session.Table("gj_product").ID(id).Cols("is_del").Update(&models.Product{IsDel: 0}); err != nil {
					glog.Error(err)
					session.Rollback()
					return out, err
				}
				if _, err := session.Table("channel_product").ID(id).Cols("is_del").Update(&models.Product{IsDel: 0}); err != nil {
					glog.Error(err)
					session.Rollback()
					return out, err
				}
			}
			continue
		}

		RecordProduct := models.RecordProduct{}
		if has {
			RecordProduct.ProductName = product.Name
			RecordProduct.Upc = product.BarCode
		}

		RecordProduct.ProductId = int(id)
		RecordProduct.RecordType = 2
		skuMode := models.SkuThird{}
		isok, _ := session.Where("product_id=?", id).Get(&skuMode)
		if isok {
			RecordProduct.SkuId = int(skuMode.SkuId)
		}

		RecordProduct.UpdateDate = time.Now()
		RecordProduct.CreateDate = time.Now()
		RecordProduct.RecordTime = time.Now()
		userInfo := loadLoginUserInfo(ctx)
		if userInfo != nil {
			RecordProduct.UserName = userInfo.UserName
			RecordProduct.UserNo = userInfo.UserNo
		}
		_, err = session.Insert(&RecordProduct)
		if err != nil {
			glog.Error("插入平台删除日志报错", err.Error(), RecordProduct)
		}

		// todo 停用商品下架到第三方
		products := make([]models.ChannelStoreProduct, 0)
		session.Table("channel_store_product").Where("product_id = ? ", id).And("up_down_state = ?", 1).In("channel_id", 2, 3, 4).Find(&products)

		// 将上架商品下架
		_, err = session.Table("channel_store_product").Where("product_id = ?", id).
			In("channel_id", 1, 2, 3, 4).Cols("up_down_state").
			Update(&models.ChannelStoreProduct{UpDownState: 0})
		if err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}
		// 把有无库存表更新成下架
		_, err = session.Table("channel_store_product_has_stock").Where("product_id = ?", id).
			In("channel_id", 1, 2, 3, 4).Cols("has_stock_up").
			Update(&models.ChannelStoreProductHasStock{HasStockUp: 0})
		if err != nil {
			glog.Error(err)
			session.Rollback()
			return out, err
		}

		// 非组合商品
		if product.ProductType != 3 {

			count, err := Engine.Table("sku_group").Select("group_product_id").Where("group_product_id = ?", product.Id).Count()
			if err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			}
			if count > 0 {
				out.Message = "该商品已存在组合商品内，无法删除"
				session.Rollback()
				return out, err
			}
			isContinue := 0
			//删除主库，如果已使用的商品，做软删除
			if r, err := Engine.Table("product").Cols("is_del").Where("id=? AND is_use=1", id).Update(&models.Product{IsDel: 1}); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			} else if r == 1 {
				if r, err := Engine.Table("gj_product").Cols("is_del").Where("id=? AND is_use=1", id).Update(&models.GjProduct{IsDel: 1}); err != nil {
					glog.Error(err)
					session.Rollback()
					return out, err
				} else if r == 1 {
					isContinue = 1
				}
			}
			if isContinue == 0 {
				//如果不存在已使用商品，则删除
				if _, err := session.Delete(&models.Product{Id: int(id)}); err != nil {
					session.Rollback()
					return out, err
				}

				//删除商品相关属性
				if _, err := session.Exec("DELETE FROM product_attr WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}

				//删除商品SKU
				if _, err := session.Exec("DELETE FROM sku WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}

				//删除组合商品
				if _, err := session.Exec("DELETE FROM sku_group WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}

				//删除关联的第三方SKU货号
				if _, err := session.Exec("DELETE FROM sku_third WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}

				//删除SKU规格值
				if _, err := session.Exec("DELETE FROM sku_value WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}

				if _, err := session.Exec("DELETE FROM gj_product WHERE id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}
				if _, err := session.Exec("DELETE FROM gj_product_attr WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}

				//删除商品SKU
				if _, err := session.Exec("DELETE FROM gj_sku WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}

				//删除组合商品
				if _, err := session.Exec("DELETE FROM gj_sku_group WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}

				//删除关联的第三方SKU货号
				if _, err := session.Exec("DELETE FROM gj_sku_third WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}

				//删除SKU规格值
				if _, err := session.Exec("DELETE FROM gj_sku_value WHERE product_id = ?", id); err != nil {
					session.Rollback()
					return out, err
				}
			}
		} else {
			// 停用平台与管家的组合商品
			if r, err := session.Table("product").Cols("is_del").Where("id=?", id).Update(&models.Product{IsDel: 1}); err != nil {
				glog.Error(err)
				session.Rollback()
				return out, err
			} else if r == 1 {
				_, err = session.Table("gj_product").Cols("is_del").Where("id=?", id).Update(&models.Product{IsDel: 1})
				if err != nil {
					glog.Error(err)
					session.Rollback()
					return out, err
				}
				_, err = session.Table("channel_product").Cols("is_del").Where("id=?", id).Update(&models.Product{IsDel: 1})
				if err != nil {
					glog.Error(err)
					session.Rollback()
					return out, err
				}
			}

			//// 将已上架的组合商品下架
			//_, err := session.Table("channel_store_product").Where("product_id = ?", id).
			//	In("channel_id", 1, 2, 3, 4).Cols("up_down_state").
			//	Update(&models.ChannelStoreProduct{UpDownState: 0})
			//if err != nil {
			//	glog.Error(err)
			//	session.Rollback()
			//	return out, err
			//}
			//// 把有无库存表更新成下架
			//_, err = session.Table("channel_store_product_has_stock").Where("product_id = ?", id).
			//	In("channel_id", 1, 2, 3, 4).Cols("has_stock_up").
			//	Update(&models.ChannelStoreProductHasStock{HasStockUp: 0})
			//if err != nil {
			//	glog.Error(err)
			//	session.Rollback()
			//	return out, err
			//}
		}

		//  停用商品下架到第三方
		for _, v := range products {
			switch v.ChannelId {
			case ChannelMtId:

				clientMt := et.GetExternalClient()
				defer clientMt.Close()
				//通过门店财务编码查询渠道门店id
				appPoiCodeMap := GetAppPoiCodeByFinanceCode([]string{v.FinanceCode}, 2)
				appPoiCodeSlice := []string{}
				for k := range appPoiCodeMap {
					if len(k) > 0 {
						appPoiCodeSlice = append(appPoiCodeSlice, k)
					}
				}
				if len(appPoiCodeSlice) <= 0 {
					continue
				}
				storeProduct := ChannelProductUpDown{}
				up_storeProduct := ChannelProductUp_StoreProduct{}
				// 调用远程更新状态
				up_storeProduct.StoreFinanceCode = v.FinanceCode
				up_storeProduct.ProductId = cast.ToString(v.ProductId)
				up_storeProduct.StoreChannelFinanceCodes = appPoiCodeSlice[0]
				glog.Info("下架mt参数", up_storeProduct)
				err := storeProduct.downProductToMt(&up_storeProduct, clientMt)
				if err != nil {
					glog.Info("停用商品下架到第三方，下架美团失败", err.Error())
				}
			case ChannelElmId:
				storeProduct := ChannelProductUpDown{}

				var grpcClient = et.GetExternalClient()
				defer grpcClient.Close()
				up_storeProduct := ChannelProductUp_StoreProduct{}
				up_storeProduct.StoreFinanceCode = v.FinanceCode
				up_storeProduct.ProductSkuId = cast.ToString(v.SkuId)

				var StoreMap = make(map[string]*dac.StoreInfo) // 门店与渠道平台对应关系
				datacenterGrpc := GetDataCenterClient()
				defer datacenterGrpc.Close()
				res, err := datacenterGrpc.RPC.QueryStoreInfo(datacenterGrpc.Ctx, &dac.StoreInfoRequest{ChannelId: int32(v.ChannelId), FinanceCode: []string{v.FinanceCode}})
				if err != nil {
					glog.Error(err)
				} else {
					for _, detail := range res.Details {
						StoreMap[detail.FinanceCode] = detail
					}
				}
				channelStore, isChannelStore := StoreMap[v.FinanceCode] // 第三方店铺Id

				if isChannelStore {
					up_storeProduct.StoreChannelFinanceCodes = channelStore.ChannelStoreId
				}
				glog.Info("下架ele参数", up_storeProduct)
				err = storeProduct.downProductToElm(&up_storeProduct, grpcClient)
				if err != nil {
					glog.Info("停用商品下架到第三方，下架ele失败", err.Error())
				}
			case ChannelJddjId:

				var grpcClient = et.GetExternalClient()
				defer grpcClient.Close()
				storeProduct := ChannelProductUpDown{}
				up_storeProduct := ChannelProductUp_StoreProduct{}
				up_storeProduct.StoreFinanceCode = v.FinanceCode
				up_storeProduct.ProductSkuId = cast.ToString(v.SkuId)
				var StoreMap = make(map[string]*dac.StoreInfo) // 门店与渠道平台对应关系
				datacenterGrpc := GetDataCenterClient()
				defer datacenterGrpc.Close()
				res, err := datacenterGrpc.RPC.QueryStoreInfo(datacenterGrpc.Ctx, &dac.StoreInfoRequest{ChannelId: int32(v.ChannelId), FinanceCode: []string{v.FinanceCode}})
				if err != nil {
					glog.Error(err)
				} else {
					for _, detail := range res.Details {
						StoreMap[detail.FinanceCode] = detail
					}
				}
				channelStore, isChannelStore := StoreMap[v.FinanceCode] // 第三方店铺Id

				if isChannelStore {
					up_storeProduct.StoreChannelFinanceCodes = channelStore.ChannelStoreId
				}
				glog.Info("下架jddj参数", up_storeProduct)
				err = storeProduct.downProductToJd(&up_storeProduct, grpcClient)
				if err != nil {
					glog.Info("停用商品下架到第三方，下架ele失败", err.Error())
				}
			}
		}
	}

	if err := session.Commit(); err != nil {
		session.Rollback()
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 查询商品及SKU信息
func (c *Product) QueryProductSku(ctx context.Context, in *pc.OneofIdRequest) (*pc.ProductResponse, error) {
	out, err := c.QueryProductOnly(ctx, in)
	if err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	} else {
		for _, v := range out.Details {
			//sku
			if res2, err := c.QuerySku(ctx, &wrappers.Int32Value{Value: v.Id}); err != nil {
				glog.Error(err)
				out.Code = 400
				return out, err
			} else {
				v.Sku = res2.Details
			}
			//商品属性
			if res2, err := c.QueryProductAttr(ctx, &pc.IdRequest{Id: strconv.Itoa(int(v.Id))}); err != nil {
				glog.Error(err)
				out.Code = 400
				return out, err
			} else {
				v.Attr = res2.Details
			}
		}
	}
	out.Code = 200
	return out, nil
}

// 查询单个商品主体信息
func (c *Product) QueryProductOnly(ctx context.Context, in *pc.OneofIdRequest) (*pc.ProductResponse, error) {
	out := new(pc.ProductResponse)
	out.Code = 400

	productId := in.GetProductId().GetValue()

	//如果传的是skuid，先查出商品id，再查商品
	Engine := NewDbConn()
	if len(in.GetSkuId().GetValue()) > 0 {
		if err := Engine.Table("sku").In("id", in.GetSkuId().GetValue()).Select("product_id").Find(&productId); err != nil {
			glog.Error(err)
			return out, err
		}
	}

	var products []*models.Product

	if err := Engine.Table("product").In("id", productId).Find(&products); err != nil {
		glog.Error(err)
		return out, err
	}
	if len(products) > 0 {
		out.Details = make([]*pc.Product, len(products))
		for i, product := range products {
			out.Details[i] = &pc.Product{
				Id:                   int32(product.Id),
				CategoryId:           int32(product.CategoryId),
				BrandId:              int32(product.BrandId),
				Name:                 product.Name,
				Code:                 product.Code,
				BarCode:              product.BarCode,
				CreateDate:           product.CreateDate.Format(kit.DATETIME_LAYOUT),
				UpdateDate:           product.UpdateDate.Format(kit.DATETIME_LAYOUT),
				IsDel:                int32(product.IsDel),
				IsGroup:              int32(product.IsGroup),
				Pic:                  product.Pic,
				SellingPoint:         product.SellingPoint,
				Video:                product.Video,
				ContentPc:            product.ContentPc,
				ContentMobile:        product.ContentMobile,
				IsDiscount:           int32(product.IsDiscount),
				ProductType:          int32(product.ProductType),
				IsUse:                int32(product.IsUse),
				CategoryName:         product.CategoryName,
				ChannelId:            product.ChannelId,
				IsDrugs:              int32(product.IsDrugs),
				UseRange:             product.UseRange,
				TermType:             int32(product.TermType),
				TermValue:            int32(product.TermValue),
				GroupType:            int32(product.GroupType),
				VirtualInvalidRefund: int32(product.VirtualInvalidRefund),
				WarehouseType:        int32(product.WarehouseType),
				IsIntelGoods:         int32(product.IsIntelGoods),
				Disabled:             int32(product.Disabled),
				FromOms:              int32(product.FromOms),
				SourceType:           int32(product.SourceType),
				ShortName:            product.ShortName,
				DosingDays:           int32(product.DosingDays),
				DrugDosage:           product.DrugDosage,
				IsPrescribedDrug:     int32(product.IsPrescribedDrug),
				Disease:              product.Disease,
			}
		}
	}
	out.Code = 200
	return out, nil
}

// 导出商品库渠道商品列表（渠道）
func (c *Product) QueryChannelProductExport(ctx context.Context, in *pc.QueryProductExportRequest) (*pc.ChannelProductExportResponse, error) {

	glog.Info("QueryChannelProductExport导出请求参数：", in)

	fmt.Println("QueryChannelProductExport导出请求参数：", in)
	fmt.Println("QueryChannelProductExport--UpDownState请求参数：", in.UpDownState)
	out := new(pc.ChannelProductExportResponse)
	out.Code = 400
	Engine := NewDbConn()
	session := Engine.NewSession()
	defer session.Close()

	session = session.Table("channel_product").Alias("cp").
		Join("left", []string{"channel_store_product", "csp"}, "cp.id = csp.product_id").      //关联门店
		Join("left", []string{"channel_product_snapshot", "cps"}, "csp.snapshot_id = cps.id"). //关联快照
		Select("cp.`id`, cp.`category_id`, cp.`brand_id`, cp.`name`, cp.`code`, cp.`bar_code`, cp.`create_date`, cp.`update_date`, cp.`is_del`, cp.`is_group`, `pic`, " +
			"cp.`selling_point`, cp.`video`, cp.`content_pc`, cp.`content_mobile`, cp.`is_discount`, cp.`product_type`, cp.`is_use`, cp.`channel_id`,cp.`channel_category_id`, " +
			"cp.`channel_tag_id`, cp.`channel_name`, cp.`last_edit_user`, cp.`channel_category_name`, cp.`category_name`," +
			"csp.`finance_code`,csp.`up_down_state`,csp.`snapshot_id`," + //门店信息
			"cps.`user_no`,cps.`json_data`") //快照信息

	session.Where("csp.finance_code is not null ").And("csp.finance_code <> ''").And("cps.user_no is not null").And("cps.user_no <> ''")
	if in.CategoryId != 0 {
		session.And("cp.category_id=?", in.CategoryId)
	}
	if in.BrandId != 0 {
		session.And("cp.brand_id=?", in.BrandId)
	}
	if in.ProductType != 0 {
		session.And("cp.product_type=?", in.ProductType)
	}
	if in.ChannelId != 0 {
		session.And("cp.channel_id=?", in.ChannelId)
	}

	if len(in.FinanceCode) != 0 {
		session.And("csp.finance_code IN('" + strings.Join(in.FinanceCode, "','") + "')")
	}

	switch in.WhereType {
	case "":
		if in.Where != "" {
			session.And("cp.name like ? OR cp.id = ? OR cp.code = ? OR cp.bar_code = ?", "%"+in.Where+"%", in.Where, in.Where, in.Where)
		}
		break
	case "name":
		session.And("cp.name like ?", "%"+in.Where+"%")
		break
	case "code", "id", "bar_code":
		session.And("cp."+in.WhereType+"=?", in.Where)
		break
	case "third_spu_sku_id":
		var productID []int
		if err := Engine.Table("channel_sku_third").Select("product_id").Where("third_spu_sku_id LIKE ?", in.Where+"%").Find(&productID); err != nil {
			glog.Error(err)
		}

		if err := Engine.Table("channel_sku_third").Select("product_id").Where("third_spu_sku_id LIKE ?", "%"+in.Where).Find(&productID); err != nil {
			glog.Error(err)
		}

		session.In("cp.id", productID)
		break
	default:
	}

	if err := session.Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("cp.id desc").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	//重组数据
	pid := []int32{}
	for _, v := range out.Details {
		pid = append(pid, v.Id)
	}

	var datas []*pc.ChannelProducExportModel
	res, _ := c.QuerySkuThird(ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: pid}}})
	if len(out.Details) > 0 && len(res.Details) > 0 {
		for _, v := range out.Details {
			for _, val := range res.Details {
				if v.Id == val.ProductId {
					v.SkuId = val.SkuId
					break
				}
			}
			datas = append(datas, v)
		}
		out.Details = datas
	}

	out.Code = 200
	return out, nil
}

// 商品库商品列表
func (c *Product) QueryProduct(ctx context.Context, in *pc.QueryProductRequest) (*pc.ProductResponse, error) {
	out := new(pc.ProductResponse)
	out.Code = 400

	Engine := NewDbConn()

	session, err := models.QueryProductListByReq(Engine, in)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}

	sessionCount := *session

	if rows, err := sessionCount.Count(); err != nil {
		glog.Error(err)
		return out, err
	} else if rows > 0 {
		out.TotalCount = int32(rows)

		if err := session.Select("`id`, `category_id`, `brand_id`, `name`,  `short_name`,`code`, `bar_code`, "+
			"`create_date`, `update_date`, `is_del`, `is_group`, `pic`, `selling_point`, `video`, `content_pc`, "+
			"`content_mobile`, `is_discount`, `product_type`, `is_use`, `channel_id`,`category_name`,is_drugs,is_prescribed_drug, "+
			"`use_range`, `group_type`, `term_type`, `term_value`, `virtual_invalid_refund`,`is_intel_goods`,`disabled`,`from_oms`,`source_type`").
			Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
			OrderBy("id desc").
			Find(&out.Details); err != nil {
			glog.Error(err)
			return out, err
		}
		pID := make([]int32, 0)
		for _, v := range out.Details {
			pID = append(pID, v.Id)
		}
		skuIDMap := GetSkuIDByPID(Engine, pID)
		for _, v := range out.Details {
			var eleSku pc.Sku
			eleSku.Id = skuIDMap[v.Id]
			v.Sku = append(v.Sku, &eleSku)
		}
	}

	out.Code = 200
	return out, nil
}

// sku商品列表
func (c *Product) ListProductSku(ctx context.Context, in *pc.ListProductSkuRequest) (*pc.ListProductSkuResponse, error) {
	out := new(pc.ListProductSkuResponse)
	out.Code = http.StatusBadRequest

	Engine := NewDbConn()
	session := Engine.NewSession()
	defer session.Close()

	ProductTable := "product"
	SkuTable := "sku"
	skuThirdTable := "sku_third"
	session = session.Table(SkuTable).Alias("s").Join("inner", ProductTable+" p", "s.product_id=p.id").Where("p.is_del = 0")
	if in.IsGroup == 0 {
		session.And("p.product_type in (1, 2)")
	} else if in.IsGroup == 1 {
		session.And("p.product_type = 3")
	}

	if in.IsExclude == 1 {
		session.And("(p.product_type = 2 and p.term_type=1 AND  FROM_UNIXTIME(p.term_value)>NOW()) or (p.term_type != 1)")
	}

	switch in.WhereType {
	case "":
		if in.Where != "" {
			session.And("p.name like ? OR p.id = ? OR p.code = ? OR p.bar_code = ?", "%"+in.Where+"%", in.Where, in.Where, in.Where)
		}
	case "name":
		session.And("p."+in.WhereType+" like ?", "%"+in.Where+"%")
	case "code", "id", "bar_code":
		if len(in.Where) > 0 {
			session.And("p."+in.WhereType+"=?", in.Where)
		}
	case "third_spu_sku_id":
		if len(in.Where) > 0 {
			var productID []int
			if err := Engine.Table(skuThirdTable).Select("product_id").Where("third_spu_sku_id LIKE ?", in.Where+"%").Find(&productID); err != nil {
				glog.Error(err)
			}

			if err := Engine.Table(skuThirdTable).Select("product_id").Where("third_spu_sku_id LIKE ?", "%"+in.Where).Find(&productID); err != nil {
				glog.Error(err)
			}

			session.In("p.id", productID)
		}
	case "sku_id":
		if len(in.Where) > 0 {
			session.Where("s.id=?", in.Where)
		}
	case "is_drugs":
		session.In("p.is_drugs=?", in.Where)
	default:
		out.Message = "查询条件类型有误"
		return out, nil
	}

	sessionCount := session.Clone()
	defer sessionCount.Close()

	rows, err := sessionCount.Count()
	if err != nil {
		glog.Error(err)
		return out, err
	}
	// 如果查询不到数据直接return
	if rows == 0 {
		out.Code = http.StatusOK
		return out, nil
	}

	out.TotalCount = int32(rows)

	var list []productSkuRow
	selectFields := []string{
		`p.id`,
		`p.category_id`,
		`p.brand_id`,
		`p.channel_id`,
		`p.name`,
		`p.code`,
		`p.product_type`,
		`s.id sku_id`,
		`p.term_type`,
		`p.term_value`,
	}
	if err := session.Select(strings.Join(selectFields, ",")).
		Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
		OrderBy("p.id desc,s.id asc").
		Find(&list); err != nil {
		glog.Errorf("ListProductSku 查询sku商品列表异常,err:%+v", err)
		return out, err
	}

	skuIds := make([]int32, 0, len(list))
	for _, v := range list {
		skuIds = append(skuIds, v.SkuId)
	}
	// 获取sku规格信息
	specMap, err := querySkuSpecMap(skuIds...)
	if err != nil {
		glog.Errorf("ListProductSku 商品规格信息异常,err:%+v", err)
		return out, err
	}

	// 拼接响应数据
	for _, v := range list {
		item := &pc.ProductSku{
			Id:          v.Id,
			SkuId:       v.SkuId,
			CategoryId:  v.CategoryId,
			Name:        v.Name,
			Code:        v.Code,
			ProductType: v.ProductType,
			ChannelId:   v.ChannelId,
			TermType:    v.TermType,
			TermValue:   v.TermValue,
		}
		for _, spec := range specMap[v.SkuId] {
			item.SpecList = append(item.SpecList, &pc.SpecValue{
				Id:     spec.Id,
				SpecId: spec.SpecId,
				Name:   spec.SpecName,
				Value:  spec.SpecValueValue,
			})
		}
		out.Details = append(out.Details, item)
	}

	out.Code = http.StatusOK
	return out, nil
}

type productSkuRow struct {
	Id int32
	//分类id
	CategoryId int32
	//品牌id
	BrandId int32
	//商品名称
	Name string
	//商品编号
	Code string
	//商品类别（1-实物商品，2-虚拟商品, 3-组合商品）
	ProductType int32
	//商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
	//渠道id
	ChannelId string
	SkuId     int32
	TermType  int32
	TermValue int32
}

// 根据商品ID查询第三方货号信息 全渠道用
func (c *Product) QuerySkuIddBySkuThird(ctx context.Context, in *pc.NewSkuThirdRequest) (*pc.NewSkuThirdResponse, error) {
	out := new(pc.NewSkuThirdResponse)

	Engine := NewDbConn()
	par := models.SkuThird{}
	_, err := Engine.Where("third_sku_id=? and erp_id=?", in.Sku_Third, in.ErpId).Get(&par)

	if err != nil {
		return nil, err
	}
	out.SpuId = fmt.Sprintf("%d", par.ProductId)
	out.SkuId = fmt.Sprintf("%d", par.SkuId)
	return out, nil
}

// 根据SKUID和ERPID查询第三方货号 全渠道用
func (c *Product) QuerySkuThirdQQd(ctx context.Context, in *pc.NewSkuThirdResponse) (*pc.NewSkuThirdRequest, error) {
	out := new(pc.NewSkuThirdRequest)

	Engine := NewDbConn()
	par := models.SkuThird{}
	_, err := Engine.Where("sku_id=? and erp_id=?", in.SkuId, in.ErpId).Get(&par)

	if err != nil {
		return nil, err
	}
	out.Sku_Third = fmt.Sprintf("%s", par.ThirdSkuId)
	return out, nil
}

// 根据商品ID查询第三方货号信息
func (c *Product) QuerySkuThirdList(ctx context.Context, in *pc.SkuThirdListRequest) (*pc.SkuThirdListResponse, error) {
	out := new(pc.SkuThirdListResponse)
	out.Code = 400

	Engine := NewDbConn()
	session := Engine.Table("sku_third").Join("INNER", "product", "sku_third.product_id=product.id").Select(
		"sku_third.`id`, sku_third.`sku_id`, sku_third.`third_sku_id`,sku_third.`product_id`,product.`name` as productname,product.`bar_code`").Where("1=1")
	countSession := Engine.Table("sku_third").Join("INNER", "product", "sku_third.product_id=product.id").Select(
		"count(sku_third.`id`) totalcount").Where("1=1")
	if len(in.Systemid) > 0 {
		idsArry := strings.Split(in.Systemid, ",")

		var ids []int
		for _, v := range idsArry {
			id, err := strconv.Atoi(v)
			if err == nil {
				ids = append(ids, id)
			}
		}

		session.In("erp_id", ids)
		countSession.In("erp_id", ids)
	}

	if len(in.Thirdskuid) > 0 {
		session.And("sku_third.`third_sku_id` like '%" + in.Thirdskuid + "%'")
		countSession.And("sku_third.`third_sku_id` like '%" + in.Thirdskuid + "%'")
	}
	if len(in.Productname) > 0 {
		session.And("product.`name` like '%" + in.Productname + "%'")
		countSession.And("product.`name` like '%" + in.Productname + "%'")
	}
	if len(in.Startmodified) > 0 {
		session.And("product.`update_date` > " + in.Startmodified)
		countSession.And("product.`update_date` > " + in.Startmodified)
	}
	if len(in.Endmodified) > 0 {
		session.And("product.`update_date` <= " + in.Endmodified)
		countSession.And("product.`update_date` <= " + in.Endmodified)
	}

	//countSession := *session
	//skuthird := new(pc.SkuThirdProduct)

	totalcount := 0
	issucess, _ := countSession.Get(&totalcount)
	if issucess {
		out.TotalCount = int32(totalcount)
	}

	if err := session.Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("id desc").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 根据商品ID或者SKUID查询组合商品的组合信息
func (c *Product) QuerySkuGroup(ctx context.Context, in *pc.OneofIdRequest) (*pc.SkuGroupResponse, error) {
	out := new(pc.SkuGroupResponse)
	out.Code = 400

	if len(in.GetSkuId().GetValue()) == 0 && len(in.GetProductId().GetValue()) == 0 {
		out.Message = "sku_id与product_id必须填写一个"
		return out, nil
	}

	Engine := NewDbConn()
	session := Engine.Select("a.id, a.product_id, a.sku_id, a.group_product_id, a.group_sku_id, a.count, "+
		"a.discount_value, a.discount_type, a.market_price, b.name product_name, b.product_type, b.term_type, b.term_value ").
		Table("sku_group").Alias("a").
		Join("inner", "product as b", "b.Id = a.group_product_id")
	if len(in.GetSkuId().GetValue()) != 0 {
		session = session.In("a.sku_id", in.GetSkuId().GetValue())
	} else {
		session = session.In("a.product_id", in.GetProductId().GetValue())
	}

	if err := session.GroupBy("a.group_sku_id").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	// 设置组合商品规格信息
	err := setGroupSpecInfo(out.Details)
	if err != nil {
		glog.Errorf("Product/QuerySkuGroup 查询组合商品规格信息异常,err:%+v", err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

func setGroupSpecInfo(list []*pc.SkuGroup) error {
	skuIds := make([]int32, 0, len(list))
	for _, item := range list {
		skuIds = append(skuIds, item.GroupSkuId)
	}
	specMap, err := querySkuSpecMap(skuIds...)
	if err != nil {
		return err
	}
	for _, item := range list {
		item.SpecList = make([]*pc.SpecValue, 0, len(specMap[item.GroupSkuId]))
		for _, v := range specMap[item.GroupSkuId] {
			item.SpecList = append(item.SpecList, &pc.SpecValue{
				SpecId: v.SpecId,
				Name:   v.SpecName,
				Value:  v.SpecValueValue,
			})
		}
	}
	return nil
}

// 查询商品规格信息
func querySkuSpecMap(skuIds ...int32) (map[int32][]*pc.SkuValue, error) {
	if len(skuIds) == 0 {
		return nil, nil
	}
	var skuValueList []*pc.SkuValue
	engine := NewDbConn()
	err := engine.Table("sku_value").In("sku_id", skuIds).Select("`id`, `spec_id`, `spec_value_id`, `sku_id`, `product_id`, `pic`").OrderBy("id").Find(&skuValueList)

	if err != nil {
		return nil, err
	}

	if len(skuValueList) == 0 {
		return nil, nil
	}

	specIdMap := make(map[int32]struct{}, len(skuValueList))
	valueIdMap := make(map[int32]struct{}, len(skuValueList))
	for _, item := range skuValueList {
		specIdMap[item.SpecId] = struct{}{}
		valueIdMap[item.SpecValueId] = struct{}{}
	}

	specIds := make([]int32, 0, len(specIdMap))
	valueIds := make([]int32, 0, len(valueIdMap))
	for k := range specIdMap {
		specIds = append(specIds, k)
	}
	for k := range valueIdMap {
		valueIds = append(valueIds, k)
	}

	// 查找规格名称
	var specList []pc.Spec
	err = engine.Table("spec").In("id", specIds).Select("`id`, `name`, `sort`, `has_pic`, `category_id`").Find(&specList)
	if err != nil {
		return nil, err
	}
	specMap := make(map[int32]string, len(specList))
	for _, item := range specList {
		specMap[item.Id] = item.Name
	}

	// 查找规格值
	var specValueList []pc.SpecValue
	err = engine.Table("spec_value").In("id", valueIds).Select("`id`, `spec_id`, `value`").Find(&specValueList)
	if err != nil {
		return nil, err
	}
	specValueMap := make(map[int32]string, len(specValueList))
	for _, item := range specValueList {
		specValueMap[item.Id] = item.Value
	}

	// 拼装规格信息
	result := make(map[int32][]*pc.SkuValue, len(skuValueList))
	for _, item := range skuValueList {
		item.SpecName = specMap[item.SpecId]
		item.SpecValueValue = specValueMap[item.SpecValueId]
		result[item.SkuId] = append(result[item.SkuId], item)
	}
	return result, nil
}

// 根据商品ID查询SKU规格详情(系统默认)
func (c *Product) QuerySkuValue(ctx context.Context, in *pc.OneofIdRequest) (*pc.SkuValueResponse, error) {
	out := new(pc.SkuValueResponse)
	out.Code = 400

	if len(in.GetSkuId().GetValue()) == 0 && len(in.GetProductId().GetValue()) == 0 {
		out.Message = "sku_id与product_id必须填写一个"
		return out, nil
	}

	Engine := NewDbConn()
	session := Engine.Table("sku_value").Select("`id`, `spec_id`, `spec_value_id`, `sku_id`, `product_id`, `pic`")
	if len(in.GetSkuId().GetValue()) != 0 {
		session = session.In("sku_id", in.GetSkuId().GetValue())
	} else {
		session = session.In("product_id", in.GetProductId().GetValue())
	}

	if err := session.OrderBy("sort").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 根据商品ID查询SKU规格详情(系统默认)（渠道）
func (c *Product) QueryChannelSkuValue(ctx context.Context, in *pc.OneofIdRequest) (*pc.SkuValueResponse, error) {
	out := new(pc.SkuValueResponse)
	out.Code = 400

	session := NewDbConn().Table("channel_sku_value").Select("`id`, `spec_id`, `spec_value_id`, `sku_id`, `product_id`, `pic`").Where("product_id = ? AND channel_id = ?", in.GetProductId().GetValue()[0], in.ChannelId)
	if err := session.OrderBy("sort").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 根据商品ID查询SKU信息
func (c *Product) QuerySku(ctx context.Context, in *wrappers.Int32Value) (*pc.SkuResponse, error) {
	out := new(pc.SkuResponse)
	out.Code = 400

	Engine := NewDbConn()
	if err := Engine.Table("sku").Select("`id`, `product_id`, `market_price`, `retail_price`,`bar_code`, `weight_for_unit`").
		Where("`product_id`=?", in.Value).Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	//查询SKU组合的值信息
	var skuValue []*pc.SkuValue
	if res, err := c.QuerySkuValue(ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{out.Details[0].ProductId}}}}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		skuValue = res.Details
	}

	//第三方货号信息
	var skuThird []*pc.SkuThird
	if res, err := c.QuerySkuThird(ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{out.Details[0].ProductId}}}}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		skuThird = res.Details
	}

	// 组合商品信息
	var skuGroup []*pc.SkuGroup
	if res, err := c.QuerySkuGroup(ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{out.Details[0].ProductId}}}}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		skuGroup = res.Details
	}

	//获得specid,specvalueid
	var specID strings.Builder
	var specValueID strings.Builder
	for i, v := range skuValue {
		specID.WriteString(strconv.Itoa(int(v.SpecId)))
		specValueID.WriteString(strconv.Itoa(int(v.SpecValueId)))
		if i != len(skuValue)-1 {
			specID.WriteString(",")
			specValueID.WriteString(",")
		}
	}

	var spec map[int32]*pc.Spec
	var specValue map[int32]*pc.SpecValue
	if res, err := c.QuerySpecMap(ctx, &pc.IdRequest{Id: specID.String()}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		spec = res.Spec
	}
	if res, err := c.QuerySpecValueMap(ctx, &pc.IdRequest{Id: specValueID.String()}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		specValue = res.SpecValue
	}

	for _, v := range out.Details {
		//var skuValue *pc.SkuValue
		for _, s := range skuValue {
			if v.Id == s.SkuId {
				sv := *s
				sv.SpecName = spec[sv.SpecId].Name
				sv.SpecValueValue = specValue[sv.SpecValueId].Value
				v.SkuValue = append(v.SkuValue, &sv)
			}
		}

		for _, t := range skuThird {
			if v.Id == t.SkuId {
				v.SkuThird = append(v.SkuThird, t)
			}
		}

		for _, g := range skuGroup {
			if v.Id == g.SkuId {
				v.SkuGroup = append(v.SkuGroup, g)
			}
		}
	}

	out.Code = 200
	return out, nil
}

// 根据商品ID查询商品属性
func (c *Product) QueryProductAttr(ctx context.Context, in *pc.IdRequest) (*pc.ProductAttrResponse, error) {
	out := new(pc.ProductAttrResponse)
	out.Code = 400

	Engine := NewDbConn()
	if err := Engine.Table("product_attr").Select("`id`, `product_id`, `attr_id`, `attr_value_id`").Where("product_id=?", in.Id).Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 根据商品ID查询渠道商品属性
func (c *Product) QueryChannelProductAttr(ctx context.Context, in *pc.OneofIdRequest) (*pc.ChannelProductAttrResponse, error) {
	out := new(pc.ChannelProductAttrResponse)
	out.Code = 400

	if err := NewDbConn().Table("channel_product_attr").Select("`id`, `product_id`, `attr_id`, `attr_name`, `attr_value_id`, `attr_value`, `channel_id`").Where("product_id=? AND channel_id = ?", in.GetProductId().GetValue()[0], in.ChannelId).Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 新增/编辑分类归属
func (c *Product) NewCategoryAttr(ctx context.Context, in *pc.CategoryAttr) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	model := models.CategoryAttr{
		Name: in.Name,
	}

	Engine := NewDbConn()
	if in.Id == 0 {
		if _, err := Engine.Insert(&model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
	} else {
		if _, err := Engine.ID(in.Id).Update(&model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
	}

	return out, nil
}

// 删除分类归属
func (c *Product) DelCategoryAttr(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	ids := strings.Split(in.Id, ",")

	Engine := NewDbConn()
	if _, err := Engine.Exec("DELETE FROM category_attr WHERE id IN('" + strings.Join(ids, "','") + "')"); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 查询分类归属
func (c *Product) QueryCategoryAttr(ctx context.Context, in *pc.CategoryAttrRequest) (*pc.CategoryAttrResponse, error) {
	out := new(pc.CategoryAttrResponse)
	out.Code = 400

	Engine := NewDbConn()
	session := Engine.Table("category_attr").Where("1=1")
	if in.Where.Name != "" {
		session.And("name like ?", "%"+in.Where.Name+"%")
	}

	countSession := *session

	count, _ := countSession.Count()
	out.TotalCount = int32(count)

	if err := session.Select("`id`, `name`").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 新增/编辑分类导航
func (c *Product) NewCategoryNav(ctx context.Context, in *pc.CategoryNav) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200

	if in.CategoryId == 0 {
		out.Code = 400
		out.Message = "分类ID不能为空"
	}

	model := models.CategoryNav{
		CategoryId:  in.CategoryId,
		Alias:       in.Alias,
		Pic:         in.Pic,
		Categoryids: in.Categoryids,
		Brandids:    in.Brandids,
		Adv1:        in.Adv1,
		Adv2:        in.Adv2,
		Adv1Aim:     in.Adv1Aim,
		Adv2Aim:     in.Adv2Aim,
	}

	Engine := NewDbConn()
	count, _ := Engine.Table("category_nav").Where("category_id = ?", in.CategoryId).Count()

	if count == 0 {
		if _, err := Engine.Insert(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
	} else {
		if _, err := Engine.Where("category_id = ?", in.CategoryId).Update(model); err != nil {
			glog.Error(err)
			out.Code = 400
		}
	}

	return out, nil
}

// 查询分类导航
func (c *Product) QueryCategoryNav(ctx context.Context, in *pc.CategoryNavRequest) (*pc.CategoryNavResponse, error) {
	out := new(pc.CategoryNavResponse)
	out.Code = 200

	if in.Where.CategoryId == 0 {
		out.Message = "分类ID不允许为空！"
		out.Code = 400
		return out, nil
	}

	Engine := NewDbConn()
	if err := Engine.Table("category_nav").Where("category_id = ?", in.Where.CategoryId).Select("`category_id`, `alias`, `pic`, `categoryids`, `brandids`, `adv1`, `adv2`, `adv1_aim`, `adv2_aim`").Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}
	return out, nil
}

// 获取类别菜单列表
func (c *Product) GetCategoryListTree(ctx context.Context, in *pc.IdRequest) (*pc.CategoryListTreeResponse, error) {
	out := new(pc.CategoryListTreeResponse)
	out.Code = 200
	var model []*models.CategoryList
	//var tree
	//var list []*models.CategoryList
	Engine := NewDbConn()
	if err := Engine.Table("category").Select("`id`, `name`, `parent_id`,is_invented").Find(&model); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Details = GetChild(0, model)
	//fmt.Printf("%v", GetNavByParentId("10", model))
	return out, nil
}

func GetChild(pid int, list []*models.CategoryList) []*pc.CategoryListTree {
	var treeList []*pc.CategoryListTree
	for _, v := range list {
		if pid == v.ParentId {
			//fmt.Printf("%v\n", v.Id)
			child := GetChild(v.Id, list)
			node := &pc.CategoryListTree{
				Id:         int32(v.Id),
				Name:       v.Name,
				ParentId:   int32(v.ParentId),
				IsInvented: int32(v.IsInvented),
			}
			node.List = child
			treeList = append(treeList, node)
		}
	}
	return treeList
}

// 根据菜单ID获取菜单导航
func GetNavByParentId(id int, connector string, list []*models.CategoryList) string {
	str := ""
	for _, v := range list {
		//if id == v.Id && v.ParentId == "" {
		//	return v.Name
		//}
		if id == v.Id && v.ParentId > 0 {
			return strings.TrimLeft(GetNavByParentId(v.ParentId, connector, list)+connector+v.Name, connector)
		}
	}
	return str
}

// 判断是否存在子菜单
func HasSubMenu(id int, list []*models.CategoryList) int32 {
	//int i := 0;
	for _, v := range list {
		if id == v.ParentId {
			return 1
		}
	}
	return 0
}

// 图片、媒体库空间列表
func (c *Product) MediaClassList(ctx context.Context, in *pc.MediaClassListRequest) (*pc.MediaClassListResponse, error) {
	out := &pc.MediaClassListResponse{Code: 200}
	Engine := NewDbConn()
	session := Engine.Table("media_class").Select("media_class.aclass_id,media_class.apic_type,media_class.aclass_name,media_class.store_id,media_class.store_name,count(media_item.apic_id) as num").Join("left", "media_item", "media_class.aclass_id = media_item.aclass_id")
	session.Where("1=1")

	countsession := Engine.Table("media_class")
	countsession.Where("1=1")
	if in.KeywordType != 0 && in.Keyword != "" {
		switch in.KeywordType {
		case 1:
			session.And("media_class.aclass_id = ?", in.Keyword)
			countsession.And("aclass_id = ?", in.Keyword)
		case 2:
			session.And("media_class.aclass_name like ?", "%"+in.Keyword+"%")
			countsession.And("aclass_name = ?", "%"+in.Keyword+"%")
		case 3:
			session.And("media_class.store_id = ?", in.Keyword)
			countsession.And("store_id = ?", in.Keyword)
		}
	}

	if in.ApicType != 0 {
		session.And("media_class.apic_type = ?", in.ApicType)
		countsession.And("apic_type = ?", in.ApicType)
	}
	session.GroupBy("media_class.aclass_id")
	count, _ := countsession.Count()

	if in.PageSize == 0 {
		in.PageSize = 10
	}
	if in.PageIndex == 0 {
		in.PageIndex = 1
	}

	err := session.Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).Find(&out.MediaClass)
	if err != nil {
		out.Message = err.Error()
		out.Code = 400
		return out, nil
	}
	out.TotalCount = int32(count)
	return out, nil
}

// 图片、视频列表
func (c *Product) MediaItemList(ctx context.Context, in *pc.MediaItemListRequest) (*pc.MediaItemListResponse, error) {
	out := pc.MediaItemListResponse{Code: 200}
	if in.PageSize == 0 {
		in.PageSize = 10
	}
	if in.PageIndex == 0 {
		in.PageIndex = 1
	}
	Engine := NewDbConn()
	session := Engine.Select("apic_id,apic_name,apic_path,FROM_UNIXTIME(upload_time) upload_time,store_name,apic_size")
	session.Where("1=1")
	countSession := Engine.Table("media_item")
	countSession.Where("1=1")
	if in.AclassId != "" {
		session.And("aclass_id = ?", in.AclassId)
		countSession.And("aclass_id = ?", in.AclassId)
	}

	if in.MediaType != 0 {
		session.And("apic_type = ?", in.MediaType)
		countSession.And("apic_type = ?", in.MediaType)
	}
	err := session.Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).Find(&out.MediaItemClass)

	for i, i2 := range out.MediaItemClass {
		ApicSize, _ := strconv.ParseInt(i2.ApicSize, 10, 64)
		out.MediaItemClass[i].ApicSize = formatFileSize(ApicSize)
	}

	if err != nil {
		out.Message = err.Error()
		out.Code = 400
		return &out, nil
	}

	count, _ := countSession.Count()
	out.TotalCount = int32(count)
	return &out, nil
}

func formatFileSize(fileSize int64) (size string) {
	if fileSize < 1024 {
		//return strconv.FormatInt(fileSize, 10) +  "B"
		return fmt.Sprintf("%.2fB", float64(fileSize)/float64(1))
	} else if fileSize < (1024 * 1024) {
		return fmt.Sprintf("%.2fKB", float64(fileSize)/float64(1024))
	} else if fileSize < (1024 * 1024 * 1024) {
		return fmt.Sprintf("%.2fMB", float64(fileSize)/float64(1024*1024))
	} else if fileSize < (1024 * 1024 * 1024 * 1024) {
		return fmt.Sprintf("%.2fGB", float64(fileSize)/float64(1024*1024*1024))
	} else if fileSize < (1024 * 1024 * 1024 * 1024 * 1024) {
		return fmt.Sprintf("%.2fTB", float64(fileSize)/float64(1024*1024*1024*1024))
	} else { //if fileSize < (1024 * 1024 * 1024 * 1024 * 1024 * 1024)
		return fmt.Sprintf("%.2fEB", float64(fileSize)/float64(1024*1024*1024*1024*1024))
	}
}

// 媒体文件上传
func (c *Product) MediaUpload(ctx context.Context, in *pc.MediaUploadRequest) (*pc.MediaUploadResponse, error) {
	out := new(pc.MediaUploadResponse)
	out.Code = 200

	if in.ApicType == 0 {
		in.ApicType = 1
	}

	session := NewDbConn().NewSession()
	defer session.Close()
	session.Begin()

	mediaModel := models.MediaClass{}
	mediaDB := NewDbConn().Where("apic_type = ? and store_id = ?", in.ApicType, in.StoreId)
	if in.AclassId > 0 {
		mediaDB.Where("aclass_id = ? ", in.AclassId)
	}
	ok, err := mediaDB.Get(&mediaModel)
	if !ok {
		MediaClass := &models.MediaClass{
			ApicType:    in.ApicType,
			AclassName:  "默认相册",
			StoreId:     in.StoreId,
			StoreName:   in.StoreName,
			AclassCover: in.ApicPath,
			UploadTime:  int32(time.Now().Unix()),
			IsDefault:   1,
		}

		_, err := session.Insert(MediaClass)
		if err != nil {
			glog.Error(err)
			out.Code = 400
			session.Rollback()
			return out, err
		}

		session.Where("apic_type = ? and store_id = ?", in.ApicType, in.StoreId).OrderBy("aclass_id desc").Get(&mediaModel)

		mediaItemModel := models.MediaItem{
			ApicType:   mediaModel.ApicType,
			ApicName:   in.ApicName,
			AclassId:   mediaModel.AclassId,
			ApicPath:   in.ApicPath,
			ApicSize:   in.ApicSize,
			ApicSpec:   in.ApicSpec,
			StoreId:    in.StoreId,
			StoreName:  in.StoreName,
			UploadTime: int32(time.Now().Unix()),
		}
		_, err = session.Insert(mediaItemModel)
		if err != nil {
			glog.Error(err)
			out.Code = 400
			session.Rollback()
			return out, err
		}
	} else {
		mediaItemModel := models.MediaItem{
			ApicType:   mediaModel.ApicType,
			ApicName:   in.ApicName,
			AclassId:   mediaModel.AclassId,
			ApicPath:   in.ApicPath,
			ApicSize:   in.ApicSize,
			ApicSpec:   in.ApicSpec,
			StoreId:    mediaModel.StoreId,
			StoreName:  mediaModel.StoreName,
			UploadTime: int32(time.Now().Unix()),
		}
		_, err = session.Insert(mediaItemModel)
		if err != nil {
			glog.Error(err)
			out.Code = 400
			session.Rollback()
			return out, err
		}
	}

	if err = session.Commit(); err != nil {
		glog.Error(err)
		out.Code = 400
		session.Rollback()
		return out, err
	}
	out.Message = "ok"
	out.Url = in.ApicPath
	return out, err
}

// 图片、视频删除
func (c *Product) MediaItemDel(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	ids := strings.Split(in.Id, ",")
	if len(ids) == 0 {
		out.Code = 400
		out.Message = "id不能为空"
		return out, nil
	}
	Engine := NewDbConn()
	if _, err := Engine.Exec("DELETE FROM media_item WHERE apic_id IN('" + strings.Join(ids, "','") + "')"); err != nil {
		glog.Error(err)
		out.Code = 400
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 相册、视频库删除
func (c *Product) MediaClassDel(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	ids := strings.Split(in.Id, ",")
	if len(ids) == 0 {
		out.Code = 400
		out.Message = "id不能为空"
		return out, nil
	}

	if utils.IsValueInList("1", ids) || utils.IsValueInList("2", ids) {
		out.Code = 400
		out.Message = "默认库不可删除"
		return out, nil
	}

	Engine := NewDbConn()
	session := Engine.NewSession()
	defer session.Close()
	session.Begin()

	if _, err := Engine.Exec("DELETE FROM media_class WHERE aclass_id IN('" + strings.Join(ids, "','") + "')"); err != nil {
		glog.Error(err)
		out.Code = 400
		session.Rollback()
		return out, err
	}

	if _, err := Engine.Exec("DELETE FROM media_item WHERE aclass_id IN('" + strings.Join(ids, "','") + "')"); err != nil {
		glog.Error(err)
		out.Code = 400
		session.Rollback()
		return out, err
	}
	if err := session.Commit(); err != nil {
		glog.Error(err)
		out.Code = 400
		session.Rollback()
		return out, err
	}
	out.Code = 200
	return out, nil
}

func (c *Product) EditProductIsUse(session *xorm.Session, ctx context.Context, in *pc.ProductRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400
	//查出之前存储的渠道id
	p := new(models.Product)
	if _, err := session.SQL("select * from gj_product where id = ?", in.Product.Id).Get(p); err != nil {
		glog.Error(err)
		return out, err
	}
	//渠道id去重
	channelIdSlice := []string{}
	if len(p.ChannelId) > 0 {
		channelIdSlice = append(strings.Split(p.ChannelId, ","), strings.Split(in.Product.ChannelId, ",")...)
	} else {
		channelIdSlice = strings.Split(in.Product.ChannelId, ",")
	}
	channelId := strings.Join(utils.SliceUnique(channelIdSlice), ",")
	//平台商品库更改使用状态
	if _, err := session.Table("product").ID(in.Product.Id).Cols("is_use, channel_id").Update(&models.Product{IsUse: 1, ChannelId: channelId}); err != nil {
		glog.Error(err)
		return out, err
	}
	//管家商品库更改使用状态
	if _, err := session.Table("gj_product").ID(in.Product.Id).Cols("is_use, channel_id").Update(&models.Product{IsUse: 1, ChannelId: channelId}); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 处理分类名称
func (c *Product) getCategoryName(parentId int32, arr []*pc.Category) string {
	for _, v := range arr {
		if parentId == v.ParentId {
			c.categoryNames = append(c.categoryNames, v.Name)
			c.getCategoryName(v.Id, arr)
		}
	}
	return strings.Join(c.categoryNames, ">")
}

// 根据商品ID查询第三方货号信息
func (c *Product) QuerySkuThird(ctx context.Context, in *pc.OneofIdRequest) (*pc.SkuThirdResponse, error) {
	out := new(pc.SkuThirdResponse)
	out.Code = 400

	Engine := NewDbConn()
	session := Engine.Table("sku_third").Join("INNER", "erp", "sku_third.erp_id=erp.id").Select("sku_third.`id`, sku_third.`sku_id`, sku_third.`third_spu_id`,sku_third.`third_sku_id`, sku_third.`erp_id`, sku_third.`product_id`,erp.`name` as erp_name")
	if len(in.GetSkuId().GetValue()) > 0 {
		session.In("sku_third.sku_id", in.GetSkuId().GetValue())
	} else if len(in.GetProductId().GetValue()) > 0 {
		session.In("sku_third.product_id", in.GetProductId().GetValue())
	} else if len(in.GetErpId().GetValue()) > 0 {
		session.In("sku_third.erp_id", in.GetErpId().GetValue())
	}

	if err := session.Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

func (c *Product) GetThirdSpuSkuId(ctx context.Context, in *pc.ArrayIntValue) (*pc.ThirdSpuSkuIdResponse, error) {
	out := new(pc.ThirdSpuSkuIdResponse)
	out.Code = 400

	if res, err := c.QuerySkuThird(ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ErpId{ErpId: &pc.ArrayIntValue{Value: in.Value}}}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		out.Details = new(structpb.Struct)
		out.Details.Fields = make(map[string]*structpb.Value)
		for _, v := range res.Details {
			thirdSpuSkuId := fmt.Sprintf("%s@%s", v.ThirdSpuId, v.ThirdSkuId)
			thirdSpuSkuId = strings.Trim(thirdSpuSkuId, "@")
			out.Details.Fields[thirdSpuSkuId] = &structpb.Value{Kind: &structpb.Value_StringValue{StringValue: ""}}
		}
	}
	out.Code = 200
	return out, nil
}

// excel导入商品到商品库, 错误信息导入excel上传至七牛云
// updateType 默认0是导入实物商品 123是更新实物商品 4是导入虚拟商品
func (c *Product) ImportProduct(url string, updateType int) (*pc.ImportExcelProductResponse, error) {
	out := new(pc.ImportExcelProductResponse)
	out.Code = 400
	// 下载excel
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	//client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}

	var productErrList [][]string
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, _ := f.GetRows(sheetName)
	var virtualProductMap = make(map[string][][]string)
	var oneRow []string
	for i, row := range rows {
		if i == 0 {
			oneRow = row
			continue
		}
		// excel商品信息导入数据库
		var productErr string
		if updateType == 0 {
			row1 := make([]string, 21)
			copy(row1, row) // 保证长度
			row = row1
			productErr = insertExcelProduct(row)
		} else if updateType > 0 && updateType < 4 {
			row1 := make([]string, 24)
			copy(row1, row) // 保证长度
			row = row1
			productErr = insertExcelProductUpdate(row, updateType)
		} else if updateType == 4 {
			if len(row[3]) == 0 {
				productErr = importVirtualProduct([][]string{row})
			}
			virtualProductMap[row[3]] = append(virtualProductMap[row[3]], row)
		}

		if len(productErr) == 0 {
			out.SuccessNum = out.SuccessNum + 1
		} else {
			msgs := append(row, productErr)
			out.FailNum = out.FailNum + 1
			productErrList = append(productErrList, msgs)
		}
	}
	if len(virtualProductMap) > 0 {
		for _, rows := range virtualProductMap {
			if len(rows) == 0 {
				continue
			}
			productErr := importVirtualProduct(rows)
			if len(productErr) == 0 {
				out.SuccessNum = out.SuccessNum + 1
			} else {
				msgs := append(rows[0], productErr)
				out.FailNum = out.FailNum + 1
				productErrList = append(productErrList, msgs)
			}
		}
	}
	if out.FailNum > 0 {
		// 将处理失败的商品信息导入excel上传至七牛云
		oneRow = append(oneRow, "失败原因")
		errList := append([][]string{}, oneRow)
		errList = append(errList, productErrList...)

		url, err = ExportProductErr(errList)
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, nil
		}
		out.QiniuUrl = url
	}
	out.Code = 200
	return out, nil
}

// excel批量更新商品信息
func (c *Product) ImportProductUpdate(url string, updateType int) (*pc.ImportExcelProductResponse, error) {
	out := new(pc.ImportExcelProductResponse)
	out.Code = 400
	// 下载excel
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	//client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}

	var productErrList [][]string
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, _ := f.GetRows(sheetName)
	var oneRow []string
	for i, row := range rows {
		if i == 0 {
			oneRow = row
			continue
		}
		// excel商品信息更新
		productErr := insertExcelProduct(row)
		if len(productErr) == 0 {
			out.SuccessNum = out.SuccessNum + 1
		} else {
			msgs := append(row, productErr)
			out.FailNum = out.FailNum + 1
			productErrList = append(productErrList, msgs)
		}
	}
	if out.FailNum > 0 {
		// 将处理失败的商品信息导入excel上传至七牛云
		oneRow = append(oneRow, "失败原因")
		errList := append([][]string{}, oneRow)
		errList = append(errList, productErrList...)

		url, err = ExportProductErr(errList)
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, nil
		}
		out.QiniuUrl = url
	}
	out.Code = 200
	return out, nil
}

// excel更新商品信息
func insertExcelProductUpdate(row []string, updateType int) string {
	Db := NewDbConn()
	session := Db.NewSession()
	defer session.Close()
	session.Begin()
	// 错误信息
	var msg string
	skuModel := models.Sku{}
	productModel := models.Product{}
	/*if len(row) < 20 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}*/
	if len(row[17]) == 0 || cast.ToFloat32(row[17]) == 0 {
		msg = "市场价不能为空或为0"
		return msg
	}
	if len(row[19]) == 0 {
		return "商品条码不能为空"
	}
	//tempInt, err := cast.ToInt64E(row[19])
	tempInt, err := strconv.ParseFloat(row[19], 64)
	if err != nil {
		msg = "非法商品条码 " + err.Error()
		return msg
	}

	if len(row[19]) == 0 || tempInt == 0 {
		msg = "商品条码不能为空或为0"
		return msg
	}
	switch updateType { // 更新类型按照货号还是sku还是条形码
	case 1:
		if row[0] == "" {
			return "匹配项为空！"
		}
		session.Table("sku").Where("id = ?", row[0]).Get(&skuModel)
		session.Table("product").Where("id = ?", skuModel.ProductId).Get(&productModel)
	case 2:
		if row[1] == "" {
			return "匹配项为空！"
		}
		//session.Table("sku_third").Select("sku_third.*").Join("inner","sku","sku.id = sku_third.sku_id").Where("sku.bar_code = ?",row[1]).Get(&skuModel)
		session.Table("sku").Where("bar_code = ?", row[1]).Get(&skuModel)
		session.Table("product").Where("id = ?", skuModel.ProductId).Get(&productModel)
	case 3:
		if row[2] == "" && row[3] == "" {
			return "匹配项为空！"
		}
		session.Table("sku").Join("inner", "sku_third", "sku.id = sku_third.sku_id").Where("sku_third.third_sku_id = ? or sku_third.third_sku_id = ?", row[2], row[3]).Get(&skuModel)
		session.Table("product").Where("id = ?", skuModel.ProductId).Get(&productModel)
	}

	if skuModel.Id == 0 {
		return "商品未找到！"
	}

	//商品名称
	if row[4] != "" {
		_, err := session.Where("id = ?", skuModel.ProductId).Update(models.Product{Name: row[4]})
		if err != nil {
			return "商品名称更新错误！" + err.Error()
		}
	}

	//商品卖点
	if row[5] != "" {
		_, err := session.Where("id = ?", skuModel.ProductId).Update(models.Product{SellingPoint: row[5]})
		if err != nil {
			return "商品卖点更新错误！" + err.Error()
		}
	}

	//商品图片
	if row[6] != "" || row[7] != "" || row[8] != "" || row[9] != "" || row[10] != "" {
		pic := []string{row[6], row[7], row[8], row[9], row[10]}
		_, err := session.Where("id = ?", skuModel.ProductId).Update(models.Product{Pic: strings.Join(pic, ",")})
		if err != nil {
			return "商品图片平台更新错误！" + err.Error()
		}

		_, err = session.Where("id = ?", skuModel.ProductId).Update(models.GjProduct{Pic: strings.Join(pic, ",")})
		if err != nil {
			return "商品图片管家错误！" + err.Error()
		}
	}

	//视频地址
	if row[11] != "" {
		_, err := session.Where("id = ?", skuModel.ProductId).Update(models.Product{Video: row[11]})
		if err != nil {
			return "视频地址错误！" + err.Error()
		}
	}

	var typeId int
	_, err = session.Table("category").Select("type_id").Where("id = ?", productModel.CategoryId).Get(&typeId)
	if err != nil {
		return "获取分类类型错误！" + err.Error()
	}

	//商品品牌
	if row[12] != "" {
		brandModel := models.Brand{}
		has, err := session.Where("name = ?", row[12]).Get(&brandModel)
		if err != nil {
			return "品牌查询错误！"
		}

		BrandId := int(brandModel.Id)
		if has {
			_, err := session.Where("id = ?", skuModel.ProductId).Update(models.Product{BrandId: BrandId})
			if err != nil {
				return "品牌更新错误！" + err.Error()
			}
		} else {
			brandModel := models.Brand{}
			brandModel.Name = row[12]
			brandModel.ShowType = 1
			_, err := session.Insert(&brandModel)
			if err != nil {
				return "品牌新增错误！" + err.Error()
			}

			BrandId = int(brandModel.Id)
			_, err = session.Where("id = ?", skuModel.ProductId).Update(models.Product{BrandId: BrandId})
			if err != nil {
				return "品牌更新错误！" + err.Error()
			}
		}

		has, err = session.Where("type_id = ? and brand_id = ?", typeId, BrandId).Get(&models.TypeBrand{})
		if err != nil {
			return "品牌关联查询错误！"
		}

		if !has {
			_, err := session.Table("type_brand").Insert(models.TypeBrand{
				TypeId:  int32(typeId),
				BrandId: int32(BrandId),
			})
			if err != nil {
				return "品牌关联插入错误！" + err.Error()
			}
		}
	}

	//规格
	if row[13] != "" {
		//更新到平台
		specValueModel := models.SpecValue{}
		has, err := session.Where("value = ? and spec_id = 2", row[13]).Get(&specValueModel)
		if err != nil {
			return "规格查询错误！"
		}
		if !has {
			specValueModel.Value = row[13]
			specValueModel.SpecId = 2
			_, err := session.Insert(&specValueModel)
			if err != nil {
				return "规格插入错误！" + err.Error()
			}
		}

		//更新平台数据
		_, err = session.Where("sku_id = ? and spec_id = 2", skuModel.Id).Update(models.SkuValue{SpecValueId: specValueModel.Id})
		if err != nil {
			return "规格平台更新错误！" + err.Error()
		}

		//更新管家数据
		_, err = session.Where("sku_id = ? and spec_id = 2", skuModel.Id).Update(models.GjSkuValue{SpecValueId: int(specValueModel.Id)})
		if err != nil {
			return "规格管家更新错误！" + err.Error()
		}
	}

	//规格图片
	if row[14] != "" {
		_, err := session.Where("sku_id = ? and spec_id = 2", skuModel.Id).Update(models.SkuValue{Pic: row[14]})
		if err != nil {
			return "规格图片平台更新错误！" + err.Error()
		}

		/*_, err = session.Where("sku_id = ? and spec_id = 2", skuModel.Id).Update(models.GjSkuValue{Pic: row[14]})
		if err != nil {
			return "规格图片管家更新错误！" + err.Error()
		}*/
	}

	//A8货号
	if row[15] != "" {
		row[15] = strings.TrimSpace(row[15])
		//平台更新
		SkuThird := models.SkuThird{}
		has, err := session.Where("sku_id = ? and erp_id = 2", skuModel.Id).Get(&SkuThird)
		if err != nil {
			return "A8货号平台查询错误！"
		}

		if has {
			_, err := session.Where("id = ?", SkuThird.Id).Update(models.SkuThird{ThirdSkuId: row[15], ThirdSpuSkuId: "," + row[15]})
			if err != nil {
				return "A8货号平台更新错误！" + err.Error()
			}
		} else {
			_, err := session.Insert(models.SkuThird{
				SkuId:         int32(skuModel.Id),
				ThirdSkuId:    row[15],
				ThirdSpuId:    "",
				ErpId:         2,
				ProductId:     int32(skuModel.ProductId),
				ThirdSpuSkuId: "," + row[15],
			})
			if err != nil {
				return "A8货号平台插入错误！" + err.Error()
			}
		}

		//管家更新
		GjSkuThird := models.GjSkuThird{}
		has, err = session.Where("sku_id = ? and erp_id = 2", skuModel.Id).Get(&GjSkuThird)
		if err != nil {
			return "A8货号管家查询错误！"
		}

		if has {
			_, err := session.Where("id = ?", GjSkuThird.Id).Update(models.GjSkuThird{ThirdSkuId: row[15], ThirdSpuSkuId: "," + row[15]})
			if err != nil {
				return "A8货号管家更新错误！" + err.Error()
			}
		} else {
			_, err := session.Insert(models.GjSkuThird{
				SkuId:         int(skuModel.Id),
				ThirdSkuId:    row[15],
				ThirdSpuId:    "",
				ErpId:         2,
				ProductId:     int(skuModel.ProductId),
				ThirdSpuSkuId: "," + row[15],
			})
			if err != nil {
				return "A8货号管家插入错误！" + err.Error()
			}
		}
	}

	//子龙货号
	if row[16] != "" {
		row[16] = strings.TrimSpace(row[16])
		//平台更新
		SkuThird := models.SkuThird{}
		has, err := session.Where("sku_id = ? and erp_id = 4", skuModel.Id).Get(&SkuThird)
		if err != nil {
			return "子龙货号平台查询错误！"
		}

		var thirdChanged bool

		if has {
			_, err := session.Where("id = ?", SkuThird.Id).Update(models.SkuThird{ThirdSkuId: row[16], ThirdSpuSkuId: "," + row[16]})
			if err != nil {
				return "子龙货号平台更新错误！" + err.Error()
			}
			if SkuThird.ThirdSkuId != row[16] {
				thirdChanged = true
			}
		} else {
			_, err := session.Insert(models.SkuThird{
				SkuId:         int32(skuModel.Id),
				ThirdSkuId:    row[16],
				ThirdSpuId:    "",
				ErpId:         4,
				ProductId:     int32(skuModel.ProductId),
				ThirdSpuSkuId: "," + row[16],
			})
			if err != nil {
				return "子龙货号平台插入错误！" + err.Error()
			}
			thirdChanged = true
		}
		//更新价格同步表子龙的对应关系
		//旧的子龙货号，清空对应关系
		if has {
			if _, err := session.Table("price_sync").Where("zl_productid = ?", SkuThird.ThirdSkuId).Cols("product_id,sku").Update(&models.PriceSync{ProductId: 0, Sku: 0}); err != nil {
				return "子龙货号平台同步价格更新错误！" + err.Error()
			}
		}
		//新的子龙货号，如果已存在，则更新对应关系
		var newSkuThird models.SkuThird
		if _, err := session.Table("sku_third").Where("erp_id = ?", 4).And("sku_id=?", row[16]).Get(&newSkuThird); err != nil {
			return "子龙货号平台同步价格更新错误！" + err.Error()
		}
		if len(newSkuThird.ThirdSkuId) > 0 { //新的子龙货号之前被使用
			if _, err := session.Table("price_sync").Where("zl_productid = ?", row[16]).Cols("product_id,sku").Update(&models.PriceSync{ProductId: int(skuModel.ProductId), Sku: int(skuModel.Id)}); err != nil {
				return "子龙货号平台同步价格更新错误！" + err.Error()
			}
		}

		GjSkuThird := models.GjSkuThird{}
		has, err = session.Where("sku_id = ? and erp_id = 4", skuModel.Id).Get(&GjSkuThird)
		if err != nil {
			return "子龙货号查询错误！"
		}

		if has {
			_, err := session.Where("id = ?", GjSkuThird.Id).Update(models.GjSkuThird{ThirdSkuId: row[16], ThirdSpuSkuId: "," + row[16]})
			if err != nil {
				return "子龙货号更新错误！" + err.Error()
			}
		} else {
			_, err := session.Insert(models.GjSkuThird{
				SkuId:         int(skuModel.Id),
				ThirdSkuId:    row[16],
				ThirdSpuId:    "",
				ErpId:         4,
				ProductId:     int(skuModel.ProductId),
				ThirdSpuSkuId: "," + row[16],
			})
			if err != nil {
				return "子龙货号插入错误！" + err.Error()
			}
		}

		if thirdChanged {
			// 实物商品变更处方药属性
			etClient := et.GetExternalClient()
			if out, err := etClient.ZiLong.ProductList(etClient.Ctx, &et.ZiLongProductListReq{
				ProductCode: []string{strings.TrimSpace(row[16])},
			}); err != nil {
				return err.Error()
			} else if out.Code != 200 {
				return out.Message
			} else {
				product := new(models.Product)
				if len(out.Data) > 0 {
					if out.Data[0].CanSell == "0" {
						return "商品在子龙不可销"
					}
					if out.Data[0].IsPrescribedDrug == "1" {
						return "处方药属性不完整，无法更新，请人工更新"
					} else if out.Data[0].ProductType == "1001" {
						product.IsDrugs = 1
					} else {
						product.IsDrugs = 0
					}
				} else {
					return "子龙货号无效"
				}
				if _, err := session.Where("id = ?", skuModel.ProductId).Cols("is_drugs,dosing_days,disease,is_prescribed_drug,drug_dosage").Update(product); err != nil {
					return err.Error()
				}
				if _, err := session.Where("id = ?", skuModel.ProductId).Cols("is_drugs,is_prescribed_drug").Update(&models.GjProduct{
					IsPrescribedDrug: product.IsPrescribedDrug,
					IsDrugs:          product.IsDrugs,
				}); err != nil {
					return err.Error()
				}
			}
		}
	}

	//市场价
	if row[17] != "" {
		marketPrice, err := strconv.ParseFloat(row[17], 64)
		if err != nil {
			msg = "市场价格式错误, err: " + err.Error()
			return msg
		}
		decimalValue := decimal.NewFromFloat(marketPrice)
		decimalValue = decimalValue.Mul(decimal.NewFromInt(100))
		price, _ := decimalValue.Float64()
		_, err = session.Where("id = ?", skuModel.Id).Update(models.Sku{MarketPrice: int32(price)})
		if err != nil {
			return "市场价更新错误！" + err.Error()
		}
	}

	//建议零售价
	if row[18] != "" {
		retailPrice, err := strconv.ParseFloat(row[18], 64)
		if err != nil {
			msg = "建议零售价格式错误, err: " + err.Error()
			return msg
		}
		decimalValue := decimal.NewFromFloat(retailPrice)
		decimalValue = decimalValue.Mul(decimal.NewFromInt(100))
		price, _ := decimalValue.Float64()
		_, err = session.Where("id = ?", skuModel.Id).Update(models.Sku{RetailPrice: int32(price)})
		if err != nil {
			return "建议零售价更新错误！" + err.Error()
		}
	}

	//商品条码
	if row[19] != "" {
		_, err := session.Where("id = ?", skuModel.Id).Update(models.Sku{BarCode: row[19]})
		if err != nil {
			return "商品条码平台更新错误！" + err.Error()
		}

		_, err = session.Where("id = ?", skuModel.Id).Update(models.GjSku{BarCode: row[19]})
		if err != nil {
			return "商品条码管家更新错误！" + err.Error()
		}
	}

	//商品详情
	if row[20] != "" {
		_, err := session.Where("id = ?", skuModel.ProductId).Update(models.Product{ContentPc: row[20]})
		if err != nil {
			return "商品详情平台更新错误！" + err.Error()
		}

		_, err = session.Where("id = ?", skuModel.ProductId).Update(models.GjProduct{ContentPc: row[20]})
		if err != nil {
			return "商品详情管家更新错误！" + err.Error()
		}
	}

	// 虚拟商品有效期及是否支持过期退款
	if productModel.ProductType == 2 {
		if row[21] != "" {
			termList := strings.Split(row[21], "/")
			if len(termList) == 3 {
				stamp, err := time.ParseInLocation("2006/01/02 15:04:05", row[17]+" 23:59:59", time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
				if err != nil {
					glog.Error("虚拟商品过期时间格式错误, ", err)
					msg = "虚拟商品过期时间格式错误"
					return msg
				}
				productModel.TermType = 1
				productModel.TermValue = int(stamp.Unix())
			} else if cast.ToInt(row[21]) > 0 {
				productModel.TermType = 2
				productModel.TermValue = cast.ToInt(row[21])
			} else {
				msg = "虚拟商品过期时间格式不正确"
				return msg
			}
		}
		if strings.Contains(row[22], "是") {
			productModel.VirtualInvalidRefund = 1
		} else if strings.Contains(row[22], "否") {
			productModel.VirtualInvalidRefund = 0
		} else {
			productModel.VirtualInvalidRefund = 0
		}
	}

	if row[23] != "" {
		if row[23] == "是" {
			productModel.IsIntelGoods = 1
			_, err = session.Where("id = ?", skuModel.ProductId).Cols("is_intel_goods").Update(models.Product{IsIntelGoods: productModel.IsIntelGoods})
			_, err = session.Where("id = ?", skuModel.ProductId).Cols("is_intel_goods").Update(models.GjProduct{IsIntelGoods: productModel.IsIntelGoods})
		}
		if row[23] == "否" {
			productModel.IsIntelGoods = 0
			_, err = session.Where("id = ?", skuModel.ProductId).Cols("is_intel_goods").Update(models.Product{IsIntelGoods: productModel.IsIntelGoods})
			_, err = session.Where("id = ?", skuModel.ProductId).Cols("is_intel_goods").Update(models.GjProduct{IsIntelGoods: productModel.IsIntelGoods})
		}
	}

	session.Commit()
	return msg
}

// excel实物商品导入数据库
func insertExcelProduct(row []string) string {

	// 错误信息
	var msg string
	// 新增商品信息
	var pr pc.ProductRequest
	// 商品主表
	var product pc.Product
	if len(row) < 16 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}
	if len(row[1]) == 0 {
		msg = "商品名称不能为空"
		return msg
	}
	if len(row[13]) == 0 || cast.ToFloat32(row[13]) == 0 {
		msg = "市场价不能为空或为0"
		return msg
	}
	if len(row[15]) == 0 {
		msg = "商品条码不能为空"
		return msg
	}
	//tempInt, err := cast.ToInt64E(row[15])
	tempInt, err := strconv.ParseFloat(row[15], 64)
	if err != nil {
		msg = "非法商品条码:" + err.Error()
		return msg
	}
	if tempInt == 0 {
		msg = "商品条码不能为0"
		return msg
	}
	product.Name = row[1]
	// 商品类别
	var productType int32
	if len(row[0]) == 0 {
		msg = "商品类别不能为空"
		return msg
	}
	if strings.Contains(row[0], "实物商品") {
		productType = 1
	} else if strings.Contains(row[0], "虚拟商品") {
		productType = 2
	}
	product.ProductType = productType
	// 商品分类
	//var categoryList []string
	//if len(row[1]) > 0 {
	//	categoryList = append(categoryList, row[1])
	//}
	//if len(row[2]) > 0 {
	//	categoryList = append(categoryList, row[2])
	//}
	//if len(row[3]) > 0 {
	//	categoryList = append(categoryList, row[3])
	//}
	var categoryId int32
	categoryName := "未分类"
	//if len(categoryList) == 0 {
	//	msg = "商品分类不能为空"
	//	productErr = append(row, msg)
	//	return productErr
	//} else if len(categoryList) == 1 {
	//	_, err := NewDbConn().SQL("select id from category where name = ?", categoryList[0]).Get(&categoryId)
	//	if err != nil {
	//		msg = "查询分类id失败, err: " + err.Error()
	//		productErr = append(row, msg)
	//		return productErr
	//	}
	//	categoryName = categoryList[0]
	//} else if len(categoryList) > 1 {
	//	categoryTag := strings.Join(categoryList, ",")
	//	_, err := NewDbConn().SQL("select id from category where tag = ?", categoryTag).Get(&categoryId)
	//	if err != nil {
	//		msg = "查询分类id失败, err: " + err.Error()
	//		productErr = append(row, msg)
	//		return productErr
	//	}
	//	categoryName = strings.Join(categoryList, ">")
	//}
	_, err = NewDbConn().SQL("select max(id) from category where name = '未分类'").Get(&categoryId)
	if err != nil {
		msg = "查询分类id失败, err: " + err.Error()
		return msg
	}
	product.CategoryId = categoryId
	product.CategoryName = categoryName
	// 商品卖点
	product.SellingPoint = row[2]
	// 商品图片
	if len(row[3]) == 0 {
		msg = "主图片不能为空"
		return msg
	}
	product.Pic = row[3] + "," + row[4] + "," + row[5] + "," + row[6] + "," + row[7]
	// 商品视频
	product.Video = row[8]
	// 品牌id
	var brandId int32
	if len(row[9]) > 0 {
		_, err := NewDbConn().SQL("select id from brand where name = ?", strings.TrimSpace(row[9])).Get(&brandId)
		if err != nil {
			msg = "查询分类id失败, err: " + err.Error()
			return msg
		}
		product.BrandId = brandId
	}
	// 商品条码
	product.BarCode = row[15]
	// 商品详情
	if len(row[16]) > 0 {
		product.ContentPc = row[16]
		product.ContentMobile = row[16]
	}

	// 是否互联网商品
	if len(row[19]) > 0 {
		if row[19] == "是" {
			product.IsIntelGoods = 1
		}
		if row[19] == "否" {
			product.IsIntelGoods = 0
		}
	}
	// 是否药品
	if len(row[20]) > 0 {
		if row[20] == "是" {
			product.IsDrugs = 1
		}
		if row[20] == "否" {
			product.IsDrugs = 0
		}
	}
	// sku
	var specValue []models.SpecValue
	var sv models.SpecValue
	if err := NewDbConn().Where("value=?", strings.TrimSpace(row[10])).Find(&specValue); err != nil {
		msg = "查询规格信息失败, err: " + err.Error()
		return msg
	} else if len(specValue) > 0 {
		sv = specValue[0]
	} else {
		sv = models.SpecValue{SpecId: 2, Value: row[10]}
		if _, err := NewDbConn().Insert(&sv); err != nil {
			msg = "新增规格信息失败, err: " + err.Error()
			return msg
		}
	}
	// 商品条码
	skuInfo := pc.SkuInfo{BarCode: product.BarCode}
	// 规格信息
	skuInfo.Skuv = append(skuInfo.Skuv, &pc.SkuValue{SpecId: sv.SpecId, SpecValueId: sv.Id})
	// 建议零售价
	if len(row[14]) > 0 {
		retailPrice, err := strconv.ParseFloat(row[14], 64)
		if err != nil {
			msg = "建议零售价格式错误, err: " + err.Error()
			return msg
		}
		decimalValue := decimal.NewFromFloat(retailPrice)
		decimalValue = decimalValue.Mul(decimal.NewFromInt(100))
		price, _ := decimalValue.Float64()
		skuInfo.RetailPrice = int32(price)
	}
	// 市场价
	if len(row[13]) > 0 {
		marketPrice, err := strconv.ParseFloat(row[13], 64)
		if err != nil {
			msg = "市场价格式错误, err: " + err.Error()
			return msg
		}
		decimalValue := decimal.NewFromFloat(marketPrice)
		decimalValue = decimalValue.Mul(decimal.NewFromInt(100))
		price, _ := decimalValue.Float64()
		skuInfo.MarketPrice = int32(price)
	}
	// 第三方货号
	if len(row[11]) > 0 {
		skuInfo.SkuThird = append(skuInfo.SkuThird, &pc.SkuThird{ThirdSkuId: strings.TrimSpace(row[11]), ErpId: 2})
	}
	// 子龙货号
	if len(row[12]) > 0 {
		skuInfo.SkuThird = append(skuInfo.SkuThird, &pc.SkuThird{ThirdSkuId: strings.TrimSpace(row[12]), ErpId: 4})
		// 实物商品查处方药
		if product.ProductType == 1 {
			etClient := et.GetExternalClient()
			if out, err := etClient.ZiLong.ProductList(etClient.Ctx, &et.ZiLongProductListReq{
				ProductCode: []string{strings.TrimSpace(row[12])},
			}); err != nil {
				return err.Error()
			} else if out.Code != 200 {
				return out.Message
			} else if len(out.Data) > 0 {
				if out.Data[0].CanSell == "0" {
					return "商品在子龙不可销"
				}
				if out.Data[0].IsPrescribedDrug == "1" {
					product.IsPrescribedDrug = 1
					product.IsDrugs = 1
					product.DrugDosage = &pc.ProductDrugDosage{
						DosingUnit:       out.Data[0].DosingUnit,
						DosingUnitName:   out.Data[0].DosingUnitName,
						DosingWay:        out.Data[0].DosingWay,
						DosingWayName:    out.Data[0].DosingWayName,
						UseFrequency:     out.Data[0].UseFrequency,
						UseFrequencyName: out.Data[0].UseFrequencyName,
					}
					product.DrugDosage.RecommendDosage = make([]*pc.ProductDrugDosage_RecommendDosage, len(out.Data[0].RecommendDosage))
					for i, rd := range out.Data[0].RecommendDosage {
						product.DrugDosage.RecommendDosage[i] = &pc.ProductDrugDosage_RecommendDosage{
							Code:  rd.Code,
							Value: rd.Value,
							Name:  rd.Name,
						}
					}
				} else if out.Data[0].ProductType == "1001" {
					product.IsDrugs = 1
				} else {
					product.IsDrugs = 0
				}
			} else {
				return "子龙货号不存在"
			}
		}
	}
	if len(skuInfo.SkuThird) == 0 {
		msg = "至少填写一个第三方货号"
		return msg
	}
	product.SourceType = 3
	pr.SkuInfo = append(pr.SkuInfo, &skuInfo)
	pr.Product = &product
	var client Product
	if res, err := client.NewProduct(context.Background(), &pr); err != nil {
		msg = "入库失败, err: " + err.Error()
		return msg
	} else if res.Code != 200 {
		msg = res.Message
		return msg
	}
	return msg
}

// 虚拟商品导入数据库
func importVirtualProduct(rows [][]string) string {
	// 新增商品信息
	var p pc.ProductRequest
	// 取第一条信息作为spu信息
	var product pc.Product
	rowMaster := rows[0]
	if len(rowMaster) == 0 {
		return "主商品信息为空"
	}
	if len(rowMaster[1]) == 0 {
		return "有一个或多个商品名称为空，导入失败"
	}
	if len(rowMaster[5]) == 0 {
		return "有一个或多个商品图片为空，导入失败"
	}
	product.ProductType = 2     // 商品类型
	product.Name = rowMaster[1] // 商品名称
	// 商品分类
	product.CategoryName = "未分类"
	_, err := NewDbConn().SQL("select max(id) from category where name = '未分类'").Get(&product.CategoryId)
	if err != nil {
		return "查询分类id失败, err: " + err.Error()
	}
	if len(rowMaster[2]) > 0 {
		categoryNameList := strings.Split(rowMaster[2], "-")
		if len(categoryNameList) == 2 {
			// 查询父类id
			var parentId int32
			_, err = NewDbConn().SQL("select id from category where name = ?", categoryNameList[0]).Get(&parentId)
			if err != nil {
				return "查询分类id失败, err: " + err.Error()
			}
			if parentId > 0 {
				// 查询子分类id
				var childId int32
				_, err = NewDbConn().SQL("select id from category where name = ? and parent_id = ?", categoryNameList[1], parentId).Get(&childId)
				if err != nil {
					return "查询分类id失败, err: " + err.Error()
				}
				if childId > 0 {
					product.CategoryId = childId
					product.CategoryName = strings.Join(categoryNameList, ">")
				}
			}
		}
	}
	//商品卖点
	product.SellingPoint = rowMaster[4]
	// 商品图片
	product.Pic = rowMaster[5] + "," + rowMaster[6] + "," + rowMaster[7] + "," + rowMaster[8] + "," + rowMaster[9]
	// 图片视频
	product.Video = rowMaster[10]
	// 商品详情
	product.ContentPc = rowMaster[15]
	product.ContentMobile = rowMaster[15]
	// 有效时间
	if len(rowMaster[16]) == 0 {
		return "虚拟商品有效期不能为空"
	}
	if len(rowMaster[17]) == 0 {
		return "虚拟商品需要选择是否支持过期退款"
	}
	termList := strings.Split(rowMaster[16], "/")
	if len(termList) == 3 {
		stamp, err := time.ParseInLocation("2006/1/2 15:04:05", rowMaster[16]+" 23:59:59", time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
		if err != nil {
			glog.Error("虚拟商品过期时间格式错误, ", err)
			return "虚拟商品过期时间格式错误"
		}
		product.TermType = 1
		product.TermValue = int32(stamp.Unix())
	} else if cast.ToInt32(rowMaster[16]) > 0 {
		product.TermType = 2
		product.TermValue = cast.ToInt32(rowMaster[16])
	} else {
		return "虚拟商品过期时间格式不正确"
	}
	if strings.Contains(rowMaster[17], "是") {
		product.VirtualInvalidRefund = 1
	} else if strings.Contains(rowMaster[17], "否") {
		product.VirtualInvalidRefund = 0
	} else {
		product.VirtualInvalidRefund = 0
	}
	product.SourceType = 3
	p.Product = &product
	// 组建规格
	for _, row := range rows {
		var skuInfo pc.SkuInfo
		var specValue []models.SpecValue
		var sv models.SpecValue
		if err := NewDbConn().Where("value=?", strings.TrimSpace(row[11])).Find(&specValue); err != nil {
			return "查询规格信息失败, err: " + err.Error()
		} else if len(specValue) > 0 {
			sv = specValue[0]
		} else {
			sv = models.SpecValue{SpecId: 2, Value: row[11]}
			if _, err := NewDbConn().Insert(&sv); err != nil {
				return "新增规格信息失败, err: " + err.Error()
			}
		}
		// 规格信息
		skuInfo.Skuv = append(skuInfo.Skuv, &pc.SkuValue{SpecId: sv.SpecId, SpecValueId: sv.Id, Pic: rowMaster[12]})
		// 建议零售价
		if len(row[14]) > 0 {
			retailPrice, err := strconv.ParseFloat(row[14], 64)
			if err != nil {
				return "建议零售价格式错误, err: " + err.Error()
			}
			decimalValue := decimal.NewFromFloat(retailPrice)
			decimalValue = decimalValue.Mul(decimal.NewFromInt(100))
			price, _ := decimalValue.Float64()
			skuInfo.RetailPrice = int32(price)
		}
		// 市场价
		if len(row[13]) > 0 {
			marketPrice, err := strconv.ParseFloat(row[13], 64)
			if err != nil {
				return "市场价格式错误, err: " + err.Error()
			}
			decimalValue := decimal.NewFromFloat(marketPrice)
			decimalValue = decimalValue.Mul(decimal.NewFromInt(100))
			price, _ := decimalValue.Float64()
			skuInfo.MarketPrice = int32(price)
		}
		p.SkuInfo = append(p.SkuInfo, &skuInfo)
	}
	var client Product
	if res, err := client.NewProduct(context.Background(), &p); err != nil {
		return "入库失败, err: " + err.Error()
	} else if res.Code != 200 {
		return res.Message
	}
	return ""
}

// 去除货号的空格
func RemoveThirdSkuSpaces(skuInfo []*pc.SkuInfo) []*pc.SkuInfo {

	for i := range skuInfo {
		for k := range skuInfo[i].SkuThird {
			thirdSkuId := skuInfo[i].SkuThird[k].ThirdSkuId
			thirdSpuId := skuInfo[i].SkuThird[k].ThirdSpuId
			skuInfo[i].SkuThird[k].ThirdSkuId = strings.TrimSpace(thirdSkuId)
			skuInfo[i].SkuThird[k].ThirdSpuId = strings.TrimSpace(thirdSpuId)
		}
	}

	return skuInfo
}

// 查询商品库所有商品信息
func (c *Product) ExportAllProduct(ctx context.Context, in *pc.QueryProductRequest) (*pc.ExportProductResponse, error) {
	out := new(pc.ExportProductResponse)
	out.Code = 400

	type SkuV struct {
		SkuId int32
		Name  string
		Value string
	}
	type SkuT struct {
		ErpName       string
		SkuId         int32
		ThirdSpuSkuId string
		ErpId         int32
	}

	db := NewDbConn()

	var skuv []SkuV
	if err := db.SQL("SELECT sku_value.sku_id,spec.`name`,spec_value.`value` from sku_value INNER JOIN spec on spec.id=sku_value.spec_id INNER JOIN spec_value on spec_value.id=sku_value.spec_value_id").Find(&skuv); err != nil {
		out.Error = err.Error()
		out.Message = "查询规格信息失败"
		return out, nil
	}

	skuVMap := make(map[int32][]SkuV)
	for _, v := range skuv {
		skuVMap[v.SkuId] = append(skuVMap[v.SkuId], v)
	}

	var skut []SkuT
	if err := db.SQL("SELECT sku_third.third_spu_sku_id,sku_third.sku_id,erp.`name` as erp_name,sku_third.erp_id FROM sku_third INNER JOIN erp on sku_third.erp_id=erp.id").Find(&skut); err != nil {
		out.Error = err.Error()
		out.Message = "查询第三方货号失败"
		return out, nil
	}

	skuTMap := make(map[int32][]SkuT)
	for _, v := range skut {
		skuTMap[v.SkuId] = append(skuTMap[v.SkuId], v)
	}

	session, err := models.QueryProductListByReq(db, in)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}

	session.Join("inner", "sku s", "s.product_id = p.id").
		Join("left", "brand b", "b.id = p.brand_id").Select(`p.id AS spu_id, s.id AS sku_id, p.name, p.category_id,
p.category_name, b.name AS brand_name, s.market_price, s.retail_price, 
p.pic, p.selling_point, p.video, p.content_pc, p.product_type, p.term_type, p.term_value, p.virtual_invalid_refund,
p.is_drugs, p.is_prescribed_drug, p.drug_dosage, p.dosing_days, p.disease,
p.warehouse_type, s.bar_code`).
		OrderBy("p.id desc").
		Limit(int(in.PageSize), int((in.PageIndex-1)*in.PageSize))

	if err := session.Find(&out.Details); err != nil {
		out.Error = err.Error()
		out.Message = "查询商品信息失败"
		return out, nil
	}
	for _, v := range out.Details {
		if skuVList, ok := skuVMap[v.SkuId]; ok {
			for _, v1 := range skuVList {
				v.SpecName += v1.Name + ","
				v.SpecValue += v1.Value + ","
			}
		}

		if skuTList, ok := skuTMap[v.SkuId]; ok {
			for _, v2 := range skuTList {
				v2.ThirdSpuSkuId = strings.Trim(v2.ThirdSpuSkuId, ",")
				switch v2.ErpId {
				case 1:
					v.ThirdSpuSkuId1 = v2.ThirdSpuSkuId
					break
				case 2:
					v.ThirdSpuSkuId2 = v2.ThirdSpuSkuId
					break
				case 3:
					v.ThirdSpuSkuId3 = v2.ThirdSpuSkuId
					break
				case 4:
					v.ThirdSpuSkuId4 = v2.ThirdSpuSkuId
					break
				}
			}
		}
	}

	out.Code = 200
	out.Message = "ok"
	return out, nil
}

// 查询全渠道商品
func (c *Product) QueryAllChannelProduct(ctx context.Context, in *pc.QueryAllChannelRequest) (*pc.QueryAllChannelResponse, error) {
	out := new(pc.QueryAllChannelResponse)
	out.Code = 400

	db := NewDbConn()
	session := db.NewSession()
	defer session.Close()
	session.Select("product.id AS spu, sku_third.sku_id AS sku, product.name, sku_third.third_spu_id AS thirdspu, sku_third.third_sku_id AS thirdsku").
		Table("sku_third").
		Join("inner", "product", "sku_third.product_id = product.id").
		Where("1=1")

	if in.ErpId > 0 {
		session.And("sku_third.erp_id = ?", in.ErpId)
	}
	if in.ProductId > 0 {
		session.And("sku_third.product_id = ?", in.ProductId)
	}
	if in.SkuId > 0 {
		session.And("sku_third.sku_id = ?", in.SkuId)
	}
	if len(in.Name) > 0 {
		session.And("product.name like ?", "%"+in.Name+"%")
	}

	if err := session.Limit(int(in.PageSize), int((in.PageIndex-1)*in.PageSize)).Find(&out.Data); err != nil {
		return nil, err
	}
	out.Code = 200
	return out, nil
}

// 批量新建---阿闻渠道   todo 周翔整理  无用方法
func (c *Product) BatchCreateToAW(ctx context.Context, in *pc.BatchCreateToAWRequest) (*pc.BatchBaseResponse, error) {
	glog.Info("BatchCreateToAW请求参数==", in)
	out := new(pc.BatchBaseResponse)
	//	if in.ChannelId <= 0 {
	//		out.Message = "请求参数不能为空"
	//		out.Code = 400
	//		return out, nil
	//	}
	//	var finance_code_arr []string
	//	//判断是全部门店
	//	if in.IsAll == 1 {
	//		if len(in.UserNo) <= 0 {
	//			out.Message = "用户编码不能为空"
	//			out.Code = 400
	//			return out, nil
	//		}
	//		client := GetDataCenterClient()
	//		defer client.Conn.Close()
	//		defer client.Cf()
	//		var params dac.GetHospitalListByUserNoRequest
	//		params.UserNo = in.UserNo
	//		//用户校验
	//		userInfo := loadLoginUserInfo(ctx)
	//		if userInfo.IsGeneralAccount {
	//			params.IsLogo = 1
	//		} else {
	//			params.IsLogo = 0
	//		}
	//		params.ChannelId = in.ChannelId
	//		params.Category = in.Category
	//		out_result, err := client.RPC.GetHospitalListByUserNo(client.Ctx, &params)
	//		if err != nil {
	//			out.Message = "请求GetHospitalListByUserNo失败"
	//			out.Error = err.Error()
	//			out.Code = 400
	//			return out, nil
	//		}
	//		for _, v := range out_result.Data {
	//			finance_code_arr = append(finance_code_arr, v.StructOuterCode)
	//		}
	//	} else {
	//		if len(in.FinanceCode) > 0 {
	//			finance_code_arr = strings.Split(in.FinanceCode, ",")
	//		} else {
	//			out.Message = "财务编码或标识门店不能为空"
	//			out.Code = 400
	//			return out, nil
	//		}
	//	}
	//	var product_id_arr []string
	//	if len(in.ProductId) > 0 {
	//		product_id_arr = strings.Split(in.ProductId, ",")
	//	}
	//	conn := NewDbConn()
	//	redis := GetRedisConn()
	//
	//	session := conn.NewSession()
	//	defer session.Close()
	//
	//	session.Begin()
	//	var channel_product_list []models.ChannelProduct
	//	session.Where("channel_id=?", in.ChannelId).Find(&channel_product_list)
	//	glog.Info("channel_product_list结果集：", in.ChannelId, channel_product_list, finance_code_arr)
	//
	//	request := 0
	//	err_int := 0
	//	var params [][]string
	//	for _, v := range finance_code_arr {
	//		if len(in.ProductId) <= 0 {
	//			//todo 把上过架的过滤掉,把未上架的加入到产品数组里面
	//			var product_id_map = make(map[string]interface{}, 0)
	//			for _, l := range channel_product_list {
	//				var model models.ChannelStoreProduct
	//				conn.Where("finance_code=?", v).And("up_down_state = 1").And("product_id=?", l.Id).And("channel_id=?", in.ChannelId).Get(&model)
	//				glog.Info("ChannelStoreProduct结果集：", v, l.Id, in.ChannelId, model)
	//				IsEmpty := reflect.DeepEqual(model, models.ChannelStoreProduct{})
	//				if IsEmpty {
	//					product_id_str := strconv.Itoa(int(l.Id))
	//					if _, ok := product_id_map[product_id_str]; !ok {
	//						product_id_arr = append(product_id_arr, product_id_str)
	//					}
	//				}
	//			}
	//		}
	//		glog.Info("product_id_arr结果集：", product_id_arr)
	//		for _, k := range product_id_arr {
	//			request++
	//			var exl_str []string
	//			exl_str = append(exl_str, v)
	//			exl_str = append(exl_str, k)
	//
	//			//根据财务编码获取门店对应的仓库 -- 便于取商品价格
	//			dcClinet := GetDispatchClient()
	//			defer dcClinet.Conn.Close()
	//			defer dcClinet.Cf()
	//
	//			warehouse, err := dcClinet.RPC.GetWarehouseInfoByFanceCode(dcClinet.Ctx, &dc.GetWarehouseInfoByFanceCodeRequest{FinanceCode: v})
	//			if err != nil {
	//				glog.Info("GetWarehouseInfoByFanceCode根据财务编码查询仓库类型失败：", err)
	//				exl_str = append(exl_str, "根据财务编码查询仓库类型失败")
	//				params = append(params, exl_str)
	//				err_int++
	//				continue
	//			}
	//			var model models.ChannelStoreProduct
	//			session.Where("product_id=?", k).And("channel_id=?", in.ChannelId).And("finance_code=?", v).Get(&model)
	//			Is_Empty := reflect.DeepEqual(model, models.ChannelStoreProduct{})
	//			if !Is_Empty {
	//				exl_str = append(exl_str, "该商品已上架")
	//				params = append(params, exl_str)
	//				err_int++
	//				continue
	//			}
	//			var snapid = 0 //快照id
	//			var channel_product_snapshot models.ChannelProductSnapshot
	//			session.Where("finance_code=?", v).And("channel_id=?", in.ChannelId).And("product_id=?", k).Get(&channel_product_snapshot)
	//			IsEmpty := reflect.DeepEqual(channel_product_snapshot, models.ChannelProductSnapshot{})
	//			json_data := ""
	//			if IsEmpty { //如果不存在，则新增
	//				ProductId, _ := strconv.Atoi(k)
	//				result_bool, result_id, jsondata := c.NewProductSnapshot(v, int32(ProductId), in.ChannelId, session)
	//				if !result_bool {
	//					session.Rollback()
	//
	//					exl_str = append(exl_str, "创建快照失败")
	//					params = append(params, exl_str)
	//					err_int++
	//					continue
	//				} else {
	//					snapid = int(result_id)
	//					json_data = jsondata
	//				}
	//			} else { //如果存在，直接调用
	//				snapid = channel_product_snapshot.Id
	//				json_data = channel_product_snapshot.JsonData
	//			}
	//
	//			//判断重量是否符合标准
	//			var newSnap pc.ChannelProductRequest
	//			err = json.Unmarshal([]byte(json_data), &newSnap)
	//			if err != nil {
	//				glog.Error("BatchOnTheShelf方法查询快照信息失败,", err)
	//
	//				exl_str = append(exl_str, "BatchOnTheShelf方法查询快照信息失败")
	//				params = append(params, exl_str)
	//				err_int++
	//				continue
	//			}
	//
	//			//处理价格问题
	//			var priceModel models.ChannelStoreProductPrice
	//			session.SQL("SELECT finance_code,product_id,sku_id,price,warehousecatory FROM dc_product.channel_store_product_price WHERE finance_code =? AND warehousecatory = ? AND product_id = ?",
	//				v, warehouse.Warehouseinfo.Category, k).Get(&priceModel)
	//			if priceModel.ProductId > 0 {
	//				priceInt32, _ := strconv.Atoi(priceModel.Price)
	//				if priceInt32 == 0 {
	//					exl_str = append(exl_str, "价格表中的价格不能为空")
	//					params = append(params, exl_str)
	//					err_int++
	//					continue
	//				}
	//				if int32(priceInt32) != newSnap.SkuInfo[0].MarketPrice {
	//					newSnap.SkuInfo[0].MarketPrice = int32(priceInt32)
	//					bj, _ := json.Marshal(newSnap)
	//					c.NewChannelProductSnapshot(context.Background(), &pc.ChannelProductSnapshot{
	//						Id:          int32(snapid),
	//						ChannelId:   in.ChannelId,
	//						ProductId:   cast.ToInt32(k),
	//						JsonData:    string(bj),
	//						FinanceCode: v,
	//					})
	//				}
	//			}
	//
	//			//判断商品的重量
	//			isWeightOk, err := JudgeChannelStoreProductWeight(newSnap)
	//			if !isWeightOk {
	//				exl_str = append(exl_str, "重量不能为空")
	//				params = append(params, exl_str)
	//				err_int++
	//				continue
	//			}
	//
	//			//根据不同的渠道处理库存逻辑
	//			if in.ChannelId == ChannelAwenId {
	//				//判断库存是否足够
	//				var channelSkuThirds []models.ChannelSkuThird
	//				session.SQL("SELECT id,product_id,sku_id,third_spu_id,third_sku_id,erp_id,channel_id,third_spu_sku_id,is_use FROM dc_product.channel_sku_third WHERE channel_id = 1 AND product_id = ?", k).Find(&channelSkuThirds)
	//				StringCmd := redis.HGet("store:relation:dctozl", v)
	//				zlId := cast.ToInt32(StringCmd.Val())
	//				skuId := "0"
	//				if len(channelSkuThirds) == 0 {
	//					glog.Info("不存在ChannelSkuThird，productId：", k)
	//
	//					exl_str = append(exl_str, "不存在ChannelSkuThird，productId")
	//					params = append(params, exl_str)
	//					err_int++
	//					continue
	//				}
	//
	//				var skuCodeInfos []*ic.SkuCodeInfo
	//				for _, _sku := range channelSkuThirds {
	//					skuId = cast.ToString(_sku.SkuId)
	//					skuCodeInfo := &ic.SkuCodeInfo{
	//						FinanceCode: v,
	//						Sku:         skuId,
	//						Spu:         _sku.ThirdSpuId,
	//						ThirdSkuid:  _sku.ThirdSkuId,
	//						ZlId:        zlId,
	//						ErpId:       _sku.ErpId,
	//					}
	//					skuCodeInfos = append(skuCodeInfos, skuCodeInfo)
	//				}
	//
	//				//查询子龙的库存
	//				//如果子龙库存=0，则跳过
	//				stockMap, _ := GetStockInfoBySkuCode(skuCodeInfos)
	//				keyStr := fmt.Sprintf("%s:%s", v, skuId)
	//				glog.Info("核实库存结果：", stockMap, ";对应的库存key值：", keyStr)
	//				if stockMap[keyStr] <= 0 {
	//					glog.Info("库存为0需要过滤的skuCodeInfos=", skuCodeInfos)
	//
	//					exl_str = append(exl_str, "库存为0")
	//					params = append(params, exl_str)
	//					err_int++
	//					continue
	//				}
	//			}
	//
	//			var insert models.ChannelStoreProduct
	//			insert.ChannelId = int(in.ChannelId)
	//			insert.FinanceCode = v
	//			insert.ProductId, _ = strconv.Atoi(k)
	//			insert.UpDownState = 1
	//			insert.CreateDate = time.Now()
	//			insert.UpdateDate = time.Now()
	//			insert.SnapshotId = snapid
	//			insert.ChannelCategoryId = int(newSnap.Product.ChannelCategoryId)
	//			insert.IsRecommend = int(newSnap.Product.IsRecommend)
	//			session.Insert(&insert)
	//
	//			//处理ES数据  todo 独立出去处理
	//			pcStoreProduct := pc.ChannelStoreProduct{
	//				Id:          int32(insert.Id),
	//				ChannelId:   int32(insert.ChannelId),
	//				FinanceCode: insert.FinanceCode,
	//				ProductId:   int32(insert.ProductId),
	//				UpDownState: int32(insert.UpDownState),
	//				SnapshotId:  int32(insert.SnapshotId),
	//			}
	//			list := make([]pc.ChannelStoreProduct, 0)
	//			list = append(list, pcStoreProduct)
	//			//推送到MQ
	//			bt, _ := json.Marshal(list)
	//			m := mqgo.SyncMqInfo{
	//				Exchange: DatacenterExchange,
	//				Queue:    ChannelProductToEs,
	//				RouteKey: ChannelProductToEs,
	//				Request:  string(bt),
	//			}
	//			if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
	//				glog.Error(err)
	//			}
	//		}
	//	}
	//	err := session.Commit()
	//	if err != nil {
	//		session.Rollback()
	//		out.Message = "提交事务失败"
	//		out.Code = 400
	//		return out, nil
	//	}
	//
	//	out.FailNum = int32(err_int)
	//	out.SuccessNum = int32(request) - out.FailNum
	//
	//	//todo 上传至七牛云
	//	if out.FailNum > 0 {
	//		var url string
	//		url, err = ExportProductErr(params)
	//		if err != nil {
	//			out.Message = "错误信息上传失败; err: " + err.Error()
	//			return out, nil
	//		}
	//		out.QiniuUrl = url
	//	}
	//	out.Code = 200
	return out, nil
}

// 获取用户权限内的门店医院列表---todo 后期可删
func (c *Product) GetHospitalListByUserNo(user_no string, channel_id int32) (hospital models.HospitalResponse, code int, msg string) {
	if len(user_no) <= 0 {
		return hospital, 400, "user_no不能为空"
	}
	userNo := user_no
	mp := createCommonBjAcpParam()
	mp["systemCode"] = config.GetString("BJAuth.SystemCode.api.out.priv.hospital.list")
	mp["func"] = config.GetString("BJAuth.func.api.out.priv.hospital.list")
	mp["structOuterCode"] = config.GetString("BJAuth.structOuterCode.api.out.priv.hospital.list")
	mp["userCode"] = userNo
	url := config.GetString("bj-user-auth-url") + "/api/out/priv/hospital/list"
	//2：调用北京获取用户权限下门店信息
	//var hospital models.HospitalResponse
	hospital.Code = 400
	code, data := utils.HttpPostForm(url, "", "", mp)
	glog.Infof("调用北京获取用户权限下门店信息 %s %v %d %s ", url, mp, code, data)
	if code == 200 {
		if err := json.Unmarshal([]byte(data), &hospital); err != nil {
			glog.Error(err)
			hospital.Message = err.Error()
		} else {
			if len(hospital.Data.List) > 0 {
				hospital.Code = 200
				//落地用户权限门店数据
				client := GetDataCenterClient()
				defer client.Conn.Close()
				defer client.Cf()
				var srr dac.StoreRelationRequest
				srr.ChannelId = channel_id
				//根据财务编码获取美团注册的门店数据
				if out, err := client.RPC.GetRelationList(client.Ctx, &srr); err != nil {
					return hospital, 400, err.Error()
				} else {
					var hasHospital []models.Hospital
					for _, v := range out.List {
						for _, v2 := range hospital.Data.List {
							if v.FinanceCode == v2.StructOuterCode {
								hasHospital = append(hasHospital, v2)
							}
						}
					}
					hospital.Data.List = hasHospital
				}
			}
		}
	} else {
		hospital.Message = data
	}
	return hospital, 200, ""
}

// 北京acp接口公用参数
func createCommonBjAcpParam() map[string]interface{} {
	dataArr := make(map[string]interface{})

	apiid := config.GetString("BJAuth.AppId")
	apiSecret := config.GetString("BJAuth.ApiSecret")
	apiStr := utils.GenSonyflake() //自己生成，唯一的十六位随机字符串
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%s&apiSecret=%s", apiSecret, apiStr, apiid, timestamp, apiSecret)
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))

	dataArr["apiId"] = apiid
	dataArr["apiStr"] = apiStr
	dataArr["timestamp"] = timestamp
	dataArr["sign"] = md5sign

	return dataArr
}

///创建快照
//finance_code 财务编码
//产品id
//渠道id(1-阿闻，2-美团，3-饿了么)
//snap_id 返回自增的快照id      todo周翔整理，测试无用后删除
//func (c *Product) NewProductSnapshot(finance_code string, product_id int32, channel_id int32, session *xorm.Session) (Bool bool, snap_id int32, jsonData string) {
//	//var jsonData string
//	var newSnap pc.ChannelProductRequest
//	req := &pc.OneofIdRequest{ChannelId: channel_id, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{product_id}}}}
//	//查询商品主库信息
//	if res, err := c.QueryChannelProductOnly(context.Background(), req); err != nil {
//		glog.Error(err)
//		return false, 0, ""
//	} else if len(res.Details) == 0 {
//		return false, 0, ""
//	} else {
//		newSnap.Product = res.Details[0]
//	}
//
//	//查询商品属性
//	if res, err := c.QueryChannelProductAttr(context.Background(), req); err != nil {
//		glog.Error(err)
//		return false, 0, ""
//	} else {
//		newSnap.ProductAttr = res.Details
//	}
//
//	//查询商品SKU
//	var sku []*pc.Sku
//	if res, err := c.QueryChannelSku(context.Background(), req); err != nil {
//		glog.Error(err)
//		return false, 0, ""
//	} else {
//		sku = res.Details
//	}
//
//	//查询SKU值
//	var skuValue []*pc.SkuValue
//	if res, err := c.QueryChannelSkuValue(context.Background(), req); err != nil {
//		glog.Error(err)
//		return false, 0, ""
//	} else {
//		skuValue = res.Details
//	}
//	//查询第三方货号
//	var skuThird []*pc.SkuThird
//	if res, err := c.QueryChannelSkuThird(context.Background(), req); err != nil {
//		glog.Error(err)
//		return false, 0, ""
//	} else {
//		skuThird = res.Details
//	}
//
//	//规格、规格值
//	spec := make(map[int32]string)
//	specValue := make(map[int32]string)
//	var specID strings.Builder
//	var specValueID strings.Builder
//	for i, v := range skuValue {
//		specID.WriteString(strconv.Itoa(int(v.SpecId)))
//		specValueID.WriteString(strconv.Itoa(int(v.SpecValueId)))
//		if i != len(skuValue)-1 {
//			specID.WriteString(",")
//			specValueID.WriteString(",")
//		}
//	}
//
//	if res, err := c.QuerySpecSingle(context.Background(), &pc.IdRequest{Id: specID.String()}); err != nil {
//		glog.Error(err)
//	} else {
//		for _, v := range res.Details {
//			spec[v.Id] = v.Name
//		}
//	}
//
//	if res, err := c.QuerySpecValue(context.Background(), &pc.IdRequest{Id: specValueID.String()}); err != nil {
//		glog.Error(err)
//	} else {
//		for _, v := range res.Details {
//			specValue[v.Id] = v.Value
//		}
//	}
//
//	for _, s := range sku {
//		skuInfo := &pc.SkuInfo{
//			RetailPrice:   s.RetailPrice,
//			SkuId:         s.Id,
//			ProductId:     s.ProductId,
//			MarketPrice:   s.MarketPrice,
//			BarCode:       s.BarCode,
//			WeightForUnit: s.WeightForUnit,
//			WeightUnit:    s.WeightUnit,
//			PriceUnit:     s.PriceUnit,
//		}
//		//第三方货号
//		for _, t := range skuThird {
//			if t.SkuId == s.Id {
//				skuInfo.SkuThird = append(skuInfo.SkuThird, t)
//			}
//		}
//
//		//sku value
//		for _, v := range skuValue {
//			if v.SkuId == s.Id {
//				skuv := *v
//				skuv.SpecName = spec[skuv.SpecId]
//				skuv.SpecValueValue = specValue[skuv.SpecValueId]
//				skuInfo.Skuv = append(skuInfo.Skuv, &skuv)
//			}
//		}
//
//		newSnap.SkuInfo = append(newSnap.SkuInfo, skuInfo)
//	}
//	bt, _ := json.Marshal(newSnap)
//	jsonData = string(bt)
//	//grpc写入快照
//	var model models.ChannelProductSnapshot
//	model.ChannelId = int(channel_id)
//	model.UserNo = ""
//	model.FinanceCode = finance_code
//	model.ProductId = product_id
//	model.JsonData = jsonData
//	model.CreateDate = time.Now()
//	sql := "INSERT INTO `channel_product_snapshot` (`channel_id`,`user_no`,`finance_code`,`product_id`,`json_data`,`create_date`) VALUES (?, ?, ?, ?, ?, ?)"
//	glog.Info("插入channel_product_snapshot：", model)
//	r, err := session.Exec(sql, model.ChannelId, model.UserNo, model.FinanceCode, model.ProductId, model.JsonData, model.CreateDate)
//	if err != nil {
//		return false, 0, ""
//	}
//	result, err := r.LastInsertId()
//	if err != nil {
//		return false, 0, ""
//	}
//	return true, int32(result), jsonData
//}

// 阿闻渠道--批量更新 todo周翔整理，测试无用后删除
func (c *Product) BatchUpdateToAW(ctx context.Context, in *pc.BatchUpdateToAWRequest) (*pc.BatchUpdateToAWResponse, error) {
	out := new(pc.BatchUpdateToAWResponse)
	//	out.Code = 200
	//	var finance_code_arr []string
	//	//判断是全部门店
	//	if in.IsAll == 1 {
	//		if len(in.UserNo) <= 0 {
	//			out.Message = "用户编码不能为空"
	//			out.Code = 400
	//			return out, nil
	//		}
	//		client := GetDataCenterClient()
	//		defer client.Conn.Close()
	//		defer client.Cf()
	//		var params dac.GetHospitalListByUserNoRequest
	//		params.UserNo = in.UserNo
	//		//用户校验
	//		userInfo := loadLoginUserInfo(ctx)
	//		if userInfo != nil && userInfo.IsGeneralAccount {
	//			params.IsLogo = 1
	//		} else {
	//			params.IsLogo = 0
	//		}
	//		params.ChannelId = in.ChannelId
	//		out_result, err := client.RPC.GetHospitalListByUserNo(client.Ctx, &params)
	//		if err != nil {
	//			out.Message = "请求GetHospitalListByUserNo失败"
	//			out.Error = err.Error()
	//			out.Code = 400
	//			return out, nil
	//		}
	//		for _, v := range out_result.Data {
	//			finance_code_arr = append(finance_code_arr, v.StructOuterCode)
	//		}
	//	} else {
	//		if len(in.FinanceCode) > 0 {
	//			finance_code_arr = strings.Split(in.FinanceCode, ",")
	//		} else {
	//			out.Message = "财务编码或标识门店不能为空"
	//			out.Code = 400
	//			return out, nil
	//		}
	//	}
	//	session := NewDbConn().NewSession()
	//	defer session.Close()
	//	var product_id_arr []string
	//	// 如果产品id为空，则查更新所有的，如果不为空，则更新所选的产品id
	//	if len(in.ProductId) > 0 {
	//		product_id_arr = strings.Split(in.ProductId, ",")
	//	}
	//	for _, v := range finance_code_arr {
	//		if len(in.ProductId) <= 0 {
	//			var list []models.ChannelStoreProduct
	//			session.Where("channel_id=?", in.ChannelId).And("finance_code=?", v).Find(&list)
	//			for _, k := range list {
	//				id_str := strconv.Itoa(int(k.Id))
	//				product_id_arr = append(product_id_arr, id_str)
	//			}
	//		}
	//		for _, k := range product_id_arr {
	//			ProductId, _ := strconv.Atoi(k)
	//			//更新所有字段
	//			if in.UpdateType == 1 {
	//				result_bool, _, _ := c.NewProductSnapshot(v, int32(ProductId), in.ChannelId, session)
	//				if !result_bool {
	//					session.Rollback()
	//					out.Message = "创建快照失败,财务编码为：" + v + "产品id为：" + k
	//					out.Code = 400
	//					return out, nil
	//				}
	//			} else { //更新所选字段
	//				var model models.UpdateSnapshotDto
	//				//更新字段--商品名称
	//				model.UpdateGoodsName = in.UpdateGoodsName
	//				//更新字段--商品卖点
	//				model.UpdateSellingPoint = in.UpdateSellingPoint
	//				//更新字段--商品图片
	//				model.UpdatePic = in.UpdatePic
	//				//更新字段--商品视频
	//				model.UpdateVideo = in.UpdateVideo
	//				//更新字段--商品品牌
	//				model.UpdateBrandId = in.UpdateBrandId
	//				//更新字段--商品属性
	//				model.UpdateAttr = in.UpdateAttr
	//				//更新字段--货号
	//				model.UpdateCode = in.UpdateCode
	//				//更新字段--市场价
	//				model.UpdateMarketPrice = in.UpdateMarketPrice
	//				//更新字段--建议零售价
	//				model.UpdateRetailPrice = in.UpdateRetailPrice
	//				//更新字段--商品条码
	//				model.UpdateBarCode = in.UpdateBarCode
	//				//更新字段--图片详情
	//				model.UpdatePicDetails = in.UpdatePicDetails
	//				//更新字段--重量
	//				model.UpdateHeight = in.UpdateHeight
	//				r, _ := c.UpdateProductSnapshot(v, int32(ProductId), in.ChannelId, model, session)
	//				if !r {
	//					session.Rollback()
	//					out.Message = "事务提交失败"
	//					out.Code = 400
	//					return out, nil
	//				}
	//			}
	//		}
	//	}
	//	err := session.Commit()
	//	if err != nil {
	//		session.Rollback()
	//		out.Message = "事务提交失败"
	//		out.Code = 400
	//		return out, nil
	//	}
	//	out.Code = 200
	return out, nil
}

// /修改快照 有快照则修改更新，没有则创建(公共方法)
// finance_code 财务编码
// 产品
// 渠道id(1-阿闻，2-美团，3-饿了么)
// snap_id 返回自增的快照id
func (c *Product) UpdateProductSnapshot(finance_code string, product_id int32, channel_id int32, dto models.UpdateSnapshotDto, session *xorm.Session) (Bool bool, snap_id int32) {
	var jsonData string
	var newSnap pc.ChannelProductRequest
	req := &pc.OneofIdRequest{ChannelId: channel_id, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{product_id}}}}
	//查询商品主库信息
	if res, err := c.QueryChannelProductOnly(context.Background(), req); err != nil {
		glog.Error(err)
		return false, 0
	} else if len(res.Details) == 0 {
		return false, 0
	} else {
		newSnap.Product = res.Details[0]
	}

	//查询商品属性
	if res, err := c.QueryChannelProductAttr(context.Background(), req); err != nil {
		glog.Error(err)
		return false, 0
	} else {
		newSnap.ProductAttr = res.Details
	}

	//查询商品SKU
	var sku []*pc.Sku
	if res, err := c.QueryChannelSku(context.Background(), req); err != nil {
		glog.Error(err)
		return false, 0
	} else {
		sku = res.Details
	}

	//查询SKU值
	var skuValue []*pc.SkuValue
	if res, err := c.QueryChannelSkuValue(context.Background(), req); err != nil {
		glog.Error(err)
		return false, 0
	} else {
		skuValue = res.Details
	}

	//查询第三方货号
	var skuThird []*pc.SkuThird
	if res, err := c.QueryChannelSkuThird(context.Background(), req); err != nil {
		glog.Error(err)
		return false, 0
	} else {
		skuThird = res.Details
	}

	//规格、规格值
	spec := make(map[int32]string)
	specValue := make(map[int32]string)
	var specID strings.Builder
	var specValueID strings.Builder
	for i, v := range skuValue {
		specID.WriteString(strconv.Itoa(int(v.SpecId)))
		specValueID.WriteString(strconv.Itoa(int(v.SpecValueId)))
		if i != len(skuValue)-1 {
			specID.WriteString(",")
			specValueID.WriteString(",")
		}
	}

	if res, err := c.QuerySpecSingle(context.Background(), &pc.IdRequest{Id: specID.String()}); err != nil {
		glog.Error(err)
	} else {
		for _, v := range res.Details {
			spec[v.Id] = v.Name
		}
	}

	if res, err := c.QuerySpecValue(context.Background(), &pc.IdRequest{Id: specValueID.String()}); err != nil {
		glog.Error(err)
	} else {
		for _, v := range res.Details {
			specValue[v.Id] = v.Value
		}
	}

	for _, s := range sku {
		skuInfo := &pc.SkuInfo{
			RetailPrice:   s.RetailPrice,
			SkuId:         s.Id,
			ProductId:     s.ProductId,
			MarketPrice:   s.MarketPrice,
			BarCode:       s.BarCode,
			WeightForUnit: s.WeightForUnit,
			WeightUnit:    s.WeightUnit,
			PriceUnit:     s.PriceUnit,
		}
		//第三方货号
		for _, t := range skuThird {
			if t.SkuId == s.Id {
				skuInfo.SkuThird = append(skuInfo.SkuThird, t)
			}
		}

		//sku value
		for _, v := range skuValue {
			if v.SkuId == s.Id {
				skuv := *v
				skuv.SpecName = spec[skuv.SpecId]
				skuv.SpecValueValue = specValue[skuv.SpecValueId]
				skuInfo.Skuv = append(skuInfo.Skuv, &skuv)
			}
		}

		newSnap.SkuInfo = append(newSnap.SkuInfo, skuInfo)
	}

	//商品名称
	if dto.UpdateGoodsName == 0 {
		newSnap.Product.Name = ""
	}
	//商品卖点
	if dto.UpdateSellingPoint == 0 {
		newSnap.Product.SellingPoint = ""
	}
	//商品图片
	if dto.UpdatePic == 0 {
		newSnap.Product.Pic = ""
	}
	//商品视频
	if dto.UpdateVideo == 0 {
		newSnap.Product.Video = ""
	}
	//商品品牌
	if dto.UpdateBrandId == 0 {
		newSnap.Product.BrandId = 0
	}
	//商品属性
	if dto.UpdateAttr == 0 {
		newSnap.ProductAttr = nil
	}
	//商品货号
	if dto.UpdateCode == 0 {
		newSnap.Product.Code = ""
	}

	for _, v := range newSnap.SkuInfo {
		//市场价
		if dto.UpdateMarketPrice == 0 {
			v.MarketPrice = 0
		}
		//建议零售价
		if dto.UpdateRetailPrice == 0 {
			v.RetailPrice = 0
		}
	}
	//商品条码
	if dto.UpdateBarCode == 0 {
		newSnap.Product.BarCode = ""
	}
	//图片详情
	if dto.UpdatePicDetails == 0 {
		newSnap.Product.ContentPc = ""
	}

	bt, _ := json.Marshal(newSnap)
	jsonData = string(bt)

	//查询、如果有就更新，没有就新增

	//grpc写入快照
	var model models.ChannelProductSnapshot
	session.Where("channel_id=?", channel_id).And("finance_code=?", finance_code).And("product_id=?", product_id).Get(&model)
	IsEmpty := reflect.DeepEqual(model, models.ChannelProductSnapshot{})
	//判断非空
	if IsEmpty {
		//todo 新增
		model.ChannelId = int(channel_id)
		model.UserNo = ""
		model.FinanceCode = finance_code
		model.ProductId = product_id
		model.JsonData = jsonData
		model.CreateDate = time.Now()
		glog.Info("插入channel_product_snapshot：", model)
		sql := "INSERT INTO `channel_product_snapshot` (`channel_id`,`user_no`,`finance_code`,`product_id`,`json_data`,`create_date`) VALUES (?, ?, ?, ?, ?, ?)"
		r, err := session.Exec(sql, model.ChannelId, model.UserNo, model.FinanceCode, model.ProductId, model.JsonData, model.CreateDate)
		if err != nil {
			return false, 0
		}
		result, err := r.LastInsertId()
		if err != nil {
			return false, 0
		}
		return true, int32(result)
	} else {
		//todo 修改
		model.JsonData = jsonData
		model.CreateDate = time.Now()
		r, err := session.Id(model.Id).Update(&model)
		if err != nil {
			return false, 0
		}
		return true, int32(r)
	}
}

// 判断商品重量是否满足需求
func JudgeChannelStoreProductWeight(newSnap pc.ChannelProductRequest) (bool, error) {
	if len(newSnap.SkuInfo) == 0 {
		return false, nil
	}
	for _, v := range newSnap.SkuInfo {
		if v.WeightForUnit == 0 {
			err := errors.New(cast.ToString(v.SkuId) + "，商品重量不能为空")
			return false, err
		}
	}
	return true, nil
}

// 平台商品库--新增异步任务数据接口
func (c *Product) CreateBatchTask(ctx context.Context, in *pc.CreateBatchTaskRequest) (*pc.CreateBatchTaskResponse, error) {
	fmt.Println("新建异步任务信息")
	out := &pc.CreateBatchTaskResponse{
		Code:    200,
		Message: "新增异步任务成功",
	}
	//插入数据拼装
	model := models.TaskList{
		TaskContent:      in.TaskContent,
		TaskStatus:       1,
		OperationFileUrl: in.OperationFileUrl,
		ChannelId:        in.ChannelId,
		RequestHeader:    in.RequestHeader,
		Status:           1,
		ModifyId:         in.CreateId,
		ModifyTime:       time.Now(),
		CreateId:         in.CreateId,
		CreateTime:       time.Now(),
		CreateName:       in.CreateName,
		CreateMobile:     in.CreateMobile,
		CreateIp:         in.CreateIp,
		IpLocation:       in.IpLocation,
		ExtendedData:     in.ExtendedData,
	}
	Engine := NewDbConn()
	if _, err := Engine.Insert(&model); err != nil {
		glog.Error(err)
		out.Code = 400
		out.Error = err.Error()
		out.Message = "新增异步任务失败"
		return out, err
	} else {
		//成功创建药品相关任务后，使用协程异步调用药品批量任务
		if model.TaskContent == 8 || model.TaskContent == 9 {
			go c.DrugTask(ctx, model.TaskContent)
		}
	}

	return out, nil
}

// 平台商品库--删除异步任务数据接口
func (c *Product) DeleteTask(ctx context.Context, in *pc.DeleteTaskRequest) (*pc.DeleteTaskResponse, error) {
	out := &pc.DeleteTaskResponse{
		Code:    200,
		Message: "删除异步任务成功",
	}
	//判断任务是否已经完成
	Engine := NewDbConn()
	taskdetail := models.TaskList{}
	if ok, err := Engine.ID(in.TaskId).Get(&taskdetail); ok && err == nil {
		if taskdetail.TaskStatus == 3 {
			//更新数据拼装,进行冻结数据
			freezeModel := models.TaskList{
				Status:     2,
				ModifyId:   in.ModifyId,
				ModifyTime: time.Now(),
			}
			affected, err := Engine.Id(in.TaskId).Update(&freezeModel)
			if err != nil {
				glog.Error(err)
				out.Code = 400
				out.Error = err.Error()
				out.Message = "删除异步任务失败"
			} else {
				out.Message = fmt.Sprintf("删除异步任务成功,受影响的行数为%d", affected)
			}
		} else {
			out.Message = "任务未完成"
		}
	} else {
		glog.Error(err)
		out.Code = 400
		out.Error = err.Error()
		out.Message = "未匹配到相应数据"
	}

	return out, nil
}

// 平台商品库--获取异步任务数据接口
func (c *Product) GetTaskList(ctx context.Context, in *pc.GetTaskListRequest) (*pc.GetTaskListResponse, error) {
	Engine := NewDbConn()
	out := &pc.GetTaskListResponse{
		Code:     200,
		Message:  "获取异步任务列表成功",
		Page:     in.Page,
		PageSize: in.PageSize,
	}
	//获取列表数据sql拼装
	//默认分页

	var sql string
	if in.TaskContent == int32(2240) {
		sql = "select id, channel_id, task_content, task_status, task_detail, operation_file_url, " +
			"request_header, resulte_file_url, status, modify_id, modify_time, create_id, create_time, create_name, " +
			"create_mobile, create_ip, ip_location, success_num, fail_num, extended_data, shop_num, category from task_list "
	} else {
		sql = "select * from task_list "
	}

	countSql := "select count(*) from task_list "
	whereSql := " where 1 = 1 "
	if in.CreateId != "" && in.Promoter == 0 {
		//whereSql += fmt.Sprintf(" and create_id = \"%s\" ", in.CreateId)
		whereSql += fmt.Sprintf(" and create_id =  '" + in.CreateId + "'")
	}

	if in.Promoter == 2 {
		whereSql += fmt.Sprintf("and create_id != '" + in.CreateId + "'")
	}

	if in.Status != 0 {
		whereSql += fmt.Sprintf(" and status = %d ", in.Status)
	}
	if in.ChannelId != -1 {
		if in.TaskContent != 7 && in.TaskContent != 67 && in.TaskContent != 153 && in.TaskContent != 154 && in.TaskContent != 155 {
			whereSql += fmt.Sprintf(" and channel_id = %d ", in.ChannelId)
		}
		//else {
		//	whereSql += fmt.Sprintf(" and ( channel_id = 5 or channel_id = %d ) ", in.ChannelId) // 认领加上了虚拟商品
		//}
	}
	if in.TaskStatus != 0 {
		whereSql += fmt.Sprintf(" and task_status = %d ", in.TaskStatus)
	}
	if in.Id != 0 {
		whereSql += fmt.Sprintf(" and id = %d ", in.Id)
	}

	if in.TaskContent == 19 {
		whereSql += fmt.Sprintf(" and task_content in (19,41)")
	} else if in.TaskContent != 0 && in.TaskContent != 7 && in.TaskContent != 10 && in.TaskContent != 101 &&
		in.TaskContent != 102 && in.TaskContent != 151 && in.TaskContent != 152 && in.TaskContent != 35 && in.TaskContent != 37 &&
		in.TaskContent != 153 && in.TaskContent != 154 && in.TaskContent != 155 && in.TaskContent != 2240 {
		whereSql += fmt.Sprintf(" and task_content = %d ", in.TaskContent)
	} else if in.TaskContent == 7 {
		//管家--商品库
		whereSql += fmt.Sprintf(" and task_content in (5,6)")
	} else if in.TaskContent == 10 {
		whereSql += fmt.Sprintf(" and task_content in (8,9)")
	} else if in.TaskContent == 101 {
		whereSql += fmt.Sprintf(" and task_content in (1,2,3,11,13,20,21,35,36,37,38)")
	} else if in.TaskContent == 102 {
		whereSql += fmt.Sprintf(" and task_content in (4,12,18,42)")
	} else if in.TaskContent == 151 {
		//平台--商品库页面
		whereSql += fmt.Sprintf(" and task_content in (1,2,20)")
	} else if in.TaskContent == 152 {
		//管家--商品库
		whereSql += fmt.Sprintf(" and task_content in (4,11,12,18,42)")
	} else if in.TaskContent == 153 {
		//管家--实物批量认领
		whereSql += fmt.Sprintf(" and task_content in (5,6) and channel_id = 0")
	} else if in.TaskContent == 154 {
		//管家--虚拟商品库
		whereSql += fmt.Sprintf(" and task_content in (5,6) and channel_id = 5")
	} else if in.TaskContent == 155 {
		//管家--组合商品库
		whereSql += fmt.Sprintf(" and task_content in (5,6) and channel_id = 1")
	}
	if in.TaskContent == 35 {
		//互联网医院商品价格
		whereSql += fmt.Sprintf(" and task_content in (35,36)")
	}
	if in.TaskContent == 37 {
		//仓库白名单
		whereSql += fmt.Sprintf(" and task_content in (37,38)")
	}
	// 给前端查询分类的任务列表展示 移动分类和移动商品
	if in.TaskContent == 2240 {
		whereSql += fmt.Sprintf(" and task_content in (22,40)")
	}
	//获取列表会传创建时间
	if in.Createtime != "" {
		taskDay := 7
		timeNum := cast.ToInt(config.GetString("taskDay"))
		if timeNum > 0 {
			taskDay = timeNum
		}

		endTime := time.Now().AddDate(0, 0, -taskDay)
		whereSql += fmt.Sprintf(" and create_time >= '%v' ", kit.GetTimeNow(endTime))
	}
	if in.Sort != "" {
		if in.Sort == "createTimeDesc" {
			whereSql += " order by create_time desc "
		}
		if in.Sort == "createTimeAsc" {
			whereSql += " order by create_time asc "
		}
		if in.Sort == "idDesc" {
			whereSql += " order by id desc "
		}
		if in.Sort == "idAsc" {
			whereSql += " order by id asc"
		}
	}
	//获取数据列表总条数
	tasklist := new(models.TaskList)
	s := countSql + whereSql
	glog.Info("20240926 日志SQL：", s)
	count, err := Engine.SQL(s).Count(tasklist)
	if err != nil {
		count = 0
	}
	out.Count = int32(count)
	if out.Count > 0 {
		//分页
		if in.Page > 0 && in.PageSize > 0 {
			whereSql += fmt.Sprintf(" LIMIT %d,%d ", (in.Page-1)*in.PageSize, in.PageSize)
		}
		sqls := sql + whereSql
		if err := Engine.SQL(sqls).Find(&out.TaskList); err != nil {
			glog.Error(err)
			out.Code = 400
			out.Error = err.Error()
			out.Message = "获取异步任务列表失败"
		}
		for i, v := range out.TaskList {
			if v.TaskContent == 31 {
				if v.ChannelId == 2 {
					out.TaskList[i].TaskContentName = "美团分类同步"
				}
			}
		}
	}
	//glog.Info("异步任务的SQL", sql)
	return out, nil
}

// 京东 TODO DONE: 京东到家批量新建 (product.go)
func (c *Product) BatchToJddj(ctx context.Context, in *pc.BatchToJddjRequest) (*pc.BatchToJddjResponse, error) {
	glog.Info("BatchToJddj输入参数", in)
	out := &pc.BatchToJddjResponse{
		Code: 400,
	}
	session := NewDbConn().NewSession()
	defer session.Close()
	var productIdStr string
	var productSlice []string
	// 如果产品id为空，则查新建所有，如果不为空，则新建所选的产品id
	if len(in.ProductId) > 0 {
		productIdStr = in.ProductId
	} else {
		if err := session.SQL("select id from channel_product").Where("channel_id=?", in.ChannelId).Find(&productSlice); err != nil {
			glog.Error(err)
			out.Message = err.Error()
			return out, nil
		} else {
			for _, v := range productSlice {
				productIdStr += v + ","
			}
		}
	}
	req, _ := json.Marshal(map[string]interface{}{
		"channelId":      in.ChannelId,
		"productIdSlice": productIdStr,
	})

	m := mqgo.SyncMqInfo{
		Exchange: "datacenter",
		Queue:    ChannelProductBatchQueueJddj,
		RouteKey: ChannelProductBatchQueueJddj,
		Request:  string(req),
	}
	glog.Info("BatchToJddj发布mq消息")
	if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}
	out.Code = 200
	out.Message = "操作成功，结果请到后台查看"
	return out, nil
}

// 批量新建异步返回错误信息
func (c *Product) BatchNewImport(url string) (*pc.ImportExcelProductResponse, error) {
	return nil, nil
}

// 根据SKUID和ERPID查询第三方货号 子龙库存用
func (c *Product) QuerySkuThirdzlCK(ctx context.Context, in *pc.NewSkuThirdzlRequest) (*pc.SkuThirdResponse, error) {
	out := new(pc.SkuThirdResponse)
	out.Code = 400
	if in.ErpId == 0 {
		in.ErpId = 4
	}
	Engine := NewDbConn()
	instr := strings.Join(in.Sku_Third_List, "','")
	//session := Engine.Table("sku_third").Join("INNER", "erp", "sku_third.erp_id=erp.id").Select("sku_third.`id`, sku_third.`sku_id`, sku_third.`third_spu_id`,sku_third.`third_sku_id`, sku_third.`erp_id`, sku_third.`product_id`,erp.`name` as erp_name")
	session := Engine.SQL("select sku_third.`id`, sku_third.`sku_id`, sku_third.`third_spu_id`,sku_third.`third_sku_id`, sku_third.`erp_id`, sku_third.`product_id` from sku_third where sku_third.`erp_id`=? "+
		" and exists(select 1 from channel_store_product where up_down_state =  1 AND sku_third.`product_id`=`channel_store_product`.product_id) "+
		"and sku_third.third_sku_id in ('"+instr+"')", in.ErpId)

	//session.In("sku_third.third_sku_id", in.Sku_Third_List)

	if err := session.Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 根据快照id,和sku新增es--阿闻的（local）
func (c *Product) NewESData(ctx context.Context, in *pc.NewESDataRequest) (*pc.BaseResponse, error) {
	var out = new(pc.BaseResponse)
	var snap_model models.ChannelProductSnapshot
	Engine := NewDbConn()
	Engine.Where("id=?", in.Id).Get(&snap_model)
	IsEmpty := reflect.DeepEqual(snap_model, models.ChannelProductSnapshot{})
	if IsEmpty {
		out.Code = 400
		out.Message = fmt.Sprintf("查询不到快照信息，其中快照id=%d", in.Id)
		return out, nil
	}
	if len(snap_model.JsonData) <= 0 {
		out.Code = 400
		out.Message = fmt.Sprintf("查询到的快照信息JsonData为空，其中快照id=%d", in.Id)
		return out, nil
	}
	var es pc.ChannelProductRequest
	if err := json.Unmarshal([]byte(snap_model.JsonData), &es); err != nil {
		glog.Error(err)
		out.Code = 400
		out.Message = fmt.Sprintf("查询到的快照信息JsonData反序列化失败，其中快照id=%d", in.Id)
		return out, nil
	}
	var tag models.ProductTag
	engine.Where("sku_id=?", in.SkuId).Get(&tag)
	var tag_string []string
	if tag.Id > 0 {
		if len(tag.Species) > 0 && tag.Species != "不限" {
			tag_string = append(tag_string, tag.Species)
		}
		if len(tag.Varieties) > 0 && tag.Varieties != "不限" {
			tag_string = append(tag_string, tag.Varieties)
		}
		if len(tag.Sex) > 0 && tag.Sex != "不限" {
			tag_string = append(tag_string, tag.Sex)
		}
		if len(tag.Shape) > 0 && tag.Shape != "不限" {
			tag_string = append(tag_string, tag.Shape)
		}
		if len(tag.Age) > 0 && tag.Age != "不限" {
			tag_string = append(tag_string, tag.Age)
		}
		if len(tag.SpecialStage) > 0 && tag.SpecialStage != "不限" {
			tag_string = append(tag_string, tag.SpecialStage)
		}
		if len(tag.IsSterilization) > 0 && tag.IsSterilization != "不限" {
			tag_string = append(tag_string, tag.IsSterilization)
		}
		if len(tag.ContentType) > 0 && tag.ContentType != "不限" {
			tag_string = append(tag_string, tag.ContentType)
		}
		if len(tag.Status) > 0 && tag.Status != "不限" {
			tag_string = append(tag_string, tag.Status)
		}
	}
	var toes models.EsToStruct
	toes.Product = es.Product
	toes.SkuInfo = es.SkuInfo
	toes.ProductAttr = es.ProductAttr
	toes.FinanceCode = snap_model.FinanceCode
	toes.Tags = strings.Join(tag_string, ",")

	out.Code = 200
	return out, nil
}

// 根据快照id,和sku新增es---电商的es
func (c *Product) UPetNewESData(ctx context.Context, in *pc.NewESDataRequest) (*pc.BaseResponse, error) {
	var out = new(pc.BaseResponse)
	var snap_model models.UpetGoods
	Engine := UpetNewDbConn()
	Engine.Where("goods_id=? AND store_id=1", in.SkuId).Get(&snap_model)
	if snap_model.GoodsId <= 0 {
		out.Code = 400
		out.Message = fmt.Sprintf("查询不到商品信息，其skuid为=%d", in.SkuId)
		return out, nil
	}
	var tag models.ProductTag
	engine.Where("sku_id=?", in.SkuId).Get(&tag)
	var tag_string []string
	if tag.Id > 0 {
		if len(tag.Species) > 0 && tag.Species != "不限" {
			tag_string = append(tag_string, tag.Species)
		}
		if len(tag.Varieties) > 0 && tag.Varieties != "不限" {
			tag_string = append(tag_string, tag.Varieties)
		}
		if len(tag.Sex) > 0 && tag.Sex != "不限" {
			tag_string = append(tag_string, tag.Sex)
		}
		if len(tag.Shape) > 0 && tag.Shape != "不限" {
			tag_string = append(tag_string, tag.Shape)
		}
		if len(tag.Age) > 0 && tag.Age != "不限" {
			tag_string = append(tag_string, tag.Age)
		}
		if len(tag.SpecialStage) > 0 && tag.SpecialStage != "不限" {
			tag_string = append(tag_string, tag.SpecialStage)
		}
		if len(tag.IsSterilization) > 0 && tag.IsSterilization != "不限" {
			tag_string = append(tag_string, tag.IsSterilization)
		}
		if len(tag.ContentType) > 0 && tag.ContentType != "不限" {
			tag_string = append(tag_string, tag.ContentType)
		}
		if len(tag.Status) > 0 && tag.Status != "不限" {
			tag_string = append(tag_string, tag.Status)
		}
	}
	var toes models.UPetEsToStruct
	toes.GoodsId = snap_model.GoodsId
	toes.GoodsCommonid = snap_model.GoodsCommonid
	toes.GoodsName = snap_model.GoodsName
	toes.GoodsJingle = snap_model.GoodsJingle
	toes.StoreId = snap_model.StoreId
	toes.StoreName = snap_model.StoreName
	toes.GcId = snap_model.GcId
	toes.BrandId = snap_model.BrandId
	toes.GoodsPrice = snap_model.GoodsPrice
	toes.GoodsPromotionPrice = snap_model.GoodsPromotionPrice
	toes.GoodsPromotionType = snap_model.GoodsPromotionType
	toes.GoodsMarketprice = snap_model.GoodsMarketprice
	toes.GoodsSerial = snap_model.GoodsSerial
	toes.GoodsStorageAlarm = snap_model.GoodsStorageAlarm
	toes.GoodsBarcode = snap_model.GoodsBarcode
	toes.GoodsClick = snap_model.GoodsClick
	toes.GoodsSalenum = snap_model.GoodsSalenum
	toes.GoodsCollect = snap_model.GoodsCollect
	toes.SpecName = snap_model.SpecName
	toes.GoodsSpec = snap_model.GoodsSpec
	toes.GoodsStorage = snap_model.GoodsStorage
	toes.GoodsImage = snap_model.GoodsImage
	toes.GoodsBody = snap_model.GoodsBody
	toes.MobileBody = snap_model.MobileBody
	toes.GoodsState = snap_model.GoodsState
	toes.GoodsVerify = snap_model.GoodsVerify
	toes.GoodsAddtime = snap_model.GoodsAddtime
	toes.GoodsEdittime = snap_model.GoodsEdittime
	toes.ColorId = snap_model.ColorId
	toes.TransportId = snap_model.TransportId
	toes.GoodsFreight = snap_model.GoodsFreight
	toes.GoodsVat = snap_model.GoodsVat
	toes.GoodsCommend = snap_model.GoodsCommend
	toes.GoodsStcids = snap_model.GoodsStcids
	toes.EvaluationGoodStar = snap_model.EvaluationGoodStar
	toes.EvaluationCount = snap_model.EvaluationCount
	toes.IsVirtual = snap_model.IsVirtual
	toes.VirtualIndate = snap_model.VirtualIndate
	toes.VirtualLimit = snap_model.VirtualLimit
	toes.VirtualInvalidRefund = snap_model.VirtualInvalidRefund
	toes.IsFcode = snap_model.IsFcode
	toes.IsPresell = snap_model.IsPresell
	toes.PresellDeliverdate = snap_model.PresellDeliverdate
	toes.IsBook = snap_model.IsBook
	toes.BookDownPayment = snap_model.BookDownPayment
	toes.BookFinalPayment = snap_model.BookFinalPayment
	toes.BookDownTime = snap_model.BookDownTime
	toes.BookBuyers = snap_model.BookBuyers
	toes.HaveGift = snap_model.HaveGift
	toes.IsOwnShop = snap_model.IsOwnShop
	toes.IsChain = snap_model.IsChain
	toes.GoodsTransV = snap_model.GoodsTransV
	toes.IsDis = snap_model.IsDis
	toes.IsBatch = snap_model.IsBatch
	toes.BatchPrice = snap_model.BatchPrice
	toes.GoodsInv = snap_model.GoodsInv
	toes.GoodsLimit = snap_model.GoodsLimit
	toes.GoodsRecommendNum = snap_model.GoodsRecommendNum
	toes.GCType = snap_model.GCType
	toes.Freight = snap_model.Freight
	toes.ChainId = snap_model.ChainId
	toes.RegionId = snap_model.RegionId
	toes.GoodPercent = snap_model.GoodPercent
	toes.GoodsSku = snap_model.GoodsSku
	toes.Tags = strings.Join(tag_string, ",")

	out.Code = 200
	return out, nil
}

// 城市编码，查询归属于同一城市的团餐商品id
func (c *Product) QueryChainGoodsId(ctx context.Context, in *pc.QueryChainGoodsReq) (*pc.ChainResponse, error) {
	out := new(pc.ChainResponse)
	out.GoodsId = make(map[int32]int32)
	engine := UpetNewDbConn()

	var goodsId []int32

	//城市编码为标准的省市区编码，如：450330，需要除100取城市编码 4503
	adcode := in.Adcode
	adcode = int32(adcode / 100)
	//城市编码要6位数，450300
	adcode = adcode * 100
	if err := engine.SQL("SELECT DISTINCT upet_chain_stock.goods_id FROM upet_chain_stock INNER JOIN (SELECT upet_chain.chain_id FROM upet_chain INNER JOIN upet_area ON upet_chain.area_id_2=upet_area.area_id WHERE upet_area.area_adcode=?)tmp_upet_chain ON tmp_upet_chain.chain_id=upet_chain_stock.chain_id  WHERE upet_chain_stock.chain_goods_state=1 UNION ALL SELECT goods_id FROM upet_goods WHERE region_id = 0 AND is_virtual = 1 AND goods_state = 1 AND store_id=?", adcode, in.OrgId).Find(&goodsId); err != nil {
		glog.Error(err)
	} else {
		for _, v := range goodsId {
			out.GoodsId[v] = v
		}
	}
	println(len(out.GoodsId))

	return out, nil
}

// 新增请求商品价格同步接口参数
func (c *Product) AddProductPrice(ctx context.Context, in *pc.AddProductRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 200
	if in.Response == "" || in.Response == "null" {
		out.Code = 400
		out.Message = "请求参数不能为空"
		return out, nil
	}
	var model models.PriceSyncResponse
	model.Response = in.Response
	model.Enable = 1
	model.CreateTime = time.Now()
	model.CreateTime = time.Now()
	_, err := engine.Insert(&model)
	if err != nil {
		glog.Error("AddProductPrice fail :", err.Error())
		out.Code = 400
		out.Message = err.Error()
	}
	return out, nil
}

// 批量上架、上架、编辑上架更新价格，这里只是修改本地数据库，不涉及第三方
// 逻辑：去本地数据库price_sync表查询价格，如果查询不到，调用北京接口查询，并写到本地数据库price_sync
// 价格同步（供批量上架、上架、编辑上架）只需要更新快照表的价格信息,以及更新channel_store_product
func (c *Product) UpdatePriceToChannel(ctx context.Context, in *pc.UpdatePriceToChannelRequest) (*pc.BaseResponse, error) {
	glog.Info("UpdatePriceToChannel请求参数：", in)
	out := new(pc.BaseResponse)
	out.Code = 200
	if in.FinanceCode == "" || in.ProductId == 0 || in.SkuId == 0 || in.ChannelId == 0 || in.ProductCode == "" {
		out.Code = 400
		out.Message = "参数不能为空"
		return out, nil
	}
	//查询本地price_sync价格
	var model models.PriceSync
	engine.Where("finance_code=?", in.FinanceCode).And("sku=?", in.SkuId).Get(&model)
	session := engine.NewSession()
	session.Begin()
	defer session.Close()
	price_int := 0
	//todo 本地存在
	if model.Id > 0 {
		glog.Info("本地有价格")
		price_int = model.Price
	} else {
		glog.Info("本地无价格")
		// 本地不存在 查询北京接口
		var params pc.GetProductPriceByBJRequest
		params.ProductCode = append(params.ProductCode, in.ProductCode)
		params.StructCode = append(params.StructCode, in.FinanceCode)
		glog.Info("请求北京价格参数GetProductPriceByBJ：", params)
		res, err := c.GetProductPriceByBJ(ctx, &params)
		if err != nil {
			glog.Error("请求北京接口失败,err:", err.Error())
			out.Code = 400
			out.Message = "请求北京接口失败，err:" + err.Error()
			return out, err
		}
		glog.Info("请求北京价格查询")
		//数据落地本地
		if res.Status == 200 && len(res.Data) > 0 {
			var olist_list []pc.ProductPriceData
			var olist pc.ProductPriceData
			olist.Struct_Code = res.Data[0].Struct_Code
			olist.Product_Info = res.Data[0].Product_Info
			olist_list = append(olist_list, olist)
			jsons, _ := json.Marshal(olist_list)
			var addparams pc.AddProductRequest
			addparams.Response = string(jsons)
			_, err := c.AddProductPrice(ctx, &addparams)
			if err != nil {
				glog.Error("查询北京落地本地失败,err:", err.Error())
				out.Code = 400
				out.Message = "查询北京落地本地失败，err:" + err.Error()
				return out, err
			}
			//把元转成分
			taxRate, _ := decimal.NewFromString(res.Data[0].Product_Info[0].Sell_Price)
			kk := taxRate.Mul(decimal.NewFromInt(100))
			newstring := cast.ToString(kk)
			price_int = cast.ToInt(newstring)
			glog.Info("价格：", price_int)
		} else {
			glog.Info("没有需要处理的数据")
			out.Code = 200
			out.Message = "没有需要处理的数据"
			return out, err
		}
	}

	glog.Info("UpdatePriceToChannel 获取的价格：", price_int)

	//查询本地快照数据
	var snap models.ChannelProductSnapshot
	session.Where("finance_code=?", in.FinanceCode).And("product_id=?", in.ProductId).And("channel_id=?", in.ChannelId).Get(&snap)
	var newSnap pc.ChannelProductRequest
	if snap.Id > 0 {
		err := json.Unmarshal([]byte(snap.JsonData), &newSnap)
		if err != nil {
			glog.Error("UpdatePriceToChannel,快照信息反序列化失败,", err)
			out.Code = 400
			out.Message = "快照信息反序列化失败,err:" + err.Error()
			return out, err
		}
		newSnap.SkuInfo[0].MarketPrice = int32(price_int)
		newSnap.SkuInfo[0].StorePrice = int32(price_int)
		/*if in.ChannelId == 4 { //如果要更新京东的价格，需要更新其门店仓的价格
			newSnap.SkuInfo[0].StorePrice = int32(price_int)
		}*/
		newSnapStr, err := json.Marshal(&newSnap)
		if err != nil {
			glog.Error("快照信息反序列化失败,", err)
			out.Code = 400
			out.Message = "快照信息反序列化失败,err:" + err.Error()
			return out, err
		}
		snap.JsonData = string(newSnapStr)
		_, err = session.Id(snap.Id).Cols("json_data").Update(&snap)
		if err != nil {
			glog.Error("插入数据库失败,err:", err)
			out.Code = 400
			out.Message = "插入数据库失败,err:" + err.Error()
			return out, err
		}
		var channel_store_product models.ChannelStoreProduct
		channel_store_product.MarketPrice = price_int
		session.Where("finance_code=?", in.FinanceCode).And("product_id=?", in.ProductId).
			And("channel_id=?", in.ChannelId).Cols("market_price").Update(&channel_store_product)
		err = session.Commit()
		if err != nil {
			session.Rollback()
			glog.Error("UpdatePriceToChannel提交事务失败，err:", err)
			out.Code = 400
			out.Message = err.Error()
			return out, err
		}
		out.Code = 200
		return out, nil
	} else {
		out.Message = "没有需要处理的数据"
		out.Code = 200
		return out, nil
	}
}

// 查询北京价格数据
func (c *Product) GetProductPriceByBJ(ctx context.Context, in *pc.GetProductPriceByBJRequest) (*pc.GetProductPriceByBJResponse, error) {
	out := new(pc.GetProductPriceByBJResponse)
	out.Code = 200
	model := new(models.ProductPrice)
	model.StructCode = in.StructCode
	model.ProductCode = in.ProductCode
	apiStr := utils.GenSonyflake()
	Timestamp := strconv.Itoa(int(time.Now().Unix()))

	glog.Info("Product_price_sync_app_secret==", config.GetString("Product_price_sync_app_secret"))
	sign := fmt.Sprintf("apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%s&apiSecret=%s",
		config.GetString("Product_price_sync_app_secret"),
		apiStr,
		config.GetString("Product_price_sync_app_id"),
		Timestamp,
		config.GetString("Product_price_sync_app_secret"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	model.ApiId = config.GetString("Product_price_sync_app_id")
	model.ApiSecret = config.GetString("Product_price_sync_app_secret")
	model.ApiStr = apiStr
	model.Timestamp = Timestamp
	model.Sign = md5sign
	RequestJson, err := json.Marshal(model)
	if err != nil {
		glog.Error("反序列化失败，err:", err.Error())
		out.Code = 400
		out.Msg = "反序列失败，err:" + err.Error()
		return out, err
	}
	url := config.GetString("Product_price_sync_url")
	result, err := utils.BJHttpPost(url, RequestJson, "")
	if err != nil {
		glog.Error("请求北京接口失败,err:", err.Error())
		out.Code = 400
		out.Msg = "请求北京接口失败，err:" + err.Error()
		return out, err
	}
	glog.Info("价格同步请求北京接口返回结果：", string(result))
	err = json.Unmarshal(result, &out)
	if err != nil {
		glog.Error("反序列化失败，err:", err.Error())
		out.Code = 400
		out.Msg = "反序列失败，err:" + err.Error()
		return out, err
	}
	if out.Status == 200 {
		out.Code = 200
		if len(in.ProductCode) == 0 {
			return out, nil
		}
		session := engine.NewSession()
		session.Begin()
		defer session.Close()
		var price_sync_list []models.PriceSync
		session.In("finance_code", in.StructCode).In("zl_productid", in.ProductCode).Find(&price_sync_list)
		price_sync_map := make(map[string]models.PriceSync, len(price_sync_list))
		for _, v := range price_sync_list {
			price_sync_map[v.FinanceCode+":"+v.ZlProductid] = v
		}
		var sku_third_list []models.SkuThird
		session.Where("erp_id=4").In("third_sku_id", in.ProductCode).Find(&sku_third_list)
		sku_third_map := make(map[string]models.SkuThird, len(sku_third_list))
		for _, v := range sku_third_list {
			sku_third_map[v.ThirdSkuId] = v
		}
		for _, v := range out.Data {
			for _, f := range v.Struct_Code {
				for _, info := range v.Product_Info {
					//把元转成分
					taxRate, _ := decimal.NewFromString(info.Sell_Price)
					kk := taxRate.Mul(decimal.NewFromInt(100))
					newstring := cast.ToString(kk)
					price_int := cast.ToInt(newstring)
					if res, ok := price_sync_map[f+":"+info.Product_Code]; ok {
						//edit
						res.Price = price_int
						// 如果商品货号为0
						if res.ProductId == 0 {
							if thirdmap, isOk := sku_third_map[res.ZlProductid]; isOk {
								res.ProductId = int(thirdmap.ProductId)
								res.Sku = int(thirdmap.SkuId)
							}
						}
						_, err := session.Id(res.Id).Update(&res)
						if err != nil {
							glog.Error("价格更新到本地err:", err)
						}
					} else {
						//add
						if result, ok := sku_third_map[info.Product_Code]; ok {
							var add_model models.PriceSync
							add_model.FinanceCode = f
							add_model.ProductId = cast.ToInt(result.ProductId)
							add_model.Sku = cast.ToInt(result.SkuId)
							add_model.Price = price_int
							add_model.Enable = 1
							add_model.ZlProductid = info.Product_Code
							_, err := session.Insert(&add_model)
							if err != nil {
								glog.Error("价格新增到本地err:", err)
							}
						}
					}
				}
			}
		}
		err = session.Commit()
		if err != nil {
			session.Rollback()
			glog.Error("更新PriceSync的价格事务提交失败，err:", err.Error())
		}
	}
	return out, nil
}

// 查询北京价格数据,先查询本地，本地没有查询北京
func (c *Product) GetProductPriceByLocalBJ(ctx context.Context, in *pc.GetProductPriceByBJRequest) (*pc.GetProductPriceByBJResponse, error) {
	glog.Info("查询北京价格数据参数：", in)
	out := new(pc.GetProductPriceByBJResponse)
	out.Code = 200
	var model models.PriceSync
	StructCode := in.StructCode[0]
	zl_productid := in.ProductCode[0]
	engine.Where("finance_code=?", StructCode).And("zl_productid=?", zl_productid).Get(&model)
	if model.Id > 0 {
		var r pc.ProductPriceData
		var n pc.NewProductInfo
		n.Sell_Price = cast.ToString(model.Price)
		n.Product_Code = cast.ToString(model.ZlProductid)
		r.Product_Info = append(r.Product_Info, &n)
		out.Data = append(out.Data, &r)
		glog.Info("查询北京价格响应数据--本地存在：", in)
		return out, nil
	} else {
		var params pc.GetProductPriceByBJRequest
		params.StructCode = append(params.StructCode, StructCode)
		params.ProductCode = append(params.ProductCode, zl_productid)
		res, err := c.GetProductPriceByBJ(ctx, &params)
		newstring := ""
		//把元转成分
		for i := 0; i < len(res.Data); i++ {
			this_list := res.Data[i]
			for j := 0; j < len(this_list.Product_Info); j++ {
				taxRate, _ := decimal.NewFromString(this_list.Product_Info[j].Sell_Price)
				kk := taxRate.Mul(decimal.NewFromInt(100))
				newstring = cast.ToString(kk)
				this_list.Product_Info[j].Sell_Price = newstring
			}
		}
		glog.Info("查询北京价格响应数据：", newstring)
		if err != nil {
			out.Code = 400
			out.Msg = "查询北京接口失败,err:" + err.Error()
			return out, nil
		}
		res.Code = 200
		return res, nil
	}
}

// 京东
// sku,Price,FinanceCode必填
func (c *Product) JDPriceEx(params models.PriceSync) (response string, err error) {
	go func() {
		//调用京东单商品改价
		appPoiCodeMap := GetAppPoiCodeByFinanceCode([]string{params.FinanceCode}, 4)
		appPoiCodeSlice := []string{}
		for k := range appPoiCodeMap {
			if len(k) > 0 {
				appPoiCodeSlice = append(appPoiCodeSlice, k)
			}
		}
		if len(appPoiCodeSlice) <= 0 {
			glog.Error("京东价格同步失败,未查询到渠道门店id，具体数据:", params)
			return
		}
		clientMt := GetMtProductClient()
		defer clientMt.Conn.Close()
		var jd_params et.UpdatePriceOneRequest
		jd_params.OutStationNo = ""
		jd_params.StationNo = appPoiCodeSlice[0]
		jd_params.OutSkuId = fmt.Sprintf("%d", params.Sku)
		jd_params.Price = cast.ToInt32(params.Price)
		jd_params.ServiceNo = utils.GenSonyflake()

		storeMasterId, err := GetAppChannelByFinanceCode(params.FinanceCode)
		if err != nil {
			glog.Error("京东价格同步失败err:GetAppChannelByFinanceCode", err, "数据为:", params)
			return
		}
		jd_params.StoreMasterId = storeMasterId

		jd_paramsjson, _ := json.Marshal(jd_params)
		glog.Info("京东改价请求参数：", string(jd_paramsjson))

		res, err := clientMt.JDDJPRODUCT.UpdatePriceOne(context.Background(), &jd_params)
		if err != nil {
			glog.Error("京东价格同步失败err:", err, "数据为:", params)
			return
		}
		if res.Code != 0 {
			glog.Error("京东价格同步失败err:", res.Message, "数据为：", params)
			return
		}
	}()

	// 组合商品价格同步
	glog.Info("组合商品价格同步 jddj ", kit.JsonEncode(params))
	UpdateGroupProductPrice(params.FinanceCode, 4, 3, int32(params.ProductId), int32(params.Price), nil, cast.ToInt32(params.Sku))

	out, err := c.EditProductPrice(params, 4)
	if err != nil {
		return err.Error(), nil
	}
	if out.Code == 400 {
		glog.Error("SyncProductPriceJD getChannelProductSnapshot 未查询到数据", params)
		return errors.New("未查询到数据").Error(), nil
	}

	return "成功", nil
}

// 饿了么
// sku,Price,FinanceCode必填
func (c *Product) ELMPriceEx(params models.PriceSync) (response string, err error) {
	go func() {
		defer func() {
			// 是否有未捕获的异常
			if err := recover(); err != nil {
				glog.Error("周翔错误捕获2", err)
			}
		}()
		//调用饿了么单商品改价
		appPoiCodeMap := GetAppPoiCodeByFinanceCode([]string{params.FinanceCode}, 3)
		appPoiCodeSlice := []string{}
		for k := range appPoiCodeMap {
			if len(k) > 0 {
				appPoiCodeSlice = append(appPoiCodeSlice, k)
			}
		}
		if len(appPoiCodeSlice) <= 0 {
			glog.Error("饿了么价格同步失败,未查询到渠道门店id，具体数据:", params)
			return
		}
		clientMt := GetMtProductClient()
		defer clientMt.Conn.Close()
		var elm_params et.SkuPriceUpdateOneRequest
		elm_params.SkuidPrice = fmt.Sprintf("%d:%d,%d", params.Sku, params.Price, params.Price)
		elm_params.ShopId = appPoiCodeSlice[0]

		appChannel, err := GetAppChannelByFinanceCode(params.FinanceCode)
		if err != nil {
			return
		}

		if appChannel == 0 {
			err = errors.New("获取appChannel值错误")
			return
		}
		elm_params.AppChannel = appChannel
		glog.Info("饿了么更新价格参数：", elm_params)
		if !CanCallThirdApi(cast.ToInt(params.ProductId), ChannelElmId, params.FinanceCode) {
			glog.Error("没有第三方商品id====19,商品id:", cast.ToInt(params.ProductId), "财务编码：", params.FinanceCode)
			return
		}
		res, err := clientMt.ELMPRODUCT.SkuPriceUpdateOne(context.Background(), &elm_params)
		glog.Info("饿了么更新价格响应结果：", res, "饿了么更新价格参数", elm_params, "err为", kit.JsonEncode(err))
		if err != nil {
			glog.Error("饿了么价格同步失败err:", err, "数据为:", params)
			return
		}
		UpdateProductThirdSyncErr(cast.ToInt(params.ProductId), ChannelElmId, params.FinanceCode, res.Error)

		if res.Code != 200 {
			glog.Error("饿了么价格同步失败err:", res.Error, "数据为：", params)
			return
		}
	}()
	//跟新组合商品
	glog.Info("跟新组合商品的价格信息 ele-mt ", kit.JsonEncode(params))
	UpdateGroupProductPrice(params.FinanceCode, 3, 3, int32(params.ProductId), int32(params.Price), nil, int32(params.Sku))

	glog.Info("编辑组合商品的价格信息 ele-mt ：", kit.JsonEncode(params))

	out, err := c.EditProductPrice(params, 3)
	if err != nil {
		return err.Error(), nil
	}
	if out.Code == 400 {
		glog.Info("SyncProductPriceElm getChannelProductSnapshot 未查询到数据", params)
		return errors.New("未查询到数据").Error(), nil
	}

	return "成功", nil
}

// 美团调用第三方处理
func (c *Product) MTPriceEx(params models.PriceSync) (response string, err error) {

	//通过门店财务编码查询渠道门店id
	appPoiCodeMap := GetAppPoiCodeByFinanceCode([]string{params.FinanceCode}, 2)
	appPoiCodeSlice := []string{}
	for k := range appPoiCodeMap {
		if len(k) > 0 {
			appPoiCodeSlice = append(appPoiCodeSlice, k)
		}
	}
	if len(appPoiCodeSlice) <= 0 {
		return
	}

	go func(appPoiCodeSlice []string) {

		//调用美团的更新商品接口，更新商品信息
		clientMt := et.GetExternalClient()
		defer clientMt.Close()
		var food_data_list []et.FoodDataMt
		var food_data et.FoodDataMt
		var skus et.FoodSku
		skus.SkuId = cast.ToString(params.Sku)
		priceInt := cast.ToInt(params.Price)
		f, err := cast.ToFloat64E(priceInt)
		if err != nil {
			glog.Error("美团价格同步价格转换失败，失败原因：" + err.Error())
			return
		}
		priceStr := f / 100
		skus.Price = cast.ToString(priceStr)
		food_data.AppFoodCode = cast.ToString(params.ProductId)
		food_data.Skus = append(food_data.Skus, &skus)
		food_data_list = append(food_data_list, food_data)
		str, err := json.Marshal(food_data_list)
		if err != nil {
			glog.Error("美团价格同步反序列化失败，失败原因：" + err.Error())
			return
		}
		foodData := et.MtRetailSkuPriceRequest{}

		//storeMasterId := GetAppChannelByStoreId(appPoiCodeSlice[0])

		storeMasterId, err := GetAppChannelByFinanceCode(appPoiCodeMap[appPoiCodeSlice[0]])
		if err != nil {
			glog.Info("MTPriceEx.", "GetAppChannelByFinanceCode failed：", appPoiCodeMap[appPoiCodeSlice[0]], err)
			return
		}

		foodData.StoreMasterId = storeMasterId

		foodData.AppPoiCode = appPoiCodeSlice[0]
		foodData.FoodData = string(str)
		if !CanCallThirdApi(cast.ToInt(params.ProductId), ChannelMtId, appPoiCodeMap[appPoiCodeSlice[0]]) {
			glog.Error("没有第三方商品id====21,商品id:", params.ProductId, "财务编码：", appPoiCodeMap[appPoiCodeSlice[0]])

			glog.Error("MTPriceEx美团第三方商品id不存在")
			return
		}

		res, err := clientMt.RPC.MtRetailSkuPrice(clientMt.Ctx, &foodData)
		glog.Info("同步价格到第三方返回数据：：：", kit.JsonEncode(res), "入参：", kit.JsonEncode(foodData), "err为：", kit.JsonEncode(err))
		if err != nil {
			glog.Info("美团价格同步接口失败，失败原因：" + err.Error())
			return
		}
		errMsg := ""
		if res.Code != 200 {
			errMsg = res.Message
		}
		UpdateProductThirdSyncErr(cast.ToInt(params.ProductId), ChannelMtId, appPoiCodeMap[appPoiCodeSlice[0]], errMsg)
		if res.Code != 200 {
			glog.Info("美团价格同步接口失败，失败原因：" + res.Message)
			return
		}
	}(appPoiCodeSlice)

	//跟新组合商品
	glog.Info("跟新组合商品的mt价格UpdateGroupProductPrice：ChannelMtId ", kit.JsonEncode(params))
	UpdateGroupProductPrice(params.FinanceCode, ChannelMtId, 3, int32(params.ProductId), int32(params.Price), nil, cast.ToInt32(params.Sku))

	out, err := c.EditProductPrice(params, 2)
	if err != nil {
		return err.Error(), nil
	}
	if out.Code == 400 {
		glog.Info("SyncProductPriceMT getChannelProductSnapshot 未查询到数据", params)
		return errors.New("未查询到数据").Error(), nil
	}

	return "成功", nil
}

// 消费mq改价动作
func (c *Product) EditProductPrice(params models.PriceSync, channel_id int) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	getProductInfo := c.NewGetChannelProductSnapshot([]int32{int32(params.ProductId)}, channel_id, params.FinanceCode)

	//根据财务编码获取门店对应的仓库 -- 便于上架时更新快照中的market_price
	//dcClinet := GetDispatchClient()
	//defer dcClinet.Close()
	//warehouse, err := dcClinet.RPC.GetWarehouseInfoByFanceCode(context.Background(), &dc.GetWarehouseInfoByFanceCodeRequest{FinanceCode: params.FinanceCode})
	//glog.Info("GetWarehouseInfoByFanceCode根据财务编码查询仓库类型：", params.FinanceCode, "，返回值：", warehouse)
	//if err != nil {
	//	err = errors.New(utils.RunFuncName() + "调用GetWarehouseInfoByFanceCode根据财务编码查询仓库类型失败：" + err.Error())
	//	glog.Error(err)
	//	return out, err //解决报错的情况下，warehouse.Code无值的情况 优化bycsf
	//}
	//if warehouse.Code != 200 {
	//	err = errors.New(utils.RunFuncName() + "调用GetWarehouseInfoByFanceCode根据财务编码查询仓库类型失败：" + warehouse.Message)
	//	glog.Error(err)
	//	return out, err
	//}
	product := new(Product)
	resp, err := product.GetChannelWarehouses([]string{params.FinanceCode}, cast.ToInt32(channel_id))
	if err != nil {
		glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
		out.Message = "查询仓库绑定关系异常" + err.Error()
		return out, nil
	}
	if len(resp) <= 0 && cast.ToInt32(channel_id) == ChannelAwenId { // 如果是阿闻渠道并且查询阿闻外卖没有查询到关联关系则查询竖屏自提
		resp, err = product.GetChannelWarehouses([]string{params.FinanceCode}, ChannelAwenPickUpId)
		if err != nil {
			out.Message = "查询阿闻外卖自提仓库绑定关系异常" + err.Error()
			return out, nil
		}
	}

	if len(resp) <= 0 {
		out.Message = "未查询到渠道仓库绑定信息"
		return out, nil
	}

	if cast.ToInt32(channel_id) == 3 {
		glog.Info("EditProductPrice=", kit.JsonEncode(params), channel_id)
	}
	if len(getProductInfo) > 0 {
		ProductInfo := getProductInfo[0]
		var p pc.ChannelProductRequest
		for i := 0; i < len(ProductInfo.SkuInfo); i++ {
			data := ProductInfo.SkuInfo[i]
			for _, third := range data.SkuThird {
				if (third.ErpId == 4 && resp[0].Category == 3) || resp[0].Category == 1 {
					ProductInfo.SkuInfo[i].MarketPrice = cast.ToInt32(params.Price)
					ProductInfo.SkuInfo[i].StorePrice = cast.ToInt32(params.Price)
					/*//京东的要改门店价
					if channel_id == 4 {
						ProductInfo.SkuInfo[i].StorePrice = cast.ToInt32(params.Price)
					}*/
				} else if third.ErpId == 2 && (resp[0].Category == 4 || resp[0].Category == 5) {
					ProductInfo.SkuInfo[i].MarketPrice = cast.ToInt32(params.Price)
					ProductInfo.SkuInfo[i].PreposePrice = cast.ToInt32(params.Price)
				}
			}
		}
		ProductInfo.Product.ChannelId = fmt.Sprintf("%d", channel_id)
		p.Product = ProductInfo.Product
		p.FinanceCode = params.FinanceCode
		p.SkuInfo = ProductInfo.SkuInfo
		p.ProductAttr = ProductInfo.ProductAttr
		if cast.ToInt32(channel_id) == 3 {
			glog.Info("EditProductPrice=", kit.JsonEncode(ProductInfo))
		}
		out, err = c.EditChannelProductPriceSync(context.Background(), &p)
		if err != nil {
			glog.Error("EditProductPrice err=", err)
			return out, err
		}
	} else {
		out.Code = 400
	}
	return out, nil
}

// 获取渠道商品快照，没有快照则创建快照
func (c *Product) NewGetChannelProductSnapshot(productID []int32, channelID int, financeCode string) []pc.ChannelProductOneResponse {

	defer func() {
		if r := recover(); r != nil {
			fmt.Println("NewGetChannelProductSnapshot===", r)
		}
	}()
	var pros []pc.ChannelProductOneResponse
	clientCtx := context.Background()
	for _, id := range productID {
		var pro pc.ChannelProductOneResponse
		//取快照表数据
		if res, err := c.QueryChannelProductSnapshot(clientCtx, &pc.ChannelProductSnapshotRequest{ChannelId: int32(channelID), ProductId: []int32{id}, FinanceCode: financeCode}); err != nil {
			glog.Error("EditProductPrice=", err)
			return pros
		} else if len(res.Details) > 0 {
			var snapIn pc.ChannelProductRequest
			if err := json.Unmarshal([]byte(res.Details[0].JsonData), &snapIn); err != nil {
				glog.Error("EditProductPrice=", err)
			} else {
				pro.Product = snapIn.Product
				pro.SkuInfo = snapIn.SkuInfo
				pro.ProductAttr = snapIn.ProductAttr
			}
		} else {
			var jsonData string
			var newSnap pc.ChannelProductRequest

			//没有快照时查最后一次商品上架更新时用到的快照，否则则查询出认领的基础信息新写一条快照
			if resCSP, err := c.QueryChannelStoreProduct(clientCtx, &pc.ChannelStoreProductRequest{ProductId: []int32{id}, OrderBy: "update_date desc", ChannelId: int32(channelID), PageIndex: 1, PageSize: 1, UpDownState: -1}); err != nil {
				glog.Error("EditProductPrice=", err)
				return pros
			} else if len(resCSP.Details) > 0 && resCSP.Details[0].SnapshotId != 0 {
				//根据快照id取快照
				if res, err := c.QueryChannelProductSnapshot(clientCtx, &pc.ChannelProductSnapshotRequest{ChannelId: int32(channelID), Id: resCSP.Details[0].SnapshotId, ProductId: []int32{id}}); err != nil {
					glog.Error("EditProductPrice=", err)
					return pros
				} else if len(res.Details) > 0 {
					jsonData = res.Details[0].JsonData
					if err := json.Unmarshal([]byte(jsonData), &newSnap); err != nil {
						glog.Error("EditProductPrice=", err)
						return pros
					}
				}
			} else {
				req := &pc.OneofIdRequest{ChannelId: int32(channelID), Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{id}}}}

				//查询商品主库信息
				if res, err := c.QueryChannelProductOnly(clientCtx, req); err != nil {
					glog.Error("EditProductPrice=", err)
					return pros
				} else if len(res.Details) == 0 {
					return pros
				} else {
					newSnap.Product = res.Details[0]
				}

				//查询商品属性
				if res, err := c.QueryChannelProductAttr(clientCtx, req); err != nil {
					glog.Error("EditProductPrice=", err)
					return pros
				} else {
					newSnap.ProductAttr = res.Details
				}

				//查询商品SKU
				var sku []*pc.Sku
				if res, err := c.QueryChannelSku(clientCtx, req); err != nil {
					glog.Error("EditProductPrice=", err)
					return pros
				} else {
					sku = res.Details
				}

				//查询SKU值
				var skuValue []*pc.SkuValue
				if res, err := c.QueryChannelSkuValue(clientCtx, req); err != nil {
					glog.Error("EditProductPrice=", err)
					return pros
				} else {
					skuValue = res.Details
				}
				//查询第三方货号
				var skuThird []*pc.SkuThird
				if res, err := c.QueryChannelSkuThird(clientCtx, req); err != nil {
					glog.Error("EditProductPrice=", err)
					return pros
				} else {
					skuThird = res.Details
				}

				//规格、规格值
				spec := make(map[int32]string)
				specValue := make(map[int32]string)
				var specID strings.Builder
				var specValueID strings.Builder
				for i, v := range skuValue {
					specID.WriteString(strconv.Itoa(int(v.SpecId)))
					specValueID.WriteString(strconv.Itoa(int(v.SpecValueId)))
					if i != len(skuValue)-1 {
						specID.WriteString(",")
						specValueID.WriteString(",")
					}
				}

				if res, err := c.QuerySpecSingle(clientCtx, &pc.IdRequest{Id: specID.String()}); err != nil {
					glog.Error("EditProductPrice=", err)
				} else {
					for _, v := range res.Details {
						spec[v.Id] = v.Name
					}
				}

				if res, err := c.QuerySpecValue(clientCtx, &pc.IdRequest{Id: specValueID.String()}); err != nil {
					glog.Error("EditProductPrice=", err)
				} else {
					for _, v := range res.Details {
						specValue[v.Id] = v.Value
					}
				}

				for _, s := range sku {
					skuInfo := &pc.SkuInfo{
						RetailPrice:   s.RetailPrice,
						SkuId:         s.Id,
						ProductId:     s.ProductId,
						MarketPrice:   s.MarketPrice,
						BarCode:       s.BarCode,
						ChannelId:     s.ChannelId,
						IsUse:         s.IsUse,
						WeightForUnit: s.WeightForUnit,
						WeightUnit:    s.WeightUnit,
						MinOrderCount: s.MinOrderCount,
						PriceUnit:     s.PriceUnit,
						PreposePrice:  s.PreposePrice,
						StorePrice:    s.StorePrice,
					}
					//第三方货号
					for _, t := range skuThird {
						if t.SkuId == s.Id {
							skuInfo.SkuThird = append(skuInfo.SkuThird, t)
						}
					}

					//sku value
					for _, v := range skuValue {
						if v.SkuId == s.Id {
							skuv := *v
							skuv.SpecName = spec[skuv.SpecId]
							skuv.SpecValueValue = specValue[skuv.SpecValueId]
							skuInfo.Skuv = append(skuInfo.Skuv, &skuv)
						}
					}

					newSnap.SkuInfo = append(newSnap.SkuInfo, skuInfo)
				}
				bt, _ := json.Marshal(newSnap)
				jsonData = string(bt)
			}
			if financeCode != "" {
				//不要创建快照
				//if _, err := client.RPC.NewChannelProductSnapshot(client.Ctx, &pc.ChannelProductSnapshot{ChannelId: int32(channelID), ProductId: id, JsonData: jsonData, FinanceCode: financeCode}); err != nil {
				//	glog.Error(err)
				//	return pros
				//}
			}
			pro.Product = newSnap.Product
			pro.SkuInfo = newSnap.SkuInfo
			pro.ProductAttr = newSnap.ProductAttr
		}
		pro.Code = 200
		pros = append(pros, pro)
	}
	return pros
}

// 药品相关任务:全部下架/恢复上架
func (c *Product) DrugTask(ctx context.Context, TaskContent int32) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400
	//查询新任务的ID
	params := &pc.GetTaskListRequest{
		Sort:        "createTimeAsc",
		TaskStatus:  1,
		ChannelId:   -1, //-1 查询全部的
		TaskContent: TaskContent,
		Status:      1,
		Page:        1,
		PageSize:    1,
	}
	pd := new(Product)
	result, err := pd.GetTaskList(context.Background(), params)
	if err != nil {
		glog.Error(err)
		return out, err
	}
	//更新任务状态为进行中
	updateModel := models.TaskList{
		TaskStatus:     2,
		TaskDetail:     "",
		ResulteFileUrl: "",
		ModifyTime:     time.Now(),
	}
	_, err = engine.Id(result.TaskList[0].Id).Update(updateModel)
	if err != nil {
		glog.Info("批量认领：更新任务状态错误", err.Error())
	}
	res := new(pc.BaseResponse)
	if TaskContent == 8 {
		res, _ = c.AllDrugDown()
	} else if TaskContent == 9 {
		res, _ = c.DrugRecoverUp()
	} else {
		out.Code = 400
		out.Message = "非法任务类型"
	}
	//更新任务状态为已完成
	updateModel = models.TaskList{
		TaskStatus: 3,
		TaskDetail: "",
		ModifyTime: time.Now(),
	}
	if res.Code == 200 {
		updateModel.TaskDetail = "成功"
	} else {
		updateModel.TaskDetail = "失败:" + res.Message
	}
	_, err = engine.Id(result.TaskList[0].Id).Update(updateModel)
	if err != nil {
		glog.Info("更新任务状态错误", err.Error())
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 药品全部下架(阿闻渠道所有药品在所有门店都下架)
func (c *Product) AllDrugDown() (*pc.BaseResponse, error) {
	//对所有标识为药品并且已经上架到阿闻渠道的商品，执行下架操作
	out := new(pc.BaseResponse)
	out.Code = 400
	db := NewDbConn()

	if _, err := db.Exec("DELETE FROM channel_store_product_drugs"); err != nil {
		glog.Error(err)
		return out, err
	}

	if _, err := db.Exec("INSERT channel_store_product_drugs(id,channel_id,finance_code,sku_id,product_id,snapshot_id,up_down_state) SELECT channel_store_product.id,channel_store_product.channel_id,channel_store_product.finance_code,channel_store_product.sku_id,channel_store_product.product_id,channel_store_product.snapshot_id, 0 as up_down_state FROM channel_store_product INNER JOIN product ON channel_store_product.product_id=product.id WHERE product.is_drugs=1 AND channel_store_product.up_down_state=1 AND channel_store_product.channel_id=1"); err != nil {
		glog.Error(err)
		return out, err
	}

	//关联药品的组合商品一同下架
	if _, err := db.Exec("INSERT channel_store_product_drugs(id,channel_id,finance_code,sku_id,product_id,snapshot_id,up_down_state) SELECT channel_store_product.id,channel_store_product.channel_id,channel_store_product.finance_code,channel_store_product.sku_id,channel_store_product.product_id,channel_store_product.snapshot_id, 0 as up_down_state FROM channel_store_product INNER JOIN (SELECT channel_sku_group.sku_id FROM channel_sku_group INNER JOIN (SELECT DISTINCT sku_id FROM channel_store_product_drugs)a ON a.sku_id=channel_sku_group.group_sku_id WHERE channel_sku_group.channel_id=1)b ON b.sku_id=channel_store_product.sku_id"); err != nil {
		glog.Error(err)
		return out, err
	}

	if _, err := db.Exec("UPDATE channel_store_product INNER JOIN channel_store_product_drugs ON channel_store_product.id=channel_store_product_drugs.id SET channel_store_product.up_down_state=0"); err != nil {
		glog.Error(err)
		return out, err
	}

	//直接从es删除
	var productID []int
	if err := db.Table("channel_store_product_drugs").Select("product_id").Where("up_down_state=0").Distinct().Find(&productID); err != nil {
		glog.Error(err)
		return out, err
	}

	//电商的药品，也一起删除
	dbUpet := UpetNewDbConn()
	if err := dbUpet.Table("upet_tags_goods").Select("tag_goods_common").Where("tag_id = 225").Distinct().Find(&productID); err != nil {
		glog.Error(err)
		return out, err
	}

	client := es.NewEsClient()
	size := 50
	for {
		if size > len(productID) {
			size = len(productID)
		}

		var ids []interface{}
		for _, v := range productID[:size] {
			ids = append(ids, v)
		}

		client.DeleteByQuery(es.IndexChannelStoreProduct).Query(elastic.NewTermsQuery("product.id", ids...)).Do(context.Background())

		productID = productID[size:]
		if len(productID) == 0 {
			break
		}
	}

	out.Code = 200
	return out, nil
}

// 药品恢复上架
func (c *Product) DrugRecoverUp() (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400
	db := NewDbConn()
	if _, err := db.Exec("UPDATE channel_store_product INNER JOIN channel_store_product_drugs ON channel_store_product.id=channel_store_product_drugs.id SET channel_store_product.up_down_state=1"); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 根据财务编码、渠道id查询需要导出的数据
func (c *Product) SearchExportChannelProductInfo(channel_id int32, finance_code string) []pc.QueryExcelChannelProdcutInfo {
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("Recovered in f", r)
		}
	}()

	dac_client := dac.GetDataCenterClient()
	defer dac_client.Close()
	var params dac.ShopStoreGetRequest
	params.Finance_Code = finance_code
	params.Channel_Id = channel_id
	//取医院名称
	res, err := dac_client.RPC.ShopStoreGet(context.Background(), &params)
	if err != nil {
		glog.Error("查询ShopStoreGet失败,err:", err)
	}
	var data_list []pc.QueryExcelChannelProdcutInfo
	var channel_store_product_list []models.ChannelStoreProduct
	//取 商品名称 平台商品ID SKUID 店内分类 上下架状态
	engine.Where("finance_code=?", finance_code).And("channel_id=?", channel_id).Cols("name,product_id,sku_id,channel_category_name,up_down_state").Find(&channel_store_product_list)
	var channel_product_snapshot_list []models.ChannelProductSnapshot
	engine.Where("finance_code=?", finance_code).And("channel_id=?", channel_id).Cols("product_id,json_data").Find(&channel_product_snapshot_list)
	channel_product_snapshot_map := make(map[int32]models.ChannelProductSnapshot, len(channel_product_snapshot_list))
	for _, v := range channel_product_snapshot_list {
		channel_product_snapshot_map[v.ProductId] = v
	}

	channel_store_product_map := make(map[int]models.ChannelStoreProduct, len(channel_store_product_list))

	for _, v := range channel_store_product_list {
		channel_store_product_map[v.ProductId] = v
	}

	var channel_sku_list []models.ChannelSku
	engine.Where("channel_id=?", channel_id).Find(&channel_sku_list)
	channel_sku_map := make(map[int32]models.ChannelSku, len(channel_sku_list))
	for _, v := range channel_sku_list {
		channel_sku_map[v.ProductId] = v
	}

	var channel_product_list []models.ChannelProduct
	engine.Where("channel_id=?", channel_id).Find(&channel_product_list)
	for _, v := range channel_product_list {
		var model pc.QueryExcelChannelProdcutInfo
		model.FinanceName = res.Data.ShopName
		model.FinanceCode = res.Data.Finance_Code
		model.ProductId = v.Id
		model.CategoryName = v.ChannelCategoryName
		ProductId := int(v.Id)
		if res, ok := channel_store_product_map[ProductId]; ok {
			model.ProductName = res.Name
			if res.UpDownState == 1 {
				model.UpDownState = "上架"
			} else {
				model.UpDownState = "下架"
			}
		} else {
			model.UpDownState = "下架"
			model.ProductName = v.Name
		}
		//如果有快照，则取快照的数据，没有取渠道的数据
		if res, ok := channel_product_snapshot_map[v.Id]; ok {
			if len(res.JsonData) > 0 {
				var productData pc.ChannelProductRequest
				err := json.Unmarshal([]byte(res.JsonData), &productData)
				if err != nil {
					glog.Error("查询导出数据反序列化报错，err:", err)
				}
				if productData.Product.Id > 0 {
					model.CategoryName = productData.Product.CategoryName
					model.Weight = float32(productData.SkuInfo[0].WeightForUnit)
					chn_PreposePrice, err := cast.ToFloat64E(productData.SkuInfo[0].PreposePrice)
					if err != nil {
						glog.Error("查询导出数据int转float报错,err:", err)
					} else {
						model.PreposePrice = cast.ToString(chn_PreposePrice / 100)
					}
					chn_StorePrice, err := cast.ToFloat64E(productData.SkuInfo[0].StorePrice)
					if err != nil {
						glog.Error("查询导出数据int转float报错,err:", err)
					} else {
						model.StorePrice = cast.ToString(chn_StorePrice / 100)
					}
					for _, third := range productData.SkuInfo[0].SkuThird {
						if third.ErpId == 2 {
							//A8货号
							model.A8Third = third.ThirdSkuId
						} else if third.ErpId == 4 {
							//子龙货号
							model.ZlThird = third.ThirdSkuId
						}
					}
					model.BarCode = productData.SkuInfo[0].BarCode
				}
			}
		} else {
			if rm, ok := channel_sku_map[v.Id]; ok {
				chn_PreposePrice, err := cast.ToFloat64E(rm.PreposePrice)
				if err != nil {
					glog.Error("查询导出数据int转float报错,err:", err)
				} else {
					model.PreposePrice = cast.ToString(chn_PreposePrice / 100)
				}
				chn_StorePrice, err := cast.ToFloat64E(rm.StorePrice)
				if err != nil {
					glog.Error("查询导出数据int转float报错,err:", err)
				} else {
					model.StorePrice = cast.ToString(chn_StorePrice / 100)
				}
				model.Weight = float32(rm.WeightForUnit)
				model.BarCode = v.BarCode
				model.SkuId = rm.Id
				var skuthird_list []models.SkuThird
				engine.Where("product_id=?", v.Id).Find(&skuthird_list)
				if len(skuthird_list) > 0 {
					for _, third := range skuthird_list {
						if third.ErpId == 2 {
							//A8货号
							model.A8Third = third.ThirdSkuId
						} else if third.ErpId == 4 {
							//子龙货号
							model.ZlThird = third.ThirdSkuId
						}
					}
				}
			}
		}

		if model.SkuId == 0 {
			var sku_model models.SkuThird
			engine.Where("product_id=?", model.ProductId).Get(&sku_model)
			if sku_model.Id > 0 {
				model.SkuId = sku_model.SkuId
			}
		}
		data_list = append(data_list, model)
	}

	/*for _, v := range channel_store_product_list {
		var model pc.QueryExcelChannelProdcutInfo
		model.FinanceName = res.Data.ShopName
		model.FinanceCode = res.Data.Finance_Code
		model.ProductName = v.Name
		model.ProductId = int32(v.ProductId)
		model.SkuId = int32(v.SkuId)
		model.CategoryName = v.ChannelCategoryName
		if v.UpDownState == 1 {
			model.UpDownState = "上架"
		} else {
			model.UpDownState = "下架"
		}
		if res, ok := channel_product_snapshot_map[model.ProductId]; ok {
			if len(res.JsonData) > 0 {
				var productData pc.ChannelProductRequest
				err := json.Unmarshal([]byte(res.JsonData), &productData)
				if err != nil {
					glog.Error("查询导出数据反序列化报错，err:", err)
				}
				if productData.Product.Id > 0 {
					model.Weight = float32(productData.SkuInfo[0].WeightForUnit)
					chn_PreposePrice, err := cast.ToFloat64E(productData.SkuInfo[0].PreposePrice)
					if err != nil {
						glog.Error("查询导出数据int转float报错,err:", err)
					} else {
						model.PreposePrice = cast.ToString(chn_PreposePrice / 100)
					}
					chn_StorePrice, err := cast.ToFloat64E(productData.SkuInfo[0].StorePrice)
					if err != nil {
						glog.Error("查询导出数据int转float报错,err:", err)
					} else {
						model.StorePrice = cast.ToString(chn_StorePrice / 100)
					}
					for _, third := range productData.SkuInfo[0].SkuThird {
						if third.ErpId == 2 {
							//A8货号
							model.A8Third = third.ThirdSkuId
						} else if third.ErpId == 4 {
							//子龙货号
							model.ZlThird = third.ThirdSkuId
						}
					}
					model.BarCode = productData.SkuInfo[0].BarCode
				}
			}
		}

		data_list = append(data_list, model)
	}*/
	return data_list
}

// ChannelProductExport 渠道商品库导出
func (c *Product) ChannelProductExport(in *pc.ChannelProductListReq) (url string, num int32, err error) {
	db := NewDbConn()

	defer func() {
		if err != nil {
			glog.Info("ChannelProductExport", "，入参：", kit.JsonEncode(in), "，导出出错：", err.Error())
		}
	}()

	var name string
	if has, err := db.Table("datacenter.store").Where("finance_code = ?", in.FinanceCode).Select("name").Get(&name); err != nil {
		return "", 0, err
	} else if !has {
		return "", 0, errors.New("财务编码无效")
	}

	in.PageIndex = 1
	in.PageSize = 5000 // 一次查询数
	file := excelize.NewFile()
	writer, _ := file.NewStreamWriter("Sheet1")

	header := []interface{}{
		"门店名称", "财务编码", "商品名称", "平台商品ID", "SKUID", "店内分类", "重量",
		"价格", "A8货号", "子龙货号", "商品条码", "上下架状态", "是否药品",
	}

	if in.ChannelId == 1 {
		header = append(header[:7], append([]interface{}{"付费会员价格"}, header[7:]...)...)
	}

	ps := new(ChannelProduct)
	for {
		rs, err := ps.List(context.Background(), in)
		if err != nil {
			return "", 0, err
		}

		// 首次添加表头
		if num < 1 {
			if len(rs.WarehouseType) > 0 {
				rs.WarehouseType[0] = strings.TrimRight(rs.WarehouseType[0], "库存")
				header = append(header, rs.WarehouseType[0]+"可售库存")
				header = append(header, rs.WarehouseType[0]+"锁定库存")
			}
			if len(rs.WarehouseType) > 1 {
				rs.WarehouseType[1] = strings.TrimRight(rs.WarehouseType[1], "库存")
				header = append(header, rs.WarehouseType[1]+"可售库存")
				header = append(header, rs.WarehouseType[1]+"锁定库存")
			}
			//如果是美团渠道并且有财务编码，要查询最小起购数和是否力荐
			if in.ChannelId == 2 && in.FinanceCode != "" {
				header = append(header, "起购数")
				header = append(header, "是否力荐")
			}
			_ = writer.SetRow("A1", header)
		}

		var pIds, channelCategoryIds []int32
		pIdExists := make(map[int32]bool)
		channelCategoryIdExists := make(map[int32]bool)
		for _, data := range rs.Data {
			if !pIdExists[data.Id] {
				pIds = append(pIds, data.Id)
			}
			if !channelCategoryIdExists[data.ChannelCategoryId] {
				channelCategoryIds = append(channelCategoryIds, data.ChannelCategoryId)
			}
		}

		// 上架状态查询
		var upIds []int32
		if err = db.Table("channel_store_product").Where("up_down_state = 1 and finance_code = ? and channel_id = ?", in.FinanceCode, in.ChannelId).
			In("product_id", pIds).Select("product_id").Find(&upIds); err != nil {
			return "", 0, err
		}
		upIdMap := make(map[int32]bool)
		for _, id := range upIds {
			upIdMap[id] = true
		}

		// 分类查询
		var categories []*models.ChannelCategory
		if err = db.In("id", channelCategoryIds).And("channel_id = 1").Select("id,name").Find(&categories); err != nil {
			return "", 0, err
		}
		categoryMap := make(map[int32]string)
		for _, category := range categories {
			categoryMap[category.Id] = category.Name
		}

		for _, data := range rs.Data {
			if len(data.Skus) < 1 {
				continue
			}
			sku := data.Skus[0]

			upState := "下架"
			if upIdMap[data.Id] {
				upState = "上架"
			}
			IsDrugs := "否"
			if data.IsDrugs == 1 {
				IsDrugs = "是"
			}

			row := []interface{}{
				name, in.FinanceCode, data.Name, data.Id, sku.SkuId, categoryMap[data.ChannelCategoryId],
				decimal.NewFromFloat(sku.WeightForUnit).Round(2).String(),
				decimal.NewFromFloat(float64(sku.Price) / 100).Round(2).String(),
				sku.A8Id, sku.ZilongId, sku.BarCode, upState, IsDrugs,
			}

			if in.ChannelId == 1 {
				vipPrice := ""
				if sku.VipPrice > 0 {
					vipPrice = decimal.NewFromInt32(sku.VipPrice).Div(decimal.NewFromInt32(100)).Round(2).String()
				}
				row = append(row[:7], append([]interface{}{vipPrice}, row[7:]...)...)
			}

			if len(sku.Stocks) > 0 {
				row = append(row, sku.Stocks[0].Qty)
				row = append(row, sku.Stocks[0].LockQty)
			}
			if len(sku.Stocks) > 1 {
				row = append(row, sku.Stocks[1].Qty)
				row = append(row, sku.Stocks[1].LockQty)
			}
			if in.ChannelId == 2 && in.FinanceCode != "" {

				IsRecommend := "否"
				if data.IsRecommend == 1 {
					IsRecommend = "是"
				}
				row = append(row, data.MinOrderCount)
				row = append(row, IsRecommend)
			}

			_ = writer.SetRow("A"+cast.ToString(num+2), row)
			num++
		}

		// 不够一页，说明没了
		if len(rs.Data) < int(in.PageSize) {
			break
		}

		in.PageIndex++
	}

	if err = writer.Flush(); err != nil {
		return
	}
	// 上传excel文件
	url, err = utils.UploadExcelToQiNiu(file, "")

	return
}

// 执行导出并上传七牛云
func (c *Product) ExportChannelProductInfo(channel_id int32, finance_code string) (url string, num int32, err error) {
	data_list := c.SearchExportChannelProductInfo(channel_id, finance_code)
	f := excelize.NewFile()
	index := f.NewSheet("Sheet1")
	header := []string{"门店名称", "财务编码", "商品名称", "平台商品ID", "SKUID", "店内分类", "重量", "前置仓价格", "门店仓价格", "A8货号", "子龙货号", "商品条码", "上下架状态"}
	for i := 0; i < len(header); i++ {
		f.SetCellValue("Sheet1", string(rune(65+i))+"1", header[i])
		f.SetColWidth("Sheet1", "A", "A", 50)
		f.SetColWidth("Sheet1", "C", "C", 50)
		f.SetColWidth("Sheet1", "D", "D", 15)
		f.SetColWidth("Sheet1", "E", "E", 15)
		f.SetColWidth("Sheet1", "F", "F", 30)
		f.SetColWidth("Sheet1", "H", "I", 15)
		f.SetColWidth("Sheet1", "J", "M", 20)
	}
	num = cast.ToInt32(len(data_list))
	for i := 0; i < len(data_list); i++ {
		v := data_list[i]
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i+2), v.FinanceName)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i+2), v.FinanceCode)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i+2), v.ProductName)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i+2), v.ProductId)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i+2), v.SkuId)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), v.CategoryName)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i+2), v.Weight)
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i+2), v.PreposePrice)
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i+2), v.StorePrice)
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i+2), v.A8Third)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i+2), v.ZlThird)
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i+2), v.BarCode)
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i+2), v.UpDownState)
	}
	f.SetActiveSheet(index)

	f.Save()
	file, err := f.WriteToBuffer()
	if err != nil {
		return "", 0, err
	}

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	fileName := cast.ToString(time.Now().UnixNano()) + "_渠道商品导出.xlsx"
	fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)
	io.Copy(fileWriter, file)
	path := config.GetString("file-upload-url") + "/fss/up"
	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()
	// 上传文件
	resp, _ := http.Post(path, contentType, bodyBuffer)
	defer resp.Body.Close()

	resp_body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", 0, err
	}
	var result uploadResult
	err = json.Unmarshal(resp_body, &result)
	if err != nil {
		return "", 0, err
	}
	if len(result.Url) == 0 {
		return "", 0, errors.New(result.Err)
	}
	return result.Url, num, nil
}

// 根据财务编码和渠道id查询商品价格信息   // 注释 有库存的和原来没有上过架的（没有在channel_store_product表里面出现的）
// 5：子龙不可销下架 6：非处方药变处方药下架 这两种类型可以自动上架
func (c *Product) GetProductSimpleInfo(finance_code string, channel_id, warehouseId int, warehouseCode string) []models.PriceSync {
	cs := []string{"p.zl_productid!=''", "s.third_sku_id!=''", "s.erp_id=4"}

	//先判断门店这个渠道是否可以上架药品。再组合下面的SQL查询数据
	_engine := NewDbConn()
	//_engine.ShowSQL(true)
	CanSellDrug := 0
	_, err := _engine.SQL("select 1 from datacenter.store where finance_code=?  AND sell_drugs=1 AND FIND_IN_SET(?, drugs_channel_ids)", finance_code, channel_id).Get(&CanSellDrug)

	if err != nil {
		fmt.Println(err.Error())
	}

	//药品不允许自动上架
	if CanSellDrug == 0 {
		cs = append(cs, "p.product_id not in (select id from product where is_drugs = 1)")
	}
	sql := "SELECT p.*  FROM dc_product.`price_sync` p " +
		" INNER JOIN  dc_product.`channel_product` c  ON p.`product_id`=c.`id` " +
		" LEFT JOIN dc_product.`sku_third` s ON s.`product_id`=c.`id` " +
		" WHERE c.`channel_id`=" + fmt.Sprintf("%d", channel_id) + " AND p.`finance_code`='" + warehouseCode + "' AND  " + strings.Join(cs, " and ") +
		" and p.`product_id` not in (SELECT c.`product_id` FROM dc_product.`channel_store_product` " +
		" c WHERE c.down_type in (-1,2,3,4,5) and c.`finance_code`='" + finance_code + "' AND c.`channel_id`=" + fmt.Sprintf("%d", channel_id) + ")" +
		"AND p.sku IN (SELECT goodsid FROM (SELECT a.goodsid,(IFNULL( SUM( DISTINCT a.stock ), 0 )- IFNULL( SUM( b.stock ), 0 )) AS stock,a.warehouse_id FROM " +
		"dc_order.warehouse_goods a LEFT JOIN dc_order.order_freeze_stock b ON a.warehouse_id = b.warehouse_id AND a.goodsid = b.sku_id WHERE a.warehouse_id = ? " +
		"GROUP BY a.warehouse_id,a.goodsid) T WHERE T.stock > 0) group by p.product_id"
	var list []models.PriceSync

	err = _engine.SQL(sql, warehouseId).Find(&list)
	if err != nil {
		fmt.Println(err.Error())
	}
	return list
}

// 根据财务编码和渠道id查询商品价格信息   // 注释 有库存的和原来没有上过架的（没有在channel_store_product表里面出现的）
// v6.5.9 组合商品需要手动上架
func (c *Product) GetMedicalProductSimpleInfo(finance_code string, channel_id, warehouseId int, erpId int) []models.PriceSync {

	sql := `select * from  dc_product.channel_sku cs 
			JOIN dc_product.product p on p.id = cs.product_id 
			JOIN dc_product.sku_third s ON cs.id = s.sku_id 
			LEFT JOIN hospital_product_price hp on cs.id = hp.sku_id
			WHERE cs.channel_id= ?   
			AND s.third_sku_id!='' 
			AND s.erp_id= ? 
            and p.product_type !=3 
			and cs.product_id 
			and hp.price >0
		not in (
			SELECT c.product_id FROM dc_product.channel_store_product c WHERE c.finance_code= ? AND c.channel_id= ?  );
`
	if erpId == 2 {
		sqlSelect := " cs.store_price as price, cs.id, cs.product_id ,cs.id sku, s.third_sku_id as zl_productid,hp.price as hospital_price "
		sql = strings.Replace(sql, "*", sqlSelect, 1)
	} else {
		sqlSelect := " cs.prepose_price as price, cs.id, cs.product_id ,cs.id sku, s.third_sku_id as zl_productid,hp.price as hospital_price "
		sql = strings.Replace(sql, "*", sqlSelect, 1)
	}

	var list []models.PriceSyncMedical
	_engine := NewDbConn()
	//_engine.ShowSQL(true)
	err := _engine.SQL(sql, channel_id, erpId, finance_code, channel_id).Find(&list)
	if err != nil {
		fmt.Println(err.Error())
	}
	var listFinanceds []models.PriceSync
	for i, _ := range list {
		priceSync := models.PriceSync{
			Id:          list[i].Id,
			FinanceCode: list[i].FinanceCode,
			ProductId:   list[i].ProductId,
			Sku:         list[i].Sku,
			Price:       list[i].Price,
			ZlProductid: list[i].ZlProductid,
			Enable:      list[i].Enable,
			CreateTime:  list[i].CreateTime,
			LastTime:    list[i].LastTime,
		}
		priceSync.FinanceCode = finance_code
		if list[i].HospitalPrice > 0 {
			priceSync.Price = list[i].HospitalPrice
		}
		listFinanceds = append(listFinanceds, priceSync)
	}
	return listFinanceds
}

// GetDsProductSimpleInfo 电商仓 根据财务编码和渠道id查询商品价格信息
func (c *Product) GetDsProductSimpleInfo(financeCode string, channelId, warehouseId int) ([]models.PriceSync, error) {
	var list []models.PriceSync

	err := NewDbConn().Table("dc_product.mall_product_price").Alias("mpp").
		Join("inner", "dc_dispatch.warehouse w", "w.code = mpp.warehouse_code and w.category = 1").
		Join("inner", "dc_product.channel_sku cs", fmt.Sprintf("cs.id = mpp.sku_id and cs.channel_id = %d", channelId)).
		// 确认有a8货号
		Join("inner", "dc_product.channel_sku_third cst", fmt.Sprintf("cst.third_sku_id != '' and cst.sku_id = cs.id and cst.erp_id = 2 and cst.channel_id = %d", channelId)).
		Join("inner", "dc_product.channel_product cp", fmt.Sprintf("cp.product_type = 1 and cp.id = cs.product_id and cp.channel_id = %d", channelId)).
		Select(fmt.Sprintf("mpp.price,'%s' as finance_code,cs.bar_code,cs.id as sku,cs.product_id,cst.third_sku_id as zl_productid", financeCode)).
		// 原来没有上过架的（没有在channel_store_product表里面出现的）
		Where("mpp.sku_id not in (select sku_id from channel_store_product where finance_code = ? and channel_id = ?)", financeCode, channelId).
		// 有库存的
		Where("mpp.sku_id in (select wg.goodsid from dc_order.warehouse_goods wg "+
			"where (wg.stock - ifnull("+
			"(select sum(stock) as freeze_stock from dc_order.order_freeze_stock f where f.sku_id = wg.goodsid and f.warehouse_id = wg.warehouse_id)"+
			",0))>0 and wg.warehouse_id = w.id and mpp.sku_id = wg.goodsid)").
		Where("w.id = ?", warehouseId).
		Find(&list)

	return list, err
}

// GetQzProductSimpleInfo 获取自动上架前置仓商品信息
func GetQzProductSimpleInfo(channelId, warehouseId int, financeCode string) (list []models.PriceSync, err error) {

	//先判断门店这个渠道是否可以上架药品。再组合下面的SQL查询数据
	_engine := NewDbConn()
	//_engine.ShowSQL(true)
	CanSellDrug := 0
	_, err1 := _engine.SQL("select 1 from datacenter.store where finance_code=?  AND sell_drugs=1 AND FIND_IN_SET(?, drugs_channel_ids)", financeCode, channelId).Get(&CanSellDrug)
	if err != nil {
		fmt.Println(err1.Error())
	}

	session := NewDbConn().Table("dc_product.qzc_price_sync").Alias("p").
		// 确认有a8货号
		Join("inner", "dc_product.channel_sku_third cst", fmt.Sprintf("cst.third_sku_id != '' and cst.sku_id = p.sku_id and cst.erp_id = 2 and cst.channel_id = %d", channelId)).
		// 只上架实物商品
		Join("inner", "dc_product.channel_product cp", fmt.Sprintf("cp.product_type = 1 and cp.id = p.product_id and cp.channel_id = %d", channelId)).
		// 有库存的
		Join("inner", fmt.Sprintf(`(select g.goodsid from dc_order.warehouse_goods g
                              left join dc_order.order_freeze_stock f on f.warehouse_id = g.warehouse_id and g.goodsid = f.sku_id
                     where g.warehouse_id = %d group by g.warehouse_id, g.goodsid
                     having (min(g.stock) - sum(ifnull(f.stock, 0)) > 0)) t`, warehouseId), "t.goodsid = p.sku_id").
		Select(fmt.Sprintf("p.price,'%s' as finance_code,p.sku_id as sku,p.product_id,cst.third_sku_id as zl_productid", financeCode)).
		// 没有上架过的
		Where("not exists (select 1 from dc_product.channel_store_product s where "+
			"s.product_id = p.product_id and s.finance_code = ? and s.channel_id = ?)", financeCode, channelId)

	if CanSellDrug == 0 {
		session.And("p.product_id not in (select id from product where is_drugs = 1)")
	}

	err = session.Where("p.warehouse_id = ?", warehouseId).
		Find(&list)

	return
}

// GetProductSimpleInfo 获取自动上架门店仓商品信息 v6.27.2
func GetProductSimpleInfo(channelId, warehouseId int, financeCode, WarehouseCode string) (list []models.PriceSync, err error) {

	//先判断门店这个渠道是否可以上架药品。再组合下面的SQL查询数据
	_engine := NewDbConn()
	//_engine.ShowSQL(true)
	CanSellDrug := 0
	_, err1 := _engine.SQL("select 1 from datacenter.store where finance_code=?  AND sell_drugs=1 AND FIND_IN_SET(?, drugs_channel_ids)", financeCode, channelId).Get(&CanSellDrug)
	if err != nil {
		fmt.Println(err1.Error())
	}

	session := NewDbConn().Table("dc_product.price_sync").Alias("p").
		// 确认有a8货号
		Join("inner", "dc_product.channel_sku_third cst", fmt.Sprintf("cst.third_sku_id != '' and cst.sku_id = p.sku and cst.erp_id = 4 and cst.channel_id = %d", channelId)).
		// 只上架实物商品
		Join("inner", "dc_product.channel_product cp", fmt.Sprintf("cp.product_type = 1 and cp.id = p.product_id and cp.channel_id = %d", channelId)).
		// 有库存的
		Join("inner", fmt.Sprintf(`(select g.goodsid from dc_order.warehouse_goods g
                              left join dc_order.order_freeze_stock f on f.warehouse_id = g.warehouse_id and g.goodsid = f.sku_id
                     where g.warehouse_id = %d group by g.warehouse_id, g.goodsid
                     having (min(g.stock) - sum(ifnull(f.stock, 0)) > 0)) t`, warehouseId), "t.goodsid = p.sku").
		Select(fmt.Sprintf("p.price,'%s' as finance_code,p.sku,p.product_id,cst.third_sku_id as zl_productid", financeCode)).
		// 原来没有上过架的（没有在channel_store_product表里面出现的）
		Where("not exists (select 1 from dc_product.channel_store_product s where "+
			"s.product_id = p.product_id and s.finance_code = ? and s.channel_id = ?)", financeCode, channelId).
		Where("p.finance_code = ?", WarehouseCode)

	if CanSellDrug == 0 {
		session.And("p.product_id not in (select id from product where is_drugs = 1)")
	}

	err = session.Find(&list)
	return
}

// 单个商品价格有变动的时候，更新组合商品的价格
func (c *Product) UpdateGroupProductSkuPrice(ctx context.Context, in *pc.GroupProductUpdatePriceRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	if kit.EnvIsTest() {
		// engine.ShowSQL(true)
	}

	repeatTablePrefix := func(count int) []interface{} {
		s := strings.Split(strings.Repeat(in.TablePrefix+" ", count), " ")
		var ins []interface{}
		for i := 0; i < count; i++ {
			ins = append(ins, s[i])
		}
		return ins
	}

	groupRepeat, skuRepeat := repeatTablePrefix(10), repeatTablePrefix(11)
	if in.TablePrefix == "channel_" {
		//groupRepeat = append(groupRepeat, " AND channel_sku.channel_id = 1 AND channel_sku_group.channel_id = 1 ")
		groupRepeat = append(groupRepeat, " ") // 跟新所有渠道
	} else {
		groupRepeat = append(groupRepeat, "")
	}

	var updateSkuGroup, updateSku string

	if in.TablePrefix == "channel_" {
		updateSkuGroup = `update channel_sku_group sg
inner join channel_sku s on s.id = sg.group_sku_id and s.channel_id = sg.channel_id
set sg.market_price = s.market_price,sg.update_id = ?
where s.update_date > ? and s.market_price != sg.market_price;`

		updateSku = `UPDATE channel_sku s
    INNER JOIN (SELECT sg.sku_id, SUM(sg.discount_value / 100 * sg.market_price * sg.count) AS calc_price,sg.channel_id
                FROM channel_sku_group sg
                         INNER JOIN (SELECT DISTINCT sku_id,channel_id
                                     FROM channel_sku_group
                                     WHERE discount_type = 1 AND update_id = ?) sku_group_tmp
                                    ON sg.sku_id = sku_group_tmp.sku_id and sg.channel_id = sku_group_tmp.channel_id
                GROUP BY sg.sku_id,sg.channel_id) calc_temp
    ON calc_temp.sku_id = s.id and s.channel_id = calc_temp.channel_id
SET s.market_price=calc_temp.calc_price;`

	} else {
		//更新组合商品sku明细的价格
		updateSkuGroup = fmt.Sprintf("UPDATE %ssku_group INNER JOIN %ssku ON %ssku_group.group_sku_id = %ssku.id SET %ssku_group.market_price = %ssku.market_price,%ssku_group.update_id=? WHERE %ssku.update_date > ? AND %ssku.market_price != %ssku_group.market_price %s", groupRepeat...)
		// 更新完组合商品sku明细的价格后，重新计算组合商品本身的价格
		updateSku = fmt.Sprintf("UPDATE %ssku INNER JOIN (SELECT %ssku_group.sku_id,SUM(%ssku_group.discount_value/100*%ssku_group.market_price*%ssku_group.count) AS calc_price FROM %ssku_group INNER JOIN (SELECT DISTINCT sku_id FROM %ssku_group WHERE discount_type=1 AND update_id=?)sku_group_tmp ON %ssku_group.sku_id=sku_group_tmp.sku_id GROUP BY %ssku_group.sku_id)calc_temp ON calc_temp.sku_id=%ssku.id SET %ssku.market_price=calc_temp.calc_price", skuRepeat...)
	}

	//conn := NewDbConn()
	//conn.ShowSQL(true)
	if err := engine.Table(in.TablePrefix + "sku").Select("MAX(update_date)").Find(&out.Details); err != nil {
		glog.Error(err)
		return nil, err
	}

	updateID := uuid.New().String()
	if _, err := engine.Exec(updateSkuGroup, updateID, in.UpdateDate); err != nil {
		glog.Error(err)
		out.Code = 400
		return nil, err
	}

	if _, err := engine.Exec(updateSku, updateID); err != nil {
		glog.Error(err)
		return nil, err
	}

	//如果更新的是渠道，则需要更新组合商品的快照
	// if in.TablePrefix == "channel_" {
	// 	if _, err := engine.Exec("UPDATE channel_product_snapshot INNER JOIN (SELECT channel_store_product.snapshot_id, channel_sku.market_price FROM channel_sku INNER JOIN ( SELECT DISTINCT group_sku_id FROM channel_sku_group WHERE update_id = ? )a_channel_sku_group ON a_channel_sku_group.group_sku_id = channel_sku.id INNER JOIN channel_store_product ON channel_store_product.sku_id=channel_sku.id WHERE channel_store_product.channel_id=1)b_channel_sku ON b_channel_sku.snapshot_id=channel_product_snapshot.id SET json_data = json_set(json_data, '$[0].sku_info[0].market_price', b_channel_sku.market_price) ", updateID); err != nil {
	// 		glog.Error(err)
	// 		return nil, err
	// 	}
	// }
	//v6.0新增
	// 组合商品的价格同步到第三方 通过updateID查询更新过的组合商品的然后同步到第三方 只有在channel渠道修改的时候可能需要推到第三方（gj和平台改不需要推送）
	if in.TablePrefix == "channel_" {
		groupId := make([]int32, 0)
		engine.SQL("SELECT DISTINCT product_id FROM channel_sku_group WHERE update_id = ? ", updateID).Find(&groupId)

		glog.Info("需要更新的組合商品的id", kit.JsonEncode(groupId))
		channelProducts := make([]models.ChannelStoreProduct, 0)
		engine.Table("channel_product_snapshot").Where("up_down_state =? ", 1).
			In("product_id", groupId).Find(&channelProducts)

		glog.Info("跟新上架表的信息UpdateGroupProductSkuPrice", len(channelProducts))
		//批量编辑改价公共模块
		for _, v_channelProduct := range channelProducts {

			var channelProduct ChannelProductPriceSync
			channelProduct.ChannelId = cast.ToInt(v_channelProduct.ChannelId)
			channelProduct.ProductId = cast.ToString(v_channelProduct.Id)
			channelProduct.FinanceCode = v_channelProduct.FinanceCode
			channelProduct.ProductSkuId = cast.ToString(v_channelProduct.SkuId)
			channelProduct.SyncPrice()
		}

	}
	out.Code = 200
	return out, nil
}

// 提供对外的查询库存
func (c *Product) GetStockInfoBySkuCode(ctx context.Context, in *pc.GetStockInfoRequest) (*pc.GetStockInfoBySkuCodeResponse, error) {
	out := new(pc.GetStockInfoBySkuCodeResponse)
	out.Code = 400

	//查询库存
	skuCodes := []*ic.SkuCodeInfo{}
	for _, sku := range in.ProductsInfo {
		stockWarehouse := make([]int32, 0)
		skuCode := &ic.SkuCodeInfo{}
		skuCode.FinanceCode = sku.FinanceCode[0]
		skuCode.Sku = strconv.Itoa(int(sku.SkuId))

		skuCode.StockWarehouse = append(stockWarehouse, sku.WarehouseId)
		skuCodes = append(skuCodes, skuCode)
	}

	stockMap, err := GetStockInfoBySkuCode(in.Source, skuCodes, in.ChannelId, 1) //查询本地的库存
	if err != nil {
		out.Error = err.Error()
		return out, nil
	}
	out.Code = 200
	out.Result = stockMap
	return out, nil
}

// 提供对外的查询库存
func (c *Product) GetStockInfoBySkuCodeApi(ctx context.Context, in *pc.GetStockInfoApiRequest) (*pc.GetStockInfoBySkuCodeResponse, error) {
	out := new(pc.GetStockInfoBySkuCodeResponse)
	out.Code = 400

	var stockinforequest []*ic.ProductsInfo
	for _, _sku := range in.ProductsInfo {
		stockinfo := &ic.ProductsInfo{}
		if _sku.ProductType == 3 {
			//组合商品
			stockinfo.Type = 1
			stockinfo.IsAllVirtual = 1
		} else {
			//非组合商品
			stockinfo.Type = 2
			if _sku.ProductType == 1 {
				stockinfo.IsAllVirtual = 0
			} else {
				stockinfo.IsAllVirtual = 1
			}
		}
		stockinfo.SkuId = _sku.SkuId

		for _, _skugroup := range _sku.Details {
			stockinfoch := &ic.ChildRen{}
			stockinfoch.IsVirtual = 1
			if _skugroup.ProductType == 1 {
				stockinfoch.IsVirtual = 0
				stockinfo.IsAllVirtual = 0
			}
			stockinfoch.SkuId = _skugroup.GroupSkuId
			stockinfoch.RuleNum = _skugroup.Count
			stockinfo.ChildRen = append(stockinfo.ChildRen, stockinfoch)
		}

		stockinfo.FinanceCode = []string{in.FinanceCode}
		stockinforequest = append(stockinforequest, stockinfo)
	}

	stockParams := &ic.GetStockInfoRequest{
		ProductsInfo: stockinforequest,
		Source:       2,
		IsNeedPull:   0, //不用拉取子龙库存
	}

	retValue := map[string]int32{}
	glog.Info(utils.RunFuncName()+"库存查询参数：", stockParams)
	//clientIc := ic.GetInventoryServiceClient()
	//defer clientIc.Close()
	//if outre, err := clientIc.RPC.GetStockInfo(context.Background(), stockParams); err != nil {
	//	glog.Error(utils.RunFuncName()+"调用GetStockInfo查询库存失败，", err)
	//	out.Error = err.Error()
	//	return out, err
	//} else if outre.Code != 200 {
	//	glog.Error(utils.RunFuncName()+"调用GetStockInfo查询库存失败，", outre.Message)
	//	out.Error = outre.Message
	//	return out, errors.New(outre.Message)
	//} else {
	//
	//	glog.Info(utils.RunFuncName()+"查询库存返回：", outre)
	//	for _, v := range outre.GoodsInfo.ProductsInfo {
	//		if v.Stock < 0 {
	//			retValue[in.FinanceCode+":"+strconv.Itoa(int(v.SkuId))] = 0
	//		} else {
	//			retValue[in.FinanceCode+":"+strconv.Itoa(int(v.SkuId))] = v.Stock
	//		}
	//	}
	//	//	return retValue, nil
	//}
	out.Code = 200
	out.Result = retValue
	return out, nil
}

// 查询前置仓价格
func (c *Product) GetPreposeWarehousePrice(ctx context.Context, in *pc.GetPreposePriceRequest) (*pc.GetPreposePriceResponse, error) {
	out := new(pc.GetPreposePriceResponse)
	out.Code = 400

	// 处理前置仓门店对应id
	disclient := GetDispatchClient()
	defer disclient.Close()
	idWithCode := make(map[int32][]string)
	var idList []int32
	for _, ware := range in.StoreCode {
		id := ware.Id
		if id == 0 {
			res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{ware.Code}})
			if err != nil {
				glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
				continue
			} else if len(res.Data) == 0 {
				continue
			} else {
				id = res.Data[0].Id
			}
		}
		if _, ok := idWithCode[id]; ok {
			idWithCode[id] = append(idWithCode[id], ware.Code)
		} else {
			idWithCode[id] = []string{ware.Code}
			idList = append(idList, id)
		}
	}

	var preposeList []pc.PreposePriceInfo
	err := engine.Table("qzc_price_sync").Select("price, third_sku_id product_code, warehouse_id").
		In("warehouse_id", idList).In("third_sku_id", in.ProductCode).Find(&preposeList)
	if err != nil {
		glog.Error("查询前置仓商品价格失败：", err.Error())
		out.Message = "查询前置仓商品价格失败：" + err.Error()
		out.Error = err.Error()
		return nil, err
	}

	for _, prepose := range preposeList {
		for _, v := range idWithCode[prepose.WarehouseId] {
			var p pc.PreposePriceInfo
			p.FinanceCode = v
			p.ProductCode = prepose.ProductCode
			p.Price = prepose.Price
			p.WarehouseId = prepose.WarehouseId
			out.Data = append(out.Data, &p)
		}
	}
	out.Code = 200
	return out, nil
}

// 查询门店仓价格
func (c *Product) GetStoreWarehousePrice(ctx context.Context, in *pc.GetProductPriceByBJRequest) (*pc.GetProductPriceByBJResponse, error) {
	out := new(pc.GetProductPriceByBJResponse)
	out.Code = 400

	// 先查本地门店仓价格表
	var priceSyncs []models.PriceSync
	err := engine.In("finance_code", in.StructCode).In("zl_productid", in.ProductCode).Find(&priceSyncs)
	if err != nil {
		glog.Error("查询门店仓价格表失败: ", err)
		out.Msg = "查询门店仓价格表失败: " + err.Error()
		return out, nil
	}

	var storePriceMap = make(map[string]int)
	for _, v := range priceSyncs {
		storePriceMap[v.FinanceCode+":"+v.ZlProductid] = v.Price
	}

	var removeMap = make(map[string]bool) // 去重用
	var params pc.GetProductPriceByBJRequest
	for _, store := range in.StructCode {
		for _, pro := range in.ProductCode {
			if _, ok := storePriceMap[store+":"+pro]; !ok {
				params.StructCode = append(params.StructCode, store)
				if _, ok = removeMap[pro]; !ok {
					params.ProductCode = append(params.ProductCode, pro)
					removeMap[pro] = true
				}
			}
		}
	}

	// 剩余的查北京子龙价格
	if len(removeMap) > 0 {
		res, err := c.GetProductPriceByBJ(ctx, &params)
		if err != nil {
			glog.Error("批量请求北京失败，err:", err)
			out.Msg = "批量请求北京失败，err:" + err.Error()
			return out, nil
		}
		for _, v := range res.Data {
			for _, s := range v.Struct_Code {
				for _, info := range v.Product_Info {
					taxRate, _ := decimal.NewFromString(info.Sell_Price)
					kk := taxRate.Mul(decimal.NewFromInt(100))
					newstring := cast.ToInt(kk)
					storePriceMap[s+":"+info.Product_Code] = newstring
				}
			}
		}
	}

	for k, v := range storePriceMap {
		var data pc.ProductPriceData
		var price pc.NewProductInfo
		storePro := strings.Split(k, ":")
		data.Struct_Code = append(data.Struct_Code, storePro[0])
		price.Product_Code = storePro[1]
		price.Sell_Price = cast.ToString(v)
		data.Product_Info = append(data.Product_Info, &price)

		out.Data = append(out.Data, &data)
	}

	out.Code = 200
	return out, nil
}

// 查询前置仓价格表
func (c *Product) GetProductQzInfo(channel_id int32, id []int32) (list []models.QzcPriceSync) {
	//sql := "SELECT a.warehouse_id, b.product_id, a.sku_id, a.price FROM qzc_price_sync a JOIN `channel_sku_third` b ON a.third_sku_id = b.third_sku_id " +
	//	"WHERE  b.erp_id = 2 AND b.channel_id = ? AND a.warehouse_id = ?"
	//err := NewDbConn().SQL(sql, channel_id, id).Find(&list)
	err := NewDbConn().Select("qzc_price_sync.warehouse_id, channel_sku_third.product_id, channel_sku_third.sku_id, qzc_price_sync.price").
		Table("qzc_price_sync").
		Join("inner", "channel_sku_third", "qzc_price_sync.third_sku_id = channel_sku_third.third_sku_id").
		Where("channel_sku_third.erp_id = 2").
		And("channel_sku_third.channel_id = ?", channel_id).
		In("qzc_price_sync.warehouse_id", id).
		Find(&list)
	if err != nil {
		glog.Error(err.Error())
	}
	return list
}

// 查询前置仓门店未上架商品数据
func (c *Product) GetProductQzCodeInfo(finance_code string, channel_id, warehouseId int, a8list []models.QzcPriceSync) (list []models.PriceSync) {
	var productIdList []int
	var productAndPriceMap = make(map[int]models.QzcPriceSync)
	for _, v := range a8list {
		productIdList = append(productIdList, v.ProductId)
		productAndPriceMap[v.ProductId] = v
	}

	//先判断门店这个渠道是否可以上架药品。再组合下面的SQL查询数据
	_engine := NewDbConn()
	//_engine.ShowSQL(true)
	CanSellDrug := 0
	_, err := _engine.SQL("select 1 from datacenter.store where finance_code=?  AND sell_drugs=1 AND FIND_IN_SET(?, drugs_channel_ids)", finance_code, channel_id).Get(&CanSellDrug)
	if err != nil {
		fmt.Println(err.Error())
	}

	s := NewDbConn().Table("channel_sku").Alias("cs").
		Where("cs.channel_id = ?", channel_id).
		In("cs.product_id", productIdList).
		And("cs.product_id not in ("+fmt.Sprintf("SELECT product_id FROM channel_store_product WHERE down_type not in (8) and channel_id= %d AND finance_code = '%s'", channel_id, finance_code)+")").
		And("cs.id in (SELECT goodsid FROM (SELECT a.goodsid,(IFNULL( SUM( DISTINCT a.stock ), 0 )- IFNULL( SUM( b.stock ), 0 )) AS stock,a.warehouse_id FROM dc_order.warehouse_goods a LEFT JOIN dc_order.order_freeze_stock b ON a.warehouse_id = b.warehouse_id AND a.goodsid = b.sku_id WHERE a.warehouse_id = ? GROUP BY a.warehouse_id,a.goodsid) T WHERE T.stock > 0)", warehouseId)
	//如果门店不允许上架药品就不查询药品的数据
	if CanSellDrug == 0 {
		s.And("cs.product_id not in (select id from product where is_drugs = 1)")
	}

	var resList []int
	err = s.Select("cs.product_id").Find(&resList)
	if err != nil {
		glog.Error(err.Error())
	}

	for _, v := range resList {
		if value, ok := productAndPriceMap[v]; ok {
			list = append(list, models.PriceSync{
				FinanceCode: finance_code,
				ProductId:   v,
				Sku:         value.SkuId,
				Price:       value.Price,
			})
		}
	}

	return list
}

// 获取药品商品spu
func (*Product) GetDrugsProductId(ctx context.Context, in *wrappers.Int32Value) (*pc.DrugsProductIdResponse, error) {
	out := new(pc.DrugsProductIdResponse)

	//暂时不查本地生活的药品，因为小程序审核的时候，屏蔽的是所有商品
	// if err := engine.Table("product").Select("id").Where("is_drugs=1").Find(&out.ProductId); err != nil {
	// 	glog.Error(err)
	// 	return out, err
	// }
	// SELECT tag_goods_id FROM upet_tags_goods WHERE tag_id = 225
	upetEngine := UpetNewDbConn()
	if err := upetEngine.SQL("SELECT goods_id FROM upet_goods WHERE gc_id_1 = 1037 and goods_state = 1 AND store_id=? UNION SELECT tag_goods_id FROM upet_tags_goods WHERE tag_id = 225", in.Value).Find(&out.ProductId); err != nil {
		glog.Error(err)
		return out, err
	}
	// glog.Info("GetDrugsProductId获取到药品数量",len(out.ProductId),out.ProductId)
	out.Code = 200
	return out, nil
}

// 更新快照和上下架信息
func UpdateSnapshotAndStoreUp(session *xorm.Session, financeCode string, channelId, category, productId, price int) error {
	var channelName string
	switch channelId {
	case ChannelAwenId:
		channelName = "阿闻"
	case ChannelMtId:
		channelName = "美团"
	case ChannelElmId:
		channelName = "饿了么"
	case ChannelJddjId:
		channelName = "京东"
	}

	updatePriceName := "prepose_price"
	// 电商仓更新门店价格
	if category == 1 {
		updatePriceName = "store_price"
	}
	// 更新快照价格
	sql := fmt.Sprintf("UPDATE channel_product_snapshot SET json_data = JSON_SET(json_data, '$.sku_info[0].market_price', ?), "+
		"json_data = JSON_SET(json_data, '$.sku_info[0].%s', ?) WHERE channel_id = ? AND finance_code = ? AND product_id = ?", updatePriceName)
	_, err := session.Exec(sql, price, price, channelId, financeCode, productId)
	if err != nil {
		glog.Errorf("%s更新快照价格, 门店: %s, err: %s", channelName, financeCode, err.Error())
		return err
	}
	// 更新上架价格
	_, err = session.Where("channel_id = ? and finance_code = ? and product_id = ?",
		channelId, financeCode, productId).Update(&models.ChannelStoreProduct{
		MarketPrice: price,
	})
	if err != nil {
		glog.Errorf("%s更新上架价格, 门店: %s, err: %s", channelName, financeCode, err.Error())
		return err
	}
	return nil
}

// 写入前置仓价格同步失败的mq
func QzcPriceErrToMq(financeCode, storeId string, channelId, productId, skuId, price int) error {
	var mqContent = models.QzcMqContent{
		FinanceCode: financeCode,
		StoreId:     storeId,
		ChannelId:   channelId,
		ProductId:   productId,
		SkuId:       skuId,
		Price:       price,
	}
	content, _ := json.Marshal(&mqContent)
	var qzcMq models.QzcPriceSyncMq
	qzcMq.ChannelId = channelId
	qzcMq.IsPush = 0
	qzcMq.Content = string(content)
	_, err := NewDbConn().Insert(&qzcMq)
	return err
}

// 美团前置仓价格更新
func UpdateMtQzc(session *xorm.Session, financeCode, storeId string, productId, skuId, price int) error {
	session.Begin()
	// 更新快照和上下架信息
	err := UpdateSnapshotAndStoreUp(session, financeCode, ChannelMtId, 4, productId, price)
	if err != nil {
		session.Rollback()
		return err
	}

	// 价格同步至美团
	clientMt := et.GetExternalClient()
	defer clientMt.Close()
	var food_data_list []et.FoodDataMt
	var food_data et.FoodDataMt
	var skus et.FoodSku
	priceFloat := cast.ToFloat64(price) / 100
	skus.SkuId = cast.ToString(skuId)
	skus.Price = cast.ToString(priceFloat)
	food_data.AppFoodCode = cast.ToString(productId)
	food_data.Skus = append(food_data.Skus, &skus)
	food_data_list = append(food_data_list, food_data)
	priceStr, err := json.Marshal(food_data_list)
	if err != nil {
		glog.Error("美团价格同步反序列化失败，失败原因：" + err.Error())
		return err
	}

	foodData := et.MtRetailSkuPriceRequest{}
	foodData.AppPoiCode = storeId
	foodData.FoodData = string(priceStr)

	storeMasterId, err := GetAppChannelByFinanceCode(financeCode)
	if err != nil {
		glog.Info("UpdateMtQzc.", "GetAppChannelByFinanceCode failed：", financeCode, err)
		session.Rollback()
		return err
	}
	foodData.StoreMasterId = storeMasterId
	if !CanCallThirdApi(cast.ToInt(productId), ChannelMtId, financeCode) {
		glog.Error("没有第三方商品id====22,商品id:", cast.ToInt(productId), "财务编码：", financeCode)

		return errors.New("美团第三方商品id不存在")
	}

	res, err := clientMt.RPC.MtRetailSkuPrice(clientMt.Ctx, &foodData)
	glog.Info("同步价格到第三方返回数据：：：:", kit.JsonEncode(res), "入参：", kit.JsonEncode(foodData), "err为：", kit.JsonEncode(err))

	if err != nil {
		glog.Info("美团价格同步接口失败，失败原因：" + err.Error())
		session.Rollback()
		return err
	}
	errMsg := ""
	if res.Code != 200 {
		errMsg = res.Message
	}
	UpdateProductThirdSyncErr(cast.ToInt(productId), ChannelMtId, financeCode, errMsg)

	if res.Code != 200 {
		glog.Info("美团价格同步接口失败，失败原因：" + res.Message)
		session.Rollback()
		return err
	}
	if err := session.Commit(); err != nil {
		session.Rollback()
		glog.Error("事务提交失败,", err)
	}
	return nil
}

// 饿了么前置仓价格更新
func UpdateElmQzc(session *xorm.Session, financeCode, storeId string, productId, skuId, price int) error {
	_ = session.Begin()
	// 更新快照和上下架信息
	err := UpdateSnapshotAndStoreUp(session, financeCode, ChannelElmId, 4, productId, price)
	if err != nil {
		_ = session.Rollback()
		return err
	}

	clientElm := GetMtProductClient()
	defer clientElm.Close()
	var elm et.SkuPriceUpdateOneRequest
	elm.SkuidPrice = fmt.Sprintf("%d:%d,%d", skuId, price, price)
	elm.ShopId = storeId

	appChannel, err := GetAppChannelByFinanceCode(financeCode)
	if err != nil {
		return errors.New("获取appChannel值失败:" + err.Error())
	}

	if appChannel == 0 {
		err = errors.New("获取appChannel值错误")
		return err
	}

	elm.AppChannel = appChannel
	if !CanCallThirdApi(cast.ToInt(productId), ChannelElmId, financeCode) {
		glog.Error("没有第三方商品id====23,商品id:", productId, "财务编码：", financeCode)

		return errors.New("商品未在第三方创建，请先编辑后再进行操作")
	}
	res, err := clientElm.ELMPRODUCT.SkuPriceUpdateOne(clientElm.Ctx, &elm)
	glog.Info("饿了么更新价格响应结果：", res, "饿了么更新价格参数", elm)
	if err != nil {
		glog.Error("饿了么价格同步失败err:", err)
		_ = session.Rollback()
		return err
	}
	UpdateProductThirdSyncErr(cast.ToInt(productId), ChannelElmId, financeCode, res.Error)

	if res.Code != 200 {
		glog.Error("饿了么价格同步失败err:", res.Error)
		_ = session.Rollback()
		return err
	}

	if err = session.Commit(); err != nil {
		_ = session.Rollback()
		glog.Error("事务提交失败,", err)
	}
	return nil
}

// 京东前置仓价格更新
func UpdateJddjQzc(session *xorm.Session, financeCode, storeId string, productId, skuId, price int) error {
	session.Begin()
	// 更新快照和上下架信息
	err := UpdateSnapshotAndStoreUp(session, financeCode, ChannelJddjId, 4, productId, price)
	if err != nil {
		session.Rollback()
		return err
	}

	clientJddj := GetMtProductClient()
	defer clientJddj.Close()
	var jddj et.UpdatePriceOneRequest
	jddj.OutStationNo = ""
	jddj.StationNo = storeId
	jddj.OutSkuId = fmt.Sprintf("%d", skuId)
	jddj.Price = cast.ToInt32(price)
	jddj.ServiceNo = utils.GenSonyflake()
	storeMasterId, err := GetAppChannelByFinanceCode(financeCode)
	if err != nil {
		glog.Error("京东价格同步失败,GetAppChannelByFinanceCode err: ", financeCode, err)
		session.Rollback()
		return err
	}
	jddj.StoreMasterId = storeMasterId

	jd_paramsjson, _ := json.Marshal(jddj)
	glog.Info("京东改价请求参数：", string(jd_paramsjson))
	res, err := clientJddj.JDDJPRODUCT.UpdatePriceOne(clientJddj.Ctx, &jddj)
	if err != nil {
		glog.Error("京东价格同步失败, err: ", err)
		session.Rollback()
		return err
	}
	if res.Code != 0 {
		glog.Error("京东价格同步失败, err: ", res.Message)
		session.Rollback()
		return errors.New(res.Message)
	}

	if err := session.Commit(); err != nil {
		session.Rollback()
		glog.Error("事务提交失败,", err)
		return err
	}
	return nil
}

func (c *Product) SyncProductToEs(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
	glog.Info("SyncProductToEs run ...", kit.JsonEncode(in))
	response := pc.BaseResponse{}

	conn := NewDbConn()
	defer conn.Close()

	redisconn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisconn.Close()
	}

	client := es.NewEsClient()
	_, err := client.DeleteIndex(es.IndexChannelStoreProduct).Do(context.Background()) // 删除索引
	if err != nil {
		glog.Error("delete index error: ", err.Error())
	}

	_, err = client.CreateIndex(es.IndexChannelStoreProduct).Do(context.Background()) // 添加索引
	if err != nil {
		glog.Error("add index error: ", err.Error())
	}

	PageSize := 5000
	PageIndex := 1

	for {

		sql := `select * from (SELECT channel_store_product.id,
                             channel_store_product.update_date,
                             channel_store_product.sales_volume,
                             channel_store_product.channel_id,
                             channel_store_product.finance_code,
                             channel_store_product.sku_id,
                             channel_store_product.up_down_state,
                             REPLACE(
                               REPLACE(CONCAT(species, ',', varieties, ',', sex, ',', shape, ',', age, ',',
                                              special_stage, ',', is_sterilization, ',', content_type, ',', 'status'),
                                       '不限,', ''), ',不限', '') as tags,
                             channel_product_snapshot.json_data,
                             channel_store_product.has_stock,
                             IFNULL(c.warehouse_id, 0)        as warehouse_id,
                             IFNULL(c.has_stock, 0) as            has_stock_new
                      FROM dc_product.channel_store_product
                             INNER JOIN dc_product.channel_product_snapshot
                               ON channel_product_snapshot.id = channel_store_product.snapshot_id
                             LEFT JOIN dc_product.product_tag
                               on product_tag.sku_id = channel_store_product.sku_id AND product_tag.product_type = 3
                             left join dc_product.channel_store_product_has_stock c
                               on c.channel_store_product_id = channel_store_product.id
                      WHERE channel_store_product.channel_id = 1 and channel_store_product.id > 0
                        ) a where a.has_stock=1 and up_down_state=1 
`

		sql += fmt.Sprintf(" limit %d,%d", PageIndex*PageSize-PageSize, PageSize)
		glog.Info("sql: ", sql)
		data := make([]*pc.ChannelProductEsBaseData, 0)
		err := conn.SQL(sql).Find(&data)
		if err != nil {
			glog.Error("exec error : ", err.Error())
		}

		glog.Info(" lenData：", len(data))

		AddToESData(data, redisconn)

		if len(data) < int(PageSize) {
			break
		}
		PageIndex++
	}

	return &response, nil
}

//
//func (c *Product) UpdateProductToEs()  {
//
//	response := pc.BaseResponse{
//		Code:    400,
//		Message: "failed",
//	}
//
//	conn := NewDbConn()
//	defer conn.Close()
//
//	sql := `select * from (SELECT channel_store_product.id,
//                             channel_store_product.update_date,
//                             channel_store_product.sales_volume,
//                             channel_store_product.channel_id,
//                             channel_store_product.finance_code,
//                             channel_store_product.sku_id,
//                             channel_store_product.up_down_state,
//                             REPLACE(
//                               REPLACE(CONCAT(species, ',', varieties, ',', sex, ',', shape, ',', age, ',',
//                                              special_stage, ',', is_sterilization, ',', content_type, ',', 'status'),
//                                       '不限,', ''), ',不限', '') as tags,
//                             channel_product_snapshot.json_data,
//                             channel_store_product.has_stock,
//                             IFNULL(c.warehouse_id, 0)        as warehouse_id,
//                             IFNULL(c.has_stock, 0)as            has_stock_new
//                      FROM dc_product.channel_store_product
//                             INNER JOIN dc_product.channel_product_snapshot
//                               ON channel_product_snapshot.id = channel_store_product.snapshot_id
//                             LEFT JOIN dc_product.product_tag
//                               on product_tag.sku_id = channel_store_product.sku_id AND product_tag.product_type = 3
//                             left join dc_product.channel_store_product_has_stock c
//                               on c.channel_store_product_id = channel_store_product.id
//                      WHERE channel_store_product.channel_id = 1
//                        ) a where a.has_stock=1 and up_down_state=1;
//`
//	//AND channel_store_product.update_date > '2019-06-18 11:26:35'   YC0122-1-1011606001-25
//
//	data := make([]*pc.ChannelProductEsBaseData, 0)
//	err := conn.SQL(sql).Find(&data)
//	if err != nil {
//		glog.Error("add index error: ", err.Error())
//	}
//
//	glog.Info("data:", data, " lenData：", len(data))
//
//	AddToESData(data,)
//
//	response.Code = 200
//	response.Message = "successful"
//
//}

// 手动操作批量更新的es的data
func AddToESData(data []*pc.ChannelProductEsBaseData, conn *redis.Client) {
	var insertIds = make([]string, 0)

	client := es.NewEsClient()
	bulkRequest := client.Bulk()

	//先一次性查出仓库
	var warehouseIds []int32
	for _, v := range data {
		warehouseIds = append(warehouseIds, v.WarehouseId)
	}
	var list []*models.Warehouse
	engine.In("id", warehouseIds).Find(&list)

	var warehosueMap = make(map[int]int)
	for _, v := range list {
		warehosueMap[v.Id] = v.Category
	}

	for _, p := range data {
		id := fmt.Sprintf("%s-%s-%d-%d", p.FinanceCode, p.ChannelId, p.SkuId, p.WarehouseId)
		//warehouseCategory, err := GetStoreRelationWarehouse(p, conn)
		//p.WarehouseCategory = int32(warehouseCategory)
		//if err != nil {
		//	glog.Error("GetStoreRelationWarehouse在查找redis的仓库信息出错：", err.Error())
		//}

		//获取仓库类型同步到es
		if _, ok := warehosueMap[cast.ToInt(p.WarehouseId)]; ok {
			warehouseCategory := warehosueMap[cast.ToInt(p.WarehouseId)]
			p.WarehouseCategory = int32(warehouseCategory)
		} else {
			glog.Error("GetStoreRelationWarehouse在查找redis的仓库信息出错")
		}

		//es只保存上架状态并且有库存的商品
		if p.UpDownState == 1 && p.HasStock == 1 {
			m := es.FormatChannelProductRequestEs(p)
			bulkRequest.Add(elastic.NewBulkIndexRequest().Index(es.IndexChannelStoreProduct).Id(id).Doc(m))
			insertIds = append(insertIds, id)
		} else {
			bulkRequest.Add(elastic.NewBulkDeleteRequest().Index(es.IndexChannelStoreProduct).Id(id))
		}
	}

	if _, err := bulkRequest.Do(context.Background()); err != nil {
		glog.Error("bulkRequest.Do error: ", err)
	}
}

//增加仓库类型字段到es
//func GetStoreRelationWarehouse(esData *pc.ChannelProductEsBaseData, redisHandle *redis.Client) (int, error) {
//	glog.Info("GetStoreRelationWarehouse参数esData:", esData)
//
//	warehouse := utils.LoadWarehouseRelationCache(redisHandle, esData.FinanceCode)
//	// 加上仓库的类型
//	if len(warehouse) > 0 {
//		for _, warehouse_tr := range warehouse {
//			if warehouse_tr.Id == int(esData.WarehouseId) {
//				return warehouse_tr.Category, nil
//			}
//		}
//	}
//
//	return 0, errors.New("没有查询到仓库类型信息")
//}

// 批量创建微信视频号商品
func (this Product) CreateWxVideoProductInfo(ctx context.Context, in *pc.AddWxProductInfoRequest) (*pc.AddProductInfoListResponse, error) {
	out := new(pc.AddProductInfoListResponse)
	out.Errcode = 400

	if len(in.Params) > 20 {
		out.Errmsg = "最多同时创建20个商品"
		return out, nil
	}

	inJson, _ := json.Marshal(&in)
	glog.Info("CreateWxVideoProductInfo 批量创建微信视频号商品参数：", string(inJson))
	closeChannel := make(chan bool)
	dataChannel := make(chan *pc.AddProductInfoResponse, len(in.Params))
	defer func() {
		if _, ok := <-closeChannel; ok {
			closeChannel <- true
		}
	}()

	// 批量返回的结果
	go func() {
		for {
			select {
			case err := <-dataChannel:
				out.Data = append(out.Data, err)
			case <-closeChannel:
				close(closeChannel)
				close(dataChannel)
				return
			}
		}
	}()
	// goroutine批量执行
	channelG := make(chan bool, 5)
	wg := new(sync.WaitGroup)
	for _, v := range in.Params {
		channelG <- true
		wg.Add(1)
		data := v
		go func(data *pc.UploadProductInfoRequest) {
			defer func() {
				<-channelG
				wg.Done()
			}()
			client := et.GetExternalClient()
			var descInfo et.DescInfo
			if data.DescInfo != nil {
				descInfo.Desc = data.DescInfo.Desc
				descInfo.Imgs = data.DescInfo.Imgs
			}
			var skus []*et.SkuInfo
			if len(data.Skus) > 0 {
				var attrs []*et.SkuAttrs
				for _, v := range data.Skus {
					for _, t := range v.SkuAttrs {
						attrs = append(attrs, &et.SkuAttrs{
							AttrKey:   t.AttrKey,
							AttrValue: t.AttrValue,
						})
					}
					skus = append(skus, &et.SkuInfo{
						OutProductId: v.OutProductId,
						OutSkuId:     v.OutSkuId,
						ThumbImg:     v.ThumbImg,
						SalePrice:    v.SalePrice,
						MarketPrice:  v.MarketPrice,
						StockNum:     v.StockNum,
						Barcode:      v.Barcode,
						SkuCode:      v.SkuCode,
						SkuAttrs:     attrs,
					})
				}
			}
			res, err := client.WxVideo.AddProductInfo(context.Background(), &et.UploadProductInfoRequest{
				OutProductId:      data.OutProductId,
				Title:             data.Title,
				Path:              data.Path,
				HeadImg:           data.HeadImg,
				QualificationPics: data.QualificationPics,
				DescInfo: &et.DescInfo{
					Desc: data.DescInfo.Desc,
					Imgs: data.DescInfo.Imgs,
				},
				ThirdCatId:  data.ThirdCatId,
				BrandId:     data.BrandId,
				InfoVersion: data.InfoVersion,
				Skus:        skus,
			})
			if err != nil {
				glog.Errorf("CreateWxVideoProductInfo 创建微信商品失败, err: %s", err)
			}
			var resPc pc.AddProductInfoResponse
			resPc.Errmsg = res.Errmsg
			resPc.Errcode = res.Errcode
			if res.Data != nil {
				var skuResps []*pc.ProductInfoResponseSkus
				for _, v := range res.Data.Skus {
					skuResps = append(skuResps, &pc.ProductInfoResponseSkus{
						SkuId:    v.SkuId,
						OutSkuId: v.OutSkuId,
					})
				}
				resPc.Data.OutProductId = res.Data.OutProductId
				resPc.Data.ProductId = res.Data.ProductId
				resPc.Data.CreateTime = res.Data.CreateTime
				resPc.Data.Skus = skuResps
			}
			dataChannel <- &resPc
		}(data)
	}
	wg.Wait()
	close(channelG)
	closeChannel <- true
	return out, nil
}

//func (c *Product) SyncUpetPrice(ctx context.Context, in *pc.IdRequest) (*pc.BaseResponse, error) {
//
//	response := &pc.BaseResponse{
//		Code: 200,
//	}
//
//	upetConn := UpetNewDbConn()
//	goods := []models.UpetGoods{}
//	upetConn.SQL("select goods_marketprice,goods_id  from testupetmart.upet_goods ug where is_virtual =1 and goods_marketprice > 0;").Find(&goods)
//	glog.Info("goods: ", len(goods))
//
//
//
//	return response, nil
//}

// 获取用户对应任务的最后一条记录
func (c *Product) GetUserUnFinishedTask(ctx context.Context, in *pc.GetUserUnFinishedTaskRequest) (*pc.TaskList, error) {
	Engine := NewDbConn()
	//out := new(pc.TaskList)

	var out pc.TaskList
	var taskList models.TaskList
	_, err := Engine.Select("id,channel_id,task_content,task_status,task_detail,operation_file_url,request_header,resulte_file_url,status,modify_id,modify_time,create_id,create_time,create_name,create_mobile,create_ip,ip_location,success_num,fail_num").
		Where("create_id = ? and task_content = ? and task_status in (1,2) and channel_id = ? and status = 1 and operation_file_url = ?", in.UserNo, in.TaskContent, in.ChannelId, in.OperationData).
		OrderBy("create_time desc").
		Get(&taskList)
	if err != nil {
		glog.Error(in.UserNo + "，获取用户对应任务的最后一条记录异常（GetUserUnFinishedTask）：" + err.Error())
		return &out, err
	}
	out.Id = taskList.Id

	return &out, nil
}

// 执行平台--商品导出并上传七牛云
func (c *Product) ExportProductInfo(req *pc.QueryProductRequest) (url string, num int32, err error) {
	var productList []*pc.ExportProduct

	req.PageIndex = 1
	req.PageSize = 5000

	for {
		out, err1 := c.ExportAllProduct(context.Background(), req)
		if err1 != nil {
			err = err1
			break
		} else if out.Code == 400 {
			err = errors.New("失败")
			break
		}
		productList = append(productList, out.Details...)
		if len(out.Details) < int(req.PageSize) {
			break
		}
		req.PageIndex++
	}
	if err != nil {
		return url, num, err
	}
	f := excelize.NewFile()
	header := []string{"商品ID", "商品名称", "商品类别", "商品分类ID", "商品分类名称", "商品品牌", "商品图片（主图位）", "商品图片（展示包装位）",
		"商品图片（展示原材料位）", "商品图片（展示特写位）", "商品图片（展示卖点位）", "图片视频", "商品卖点", "商品详情", "商品条码",
		"SKUID", "规格名称", "规格值", "市场价", "建议零售价", "瑞鹏ERP货号", "A8货号", "管易货号", "子龙货号", "有效期至", "有效时长", "是否支持过期退款",
		"是否药品仓", "是否药品", "是否处方药",
		"投药方式", "使用频次", "单次用量", "单次用量单位", "投药天数", "对应病症",
	}
	var index string
	for i := 0; i < len(header); i++ {
		if i < 26 {
			index = string(rune(65 + i))
		} else {
			n := i - 26
			index = string(rune(65+n/26)) + string(rune(65+n))
		}
		f.SetCellValue("Sheet1", index+"1", header[i])
	}

	for i, v := range productList {
		// 商品ID
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i+2), v.SpuId)
		// 商品名称
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i+2), v.Name)
		// 商品类别
		var productTypeName string
		if v.ProductType == 1 {
			productTypeName = "实物商品"
		} else if v.ProductType == 2 {
			productTypeName = "虚拟商品"
		}
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i+2), productTypeName)
		// 商品分类ID
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i+2), v.CategoryId)
		// 商品分类名称
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i+2), v.CategoryName)
		// 商品品牌
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), v.BrandName)
		picList := strings.Split(v.Pic, ",")
		// 商品图片（主图位）
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i+2), picList[0])
		if len(picList) > 1 {
			for picIndex, _ := range picList {
				switch picIndex {
				case 1:
					// 商品图片（展示包装位）
					f.SetCellValue("Sheet1", "H"+strconv.Itoa(i+2), picList[picIndex])
					break
				case 2:
					// 商品图片（展示原材料位）
					f.SetCellValue("Sheet1", "I"+strconv.Itoa(i+2), picList[picIndex])
					break
				case 3:
					// 商品图片（展示特写位）
					f.SetCellValue("Sheet1", "J"+strconv.Itoa(i+2), picList[picIndex])
					break
				case 4:
					// 商品图片（展示卖点位）
					f.SetCellValue("Sheet1", "K"+strconv.Itoa(i+2), picList[picIndex])
					break
				}
			}
		}
		// 图片视频
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i+2), v.Video)
		// 商品卖点
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i+2), v.SellingPoint)
		// 商品详情
		f.SetCellValue("Sheet1", "N"+strconv.Itoa(i+2), v.ContentPc)
		// 商品条码
		f.SetCellValue("Sheet1", "O"+strconv.Itoa(i+2), v.BarCode)
		// SKUID
		f.SetCellValue("Sheet1", "P"+strconv.Itoa(i+2), v.SkuId)
		// 规格名称
		f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i+2), v.SpecName)
		// 规格值
		f.SetCellValue("Sheet1", "R"+strconv.Itoa(i+2), v.SpecValue)
		// 市场价
		f.SetCellValue("Sheet1", "S"+strconv.Itoa(i+2), float64(v.MarketPrice)/100)
		// 建议零售价
		f.SetCellValue("Sheet1", "T"+strconv.Itoa(i+2), float64(v.RetailPrice)/100)
		// 瑞鹏ERP货号
		f.SetCellValue("Sheet1", "U"+strconv.Itoa(i+2), v.ThirdSpuSkuId1)
		// A8货号
		f.SetCellValue("Sheet1", "V"+strconv.Itoa(i+2), v.ThirdSpuSkuId2)
		// 管易货号
		f.SetCellValue("Sheet1", "W"+strconv.Itoa(i+2), v.ThirdSpuSkuId3)
		// 子龙货号
		f.SetCellValue("Sheet1", "X"+strconv.Itoa(i+2), v.ThirdSpuSkuId4)
		// 虚拟商品有效期
		if v.ProductType == 2 {
			if v.TermType == 1 {
				f.SetCellValue("Sheet1", "Y"+strconv.Itoa(i+2), time.Unix(int64(v.TermValue), 0).Format("2006/01/02"))
			} else {
				f.SetCellValue("Sheet1", "Z"+strconv.Itoa(i+2), v.TermValue)
			}
			if v.VirtualInvalidRefund == 0 {
				f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i+2), "否")
			} else {
				f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i+2), "是")
			}
		}
		// 是否药品仓
		var warehouseType string
		warehouseType = "否"
		if v.WarehouseType == 1 {
			warehouseType = "是"
		}
		f.SetCellValue("Sheet1", "AB"+strconv.Itoa(i+2), warehouseType)

		isDrugs := "否"
		if v.IsDrugs == 1 {
			isDrugs = "是"
		}
		f.SetCellValue("Sheet1", "AC"+strconv.Itoa(i+2), isDrugs)

		isPrescribedDrug := "否"
		if v.IsPrescribedDrug == 1 {
			isPrescribedDrug = "是"
		}
		f.SetCellValue("Sheet1", "AD"+strconv.Itoa(i+2), isPrescribedDrug)

		if v.IsPrescribedDrug == 1 && len(v.DrugDosage) > 0 {
			dd := new(pc.ProductDrugDosage)
			if err := json.Unmarshal([]byte(v.DrugDosage), dd); err == nil {
				f.SetCellValue("Sheet1", "AE"+strconv.Itoa(i+2), dd.DosingWayName)
				f.SetCellValue("Sheet1", "AF"+strconv.Itoa(i+2), dd.UseFrequencyName)

				if len(dd.RecommendDosage) > 0 {
					rd := make([]string, len(dd.RecommendDosage))
					for i, dosage := range dd.RecommendDosage {
						rd[i] = fmt.Sprintf("%s每次%s(%s)/公斤", dosage.Name, decimal.NewFromFloat(dosage.Value).Round(2).String(), dd.DosingUnitName)
					}
					f.SetCellValue("Sheet1", "AG"+strconv.Itoa(i+2), strings.Join(rd, ";"))
				}

				f.SetCellValue("Sheet1", "AH"+strconv.Itoa(i+2), dd.DosingUnitName)
			}

			f.SetCellValue("Sheet1", "AI"+strconv.Itoa(i+2), v.DosingDays)

			if len(v.Disease) > 0 {
				var disease []*pc.ProductDiagnoseDicList
				if err := json.Unmarshal([]byte(v.Disease), &disease); err == nil {
					ds := make([]string, len(disease))
					for i, d := range disease {
						ds[i] = d.Name
					}
					f.SetCellValue("Sheet1", "AJ"+strconv.Itoa(i+2), strings.Join(ds, ";"))
				}
			}
		}

	}

	f.Save()
	file, err := f.WriteToBuffer()
	if err != nil {
		return "", 0, err
	}
	num = cast.ToInt32(len(productList))

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	fileName := cast.ToString(time.Now().UnixNano()) + "_平台商品导出.xlsx"
	fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)
	io.Copy(fileWriter, file)
	path := config.GetString("file-upload-url") + "/fss/up"
	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()
	// 上传文件
	resp, _ := http.Post(path, contentType, bodyBuffer)
	defer resp.Body.Close()

	resp_body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", 0, err
	}
	var result uploadResult
	err = json.Unmarshal(resp_body, &result)
	if err != nil {
		return "", 0, err
	}
	if len(result.Url) == 0 {
		return "", 0, errors.New(result.Err)
	}
	return result.Url, num, nil
}

// 虚拟商品过期自动上下架
func (c *Product) TestExpireProductDown(ctx context.Context, in *pc.ProductTagsRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{
		Code:    200,
		Message: "调用成功",
	}

	glog.Info("productcenter:task:expireProductDown run ...")
	var db = NewDbConn()
	var expireChannelProductQuery = db.Table(&models.ChannelProduct{}).Where("term_type=1").And("now()>from_unixtime(term_value-86400)")
	// 记录任务执行日志
	var taskLog strings.Builder
	taskLog.WriteString("expireProductDown 开始执行")
	defer func() {
		if err := recover(); err != nil {
			taskLog.WriteString("任务出现了未处理异常")
			glog.Error("expireProductDown ", err)
		}
		glog.Info(taskLog.String())

	}()

	// A.查询是否有过期的商品数量
	expireCount, err := expireChannelProductQuery.Clone().Count()
	if err != nil {
		taskLog.WriteString(fmt.Sprintf("查询过期商品数量出现了未处理的异常:%s", err.Error()))
		return out, nil
	}
	if expireCount > 0 {
		// 先处理单个商品
		var size = 1000
		var start = 0
		for {
			// 分页查询商品信息
			var channelProducts []*models.ChannelProduct
			err := expireChannelProductQuery.OrderBy("id desc").Limit(size, start).Find(&channelProducts)
			var productsList []int32
			for _, v := range channelProducts {
				productsList = append(productsList, v.Id)
			}
			glog.Info("查询的过期商品列表productsList： ", productsList)
			if err != nil {
				taskLog.WriteString(fmt.Sprintf("分页查询过期商品出现了未处理的异常:%s", err.Error()))
				break
			}
			// 处理渠道商品
			for _, channelProduct := range channelProducts {
				// 需要处理的商品列表
				var productIds []string
				// 添加组合商品
				err = db.Table(&models.ChannelSkuGroup{}).Where("channel_id=?", channelProduct.ChannelId).Where("group_product_id=?", channelProduct.Id).Distinct("product_id").Select("product_id").Find(&productIds)
				if err != nil {
					taskLog.WriteString(fmt.Sprintf("查询过期商品的组合商品出现了未处理的异常:%s", err.Error()))
					continue
				}
				// 添加本地商品
				productIds = append(productIds, cast.ToString(channelProduct.Id))

				taskLog.WriteString(fmt.Sprintf(" 开始执行下架:%s,", strings.Join(productIds, "-")))

				// 依次处理商品西甲
				for _, productId := range productIds {
					// 查询过期商品的上架信息
					var financeCodes []string
					err := db.Table(&models.ChannelStoreProduct{}).Where("channel_id=?", channelProduct.ChannelId).And("product_id=?", productId).And("up_down_state=1").Select("finance_code").Find(&financeCodes)
					if err != nil {
						taskLog.WriteString(fmt.Sprintf("查询过期商品上架出现了未处理的异常:%s", err.Error()))
						continue
					}
					// 查询单个商品组成的组合商品
					if len(financeCodes) > 0 {
						// 开始下架
						var channelProductUpDown ChannelProductUpDown
						channelProductUpDown.ProductIds = append(channelProductUpDown.ProductIds, productId)
						channelProductUpDown.ChannelId = int(channelProduct.ChannelId)
						channelProductUpDown.FinanceCodes = append(channelProductUpDown.FinanceCodes, financeCodes...)
						channelProductUpDown.UserNo = "expireProductDown"
						channelProductUpDown.UserName = "expireProductDown"
						channelProductUpDown.DownType = enum.DownRecordTypeExpire // 过期商品自动下架
						channelProductUpDown.DownPorudct()
						if channelProductUpDown.UnknownError != nil {
							taskLog.WriteString(fmt.Sprintf("渠道:%d,商品：%d,门店:%s 过期下架出现了未处理异常:%s", channelProduct.ChannelId, channelProduct.Id, strings.Join(financeCodes, "-"), err))
						}
						for _, v := range channelProductUpDown.UpResult {
							if v.IsSuccess == false {
								taskLog.WriteString(fmt.Sprintf("渠道:%d,商品：%d,门店:%s 下架失败:%s", channelProduct.ChannelId, channelProduct.Id, v.StoreProduct.StoreFinanceCode, v.Message))
							}
						}
					}

					//todo 处理组合商品下架
					glog.Info("处理组合商品下架 run ")
					var dataProduct = make([]models.ChannelStoreProduct, 0)
					db.Table("channel_store_product").SQL(`SELECT * FROM dc_product.channel_store_product b WHERE b.product_id IN (
							SELECT DISTINCT product_id FROM dc_product.channel_sku_group a WHERE a.group_product_id = ? ) AND b.up_down_state = 1 ;`, productId).Find(&dataProduct)
					glog.Info("处理组合商品下架列表： ", len(dataProduct))
					for _, v_group := range dataProduct {
						// 开始下架
						glog.Info("下架组合商品。。。", len(dataProduct), " 組合商品的id： ", v_group.ProductId, " 下架渠道：", v_group.ChannelId)
						var channelProductUpDown ChannelProductUpDown
						channelProductUpDown.ProductIds = append(channelProductUpDown.ProductIds, cast.ToString(v_group.ProductId))
						channelProductUpDown.ChannelId = int(v_group.ChannelId)
						channelProductUpDown.FinanceCodes = append(channelProductUpDown.FinanceCodes, v_group.FinanceCode)
						channelProductUpDown.UserNo = "expireProductDown"
						channelProductUpDown.UserName = "expireProductDown"
						channelProductUpDown.DownType = enum.DownRecordTypeExpire // 过期商品自动下架
						channelProductUpDown.DownPorudct()
						if channelProductUpDown.UnknownError != nil {
							taskLog.WriteString(fmt.Sprintf("渠道组合商品下架:%d,商品：%d,门店:%s 过期下架出现了未处理异常:%s", channelProduct.ChannelId, channelProduct.Id, strings.Join(financeCodes, "-"), err))
						}
						for _, v := range channelProductUpDown.UpResult {
							if v.IsSuccess == false {
								taskLog.WriteString(fmt.Sprintf("渠道组合商品下架:%d,商品：%d,门店:%s 下架失败:%s", channelProduct.ChannelId, channelProduct.Id, v.StoreProduct.StoreFinanceCode, v.Message))
							}
						}
					}

				}
			}

			// 查询组合商品

			if len(channelProducts) < size {
				break
			}

			// 取下一页数据
			start = start + size
		}
		// 处理组合商品

	}

	return out, nil
}

// excel导入商品价格
func (c *Product) ImportHospitalProductPrice(url string, task *pc.TaskList) (*pc.ImportExcelProductResponse, error) {
	out := new(pc.ImportExcelProductResponse)
	out.Code = 400
	// 下载excel
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	//client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}

	var productErrList [][]string
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, _ := f.GetRows(sheetName)

	///var oneRow []string
	for i, row := range rows {
		if i == 0 {
			///oneRow = row
			continue
		}
		// excel商品信息导入数据库
		var productErr string
		productErr = insertHospitalProductPrice(row, task)
		if len(productErr) == 0 {
			out.SuccessNum = out.SuccessNum + 1
		} else {
			msgs := append(row, productErr)
			out.FailNum = out.FailNum + 1
			productErrList = append(productErrList, msgs)
		}
	}

	/*if out.FailNum > 0 {
		// 将处理失败的商品信息导入excel上传至七牛云
		oneRow = append(oneRow, "失败原因")
		errList := append([][]string{}, oneRow)
		errList = append(errList, productErrList...)

		url, err = ExportProductErr(errList)
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, nil
		}
		out.QiniuUrl = url
	}*/
	out.Code = 200
	return out, nil
}

// excel互联网医院商品价格导入数据库
func insertHospitalProductPrice(row []string, task *pc.TaskList) string {
	// 错误信息
	var msg string
	var price float32
	skuid := cast.ToInt(strings.TrimSpace(row[0]))
	if len(row) > 1 {
		price = cast.ToFloat32(strings.TrimSpace(row[1]))
	}

	if skuid <= 0 {
		msg = "SKUID不正确"
		return msg
	}

	if price <= 0 {
		msg = "商品价格不正确"
		return msg
	}

	//入库
	Engine := NewDbConn()
	var ProductPrice models.HospitalProductPrice
	var detail string
	var operateType int32
	if hasPrice, err := Engine.Select("id,price").Where("sku_id = ?", skuid).Get(&ProductPrice); err != nil {
		msg = err.Error()
		return msg
	} else if hasPrice {
		_, err := Engine.Where("id = ?", ProductPrice.Id).Cols("price, update_date, update_by").Update(&models.HospitalProductPrice{
			Price:      int(decimal.NewFromFloat32(price * 100).Round(0).IntPart()),
			UpdateDate: time.Now().Local(),
			UpdateBy:   task.CreateId,
			UpdateName: task.CreateName,
		})
		if err != nil {
			msg = err.Error()
			glog.Errorf("HospitalProductPriceImport互联网医院商品价格导入异常2:%+v,请求参数:%d", err, skuid)
		}
		//插入一条更新操作记录
		detail = "修改商品skuid: " + cast.ToString(skuid) + "的价格，从" + cast.ToString(kit.FenToYuan(int32(ProductPrice.Price))) + "元修改为" + cast.ToString(price) + "元"
		operateType = 2

		operateRecord := models.OperateRecord{
			OperateType:   operateType,
			OperateDetail: detail,
			CreateId:      task.CreateId,
			CreateName:    task.CreateName,
			CreateIp:      task.CreateIp,
			IpLocation:    task.IpLocation,
			CreateTime:    time.Now().Local(),
		}

		_, err = Engine.Insert(operateRecord)
		if err != nil {
			msg = err.Error()
			glog.Errorf("互联网医院商品价格导入(操作记录)异常:%+v,请求参数:%v", err, kit.JsonEncode(operateRecord))
		}
	} else {
		_, err := Engine.Insert(&models.HospitalProductPrice{
			SkuId:      skuid,
			Price:      int(decimal.NewFromFloat32(price * 100).Round(0).IntPart()),
			CreateBy:   task.CreateId,
			CreateDate: time.Now().Local(),
			UpdateDate: time.Now().Local(),
			CreateName: task.CreateName,
			UpdateName: task.CreateName,
		})
		if err != nil {
			msg = err.Error()
			glog.Errorf("HospitalProductPriceImport互联网医院商品价格导入异常3:%+v,请求参数:%d", err, skuid)
		} //插入一条更新操作记录
		/*detail = "导入商品skuid: " + cast.ToString(skuid)
		operateType = 1*/
	}

	return msg
}

// 平台--互联网医院商品价格导出并上传七牛云
func (c *Product) ExportHospitalProductPrice(skuid int32) (url string, num int32, err error) {
	Engine := NewDbConn().NewSession()
	defer Engine.Close()
	//获取商品价格列表
	var productList []*models.HospitalProductPrice
	Engine.Table("hospital_product_price")
	if skuid > 0 {
		Engine.Where("sku_id=?", skuid)
	}

	err = Engine.OrderBy("update_date desc").Find(&productList)
	if err != nil || len(productList) == 0 {
		return url, num, err
	}

	f := excelize.NewFile()
	header := []string{"SKUID", "商品价格（元）", "导入人", "导入时间", "编辑人", "编辑时间"}
	var index string
	for i := 0; i < len(header); i++ {
		index = string(rune(65 + i))
		f.SetCellValue("Sheet1", index+"1", header[i])
	}

	for i, v := range productList {
		// SKUID
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i+2), v.SkuId)
		// 商品价格（元）
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i+2), kit.FenToYuan(int32(v.Price)))
		//导入人
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i+2), v.CreateName)
		// 导入时间
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i+2), v.CreateDate)
		// 编辑人
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i+2), v.UpdateName)
		// 编辑时间
		if v.UpdateName == "" {
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), "--")
		} else {
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), v.UpdateDate)
		}
	}

	f.Save()
	file, err := f.WriteToBuffer()
	if err != nil {
		return "", 0, err
	}
	num = cast.ToInt32(len(productList))

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	fileName := cast.ToString(time.Now().UnixNano()) + "_互联网商品价格导出.xlsx"
	fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)
	io.Copy(fileWriter, file)
	path := config.GetString("file-upload-url") + "/fss/up"
	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()
	// 上传文件
	resp, _ := http.Post(path, contentType, bodyBuffer)
	defer resp.Body.Close()

	resp_body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", 0, err
	}
	var result uploadResult
	err = json.Unmarshal(resp_body, &result)
	if err != nil {
		return "", 0, err
	}
	if len(result.Url) == 0 {
		return "", 0, errors.New(result.Err)
	}
	return result.Url, num, nil
}

// excel导入仓库白名单
func (c *Product) ImportWarehouseWhite(url string, task *pc.TaskList) (*pc.ImportExcelProductResponse, error) {
	out := new(pc.ImportExcelProductResponse)
	out.Code = 400
	// 下载excel
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}

	var productErrList [][]string
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, _ := f.GetRows(sheetName)

	///var oneRow []string
	for i, row := range rows {
		if i == 0 {
			///oneRow = row
			continue
		}
		if len(row) != 2 {
			out.FailNum = out.FailNum + 1
			continue
		}
		// excel信息导入数据库
		var productErr string
		productErr = insertWarehouseWhite(row, task)
		if len(productErr) == 0 {
			out.SuccessNum = out.SuccessNum + 1
		} else {
			msgs := append(row, productErr)
			out.FailNum = out.FailNum + 1
			productErrList = append(productErrList, msgs)
		}
	}

	/*if out.FailNum > 0 {
		// 将处理失败的商品信息导入excel上传至七牛云
		oneRow = append(oneRow, "失败原因")
		errList := append([][]string{}, oneRow)
		errList = append(errList, productErrList...)

		url, err = ExportProductErr(errList)
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, nil
		}
		out.QiniuUrl = url
	}*/
	out.Code = 200
	return out, nil
}

// excel仓库白名单导入数据库
func insertWarehouseWhite(row []string, task *pc.TaskList) string {
	// 错误信息
	var msg string
	warehouseCode := strings.TrimSpace(row[0])
	warehouseName := ""
	if len(row) > 1 {
		warehouseName = row[1]
	}

	if warehouseCode == "" {
		msg = "SKUID不能为空"
		return msg
	}

	//入库
	Engine := NewDbConn()
	var WarehouseWhite models.WarehouseWhiteList
	if has, err := Engine.Table("datacenter.warehouse_white_list").Where("warehouse_code = ?", warehouseCode).Get(&WarehouseWhite); err != nil {
		msg = err.Error()
		return msg
	} else if has {
		data := models.WarehouseWhiteList{
			WarehouseName: warehouseName,
			UpdateTime:    time.Now().Local(),
			UpdateId:      task.CreateId,
			UpdateName:    task.CreateName,
		}
		_, err := Engine.Table("datacenter.warehouse_white_list").Where("warehouse_code = ?", warehouseCode).Cols("warehouse_name, update_time, update_id, update_name").Update(&data)
		if err != nil {
			msg = err.Error()
			glog.Errorf("仓库白名单导入异常2:%+v,请求参数:%d", err, task.Id)
		}
	} else {
		data := models.WarehouseWhiteList{
			WarehouseCode: warehouseCode,
			WarehouseName: warehouseName,
			CreateId:      task.CreateId,
			CreateName:    task.CreateName,
			CreateIp:      task.CreateIp,
			IpLocation:    task.IpLocation,
			CreateTime:    time.Now().Local(),
			UpdateTime:    time.Now().Local(),
			UpdateName:    task.CreateName,
		}
		_, err := Engine.Table("datacenter.warehouse_white_list").Insert(&data)
		if err != nil {
			msg = err.Error()
			glog.Errorf("仓库白名单导入异常3:%+v,请求参数:%d", err, task.Id)
		}
	}

	return msg
}

// 仓库白名单导出并上传七牛云
func (c *Product) ExportWarehouseWhite(warehouseKey string) (url string, num int32, err error) {
	Engine := NewDbConn().NewSession()
	defer Engine.Close()
	//获取商品价格列表
	var whiteList []*models.WarehouseWhiteList
	Engine.Table("datacenter.warehouse_white_list").
		Select("id,warehouse_code,warehouse_name,create_time,create_name,update_name, update_time")
	if warehouseKey != "" {
		Engine.Where("warehouse_code=? or warehouse_name=?", warehouseKey, warehouseKey)
	}

	err = Engine.OrderBy("update_time desc").Find(&whiteList)
	if err != nil || len(whiteList) == 0 {
		return url, num, err
	}

	f := excelize.NewFile()
	header := []string{"仓库编码", "仓库名称", "导入人", "导入时间", "编辑人", "编辑时间"}
	var index string
	for i := 0; i < len(header); i++ {
		index = string(rune(65 + i))
		f.SetCellValue("Sheet1", index+"1", header[i])
	}

	for i, v := range whiteList {
		// 仓库编码
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i+2), v.WarehouseCode)
		// 仓库名称
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i+2), v.WarehouseName)
		//导入人
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i+2), v.CreateName)
		// 导入时间
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i+2), v.CreateTime)
		// 编辑人
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i+2), v.UpdateName)
		// 编辑时间
		if v.UpdateTime.Format(kit.DATE_LAYOUT) == "0001-01-01" {
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), "--")
		} else {
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), v.UpdateTime)
		}
	}

	f.Save()
	file, err := f.WriteToBuffer()
	if err != nil {
		return "", 0, err
	}
	num = cast.ToInt32(len(whiteList))

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	fileName := cast.ToString(time.Now().UnixNano()) + "_仓库白名单导出.xlsx"
	fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)
	io.Copy(fileWriter, file)
	path := config.GetString("file-upload-url") + "/fss/up"
	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()
	// 上传文件
	resp, _ := http.Post(path, contentType, bodyBuffer)
	defer resp.Body.Close()

	resp_body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", 0, err
	}
	var result uploadResult
	err = json.Unmarshal(resp_body, &result)
	if err != nil {
		return "", 0, err
	}
	if len(result.Url) == 0 {
		return "", 0, errors.New(result.Err)
	}
	return result.Url, num, nil
}

// 获取产品多级分类
func (c *Product) GetProductCategory(ctx context.Context, in *pc.GetProductCategoryRequest) (out *pc.GetProductCategoryResponse, err error) {
	out = new(pc.GetProductCategoryResponse)
	db := NewDbConn()
	if (len(in.ProductIds) == 0 || (len(in.ProductIds) == 1 && in.ProductIds[0] == "")) && len(in.SkuIds) > 0 {
		var arr []string
		err = db.SQL("SELECT product_id FROM dc_product.sku WHERE id IN (?)", strings.Join(in.SkuIds, ",")).Find(&arr)
		in.ProductIds = arr
		glog.Info("GetOrderRelationInfo调用GetProductCategory方法skuid:", in, err)
	}
	if len(in.ProductIds) == 0 {
		return
	}

	glog.Info("GetOrderRelationInfo调用GetProductCategory方法productid:", in, err, len(in.ProductIds), len(in.SkuIds))

	err = db.SQL(`SELECT id product_id,ct.category_first,ct.category_second,ct.category_third FROM dc_product.product p
	LEFT JOIN (
		SELECT a.id category_first,b.id category_second,c.id category_third FROM dc_product.category a
		LEFT JOIN dc_product.category b ON a.id = b.parent_id
		LEFT JOIN dc_product.category c ON b.id = c.parent_id
		) as ct 
 	ON p.category_id = ct.category_third WHERE p.id IN (?)`, strings.Join(in.ProductIds, ",")).Find(&out.Data)
	if err != nil {
		glog.Error("GetProductCategory获取分类出错：", err, in)
	}

	glog.Info("GetOrderRelationInfo调用GetProductCategory方法结果:", in, err, kit.JsonEncode(out))

	return
}

// CheckAddCart 检测添加购物车
func (c *Product) CheckAddCart(ctx context.Context, in *pc.CheckAddCartReq) (out *pc.CheckAddCartRes, e error) {
	out = &pc.CheckAddCartRes{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("CheckAddCart 出错，", kit.JsonEncode(out))
		}
	}()

	in.BarCode = strings.TrimSpace(in.BarCode)
	if len(in.BarCode) < 1 && in.SkuId < 1 {
		out.Message = "商品条码不能为空"
		return
	}

	db := NewDbConn()
	csp := new(models.ChannelStoreProduct)

	query := db.Where("channel_id = ? and finance_code = ?", 1, in.FinanceCode)
	// 商品条码
	if len(in.BarCode) > 0 {
		query.Where("sku_id in (select id from channel_sku where channel_id = ? and bar_code = ?)", 1, in.BarCode)
	} else if in.SkuId > 0 {
		query.Where("sku_id = ?", in.SkuId)
	}

	if has, err := query.Select("up_down_state,sku_id,product_id").OrderBy("up_down_state desc").Get(csp); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		if len(in.BarCode) > 0 {
			out.Message = "该商品条码" + in.BarCode + "不存在"
		} else {
			out.Message = fmt.Sprintf("该商品%d不存在", in.SkuId)
		}
		return
	}

	out.ProductId = int32(csp.ProductId)
	out.SkuId = int32(csp.SkuId)

	if csp.UpDownState < 1 {
		out.Message = "该商品为下架状态，不能加购物车"
		return
	}

	if stock, err := GetStockInfoBySkuCodeAndShopId(in.ChannelId, int32(csp.SkuId), in.FinanceCode); err != nil {
		out.Message = err.Error()
		return
	} else {
		out.Stock = stock
	}

	out.Code = 200
	return
}

// ChannelCategoryWithCount 获取渠道分类附加门店商品数量
func (c *Product) ChannelCategoryWithCount(ctx context.Context, in *pc.ChannelCategoryWithCountReq) (out *pc.ChannelCategoryWithCountRes, e error) {
	out = &pc.ChannelCategoryWithCountRes{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("ChannelCategoryWithCount 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.ChannelId < 1 || len(in.FinanceCode) < 1 {
		out.Message = "参数错误"
		return
	}

	db := NewDbConn()
	var ccs []*models.ChannelCategory

	if err := db.Where("channel_id = ?", 1).Select("id,name,parent_id").OrderBy("if(parent_id >0,1,0) asc,sort asc").Find(&ccs); err != nil {
		out.Message = err.Error()
		return
	}

	// 商品分类统计
	type CategoryCount struct {
		CategoryId int32 `json:"category_id"`
		Count      int32 `json:"count"`
	}
	var categoryCounts []*CategoryCount

	// 查快照分类id、如果没有，则取渠道商品库分类
	if err := db.SQL(`SELECT category_id,COUNT(*) AS count FROM (
SELECT distinct cp.id,if(cps.channel_category_id > 0,cps.channel_category_id,cp.category_id) category_id from dc_product.channel_product cp 
left join dc_product.channel_product_snapshot cps on cps.product_id = cp.id and cps.channel_id = ? AND finance_code = ? where cp.channel_id = ?
) t GROUP BY category_id`, in.ChannelId, in.FinanceCode, in.ChannelId).Find(&categoryCounts); err != nil {
		out.Message = err.Error()
		return
	}

	// 数据转换处理
	countMap := make(map[int32]int32, len(categoryCounts))
	var count int32 // 全部分类
	for _, cc := range categoryCounts {
		countMap[cc.CategoryId] = cc.Count
		count += cc.Count
	}

	categoryMap := make(map[int32]*models.ChannelCategory, len(ccs))
	childrenGroups := make(map[int32][]int32, 0)
	pIds := make([]int32, 0)

	for _, cc := range ccs {
		categoryMap[cc.Id] = cc
		pId := cc.ParentId

		if cc.ParentId == 0 { // 第一级分类
			pId = cc.Id
		}
		if _, ok := childrenGroups[pId]; !ok {
			childrenGroups[pId] = make([]int32, 0)
			pIds = append(pIds, pId)
		}
		if cc.ParentId > 0 {
			childrenGroups[cc.ParentId] = append(childrenGroups[cc.ParentId], cc.Id)
		}
	}

	out.Data = append(out.Data, &pc.ChannelCategoryWithCount{
		Id:           0,
		Name:         "全部商品",
		ProductCount: count,
	})

	for _, pId := range pIds {
		subs := childrenGroups[pId]
		pName := "未知"
		if _, ok := categoryMap[pId]; ok {
			pName = categoryMap[pId].Name
		}
		channelCategoryWithCount := &pc.ChannelCategoryWithCount{
			Id:           pId,
			Name:         pName,
			ProductCount: countMap[pId],
		}
		for _, s := range subs {
			sName := "未知"
			if _, ok := categoryMap[s]; ok {
				sName = categoryMap[s].Name
			}
			channelCategoryWithCount.Children = append(channelCategoryWithCount.Children, &pc.ChannelCategoryWithCount{
				Id:           s,
				Name:         sName,
				ProductCount: countMap[s],
			})
			channelCategoryWithCount.ProductCount += countMap[s]
		}

		out.Data = append(out.Data, channelCategoryWithCount)
	}

	out.Code = 200
	return
}

// 移动分类商品的接口
/**
3：保存定时任务
*/

func (c *Product) MoveCategoryProduct(ctx context.Context, in *pc.MoveProductVo) (*pc.BaseResponse, error) {

	prefix := "移动分类商品任务："
	glog.Info(prefix, " 入参：", kit.JsonEncode(in))
	var out = &pc.BaseResponse{Code: 400}

	if in.OldCategoryId == in.NewCategoryId {
		return out, errors.New("移动分类不能选择自身")
	}

	conn := NewDbConn()
	var oldCategory models.ChannelCategory
	conn.Table(&models.ChannelCategory{}).Where("id= ?", in.OldCategoryId).Get(&oldCategory)
	if oldCategory.Id <= 0 {
		return out, errors.New("分类不存在id:" + cast.ToString(in.OldCategoryId))
	}

	var newCategory models.ChannelCategory
	conn.Table(&models.ChannelCategory{}).Where("id= ?", in.NewCategoryId).Get(&newCategory)
	if newCategory.Id <= 0 {
		return out, errors.New("分类不存在id:" + cast.ToString(in.OldCategoryId))
	}

	//校验是否有新增分类的商品在执行，在执行中的话不让移动商品
	var syncCategorys []models.TaskList
	conn.Table(&models.TaskList{}).Where("task_content = ? ", SyncCategoryTaskContent).In("task_status", []int{1, 2}).
		Where("request_header = ? ", 1).Find(&syncCategorys)
	for i := range syncCategorys {
		category := syncCategorys[i].Category
		if category == in.NewCategoryId {
			return out, errors.New("分类：" + newCategory.Name + " 还在定时任务的创建中，待完成后可移动商品")
		}
	}

	// 校验任务是否存在
	var taskId int
	conn.Table(&models.TaskList{}).Select("id").Where("task_content = ? ", MoveCategoryProduct).In("task_status", []int{1, 2}).Get(&taskId)
	if taskId > 0 {
		return out, errors.New("已经有移动商品的异步任务在进行中，请稍后再试")
	}

	// 保存参数
	inVo, _ := json.Marshal(in)

	//保存定时任务
	list := models.TaskList{
		TaskContent:      int32(MoveCategoryProduct),
		TaskStatus:       1,
		TaskDetail:       "",
		OperationFileUrl: string(inVo),
		Status:           1,
		ModifyTime:       time.Now(),
		CreateId:         in.CreateId,
		CreateTime:       time.Now(),
		CreateName:       in.CreateName,
		CreateMobile:     in.CreateMobile,
		CreateIp:         in.CreateIp,
		IpLocation:       in.IpLocation,
		ExtendedData:     oldCategory.Name,
	}
	_, err := conn.Insert(&list)
	if err != nil {
		glog.Error(prefix, " 插入任务异常:", err.Error())
		return out, err
	}

	out.Code = 200
	return out, nil
}

type MoveCategoryResult struct {
	OldCategoryId   int32                      `json:"old_category_id"`
	NewCategoryId   int32                      `json:"new_category_id"`
	TaskId          int                        `json:"task_id"`
	OldCategoryName string                     `json:"old_category_name"`
	ErrorData       []*MoveCategoryResultError `json:"error_data"`
}

type MoveCategoryResultError struct {
	FinanceCode string `json:"finance_code"`
	Id          int    `json:"id"` // sku或者productId
	Msg         string `json:"msg"`
	FinanceName string `json:"finance_name"`
	MoveType    int    `json:"move_type"`
}

func (v *MoveCategoryResult) Add(data *MoveCategoryResultError) {
	UpMoveErr.Lock()
	defer UpMoveErr.Unlock()

	v.ErrorData = append(v.ErrorData, data)
}

// 移动阿闻分类商品数据
func (c *Product) MoveAwenCategoryProduct(ctx context.Context, in *pc.MoveProductVo, task *pc.TaskList) (*MoveCategoryResult, error) {
	prefix := "移动阿闻分类商品数据 "
	glog.Info(prefix, " 开始：", kit.JsonEncode(in), " 任务ID：", task.Id)

	// 错误处理
	var mveCategoryResult = &MoveCategoryResult{
		OldCategoryId: in.OldCategoryId,
		NewCategoryId: in.NewCategoryId,
		TaskId:        int(task.Id),
	}

	var productIds = make(map[int32]struct{}, 0)
	var err error
	var categoryName string
	defer func() {
		// 保存统计数据
		c.UpdateDetailData(in, task, productIds, categoryName, MoveProductAwenChannel)
	}()

	categoryName = c.GetCategoryName(in.NewCategoryId)

	productIds, err = c.MoveCategoryCommon(ctx, in, ChannelAwenId, categoryName)
	glog.Info(prefix, " 需要同步的商品id：", kit.JsonEncode(productIds))
	if err != nil {
		// 保存错误异常
		resultError := &MoveCategoryResultError{}
		resultError.Msg = err.Error()
		resultError.MoveType = MoveProductAwenChannel
		mveCategoryResult.Add(resultError)
		return mveCategoryResult, err
	}

	glog.Info(prefix, " 结束：", kit.JsonEncode(in), " 任务ID：", task.Id)

	return mveCategoryResult, nil
}

// 移动美团商品数据
func (c *Product) MoveMtCategoryProduct(ctx context.Context, in *pc.MoveProductVo, task *pc.TaskList) (*MoveCategoryResult, error) {
	prefix := "移动美团商品数据 "
	glog.Info(prefix, " 开始", kit.JsonEncode(in), " 任务ID为", task.Id)

	// 错误处理
	var mveCategoryResult = &MoveCategoryResult{
		OldCategoryId: in.OldCategoryId,
		NewCategoryId: in.NewCategoryId,
		TaskId:        int(task.Id),
	}

	var productIds = make(map[int32]struct{}, 0)
	var err error
	var categoryName string
	var storeMaster []*dac.StoreInfo
	var successNum int32
	defer func() {
		// 美团本地保存处理详情
		c.UpdateDetailData(in, task, productIds, categoryName, MoveProductMtBenDi)

		// 美团线上
		//var strP []string
		//for i := range productIds {
		//	strP = append(strP, cast.ToString(productIds[i]))
		//}
		// 第三方门店数
		numStroe := len(storeMaster)
		data := models.MoveProductData{
			ChannelId:    MoveProductMtThird,
			CategoryId:   int(in.NewCategoryId),
			CategoryName: categoryName,
			AllNum:       len(productIds) * numStroe,
			FailNum:      len(productIds)*numStroe - int(successNum),
			SuccessNum:   int(successNum),
			//Ids:          strP,
			MoveName: MoveProductNameMap[MoveProductMtThird],
		}

		_ = UpdateDetailTask(task, &data)

	}()

	categoryName = c.GetCategoryName(in.NewCategoryId)

	productIds, err = c.MoveCategoryCommon(ctx, in, ChannelMtId, categoryName)
	glog.Info(prefix, " 需要同步的商品id：", kit.JsonEncode(productIds))
	if err != nil {
		resultError := &MoveCategoryResultError{}
		resultError.Msg = err.Error()
		resultError.MoveType = MoveProductMtBenDi
		mveCategoryResult.Add(resultError)
		return mveCategoryResult, err
	}

	if len(productIds) <= 0 {
		glog.Info(prefix, " 渠道沒有需要同步的商品信息：", ChannelMtId)
		return mveCategoryResult, nil
	}

	// 查询有配置的第三方门店
	storeMaster, err = c.GetStoreMasterResult(ChannelMtId)
	if err != nil {
		resultError := &MoveCategoryResultError{}
		resultError.Msg = "查询第三方门店异常" + err.Error()
		resultError.MoveType = MoveProductMtThird
		mveCategoryResult.Add(resultError)
		return mveCategoryResult, err
	}
	var ids []int
	for k := range productIds {
		id := k
		ids = append(ids, int(id))
	}

	groupOf := utils.ArrayInGroupsOf(ids, 100)

	var wait sync.WaitGroup

	conn := NewDbConn()
	for i := range storeMaster {

		wait.Add(1)

		info := storeMaster[i]
		channelStoreId := info.ChannelStoreId
		financeCodeOne := info.FinanceCode
		storeMasterId := info.AppChannel

		var thirdCategoryId int
		conn.SQL("select category_id from dc_product.channel_category_thirdid cct where channel_id = ? and id = ? and channel_store_id  = ? ;",
			ChannelMtId, in.NewCategoryId, channelStoreId).Get(&thirdCategoryId)

		// 100个商品一组处理
		clientMt := GetMtGlobalProductClient()
		err = MoveMtProductAnt.Submit(func() {

			defer func() {
				wait.Done()
			}()

			if thirdCategoryId <= 0 {
				glog.Error(prefix, " 该门店没有同步新分类：", financeCodeOne, " channelStoreId: ", channelStoreId, " 新分类id", in.NewCategoryId)
				resultError := &MoveCategoryResultError{}
				resultError.Msg = " 该门店没有同步新分类" + financeCodeOne + " channelStoreId: " + channelStoreId + " 新分类id" + cast.ToString(in.NewCategoryId)
				resultError.MoveType = MoveProductMtThird
				resultError.FinanceCode = financeCodeOne
				resultError.FinanceName = info.Name
				mveCategoryResult.Add(resultError)
				return
			}

			for gi := range groupOf {
				// 获取美团渠道商品
				productIds100 := groupOf[gi]

				foodData := []*et.RetailBatchinitdata{}
				// 组装批量数据
				for si := range productIds100 {
					foodData = append(foodData, &et.RetailBatchinitdata{
						AppFoodCode: cast.ToString(productIds100[si]),
						//CategoryName: "",
						CategoryCode: cast.ToString(thirdCategoryId),
					})
				}

				var retailBatchinitdataRequest = &et.RetailBatchinitdataRequest{
					AppPoiCode:    channelStoreId,
					FoodData:      foodData,
					OperateType:   2, // 编辑
					StoreMasterId: storeMasterId,
				}
				glog.Info(prefix, " 批量编辑mt商品入参: ", kit.JsonEncode(retailBatchinitdataRequest))
				initRes, err := clientMt.RPC.RetailBatchinitdata(context.Background(), retailBatchinitdataRequest)
				glog.Info(prefix, "3333美团创建/更新商品返回数据", kit.JsonEncode(initRes), ",请求数据为", kit.JsonEncode(retailBatchinitdataRequest))
				// v7.0.11 同步第三方商品ID回来
				MtProductThirdId(enum.UpdateProductThirdIdFrom13, initRes, foodData, financeCodeOne, 1)
				if err != nil {
					glog.Error(prefix, " 批量编辑mt商品异常：", err.Error(), " 门店: ", financeCodeOne, kit.JsonEncode(groupOf))
					skuError := &MoveCategoryResultError{}
					skuError.Msg = "批量编辑mt接口调用异常" + err.Error()
					skuError.MoveType = MoveProductMtThird
					skuError.FinanceCode = financeCodeOne
					//skuError.Id = cast.ToInt(productIds100)
					skuError.FinanceName = info.Name
					mveCategoryResult.Add(skuError)
					continue
				}

				if initRes.Code != 200 {
					glog.Error(prefix, " 批量编辑mt商品异常: ", kit.JsonEncode(initRes), " 门店：", financeCodeOne)
					skuError := &MoveCategoryResultError{}
					skuError.Msg = initRes.Msg + " 接口返回 " + initRes.Message
					skuError.MoveType = MoveProductMtThird
					skuError.FinanceCode = financeCodeOne
					skuError.FinanceName = info.Name
					//skuError.Id = cast.ToInt(productIds100)
					mveCategoryResult.Add(skuError)
					continue
				} else {

					if len(initRes.Msg) > 0 {
						//200和400的code返回值不一样注意
						//[{"app_food_code":"10335431","blockFlag":1,"error_msg":"不存在此商品"},{"app_food_code":"1033543134","blockFlag":1,"error_msg":"不存在此商品"}]
						var msgDataList []models.InitResErrListMsg
						_ = json.Unmarshal([]byte(initRes.Msg), &msgDataList)

						// 全部失败的不需要记录错误日志
						skuError := &MoveCategoryResultError{}
						skuError.Msg = initRes.Msg
						skuError.MoveType = MoveProductMtThird
						skuError.FinanceCode = financeCodeOne
						skuError.FinanceName = info.Name
						//skuError.Id = cast.ToInt(msg.AppFoodCode)
						mveCategoryResult.Add(skuError)

						atomic.AddInt32(&successNum, int32((len(productIds100) - len(msgDataList))))

					} else {
						atomic.AddInt32(&successNum, int32(len(productIds100)))
					}

				}
			}
		})
		if err != nil {
			antError := &MoveCategoryResultError{}
			antError.Msg = "提交美团第三方异步任务异常：" + err.Error()
			antError.MoveType = MoveProductMtThird
			mveCategoryResult.Add(antError)
			return mveCategoryResult, err
		}
	}

	wait.Wait()
	glog.Info(prefix, " 结束：", kit.JsonEncode(in), " 任务ID：", task.Id)

	return mveCategoryResult, nil
}

// 移动eleme商品数据
func (c *Product) MoveEleMeCategoryProduct(ctx context.Context, in *pc.MoveProductVo, task *pc.TaskList) (*MoveCategoryResult, error) {
	prefix := "移动eleme商品数据 "
	glog.Info(prefix, " 开始", kit.JsonEncode(in), " 任务ID为", task.Id)

	var productIds = make(map[int32]struct{}, 0)
	var err error
	var categoryName string
	var skuids []int
	var storeMaster []*dac.StoreInfo
	var successNum int32
	defer func() {
		// eleme本地保存处理详情
		c.UpdateDetailData(in, task, productIds, categoryName, MoveProductELEmeBenDi)

		// 饿了么线上
		//var strP []string
		//for i := range skuids {
		//	strP = append(strP, cast.ToString(skuids[i]))
		//}
		// 第三方门店数
		numStroe := len(storeMaster)
		data := models.MoveProductData{
			ChannelId:    MoveProductELEmeThird,
			CategoryId:   int(in.NewCategoryId),
			CategoryName: categoryName,
			AllNum:       len(skuids) * numStroe,
			FailNum:      len(skuids)*numStroe - int(successNum),
			SuccessNum:   int(successNum),
			//Ids:          strP,
			MoveName: MoveProductNameMap[MoveProductELEmeThird],
		}
		_ = UpdateDetailTask(task, &data)

	}()

	// 错误处理
	var mveCategoryResult = &MoveCategoryResult{
		OldCategoryId: in.OldCategoryId,
		NewCategoryId: in.NewCategoryId,
		TaskId:        int(task.Id),
	}
	categoryName = c.GetCategoryName(in.NewCategoryId)
	// 先处理mt本地渠道
	productIds, err = c.MoveCategoryCommon(ctx, in, ChannelElmId, categoryName)
	glog.Info(prefix, " 需要同步的商品id：", kit.JsonEncode(productIds))
	if err != nil {
		// 保存错误异常
		resultError := &MoveCategoryResultError{}
		resultError.Msg = err.Error()
		resultError.MoveType = MoveProductELEmeBenDi
		mveCategoryResult.Add(resultError)
		return mveCategoryResult, err
	}

	if len(productIds) <= 0 {
		glog.Info(prefix, " 渠道沒有需要同步的商品信息：", ChannelElmId)
		return mveCategoryResult, nil
	}

	// 编辑到eleme第三方
	// 查询有配置的第三方门店
	storeMaster, err = c.GetStoreMasterResult(ChannelElmId)
	if err != nil {
		return mveCategoryResult, err
	}
	conn := NewDbConn()
	// eleMe使用skuId
	var ids []int32
	for k := range productIds {
		productId := k
		ids = append(ids, productId)
	}

	var productSkuIds []models.Sku
	_ = conn.Table(&models.Sku{}).Select("*").In("product_id", ids).Find(&productSkuIds)

	// sku_id和product_id的映射关系
	mpSku := make(map[int32]int32, 0)
	for i := range productSkuIds {
		skuids = append(skuids, int(productSkuIds[i].Id))
		mpSku[productSkuIds[i].Id] = productSkuIds[i].ProductId
	}

	//查询相应的上下架状态
	productStore := c.GetChannelStoreProduct(ids, ChannelElmId)
	mStore := make(map[string]struct{}, len(productStore))
	for i := range productStore {
		at := productStore[i]
		key := at.FinanceCode + ":" + cast.ToString(at.SkuId)
		mStore[key] = struct{}{}
	}

	// 查询价格，取出快照的价格
	productSnap := c.GetChannelSnapProduct(ids, ChannelElmId)
	// 快照对应map
	mSnap := make(map[string]models.ChannelProductSnapshot, len(productStore))
	for i := range productSnap {
		at := productSnap[i]
		key := at.FinanceCode + ":" + cast.ToString(at.ProductId)
		mSnap[key] = at
	}

	// 分组批量执行
	groupOfSku := utils.ArrayInGroupsOf(skuids, 50)

	var wg sync.WaitGroup
	for i := range storeMaster {

		wg.Add(1)

		info := storeMaster[i]
		financeOne := info.FinanceCode
		channelStoreId := info.ChannelStoreId
		storeMasterId := info.AppChannel

		var thirdCategoryId int
		conn.SQL("select category_id from dc_product.channel_category_thirdid cct where channel_id = ? and id = ? and channel_store_id  = ? ;",
			ChannelElmId, in.NewCategoryId, channelStoreId).Get(&thirdCategoryId)

		clientElm := GetMtProductClient()
		defer clientElm.Close()
		// 直接编辑分类到第三方即可
		err = MovePEleProductAnt.Submit(func() {

			defer func() {
				wg.Done()
			}()

			if thirdCategoryId <= 0 {
				glog.Error(prefix, " 该门店没有同步新分类：", financeOne, " channelStoreId: ", channelStoreId, " 新分类id", in.NewCategoryId)
				resultError := &MoveCategoryResultError{}
				resultError.Msg = "该门店没有同步新分类" + financeOne + " channelStoreId: " + channelStoreId + " 新分类id" + cast.ToString(in.NewCategoryId)
				resultError.MoveType = MoveProductELEmeThird
				resultError.FinanceCode = financeOne
				resultError.FinanceName = info.Name
				mveCategoryResult.Add(resultError)
				return
			}

			for gi := range groupOfSku {
				ofSku := groupOfSku[gi]

				batchRequest := et.BatchUpdateElmShopSkuRequest{
					ShopId:     channelStoreId,
					AppChannel: storeMasterId,
					UpdateList: []*et.UpdateElmShopSkuRequest{},
				}
				for vi := range ofSku {
					skuId := ofSku[vi]
					request := et.UpdateElmShopSkuRequest{
						//ShopId:      channelStoreId,
						CustomSkuId: cast.ToString(skuId),
						CategoryId:  int64(thirdCategoryId),
						//SkuId:       cast.ToString(skuId),
						//AppChannel:  storeMasterId,

					}
					// 上架状态
					key := financeOne + ":" + cast.ToString(skuId)
					if _, ok := mStore[key]; ok {
						request.Status = 1
					}
					// 价格直接获取快照的即可
					if product_id, ok := mpSku[int32(skuId)]; ok {
						mSnapKey := financeOne + ":" + cast.ToString(product_id)
						if snap, ok1 := mSnap[mSnapKey]; ok1 {
							snapshot := new(pc.ChannelProductDetail)
							if err = json.Unmarshal([]byte(snap.JsonData), snapshot); err != nil {
								glog.Error(prefix, " json解析失败，", err, "，json：", snapshot)
								continue
							}
							if snapshot != nil && len(snapshot.SkuInfo) > 0 {
								request.SalePrice = snapshot.SkuInfo[0].MarketPrice
							}
						}
					}

					// 提取库存
					skuCodes := []*ic.SkuCodeInfo{}
					skuCode := &ic.SkuCodeInfo{}
					skuCode.FinanceCode = financeOne
					skuCode.Sku = cast.ToString(skuId)
					skuCodes = append(skuCodes, skuCode)

					stockMap, err := GetStockInfoBySkuCode(0, skuCodes, ChannelElmId, 1) //查询本地的库存
					if err != nil {
						glog.Error("GetStockInfoBySkuCode查询库存失败：", err)
						continue
					}
					mapkey := financeOne + ":" + cast.ToString(skuId)
					if num, ok := stockMap[mapkey]; ok {
						request.LeftNum = num
					}
					batchRequest.UpdateList = append(batchRequest.UpdateList, &request)
				}

				// 改成批量接口测试
				result, err := clientElm.ELMPRODUCT.BatchUpdateElmShopSku(clientElm.Ctx, &batchRequest)
				glog.Info(prefix, " 3333饿了么创建/更新商品返回数据为", kit.JsonEncode(result), " 入参为", kit.JsonEncode(batchRequest), " error为", kit.JsonEncode(err))
				ElmProductThirdId(enum.UpdateProductThirdIdFrom3, result.ElmBatchReturn, batchRequest.UpdateList, financeOne, mpSku)
				if err != nil {
					glog.Error(prefix, "饿了么接口失败，失败原因："+err.Error(), "门店id：", financeOne)
					errSku := &MoveCategoryResultError{}
					errSku.Msg = "调用ele批量接口更新异常：" + err.Error()
					errSku.MoveType = MoveProductELEmeThird
					errSku.FinanceCode = financeOne
					errSku.FinanceName = info.Name
					mveCategoryResult.Add(errSku)
				} else {
					if result.Code == 200 { // 全部成功
						atomic.AddInt32(&successNum, int32(len(ofSku)))
					} else { // 部分成功
						if result.Data != nil && len(result.Data.Data) > 0 {
							atomic.AddInt32(&successNum, int32(len(result.Data.Data)))
						}
						// 错误信息记录
						errSku := &MoveCategoryResultError{}
						errSku.Msg = result.Error
						errSku.MoveType = MoveProductELEmeThird
						errSku.FinanceCode = financeOne
						errSku.FinanceName = info.Name
						mveCategoryResult.Add(errSku)
					}
				}
			}
		})
		if err != nil {
			antError := &MoveCategoryResultError{}
			antError.Msg = "提交饿了么第三方异步任务异常：" + err.Error()
			antError.MoveType = MoveProductELEmeThird
			mveCategoryResult.Add(antError)
			return mveCategoryResult, err
		}
	}

	wg.Wait()
	glog.Info(prefix, " 结束：", kit.JsonEncode(in), " 任务ID：", task.Id)
	return mveCategoryResult, nil
}

// 移动jd商品数据
func (c *Product) MoveJddjCategoryProduct(ctx context.Context, in *pc.MoveProductVo, task *pc.TaskList) (*MoveCategoryResult, error) {
	prefix := "移动jd商品数据 "
	glog.Info(prefix, " 开始：", kit.JsonEncode(in), " 任务ID：", task.Id)

	var productIds = make(map[int32]struct{}, 0)
	var err error
	var categoryName string
	var skuids []int
	var storeMaster []*dac.StoreInfo
	var successNum int

	defer func() {
		// 京东本地详情保存
		c.UpdateDetailData(in, task, productIds, categoryName, MoveProductJDdjBenDi)

		// 京东第三方保存详情 京东渠道（线上）
		//var strP []string
		//for i := range skuids {
		//	strP = append(strP, cast.ToString(skuids[i]))
		//}
		// 第三方门店数
		numStroe := len(storeMaster)
		data := models.MoveProductData{
			ChannelId:    MoveProductJDdjThird,
			CategoryId:   int(in.NewCategoryId),
			CategoryName: categoryName,
			AllNum:       len(skuids) * numStroe,
			FailNum:      len(skuids)*numStroe - successNum,
			SuccessNum:   int(successNum),
			//Ids:          strP,
			MoveName: MoveProductNameMap[MoveProductJDdjThird],
		}

		_ = UpdateDetailTask(task, &data)

	}()

	// 错误处理
	var mveCategoryResult = &MoveCategoryResult{
		OldCategoryId: in.OldCategoryId,
		NewCategoryId: in.NewCategoryId,
		TaskId:        int(task.Id),
	}

	categoryName = c.GetCategoryName(in.NewCategoryId)

	productIds, err = c.MoveCategoryCommon(ctx, in, ChannelJddjId, categoryName)
	if err != nil {
		glog.Error(prefix, " 处理jd本地渠道异常： ", err.Error())
		// 保存错误异常
		resultError := &MoveCategoryResultError{}
		resultError.Msg = err.Error()
		resultError.MoveType = MoveProductJDdjBenDi
		mveCategoryResult.Add(resultError)
		return mveCategoryResult, err
	}

	if len(productIds) <= 0 {
		glog.Info(prefix, " 渠道沒有需要同步的商品信息：", ChannelElmId)
		return mveCategoryResult, nil
	}

	conn := NewDbConn()
	var ids []int32
	for k := range productIds {
		productId := k
		ids = append(ids, productId)
	}
	_ = conn.Table(&models.Sku{}).Select("id").In("product_id", ids).Find(&skuids)

	// 查询有配置的第三方门店
	storeMaster, err = c.GetStoreMasterResult(ChannelJddjId)
	if err != nil {
		glog.Error(prefix, " 查询门店信息异常： ", err.Error())
		// 保存错误异常
		resultError := &MoveCategoryResultError{}
		resultError.Msg = "查询门店信息异常" + err.Error()
		resultError.MoveType = MoveProductJDdjThird
		mveCategoryResult.Add(resultError)

		return mveCategoryResult, err
	}

	//获取重量
	var skuData []models.GjSku
	var skuDataMap = make(map[int32]float64, 0)
	conn.Table(&models.GjSku{}).In("id", skuids).Find(&skuData)
	for i2 := range skuData {
		skuDataMap[skuData[i2].Id] = skuData[i2].WeightForUnit
	}

	// 移动jd商品分类
	for i := range storeMaster {
		info := storeMaster[i]
		financeOne := info.FinanceCode
		//channelStoreId := info.ChannelStoreId
		storeMasterId := info.AppChannel
		client := et.GetExternalClient()
		defer client.Close()

		var thirdCategoryId int
		conn.SQL("select category_id from dc_product.channel_category_thirdid cct where channel_id = ? and id = ? and channel_store_id  = ? ;",
			ChannelJddjId, in.NewCategoryId, storeMasterId).Get(&thirdCategoryId)

		if thirdCategoryId <= 0 {
			glog.Error(prefix, " 该门店没有同步新分类：", financeOne, " channelStoreId: ", cast.ToString(storeMasterId), " 新分类id", in.NewCategoryId)
			resultError := &MoveCategoryResultError{}
			resultError.Msg = " 该门店没有同步新分类：" + financeOne + " channelStoreId: " + cast.ToString(storeMasterId) + " 新分类id" + cast.ToString(in.NewCategoryId)
			resultError.MoveType = MoveProductJDdjThird
			resultError.FinanceCode = financeOne
			mveCategoryResult.Add(resultError)
			continue
		}

		for ki := range skuids {
			skuId := skuids[ki]

			skuError := &MoveCategoryResultError{}
			if wight, ok := skuDataMap[int32(skuId)]; ok {
				strSku := cast.ToString(skuId)
				JddjUpdateGoodsListRequest := &et.JddjUpdateGoodsListRequest{
					TraceId:        strSku + "-" + cast.ToString(time.Now().Nanosecond()),
					OutSkuId:       strSku,
					ShopCategories: []string{cast.ToString(thirdCategoryId)},
					StoreMasterId:  storeMasterId,
					FixedStatus:    1,
					BrandId:        int64(JDBrandId),
					CategoryId:     int64(JDCategoryId),
					Weight:         float32(wight),
				}

				glog.Info(prefix, "  跟新jd商品信息,输入:", JddjUpdateGoodsListRequest, storeMasterId)
				res, err := client.JddjProduct.JddjUpdateGoodsList(ctx, JddjUpdateGoodsListRequest)
				if err != nil {
					glog.Error(prefix, " 跟新jd商品信息,异常： ", err.Error(), " storeMasterId: ", cast.ToString(storeMasterId))
					skuError.Msg = err.Error()
					skuError.MoveType = MoveProductJDdjThird
					skuError.FinanceCode = cast.ToString(storeMasterId)
					skuError.Id = skuId
					mveCategoryResult.Add(skuError)
					continue
				} else if strings.Contains(res.Message, "失败") {
					glog.Error(prefix, "  跟新jd商品信息,返回", kit.JsonEncode(res), " storeMasterId: ", cast.ToString(storeMasterId))
					skuError.Msg = res.Message
					skuError.MoveType = MoveProductJDdjThird
					skuError.FinanceCode = cast.ToString(storeMasterId)
					skuError.Id = skuId
					mveCategoryResult.Add(skuError)
					continue
				}
				successNum++
				glog.Info(prefix, " 需要同步的商品id：", kit.JsonEncode(skuids), cast.ToString(storeMasterId))
			} else {
				skuError.Msg = "获取skuId重量不存在" + cast.ToString(skuId)
				skuError.MoveType = MoveProductJDdjThird
				skuError.FinanceCode = cast.ToString(storeMasterId)
				skuError.Id = skuId
				mveCategoryResult.Add(skuError)
			}
		}
	}

	glog.Info(prefix, " 结束：", kit.JsonEncode(in), " 任务ID：", task.Id)
	return mveCategoryResult, nil

}

// 移动互联网医疗
func (c *Product) MoveDigitalHealthCategoryProduct(ctx context.Context, in *pc.MoveProductVo, task *pc.TaskList) (*MoveCategoryResult, error) {
	prefix := "移动互联网医疗 "
	glog.Info(prefix, " 开始：", kit.JsonEncode(in), " 任务ID：", task.Id)

	// 错误处理
	var mveCategoryResult = &MoveCategoryResult{
		OldCategoryId: in.OldCategoryId,
		NewCategoryId: in.NewCategoryId,
		TaskId:        int(task.Id),
	}

	var productIds = make(map[int32]struct{}, 0)
	var err error
	var categoryName string
	defer func() {
		// 移动互联网医疗保存本地数据
		_ = c.UpdateDetailData(in, task, productIds, categoryName, MoveProductInternet)
	}()

	categoryName = c.GetCategoryName(in.NewCategoryId)

	productIds, err = c.MoveCategoryCommon(ctx, in, ChannelDigitalHealth, categoryName)
	glog.Info(prefix, " 需要同步的商品id：", kit.JsonEncode(productIds))
	if err != nil {
		glog.Error(prefix, " 移动花联网医疗商品异常： ", err.Error(), task.Id)
		resultError := &MoveCategoryResultError{}
		resultError.Msg = err.Error()
		resultError.MoveType = MoveProductInternet
		mveCategoryResult.Add(resultError)
		return mveCategoryResult, err
	}

	glog.Info(prefix, " 结束：", kit.JsonEncode(in), " 任务ID：", task.Id)

	return mveCategoryResult, nil
}

// 移动分类的公共接口
func (c *Product) MoveCategoryCommon(ctx context.Context, in *pc.MoveProductVo, channel_id int, categoryName string) (map[int32]struct{}, error) {

	prefix := "移动本地分类的公共方法： "
	glog.Info(prefix, " 参数：", kit.JsonEncode(in), " channel_id: ", channel_id, " channel_name:", categoryName)

	//conn := NewDbConn().NewSession() // 加上事务跑不动锁表去除事务
	conn := NewDbConn()

	// 改过分类的商品id集合 去重
	var productIds = make(map[int32]struct{}, 0)
	//err := conn.Begin()
	//if err != nil {
	//	return productIds, err
	//}

	// 1: 处理渠道商品库商品
	var channelData []models.ChannelProduct
	conn.Table(&models.ChannelProduct{}).Where("category_id = ?", in.OldCategoryId).And("channel_id = ? ", channel_id).Find(&channelData)

	// 渠道商品的id和渠道的关系关联关系
	var dmap = make(map[int32][]string, 0)
	for i := range channelData {
		channeld := channelData[i]
		dmap[int32(channel_id)] = append(dmap[int32(channel_id)], cast.ToString(channeld.Id)) // 渠道和商品id的对应关系

		// 保存商品id
		productIds[channeld.Id] = struct{}{}
	}

	for k, v := range dmap {
		cha_id := k // 渠道id
		channelIdList := v
		//更新
		sqlUp := "update dc_product.channel_product a set a.category_id = ? , a.channel_category_id = ?  " +
			"where channel_id = ?  and id  in (%s) ;"
		channelSqlFmt := fmt.Sprintf(sqlUp, strings.Join(channelIdList, ","))
		_, err := conn.Exec(channelSqlFmt, in.NewCategoryId, in.NewCategoryId, cha_id)
		if err != nil {
			glog.Error(prefix, " 更新渠道商品库异常渠道id：", cha_id, " sku集合：", channelIdList, err.Error())
			//conn.Rollback()
			return productIds, err
		}
	}

	// 2: 处理对应渠道快照 按照门店处理
	var fincodeStrs []string
	conn.SQL("select finance_code from datacenter.store s where length(finance_code) > 0;").Find(&fincodeStrs)
	// 按照门店分组处理，快照数据太
	of := utils.ArrayStrGroupsOf(fincodeStrs, 500)

	var shouldUpdateSnap []models.SanpCategory
	for i := range of {
		finGroup := of[i]

		//		sql := `select id, finance_code, product_id from dc_product.channel_product_snapshot cps where channel_id = ?
		//			and finance_code  in ('%s')
		//and json_extract(json_data, '$.product.channel_category_id') = ?;`

		sql := `select id, finance_code, product_id from dc_product.channel_product_snapshot cps where channel_id = ?
			and  channel_category_id = ? and finance_code  in ('%s');`

		sqlSprintf := fmt.Sprintf(sql, strings.Join(finGroup, "','"))

		var snap []models.SanpCategory
		if len(finGroup) > 0 {
			conn.SQL(sqlSprintf, channel_id, in.OldCategoryId).Find(&snap)
		}
		shouldUpdateSnap = append(shouldUpdateSnap, snap...)
	}

	//if err != nil {
	//	glog.Error(prefix, " 查询快照分类异常：", err.Error())
	//	conn.Rollback()
	//	return productIds, err
	//}

	// 3：更新快照分类数据 重跑es数据
	var SnapIds []string
	for i := range shouldUpdateSnap {
		SnapIds = append(SnapIds, cast.ToString(shouldUpdateSnap[i].Id))

		// 保存商品id
		productIds[int32(shouldUpdateSnap[i].ProductId)] = struct{}{}

	}
	if len(SnapIds) > 0 {

		upSnaspSql := "update dc_product.channel_product_snapshot a " +
			"set json_data = json_set(json_data, '$.product.channel_category_id', ?, '$.product.channel_category_name', ?) where id in (%s);"

		sprintf := fmt.Sprintf(upSnaspSql, strings.Join(SnapIds, ","))
		_, err := conn.Exec(sprintf, in.NewCategoryId, categoryName)
		if err != nil {
			//conn.Rollback()
			glog.Error(prefix, " 更新快照数据异常：", err.Error())
			return productIds, err
		}

	}

	// 处理上架表商品
	if len(SnapIds) > 0 {

		upStoreSql := "update dc_product.channel_store_product a set a.channel_category_id = ? where snapshot_id in ( %s ) and channel_id = ? ;"
		sprintf := fmt.Sprintf(upStoreSql, strings.Join(SnapIds, ","))
		_, err := conn.Exec(sprintf, in.NewCategoryId, channel_id)
		if err != nil {
			//conn.Rollback()
			glog.Error(prefix, " 更新上架表数据异常：", err.Error())
			return productIds, err
		}
	}

	//err = conn.Commit()
	//if err != nil {
	//	conn.Rollback()
	//	return productIds, err
	//}

	return productIds, nil
}

func (c *Product) GetStoreMasterResult(channel_id int) ([]*dac.StoreInfo, error) {

	var StoreMasterResult []*dac.StoreInfo
	var err error
	if channel_id == ChannelElmId || channel_id == ChannelMtId {
		StoreMasterResult, err = GetThirdStoreMaster(int32(channel_id), "")
		if err != nil {
			glog.Error("GetThirdStoreMaster err: ", err.Error())
			return StoreMasterResult, err
		}
	} else if channel_id == ChannelJddjId {
		client := dac.GetDataCenterClient()
		req := dac.GetStoreMasterListRequest{
			InfoLevel: 99,
		}
		resp, err := client.RPC.GetStoreMasterList(context.Background(), &req)
		if err != nil {
			glog.Error("GetThirdStoreMaster err: ", err.Error())
			return StoreMasterResult, err
		}
		for i := range resp.Data {
			info := resp.Data[i]
			if info.JddjAppId == "" || info.JddjAppSecret == "" || info.JddjAppMerchantId == "" {
				continue
			}
			StoreMasterResult = append(StoreMasterResult, &dac.StoreInfo{AppChannel: info.Id})
		}
	}

	return StoreMasterResult, nil
}

// 更新任务
func UpdateDetailTask(task *pc.TaskList, data *models.MoveProductData) error {
	UpLok.Lock()
	defer UpLok.Unlock()
	conn := NewDbConn()

	detail := task.TaskDetail
	var movDetail []*models.MoveProductData
	if len(task.TaskDetail) > 0 {
		json.Unmarshal([]byte(detail), &movDetail)
	}

	var flag bool
	for i := range movDetail {
		if movDetail[i].ChannelId == data.ChannelId {
			//movDetail[i].Ids = data.Ids
			movDetail[i].AllNum = data.AllNum
			movDetail[i].FailNum = data.FailNum
			movDetail[i].SuccessNum = data.SuccessNum
			flag = true
		}
	}
	if !flag {
		movDetail = append(movDetail, data)
	}

	marshal, err := json.Marshal(movDetail)
	if err != nil {
		return err
	}
	task.TaskDetail = string(marshal)

	conn.Exec("update dc_product.task_list a set a.task_detail = ? where  id = ? ", task.TaskDetail, task.Id)

	return err

}

func (p *Product) GetNumStore() int {
	//获取总的门店数量
	var numStroe int
	conn := NewDbConn()
	conn.SQL("select count(distinct finance_code)  from datacenter.store s where length(finance_code) > 0 ;").Get(&numStroe)

	return numStroe
}

func (p *Product) GetCategoryName(categoryId int32) string {

	conn := NewDbConn()
	var categoryName string
	conn.SQL("select name from dc_product.channel_category cc where id = ? ", categoryId).Get(&categoryName)
	return categoryName
}

func (c *Product) UpdateDetailData(in *pc.MoveProductVo, task *pc.TaskList, productIds map[int32]struct{}, categoryName string, dataType int) error {
	//  统计数据信息task_detail
	prefix := "统计数据信息task_detail: "
	detail := task.TaskDetail
	var movDetail []models.MoveProductData
	json.Unmarshal([]byte(detail), &movDetail)

	//var strP []string
	//for i := range productIds {
	//	strP = append(strP, cast.ToString(productIds[i]))
	//}

	//获取总的门店数量
	numStroe := c.GetNumStore()

	data := models.MoveProductData{
		ChannelId:    dataType,
		CategoryId:   int(in.NewCategoryId),
		CategoryName: categoryName,
		AllNum:       len(productIds) * numStroe,
		FailNum:      0,
		SuccessNum:   len(productIds) * numStroe,
		//Ids:          strP,
		MoveName: MoveProductNameMap[dataType],
	}
	err := UpdateDetailTask(task, &data)

	if err != nil {
		glog.Error(prefix, " 跟新任务taskDetail失败：", err.Error())
		return err
	}

	return nil

}

// 导出到线上
// fileUrl 线上url
// desc 说明
func (params *MoveCategoryResult) ExportMoveProductToExcel() (fileUrl, desc string) {
	var excelRow [][]string
	// excel文件头
	excelRow = append(excelRow, []string{"任务类型", "渠道", "操作分类", "财务编码", "门店名称", "错误信息"})
	for _, v := range params.ErrorData {

		syncTypeValue := ""
		if v.MoveType == MoveProductAwenGj {
			syncTypeValue = MoveProductNameMap[MoveProductAwenGj]
		} else if v.MoveType == MoveProductAwenChannel {
			syncTypeValue = MoveProductNameMap[MoveProductAwenChannel]
		} else if v.MoveType == MoveProductMtBenDi {
			syncTypeValue = MoveProductNameMap[MoveProductMtBenDi]
		} else if v.MoveType == MoveProductMtThird {
			syncTypeValue = MoveProductNameMap[MoveProductMtThird]
		} else if v.MoveType == MoveProductJDdjBenDi {
			syncTypeValue = MoveProductNameMap[MoveProductJDdjBenDi]
		} else if v.MoveType == MoveProductJDdjThird {
			syncTypeValue = MoveProductNameMap[MoveProductJDdjThird]
		} else if v.MoveType == MoveProductELEmeBenDi {
			syncTypeValue = MoveProductNameMap[MoveProductELEmeBenDi]
		} else if v.MoveType == MoveProductELEmeThird {
			syncTypeValue = MoveProductNameMap[MoveProductELEmeThird]
		} else if v.MoveType == MoveProductInternet {
			syncTypeValue = MoveProductNameMap[MoveProductInternet]
		}

		excelRow = append(excelRow, []string{"移动商品", syncTypeValue, params.OldCategoryName, v.FinanceCode, v.FinanceName, v.Msg})
	}
	if len(excelRow) == 0 {
		return
	}

	url, err := ExportProductErr(excelRow)
	if err != nil {
		desc = "导出表格异常：" + err.Error()
		glog.Error("ExportMoveProductToExcel,err:", err, utils.ObjectToJsonString(excelRow))
	} else {
		fileUrl = url
	}

	return
}

// 处理gj渠道
func (c *Product) MoveGJProduct(ctx context.Context, in *pc.MoveProductVo, task *pc.TaskList) (*MoveCategoryResult, error) {

	prefix := "处理gj表分类"
	var categoryName string
	categoryName = c.GetCategoryName(in.NewCategoryId)

	// 错误处理
	var mveCategoryResult = &MoveCategoryResult{
		TaskId: int(task.Id),
	}

	conn := NewDbConn().NewSession()
	defer conn.Close()

	err := conn.Begin()
	if err != nil {
		glog.Error(prefix, " 开启事务异常：", err.Error())
		resultError := &MoveCategoryResultError{}
		resultError.Msg = "开启事务异常" + err.Error()
		resultError.MoveType = MoveProductAwenGj
		mveCategoryResult.Add(resultError)
		conn.Rollback()
		return mveCategoryResult, err
	}
	var gjData []int

	err = conn.Table(&models.GjProduct{}).Select("id").Where(" category_id = ?", in.OldCategoryId).Find(&gjData)
	if err != nil {
		glog.Error(prefix, " 查询gj分类商品异常：", err.Error())
		resultError := &MoveCategoryResultError{}
		resultError.Msg = "查询gj分类商品异常" + err.Error()
		resultError.MoveType = MoveProductAwenGj
		mveCategoryResult.Add(resultError)
		conn.Rollback()
		return mveCategoryResult, err
	}

	var gjIds []string
	for i := range gjData {
		gjIds = append(gjIds, cast.ToString(gjData[i]))

	}

	if len(gjIds) > 0 {
		upJgSql := "update dc_product.gj_product a set category_id = %d, category_name = ?  where id in (%s)"

		upJgSqlFmt := fmt.Sprintf(upJgSql, in.NewCategoryId, strings.Join(gjIds, ","))
		_, err = conn.Exec(upJgSqlFmt, categoryName)
		if err != nil {
			glog.Error(prefix, "更新管家商品分类异常， ", err.Error())
			resultError := &MoveCategoryResultError{}
			resultError.Msg = "更新管家商品分类异常" + err.Error()
			resultError.MoveType = MoveProductAwenGj
			mveCategoryResult.Add(resultError)
			conn.Rollback()
			return mveCategoryResult, err
		}
	}

	err = conn.Commit()
	if err != nil {
		return mveCategoryResult, err
	}

	data := models.MoveProductData{
		ChannelId:    MoveProductAwenGj,
		CategoryId:   int(in.NewCategoryId),
		CategoryName: categoryName,
		AllNum:       len(gjIds),
		FailNum:      0,
		SuccessNum:   len(gjIds),
		//Ids:          gjIds,
		MoveName: MoveProductNameMap[MoveProductAwenGj],
	}

	// 更新gj任务详情
	UpdateDetailTask(task, &data)

	return mveCategoryResult, nil
}

// 查询其中上架状态的门店商品
func (c *Product) GetChannelStoreProduct(product_ids []int32, channel_id int32) []models.ChannelStoreProduct {

	conn := NewDbConn()

	var data []models.ChannelStoreProduct
	conn.Table(&models.ChannelStoreProduct{}).Where("channel_id = ? ", channel_id).And("up_down_state = ? ", 1).
		In("product_id", product_ids).Find(&data)

	return data
}

// 查询快照取价格
func (c *Product) GetChannelSnapProduct(product_ids []int32, channel_id int32) []models.ChannelProductSnapshot {

	conn := NewDbConn()

	var data []models.ChannelProductSnapshot
	conn.Table(&models.ChannelProductSnapshot{}).Where("channel_id = ? ", channel_id).
		In("product_id", product_ids).Find(&data)

	return data
}

// 由于r1认领了很多非宜嘉的商品到阿闻，需要删除这批商品
func (c *Product) DelProductYJ(ctx context.Context, in *pc.DelProductParameter) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	defer func() {
		if r := recover(); r != nil {
			glog.Error("执行删除r1商品异常", r)
		}
	}()

	Engine := NewDbConn()
	products := make([]models.Product, 0)
	var sIds []int
	var idstr string

	if len(in.ProductId) > 0 {
		//Engine.ShowSQL()
		err := Engine.Table("product").Select("id").Where("from_oms=1 and channel_id='' and is_use=0 ").In("id", in.ProductId).Find(&products)
		if err != nil {
			fmt.Println(err.Error())
		}
	} else {
		if len(in.BeginTime) > 0 && len(in.EndTime) > 0 {
			Engine.Table("product").Select("id").Where("from_oms=1 and channel_id='' and is_use=0 and  create_date>? and  create_date<?", in.BeginTime, in.EndTime).Find(&products)
		} else {
			//先查询出来要处理是数据
			Engine.Table("product").Select("id").Where("from_oms=1 and channel_id='' and is_use=0 and  create_date>='2022-08-06 17:00' and  create_date<='2022-08-06 21:42:29'").Find(&products)
		}
	}

	for _, item := range products {
		sIds = append(sIds, item.Id)
		idstr = idstr + cast.ToString(item.Id) + ","
		if len(sIds) >= 1000 {
			c.DelProductTable(Engine, idstr)
			//清空数组
			sIds = []int{}
			idstr = ""
		}
	}
	if len(sIds) > 0 {
		c.DelProductTable(Engine, idstr)
	}

	out.Code = 200
	return out, nil
}

// 由于r1认领了很多非宜嘉的商品到阿闻，需要删除这批商品
func (c *Product) DelProductTable(Engine *xorm.Engine, sIds string) {

	sIds = strings.TrimRight(sIds, ",")
	Engine.Exec("delete from product_tag where product_id in (" + sIds + ")")
	Engine.Exec("delete from product_attr where product_id in (" + sIds + ")")
	Engine.Exec("delete from sku where product_id in (" + sIds + ")")
	Engine.Exec("delete from sku_third where product_id in (" + sIds + ")")
	Engine.Exec("delete from sku_value where product_id in (" + sIds + ")")

	Engine.Exec("delete from gj_product_tag where product_id in (" + sIds + ")")
	Engine.Exec("delete from gj_product_attr where product_id in (" + sIds + ")")
	Engine.Exec("delete from gj_sku where product_id in (" + sIds + ")")
	Engine.Exec("delete from gj_sku_third where product_id in (" + sIds + ")")
	Engine.Exec("delete from gj_sku_value where product_id in (" + sIds + ")")
	Engine.Exec("delete from gj_product where id in (" + sIds + ")")
	Engine.Exec("delete from product where id in (" + sIds + ")")

}

/*
*
校验格式化之后的美团数据是否正常
*/
func (c *Product) CheckFooData(foodData []*et.RetailBatchinitdata, response *pc.BaseResponse, financeCodeOne string, m map[int32]int32) []*et.RetailBatchinitdata {

	var newFooData []*et.RetailBatchinitdata

	for i := range foodData {
		batchinitdata := foodData[i]

		categoryName := batchinitdata.CategoryName // 分类名称
		contents := batchinitdata.PictureContents  // 图片详情
		split := strings.Split(contents, ",")
		if len(split) > 20 {
			// 同步创建更新商品的时候，商品的详情图片超过20个会报错需要排除   {"data":"ng","msg":"","Error":{"code":705,"msg":"参数格式错误:图片详情字段中的图片数量上限是20个"}
			response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
				ProductId:   cast.ToString(batchinitdata.AppFoodCode),
				SkuId:       cast.ToString(m[cast.ToInt32(batchinitdata.AppFoodCode)]),
				IsSuccess:   false,
				Message:     "图片详情字段中的图片数量上限是20个,此商品为" + cast.ToString(len(split)),
				FinanceCode: financeCodeOne,
			})
		} else if strings.Contains(categoryName, "处方") {
			//创建到美团返回：{"data":"ng","msg":"","Error":{"code":705,"msg":"参数格式错误:分类[猫处方粮]包含敏感词：[分类和分类描述]不得包含[处方]"},"Code":400,"message":"参数格式错误:分类[猫处方粮]包含敏感词：[分类和分类描述]不得包含[处方]","error_list":[{"app_spu_code":"","msg":"参数格式错误:分类[猫处方粮]包含敏感词：[分类和分类描述]不得包含[处方]"}]}
			response.UpDownDetail = append(response.UpDownDetail, &pc.UpDownERROR{
				ProductId:   cast.ToString(batchinitdata.AppFoodCode),
				SkuId:       cast.ToString(m[cast.ToInt32(batchinitdata.AppFoodCode)]),
				IsSuccess:   false,
				Message:     fmt.Sprintf("[分类和分类描述]不得包含[处方],此商品categoryName为[%s]", categoryName),
				FinanceCode: financeCodeOne,
			})
		} else {
			newFooData = append(newFooData, batchinitdata)
		}
	}

	return newFooData
}

// R1PriceSync r1价格同步
func (c *Product) R1PriceSync(ctx context.Context, in *pc.R1PriceSyncReq) (out *pc.ProductBaseResponse, e error) {
	out = &pc.ProductBaseResponse{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("R1PriceSync 价格同步处理出错，入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.Prices) == 0 {
		out.Code = 200
		return
	}

	total := len(in.Prices)
	maxInsert := 1000

	session := NewDbConn().NewSession()
	defer session.Close()
	// 存在价格更新的货号
	var updateNos []string

	for i := 0; i < total; i = i + maxInsert {
		end := i + maxInsert
		if end > total {
			end = total
		}

		prices := in.Prices[i:end]
		var skuNos []string

		for _, price := range prices {
			skuNos = append(skuNos, price.SkuNo)
		}

		session.Begin()

		var r1Prices []*models.R1Price
		if err := session.Select("id,sku_no,centralized_purchase_price,retail_price,trade_price").
			In("sku_no", skuNos).Find(&r1Prices); err != nil {
			out.Message = err.Error()
			return
		}
		r1PricesMap := make(map[string]*models.R1Price, len(r1Prices))
		for _, price := range r1Prices {
			r1PricesMap[price.SkuNo] = price
		}

		var inserts []*models.R1Price
		// 用来标记避免重复插入导致出错
		insertRecords := make(map[string]struct{})

		for _, price := range prices {
			r1Price := new(models.R1Price)
			r1Price.SkuNo = price.SkuNo
			r1Price.RetailPrice = int(decimal.NewFromFloat32(price.RetailPrice * 100).Round(0).IntPart())
			r1Price.TradePrice = int(decimal.NewFromFloat32(price.TradePrice * 100).Round(0).IntPart())

			if price.CentralizedPurchasePrice > 0 {
				r1Price.CentralizedPurchasePrice = int(decimal.NewFromFloat32(price.CentralizedPurchasePrice * 100).Round(0).IntPart())
			} else {
				r1Price.CentralizedPurchasePrice = r1Price.TradePrice
			}

			oldR1Price, has := r1PricesMap[price.SkuNo]
			if has {
				if oldR1Price.CentralizedPurchasePrice != r1Price.CentralizedPurchasePrice {
					updateNos = append(updateNos, price.SkuNo)
				}
				// 价格未变更不处理
				if oldR1Price.CentralizedPurchasePrice == r1Price.CentralizedPurchasePrice &&
					oldR1Price.RetailPrice == r1Price.RetailPrice &&
					oldR1Price.TradePrice == r1Price.TradePrice {
					continue
				}
				if _, err := session.ID(oldR1Price.Id).Cols("centralized_purchase_price,retail_price,trade_price").
					Update(r1Price); err != nil {
					session.Rollback()
					out.Message = err.Error()
					return
				}
			} else if _, exist := insertRecords[r1Price.SkuNo]; !exist {
				inserts = append(inserts, r1Price)
				insertRecords[r1Price.SkuNo] = struct{}{}
				updateNos = append(updateNos, price.SkuNo)
			}
		}

		if len(inserts) > 0 {
			if _, err := session.Insert(inserts); err != nil {
				session.Rollback()
				out.Message = err.Error()
				return
			}
		}
		if err := session.Commit(); err != nil {
			out.Message = err.Error()
			return
		}
	}

	// R1价格同步到Sku
	if len(updateNos) > 0 {
		go c.R1PriceSyncSku(ctx, &pc.R1PriceSyncSkuReq{Search: strings.Join(updateNos, ","), Type: 2})
	}

	out.Code = 200

	return
}

// R1PriceSyncSku r1价格同步到Sku
func (c *Product) R1PriceSyncSku(ctx context.Context, in *pc.R1PriceSyncSkuReq) (out *pc.ProductBaseResponse, e error) {
	out = &pc.ProductBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("R1PriceSyncSku 价格同步到Sku处理出错，入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.Type != 9 && len(in.Search) == 0 {
		out.Message = "Search不能为空"
		return
	}

	session := NewDbConn().NewSession()
	defer session.Close()
	_ = session.Begin()

	// 受影响的行数计数
	var affected, groupAffected int64
	var groupSearch string

	// 不是限定组合商品更新
	if in.Type != 3 {
		var filter string
		switch in.Type {
		case 1:
			filter = "where s.id in (" + in.Search + ")"
		case 2:
			filter = "where rp.sku_no in ('" + strings.Join(strings.Split(in.Search, ","), "','") + "')"
		case 9:
		default:
			out.Message = "Type无效"
			return
		}
		// 先更新一波实物商品
		if rs, err := session.Exec(`update dc_product.r1_price rp
	    join dc_product.sku_third t on rp.sku_no = t.third_sku_id and erp_id = 2
	    join dc_product.sku s on s.id = t.sku_id
	set s.r1_purchase_price = rp.centralized_purchase_price ` + filter); err != nil {
			out.Message = err.Error()
			return
		} else {
			affected, _ = rs.RowsAffected()
		}

		// 再处理组合商品
		switch in.Type {
		case 1:
			groupSearch = "select distinct sku_id from dc_product.sku_group where group_sku_id in (" + in.Search + ")"
		case 2:
			groupSearch = fmt.Sprintf(`select distinct sg.sku_id from dc_product.sku_group sg 
    join dc_product.sku_third st on st.sku_id = sg.group_sku_id and st.erp_id = 2 
                          where st.third_sku_id in ('%s')`, strings.Join(strings.Split(in.Search, ","), "','"))
		}
	} else {
		groupSearch = in.Search
	}

	// 更新组合商品
	var filter string
	if len(groupSearch) > 0 {
		filter = "where sg.sku_id in (" + groupSearch + ")"
	}
	// 再更新一波组合商品
	if rs, err := session.Exec(fmt.Sprintf(`update dc_product.sku s
	    join (select sg.sku_id, if(COUNT(rp.centralized_purchase_price) = COUNT(1),sum(sg.count * rp.centralized_purchase_price),0) as purchase_price
	from dc_product.sku_group sg
	         left join dc_product.sku_third st on st.sku_id = sg.group_sku_id and st.erp_id = 2
	         left join dc_product.r1_price rp on rp.sku_no = st.third_sku_id %s
	group by sg.sku_id) t on t.sku_id = s.id
	set s.r1_purchase_price = t.purchase_price `, filter)); err != nil {
		session.Rollback()
		out.Message = err.Error()
		return
	} else {
		groupAffected, _ = rs.RowsAffected()
	}

	if err := session.Commit(); err != nil {
		out.Message = err.Error()
		return
	}

	out.Message = fmt.Sprintf("实物商品影响%d行，实实组合影响%d行", affected, groupAffected)
	out.Code = 200

	return
}

// DiagnoseDic 诊断病种列表
func (c *Product) DiagnoseDic(ctx context.Context, in *pc.ProductDiagnoseDicReq) (out *pc.ProductDiagnoseDicRes, e error) {
	out = &pc.ProductDiagnoseDicRes{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("Product DiagnoseDic，入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	in.Keyword = strings.TrimSpace(in.Keyword)

	db := GetMedicalDBConn()
	q := db.Table("pm_dic_diagnose").Where("is_enable = 2")

	if len(in.Keyword) > 0 {
		q.Where("third_class_disease like ?", "%"+in.Keyword+"%")
	}

	pr := &utils.PaginateReq{
		Count:    q.Clone().Select("count(*)"),
		List:     q.Select("disease_code as code,third_class_disease as name"),
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.TotalCount, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// QueryDisease 根据药品查询病症
func (c *Product) QueryDisease(ctx context.Context, in *pc.ProductQueryDiseaseReq) (out *pc.ProductQueryDiseaseRes, e error) {
	out = &pc.ProductQueryDiseaseRes{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("Product QueryDisease，入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.Skus) == 0 {
		out.Message = "商品不能为空"
		return
	}
	if len(in.FinanceCode) == 0 {
		out.Message = "门店财务编码不能为空"
	}

	skuIds := make([]string, len(in.Skus))
	skuNums := make(map[int32]int32)
	for i, sku := range in.Skus {
		skuIds[i] = cast.ToString(sku.SkuId)
		skuNums[sku.SkuId] += sku.Num
	}
	idsStr := strings.Join(skuIds, ",")

	type SkuDisease struct {
		SkuId       int32
		Count       int32
		ParentSkuId int32
		Name        string
		SpecName    string
		Pic         string
		Disease     []*pc.ProductDiagnoseDicList `xorm:"default '' comment('对应病症') JSON"`
	}
	var sds []*SkuDisease

	// 从快照中提取商品名及图片，因为前端列表组合商品子商品没有图片
	if err := NewDbConn().SQL(fmt.Sprintf(`select t.sku_id,t.count,t.parent_sku_id,p.disease,
                json_unquote(JSON_EXTRACT(ps.json_data,'$.product.name')) as name,
                json_unquote(JSON_EXTRACT(json_data,'$.product.pic')) as pic,
                json_unquote(JSON_EXTRACT(json_data,'$.sku_info[0].skuv[0].spec_value_value')) as spec_name,
                ps.id as s_id from (select id as sku_id,product_id,0 as count,0 as parent_sku_id from dc_product.sku s where s.id in (%s)
union all
select group_sku_id as sku_id,group_product_id as product_id,count,sku_id as parent_sku_id from dc_product.sku_group sg where sg.sku_id in (%s) and product_type = 1
) t join dc_product.product p on p.id = t.product_id
join dc_product.channel_product_snapshot ps on ps.product_id = t.product_id and ps.channel_id = 1 and ps.finance_code = ?
where p.product_type = 1 and p.is_prescribed_drug = 1;`, idsStr, idsStr), in.FinanceCode).Find(&sds); err != nil {
		out.Message = err.Error()
		return
	}

	qds := make(map[int32]*pc.ProductQueryDiseaseRes_ProductQueryDisease)
	for _, sd := range sds {
		var num int32
		// 是组合商品子商品
		if sd.ParentSkuId > 0 {
			num = skuNums[sd.ParentSkuId] * sd.Count
		} else {
			num = skuNums[sd.SkuId]
		}
		if qd, has := qds[sd.SkuId]; has {
			qd.Num += num
		} else {
			sd.Pic = strings.Split(strings.TrimLeft(sd.Pic, ","), ",")[0]
			//增加图片的等比压缩 -- 本地生活的图片进行压缩处理
			if strings.Contains(sd.Pic, "file.vetscloud.com") ||
				strings.Contains(sd.Pic, "file.rvet.cn") ||
				strings.Contains(sd.Pic, "www.vetscloud.com") {
				sd.Pic += "|equal_proportion"
			}
			qds[sd.SkuId] = &pc.ProductQueryDiseaseRes_ProductQueryDisease{
				SkuId:   sd.SkuId,
				Num:     num,
				Name:    sd.Name + " " + sd.SpecName,
				Pic:     sd.Pic,
				Disease: sd.Disease,
			}
		}
	}
	if len(qds) > 0 {
		for _, qd := range qds {
			out.Data = append(out.Data, qd)
		}
	}

	out.Code = 200
	return
}

// DrugInfo 查询商品药品属性
func (c *Product) DrugInfo(ctx context.Context, in *pc.ProductDrugInfoReq) (out *pc.ProductDrugInfoRes, e error) {
	out = &pc.ProductDrugInfoRes{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("Product DrugInfo，入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.Ids) == 0 {
		out.Code = 200
		return
	}

	db := NewDbConn()

	var ps []*models.Product
	if err := db.Table("product").In("id", in.Ids).Select("id,is_drugs,is_prescribed_drug").Find(&ps); err != nil {
		out.Message = err.Error()
		return
	}

	if len(ps) > 0 {
		out.Data = make(map[int32]*pc.ProductDrugInfoRes_DrugInfo, len(ps))
		for _, p := range ps {
			out.Data[int32(p.Id)] = &pc.ProductDrugInfoRes_DrugInfo{
				IsDrugs:          int32(p.IsDrugs),
				IsPrescribedDrug: int32(p.IsPrescribedDrug),
			}
		}
	}

	out.Code = 200
	return
}

// PetType 获取药品用量对应的宠物类型
func (c *Product) PetType(ctx context.Context, in *empty.Empty) (out *pc.ProductPetTypeRes, e error) {
	out = &pc.ProductPetTypeRes{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("Product PetType", "，返回：", kit.JsonEncode(out))
		}
	}()

	db := GetZlDBConn()

	if err := db.SQL("SELECT code,name FROM hospital_db.sys_company_dict WHERE brand_id=13 AND parent_code = 1003;").Find(&out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// ZiLongDrugSync 子龙商品处方药属性同步
func (c *Product) ZiLongDrugSync(ctx context.Context, in *pc.ZiLongDrugSyncReq) (out *pc.ProductBaseResponse, e error) {
	out = &pc.ProductBaseResponse{Code: 200}
	defer func() {
		glog.Info("Product ZiLongDrugSync 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	if len(in.Data) == 0 {
		return
	}
	// 异步处理
	go func() {
		var err error

		defer func() {
			if err != nil {
				glog.Warning("Product ZiLongDrugSync 出处理出错：", err.Error())
			}
		}()

		total := len(in.Data)
		// 单次处理的商品数量
		per := 5000
		session := NewDbConn().NewSession()
		defer session.Close()

		type SkuThird struct {
			SkuId      string
			ThirdSkuId string
		}

		for i := 0; i < total; i = i + per {
			end := i + per
			if end > total {
				end = total
			}
			changes := in.Data[i:end]

			var nos []string
			for _, change := range changes {
				nos = append(nos, change.ProductCode)
			}

			var skuThirds []*SkuThird
			if err = session.In("third_sku_id", nos).And("erp_id = 4").Select("sku_id,third_sku_id").Find(&skuThirds); err != nil {
				return
			} else if len(skuThirds) == 0 {
				continue
			}

			thirdSkuMap := make(map[string]string, len(skuThirds))
			for _, third := range skuThirds {
				thirdSkuMap[third.ThirdSkuId] = third.SkuId
			}

			// 不可销skuId
			var unSellSkuIds []string
			// 处方药skuId
			var prescribeSkuIds []string
			// 非处方药skuId
			var unPrescribeSkuIds []string

			for _, change := range changes {
				if skuId, has := thirdSkuMap[change.ProductCode]; has {
					if change.CanSell == 0 {
						unSellSkuIds = append(unSellSkuIds, skuId)
					}
					if change.IsPrescribedDrug == 0 {
						unPrescribeSkuIds = append(unPrescribeSkuIds, skuId)
					} else {
						prescribeSkuIds = append(prescribeSkuIds, skuId)
					}
				}
			}

			session.Begin()

			//			// 不可销售，上架的直接下架   燕武做的功能里面有不可销下架了。不需要重复做
			//			if len(unSellSkuIds) > 0 {
			//				ids := strings.Join(unSellSkuIds, ",")
			//				// 参考 channel_product_up DownPorudct() 逻辑
			//				if _, err = session.Exec(`update (select product_id from dc_product.sku where id in (` + ids + `)
			//union all select product_id from dc_product.sku_group where group_sku_id in (` + ids + `)) t
			//join dc_product.channel_store_product sp on sp.product_id = t.product_id and sp.channel_id = 1 and sp.up_down_state = 1
			// and sp.finance_code IN (SELECT b.shop_id FROM dc_dispatch.warehouse a
			// INNER JOIN dc_dispatch.warehouse_relation_shop b ON a.id=b.warehouse_id
			// WHERE category=3 AND b.channel_id=1)
			//left join dc_product.channel_store_product_has_stock h on h.channel_store_product_id = sp.id
			//set sp.up_down_state = 0,sp.down_type = 5,h.has_stock_up = 0;`); err != nil {
			//					session.Rollback()
			//					return
			//				}
			//			}
			// 处理处方药变非处方药，清空处方药品属性
			if len(unPrescribeSkuIds) > 0 {
				ids := strings.Join(unPrescribeSkuIds, ",")
				if _, err = session.Exec(`update dc_product.sku s
join dc_product.product p on p.id = s.product_id and p.is_prescribed_drug = 1
left join dc_product.gj_product gp on gp.id = p.id
set p.is_prescribed_drug = 0,gp.is_prescribed_drug = 0,p.disease = null,p.drug_dosage = null,p.dosing_days = 0
where s.id in (` + ids + `)`); err != nil {
					session.Rollback()
					return
				}

				// 组合商品处理
				if _, err = session.Exec(`update dc_product.product p
left join dc_product.gj_product gp on gp.id = p.id
join (select sg.product_id,max(p.is_drugs) as is_drugs,max(is_prescribed_drug) as is_prescribed_drug from dc_product.sku_group sg
         join dc_product.product p on sg.group_product_id = p.id
         where sg.sku_id in (select distinct sku_id from dc_product.sku_group where group_sku_id in (` + ids + `))
group by sg.product_id) t on t.product_id = p.id
set p.is_drugs = t.is_drugs,gp.is_drugs = t.is_drugs,
p.is_prescribed_drug = t.is_prescribed_drug,gp.is_prescribed_drug = t.is_prescribed_drug;`); err != nil {
					return
				}
			}
			// 非处方药变处方药下架
			if len(prescribeSkuIds) > 0 {
				ids := strings.Join(prescribeSkuIds, ",")
				// 参考 channel_product_up DownPorudct() 逻辑
				if _, err = session.Exec(`update(select product_id from dc_product.sku where id in (` + ids + `)
union all select product_id from dc_product.sku_group where group_sku_id in (` + ids + `)) t
join dc_product.product p on p.id = t.product_id and p.is_prescribed_drug = 0
left join dc_product.gj_product gp on gp.id = p.id
left join dc_product.channel_store_product sp on sp.product_id = p.id and sp.channel_id = 1 and sp.up_down_state = 1
 and sp.finance_code IN (SELECT b.shop_id FROM dc_dispatch.warehouse a
 INNER JOIN dc_dispatch.warehouse_relation_shop b ON a.id=b.warehouse_id
 WHERE category=3 AND b.channel_id=1) 
left join dc_product.channel_store_product_has_stock h on h.channel_store_product_id = sp.id
set sp.up_down_state = 0,sp.down_type = 6,h.has_stock_up = 0,
p.is_prescribed_drug = 1,p.is_drugs = 1,gp.is_drugs = 1,gp.is_prescribed_drug = 1`); err != nil {
					session.Rollback()
					return
				}
			}

			if err = session.Commit(); err != nil {
				return
			}
		}
	}()

	return
}

// v6.10 更改货号，获取下架商品对应的门店仓id、清除对应仓库的库存
func (c *Product) GetUpProductsWarehouseIds(skuId int32, eType int) {
	db := NewDbConn()
	redis := GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}

	type SkuIds struct {
		SkuId string `json:"sku_id"`
	}
	skuIds := make([]SkuIds, 0)
	// 查询是否有组合商品，有则同样下架并清除库存
	if err := db.SQL("select distinct s.sku_id from (select distinct g.sku_id from sku_group g left join "+
		"sku_third s on s.sku_id = g.group_sku_id where s.sku_id = ?)t left join sku_third s "+
		"on s.sku_id = t.sku_id and s.erp_id = 4;", skuId).Find(&skuIds); err != nil {
	}
	var ids = []string{cast.ToString(skuId)}
	if len(skuIds) > 0 {
		for _, v := range skuIds {
			ids = append(ids, v.SkuId)
		}
	}
	id := strings.Join(ids, ",")

	type channelWarehouse struct {
		Id          int    `json:"id"`
		Goodsid     string `json:"goodsid"`
		Stock       int    `json:"stock"`
		WarehouseId int    `json:"warehouse_id"`
	}
	warehouseGoods := make([]channelWarehouse, 0)

	if eType == 1 {
		if err := db.SQL("select g.* from dc_order.warehouse_goods g left join dc_dispatch.warehouse w  " +
			"on w.id = g.warehouse_id  where  w.category != 3 and g.goodsid in (" + id + ");").
			Find(&warehouseGoods); err != nil {
			glog.Error("更改货号,查询渠道的前置仓库信息,sku_id:", id, ",error:", err.Error())
		}
		//更新数据表库存0
		if _, err := db.Exec("update dc_order.`warehouse_goods` t join dc_dispatch.`warehouse` w " +
			"on t.`warehouse_id` =w.id and w.`category` != 3 set t.`stock`=0 where t.`goodsid` in (" + id + ")"); err != nil {
			glog.Error("更改货号,更新为自动下架类型,sku_id:", id, ",error:", err.Error())
		}
	} else if eType == 2 {
		if err := db.SQL("select g.* from dc_order.warehouse_goods g left join dc_dispatch.warehouse w  " +
			"on w.id = g.warehouse_id  where  w.category = 3 and g.goodsid in (" + id + ");").
			Find(&warehouseGoods); err != nil {
			glog.Error("更改货号,查询渠道的门店仓库信息,sku_id:", id, ",error:", err.Error())
		}
		//更新数据表库存0
		if _, err := db.Exec("update dc_order.`warehouse_goods` t join dc_dispatch.`warehouse` w " +
			"on t.`warehouse_id` =w.id and w.`category` = 3 set t.`stock`=0 where t.`goodsid` in (" + id + ")"); err != nil {
			glog.Error("更改货号,更新为自动下架类型,sku_id:", id, ",error:", err.Error())
		}
	} else if eType == 3 {
		if err := db.SQL("select g.* from dc_order.warehouse_goods g left join dc_dispatch.warehouse w  " +
			"on w.id = g.warehouse_id  where  g.goodsid in (" + id + ");").
			Find(&warehouseGoods); err != nil {
			glog.Error("更改货号,查询渠道的门店仓库信息,sku_id:", id, ",error:", err.Error())
		}
		//更新数据表库存0
		if _, err := db.Exec("update dc_order.`warehouse_goods` t join dc_dispatch.`warehouse` w " +
			"on t.`warehouse_id` =w.id set t.`stock`=0 where t.`goodsid` in (" + id + ")"); err != nil {
			glog.Error("更改货号,更新为自动下架类型,sku_id:", id, ",error:", err.Error())
		}
	}

	if len(warehouseGoods) == 0 {
		glog.Info("更改货号,查询渠道的门店无仓库信息记录,sku_id:", id)
		return
	}
	for _, v := range warehouseGoods {
		redis.HDel(fmt.Sprintf("stock:%s", v.Goodsid), cast.ToString(v.WarehouseId))
	}

	var channelIds []int
	if err := db.SQL("select channel_id from channel_store_product where sku_id in(" + id + ") group by channel_id;").
		Find(&channelIds); err != nil {
		glog.Error("更改货号,查询渠道的门店仓库信息,sku_id:", id, ",error:", err.Error())
	}

	var data []*models.UpProduct
	if err := db.SQL("select id, channel_id, finance_code ,product_id, sku_id,down_type " +
		"from channel_store_product where up_down_state = 1 and  sku_id in(" + id + ");").Find(&data); err != nil {
		glog.Error("更改货号,查询渠道的商品信息,sku_id:", id, ",error:", err.Error())
	}

	var warehouseRelationShop models.ChannelWarehouse
	for _, v := range data {
		//过滤仓库
		if _, err := db.SQL("select wrs.shop_id,wrs.shop_name,w.code as warehouse_code,w.id as warehouse_id,w.category,wrs.channel_id "+
			"from dc_dispatch.warehouse_relation_shop wrs "+
			"join dc_dispatch.warehouse w on wrs.warehouse_id  = w.id where wrs.shop_id =? and wrs.channel_id = ?;", v.FinanceCode, v.ChannelId).
			Get(&warehouseRelationShop); err != nil {
			glog.Error("更改货号,查询渠道的门店仓库信息失败,error:", err.Error())
		}

		if eType == 1 && warehouseRelationShop.Category == 3 {
			continue
		} else if eType == 2 && warehouseRelationShop.Category != 3 {
			continue
		}

		down := &ChannelProductUpDown{
			ProductIds:   []string{cast.ToString(v.ProductId)},
			ChannelId:    v.ChannelId,
			FinanceCodes: []string{v.FinanceCode},
			UserNo:       "EditDownTypeCansele",
			UserName:     "EditDownTypeCansele",
			IsSyncPrice:  true,
			DownType:     1,
		}
		down.DownPorudct()
		glog.Info("EditProduct,下架返回门店：", v.FinanceCode, " 渠道channel_id: ", v.ChannelId, " 返回数据结果： ", kit.JsonEncode(down.UpResult))
	}

	// 调用下架接口
	//if _, err := db.Exec("update dc_product.channel_store_product set down_type = 8,up_down_state = 1 where sku_id in (" + id + ")"); err != nil {
	//	glog.Error("更改货号,更新为自动下架类型,sku_id:", id, ",error:", err.Error())
	//}
	glog.Info("EditProduct 后重新认领,更改货号，更新下架商品sku_id:", id)
}
