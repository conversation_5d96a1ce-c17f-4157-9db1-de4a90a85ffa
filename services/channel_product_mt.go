package services

import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/dc"
	"_/proto/et"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 通过店铺信息,sku信息查询库存信息
func GetStockInfoBySkuCodeAndShopId(channelId, skuId int32, shopId string, args ...interface{}) (stock int32, err error) {
	redisConn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}

	skuCodeInfoSlice := []*ic.SkuCodeInfo{}
	skuCodeInfoSlice = append(skuCodeInfoSlice, &ic.SkuCodeInfo{
		Sku:         cast.ToString(skuId),
		FinanceCode: shopId,
	})
	//if channelId != 1 {
	//	skuCodeInfoSlice[0].StockWarehouse = []int32{warehouseId}
	//}

	glog.Info("GetStockInfoBySkuCodeAndShopId参数信息 channelId：", kit.JsonEncode(skuCodeInfoSlice), " args : ", kit.JsonEncode(args))
	if len(args) > 0 {
		stockMap, err := GetStockInfoBySkuCode(0, skuCodeInfoSlice, channelId, 0, args[0])
		if err != nil {
			return 0, err
		}
		return GetSkuStock(stockMap, shopId, cast.ToString(skuId)), nil
	}
	stockMap, err := GetStockInfoBySkuCode(0, skuCodeInfoSlice, channelId, 0)
	if err != nil {
		return 0, err
	}
	return stockMap[shopId+":"+cast.ToString(skuId)], nil
	//return GetSkuStock(stockMap, shopId, cast.ToString(skuId))
}

type MtMap struct {
	Product_id    int32
	Up_down_state int32
}

func (c *Product) NewMtTask(ctx context.Context, in *pc.NewMtTaskRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{
		Code: 400,
	}

	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return out, nil
	}

	taskData := models.TaskData{}

	err := json.Unmarshal([]byte(in.TaskData), &taskData)
	if err != nil {
		glog.Error("NewMtTask：解析json错误"+in.TaskData, err)
	}
	appChannel := GetAppChannelByStoreId(taskData.InitData.AppPoiCodes)

	//保存任务id到数据库
	// 宠物saas-v1.0 宠物saas ， 用线下门店端用户id
	UserNo := userInfo.UserNo
	if appChannel == cast.ToInt32(config.GetString("eshop_store_app_channel")) {
		UserNo = userInfo.ScrmId
	}
	if _, err := NewDbConn().Insert(&models.MtRetailBatchtask{
		TaskId:     int(in.TaskId),
		TaskData:   in.TaskData,
		UserNo:     UserNo,
		AppChannel: appChannel,
	}); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}

	out.Code = 200
	return out, nil
}

// 获取美团商品信息
func (c *Product) GetMtProductData(ctx context.Context, in *pc.GetMtProductDataRequest) (out *pc.BaseStringResponse, err error) {
	out = new(pc.BaseStringResponse)
	out.Code = 400

	productIdRelationSkuId := make(map[string]string, 0)
	var allProductIds []int32 // 门店下面所有的批量的商品的商品数
	for _, id := range in.ProductId {
		allProductIds = append(allProductIds, cast.ToInt32(id))
	}
	conn := NewDbConn()
	if len(allProductIds) > 0 {
		product := make([]models.FailSkuProduct, 0)
		conn.Table("sku_third").In("product_id", allProductIds).Find(&product)
		for hi := range product {
			skuProduct := product[hi]
			productIdRelationSkuId[skuProduct.ProductId] = skuProduct.SkuId
		}
	}

	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return out, nil
	}
	ctx = context.WithValue(ctx, "userInfo", userInfo)
	var errList []pc.UpDownERROR
	//取快照信息
	var snapShots []*pc.ChannelProductSnapshot
	if res, err := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
		ChannelId:   in.ChannelId,
		ProductId:   in.ProductId,
		FinanceCode: in.FinanceCode[0],
	}); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	} else if res.Code != 200 || len(res.Details) == 0 {
		b2, _ := json.Marshal(res)
		glog.Info("b2 ", string(b2))
		out.Message = "商品快照不存在，请先编辑商品"
		errList = append(errList, pc.UpDownERROR{
			ProductId:   cast.ToString(in.ProductId),
			SkuId:       productIdRelationSkuId[cast.ToString(in.ProductId)],
			IsSuccess:   false,
			Message:     out.Message,
			FinanceCode: in.FinanceCode[0],
		})
		return out, nil
	} else {
		snapShots = res.Details
	}

	foodData := make([]interface{}, 0)
	//单门店单商品
	if in.Type == 0 {
		chProduct := new(pc.ChannelProductRequest)
		if err := json.Unmarshal([]byte(snapShots[0].JsonData), chProduct); err != nil {
			glog.Error(err)
			out.Message = err.Error()
			return out, nil
		}

		if (chProduct.Product.ChannelCategoryId == 0 || chProduct.Product.ChannelTagId == 0) && in.ChannelId == ChannelMtId {
			out.Message = "请先选择店内分类或渠道分类或美团分类id"
			return out, nil
		}

		//整理美团sku数据

		clientDis := GetDispatchClient()
		defer clientDis.Close()

		// 获取门店所属仓库类型
		warehouseMap := map[string]int32{}
		//warehouse, err := clientDis.RPC.GetWarehouseInfoByFanceCodes(clientDis.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: in.FinanceCode})
		//if err != nil {
		//	out.Message = "查询门店属性错误"
		//	return out, nil
		//}
		//for _, v := range warehouse.Data {
		//	warehouseMap[v.Code] = v.Category
		//}
		product := new(Product)
		resp, err := product.GetChannelWarehouses(in.FinanceCode, ChannelMtId)
		for _, v := range resp {
			warehouseMap[v.ShopId] = int32(v.Category)
		}

		if err != nil {
			glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
			out.Message = "查询门店属性错误"
			return out, nil
		}

		skus := formatSkus(chProduct.SkuInfo, warehouseMap[in.FinanceCode[0]])
		//整理美团属性数据
		commonAttrValue := formatCommonAttrValue(chProduct.ProductAttr)

		//分类保存的是所有级的分类，这里只取末级
		categoryName := strings.Split(chProduct.Product.ChannelCategoryName, ">")

		//查询最新的分类数据
		c1 := new(ChannelProduct)
		category, err := c1.GetChannelCategoryById(chProduct.Product.ChannelCategoryId)
		if err == nil {
			if len(category.Name) > 0 {
				categoryName = []string{category.Name}
			}
		}
		code, err := c1.GetChannelCategoryCode(chProduct.Product.ChannelCategoryId, in.ChannelId, in.FinanceCode[0])
		glog.Info("获取的code：", code)
		if err != nil {
			glog.Error("GetChannelCategoryCode err: ", err.Error())
			return out, errors.New("查询第三方分类异常")
		}
		//if len(code) <= 0 {
		//	return out, errors.New("未查询到第三方分类,请更换分类")
		//}

		pattern := `(https?|ftp|file)://[-\w+&@#/%=~|?!:,.;]+[-\w+&@#/%=~|]`
		pattern = `(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]`
		picStr := strings.Join(regexp.MustCompile(pattern).FindAllString(chProduct.Product.ContentPc, -1), ",")
		split := strings.Split(picStr, ",")
		if len(split) > 20 {
			//获取前20个图片的图片，并转字符串
			picStr = strings.Join(split[0:20], ",")
		}
		// 商品卖点限制10个汉字
		if len([]rune(chProduct.Product.SellingPoint)) > 10 {
			chProduct.Product.SellingPoint = string([]rune(chProduct.Product.SellingPoint)[0:10])
		}
		data := &et.RetailInitdataRequest{
			AppFoodCode:   cast.ToString(chProduct.Product.Id), // APP方商品id，即商家中台系统里商品的编码 必镇
			Name:          chProduct.Product.Name,              // 商品名称 创建时必镇
			SellPoint:     chProduct.Product.SellingPoint,      // 商品描述  非必镇
			Skus:          skus,                                // APP方商品的skus信息，支持同时传多个sku信息。
			MinOrderCount: skus[0].MinOrderCount,               // 商品的最小购买量，创建商品时，min_order_count字段信息如不传则默认为1。 必镇
			//CategoryId:    chProduct.Product.ChannelCategoryId,
			//CategoryCode: 	cast.ToString(chProduct.Product.ChannelCategoryId),
			//CategoryName: categoryName[len(categoryName)-1], // 分类名称 创建时必填
			IsSoldOut: 1,                     // 商品上下架状态，字段取值范围：0-上架，1-下架。 非必镇
			Picture:   chProduct.Product.Pic, // 商品图片： 非必镇
			// 美团内部商品类目id
			// 门店启用结构化属性并且传递了销售或普通属性则
			TagId:           int64(chProduct.Product.ChannelTagId), // 1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。若门店未启用或未传递销售属性/普通属性则非必传
			CommonAttrValue: commonAttrValue,                       // 商品普通属性的json字符串 若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
			Properties:      []*et.Propertie{},                     // 商品属性 非必镇
			LimitSaleInfo:   new(et.LimitSaleInfo),                 // 商品限购详情 非必镇
			PictureContents: picStr,                                // 商品的图片详情
			IsSpecialty:     chProduct.Product.IsRecommend,         //是否力荐商品
		}

		//v6.4.0 同步美团商品使用code创建更新
		if len(code) > 0 {
			data.CategoryCode = code
		} else {
			data.CategoryName = categoryName[len(categoryName)-1]
		}

		glog.Info("code ", code, " data:", kit.JsonEncode(data))
		foodData = append(foodData, data)

		if data, err := json.Marshal(foodData); err != nil {
			glog.Error(err)
			out.Message = err.Error()
			return out, nil
		} else {
			out.Data = string(data)
		}
	}

	//单门店多商品
	if in.Type == 1 {
		for _, snapShot := range snapShots {
			chProduct := new(pc.ChannelProductRequest)
			if err := json.Unmarshal([]byte(snapShot.JsonData), chProduct); err != nil {
				glog.Error(err)
				continue
			}
			if chProduct.SkuInfo[0].MinOrderCount == 0 {
				chProduct.SkuInfo[0].MinOrderCount = 1
			}

			if (chProduct.Product.ChannelCategoryId == 0 || chProduct.Product.ChannelTagId == 0) && in.ChannelId == ChannelMtId {
				glog.Error(errors.New("商品" + chProduct.Product.Name + "（" + cast.ToString(chProduct.Product.Id) + "）请先选择店内分类和渠道分类"))
				errList = append(errList, pc.UpDownERROR{
					ProductId:   cast.ToString(chProduct.Product.Id),
					SkuId:       productIdRelationSkuId[cast.ToString(chProduct.Product.Id)],
					IsSuccess:   false,
					Message:     "请先选择店内分类和渠道分类",
					FinanceCode: in.FinanceCode[0],
				})
				continue
			}
			// 获取门店所属仓库类型
			var cty int32
			if in.Category == 0 {
				clientDis := GetDispatchClient()
				defer clientDis.Close()
				warehouseMap := map[string]int32{}
				//warehouse, err := clientDis.RPC.GetWarehouseInfoByFanceCodes(clientDis.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: in.FinanceCode})
				//if err != nil {
				//	out.Message = "查询门店属性错误"
				//	return out, nil
				//}

				product := new(Product)
				resp, err := product.GetChannelWarehouses(in.FinanceCode, ChannelMtId)
				if err != nil {
					glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
					out.Message = "查询门店属性错误"
					return out, nil
				}
				for _, v := range resp {
					warehouseMap[v.ShopId] = int32(v.Category)
				}
				cty = warehouseMap[in.FinanceCode[0]]
			} else {
				cty = in.Category
			}
			//整理美团sku数据
			skus := formatSkus(chProduct.SkuInfo, cty)
			//整理美团属性数据
			commonAttrValue := formatCommonAttrValue(chProduct.ProductAttr)

			//分类保存的是所有级的分类，这里只取末级
			categoryName := strings.Split(chProduct.Product.ChannelCategoryName, ">")

			//查询最新的分类数据
			c1 := new(ChannelProduct)
			category, err := c1.GetChannelCategoryById(chProduct.Product.ChannelCategoryId)
			if err == nil {
				if len(category.Name) > 0 {
					categoryName = []string{category.Name}
				}
			}
			code, err := c1.GetChannelCategoryCode(chProduct.Product.ChannelCategoryId, in.ChannelId, in.FinanceCode[0])
			glog.Info("获取的code：", code)

			if err != nil {
				glog.Error("GetChannelCategoryCode err: ", err.Error())
				return out, errors.New("查询第三方分类异常")
			}
			//if len(code) <= 0 {
			//	return out, errors.New("未查询到第三方分类,请更换分类")
			//}

			pattern := `(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]`
			picStr := strings.Join(regexp.MustCompile(pattern).FindAllString(chProduct.Product.ContentPc, -1), ",")
			split := strings.Split(picStr, ",")
			if len(split) > 20 {
				picStr = strings.Join(split[0:20], ",")
			}
			// 商品卖点限制10个汉字
			if len([]rune(chProduct.Product.SellingPoint)) > 10 {
				chProduct.Product.SellingPoint = string([]rune(chProduct.Product.SellingPoint)[0:10])
			}

			data := &et.RetailBatchinitdata{
				AppFoodCode:   cast.ToString(chProduct.Product.Id), // APP方商品id，即商家中台系统里商品的编码 必镇
				Name:          chProduct.Product.Name,              // 商品名称 创建时必镇
				SellPoint:     chProduct.Product.SellingPoint,      // 商品描述  非必镇
				Skus:          skus,                                // APP方商品的skus信息，支持同时传多个sku信息。
				MinOrderCount: skus[0].MinOrderCount,               // 商品的最小购买量，创建商品时，min_order_count字段信息如不传则默认为1。 必镇
				CategoryId:    chProduct.Product.ChannelCategoryId,
				//CategoryName:  categoryName[len(categoryName)-1], // 分类名称 创建时必填, // 分类名称 创建时必填
				IsSoldOut: 1,                     // 商品上下架状态，字段取值范围：0-上架，1-下架。 非必镇 -- 编辑不上架
				Picture:   chProduct.Product.Pic, // 商品图片： 非必镇
				// 美团内部商品类目id
				// 门店启用结构化属性并且传递了销售或普通属性则
				TagId:           int64(chProduct.Product.ChannelTagId), // 1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。若门店未启用或未传递销售属性/普通属性则非必传
				ProductName:     chProduct.Product.Name,                // 完整产品名称（如：非力蒲电风机）
				CommonAttrValue: commonAttrValue,                       // 商品普通属性的json字符串 若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
				Properties:      []*et.Propertie{},                     // 商品属性 非必镇
				LimitSaleInfo:   new(et.LimitSaleInfo),                 // 商品限购详情 非必镇
				PictureContents: picStr,                                // 商品的图片详情
				IsSpecialty:     chProduct.Product.IsRecommend,         //是否力荐商品
			}
			if len(code) > 0 {
				data.CategoryCode = code
			} else {
				data.CategoryName = categoryName[len(categoryName)-1]
			}

			glog.Info("code ", code, " data:", kit.JsonEncode(data))
			foodData = append(foodData, data)
		}
	}

	//多门店多商品
	if in.Type == 2 {
		for _, snapShot := range snapShots {
			chProduct := new(pc.ChannelProductRequest)
			if err := json.Unmarshal([]byte(snapShot.JsonData), chProduct); err != nil {
				glog.Error(err)
				continue
			}

			if chProduct.Product.ChannelCategoryId == 0 || chProduct.Product.ChannelTagId == 0 {
				out.Message = "商品" + chProduct.Product.Name + "（" + cast.ToString(chProduct.Product.Id) + "）请先选择店内分类和渠道分类"
				return out, nil
			}
			if chProduct.SkuInfo[0].MinOrderCount == 0 {
				chProduct.SkuInfo[0].MinOrderCount = 1
			}
			//整理美团sku数据
			skus := formatSkus(chProduct.SkuInfo, 4)
			//整理美团属性数据
			commonAttrValue := formatCommonAttrValue(chProduct.ProductAttr)

			//分类保存的是所有级的分类，这里只取末级
			categoryName := strings.Split(chProduct.Product.ChannelCategoryName, ">")

			//查询最新的分类数据
			c1 := new(ChannelProduct)
			category, err := c1.GetChannelCategoryById(chProduct.Product.ChannelCategoryId)
			if err == nil {
				if len(category.Name) > 0 {
					categoryName = []string{category.Name}
				}
			}

			code, err := c1.GetChannelCategoryCode(chProduct.Product.ChannelCategoryId, in.ChannelId, in.FinanceCode[0])
			glog.Info("获取的code：", code)

			if err != nil {
				glog.Error("GetChannelCategoryCode err: ", err.Error())
				return out, errors.New("查询第三方分类异常")
			}
			//if len(code) <= 0 {
			//	return out, errors.New("未查询到第三方分类,请更换分类")
			//}

			// 商品卖点限制10个汉字
			if len([]rune(chProduct.Product.SellingPoint)) > 10 {
				chProduct.Product.SellingPoint = string([]rune(chProduct.Product.SellingPoint)[0:10])
			}

			data := &et.RetailInfo{
				AppFoodCode:   cast.ToString(chProduct.Product.Id), // APP方商品id，即商家中台系统里商品的编码 必镇
				Name:          chProduct.Product.Name,              // 商品名称 创建时必镇
				SellPoint:     chProduct.Product.SellingPoint,      // 商品描述  非必镇
				Skus:          skus,                                // APP方商品的skus信息，支持同时传多个sku信息。
				MinOrderCount: skus[0].MinOrderCount,               // 商品的最小购买量，创建商品时，min_order_count字段信息如不传则默认为1。 必镇
				//CategoryId:    chProduct.Product.ChannelCategoryId,
				//CategoryName: categoryName[len(categoryName)-1], // 分类名称 创建时必填
				IsSoldOut: 1,                     // 商品上下架状态，字段取值范围：0-上架，1-下架。 非必镇
				Picture:   chProduct.Product.Pic, // 商品图片： 非必镇
				// 美团内部商品类目id
				// 门店启用结构化属性并且传递了销售或普通属性则
				TagId:           int64(chProduct.Product.ChannelTagId),                                                                    // 1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。若门店未启用或未传递销售属性/普通属性则非必传
				CommonAttrValue: commonAttrValue,                                                                                          // 商品普通属性的json字符串 若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
				LimitSaleInfo:   new(et.LimitSaleInfo),                                                                                    // 商品限购详情 非必镇
				PictureContents: strings.ReplaceAll(strings.ReplaceAll(chProduct.Product.ContentPc, "<p><img src=\"", ""), "\"></p>", ""), // 商品的图片详情
				IsSpecialty:     chProduct.Product.IsRecommend,                                                                            //是否力荐商品
			}
			if len(code) > 0 {
				data.CategoryCode = code
			} else {
				data.CategoryName = categoryName[len(categoryName)-1]
			}

			glog.Info("code ", code, " data:", kit.JsonEncode(data))
			foodData = append(foodData, data)
		}
	}

	//未找到快照信息
	if len(foodData) == 0 {
		out.Message = "没有符合条件的商品快照信息，请先编辑商品或者添加商品分类"
		glog.Info(out.Message, "，in.Type=", in.Type)
		return out, nil
	}
	if data, err := json.Marshal(foodData); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	} else {
		out.Data = string(data)
	}
	glog.Info("return data", kit.JsonEncode(out))
	out.Code = 200
	jsonstr, _ := json.Marshal(errList)
	out.ErrList = string(jsonstr)
	return out, nil
}

// 整理美团sku数据
func formatSkus(skuInfo []*pc.SkuInfo, category int32) (skus []*et.SkuParam) {
	for _, v := range skuInfo {
		spec := []string{}
		for _, vv := range v.Skuv {
			spec = append(spec, vv.SpecValueValue)
		}
		var price int32
		if category == 3 {
			price = v.StorePrice
		} else if category == 4 {
			price = v.PreposePrice
		} else {
			price = v.PreposePrice
		}
		skus = append(skus, &et.SkuParam{
			SkuId:          cast.ToString(v.SkuId),              // 是sku唯一标识码 必镇
			Spec:           strings.Join(spec, "/"),             // sku的规格名称  建时门店启用类目属性且skus中传递了销售属性则为非必填(会自动根据销售属性组合其规格)，其余情况参考字段描述里规则。 更新商品时，本参数非必填。
			Upc:            v.BarCode,                           // 为sku的商品包装上的条形码编号，UPC/EAN码；字符长度8位或者13位 非必镇
			Price:          cast.ToString(float32(price) / 100), // 默认使用前置仓价格，价格同步时如果是门店仓则修改为门店仓价格
			Unit:           v.PriceUnit,                         // 商品sku的售卖单位 非必镇
			Stock:          "0",                                 // sku的库存量 创建时必填
			MinOrderCount:  v.MinOrderCount,                     // 一个订单中此商品的最小购买量
			AvailableTimes: new(et.AvailableTimes),              // 表示sku可售时间 非必镇
			WeightForUnit:  cast.ToString(v.WeightForUnit),      // 表示sku的重量数值信息  创建时，如填写weight_unit，则weight_for_unit必填且与weight至多填写一个，否则非必填
			// 创建时，如填写weight_for_unit，则weight_unit必填且与weight至多填写一个，否则非必填
			WeightUnit: "千克(kg)", // 表示sku的重量数值单位，枚举值如下： 1."克(g)" 2."千克(kg)" 3."毫升(ml)" 4."升(L)" 5."磅" 6."斤" 7."两"。
		})
	}

	return
}

// 整理美团属性数据
func formatCommonAttrValue(productAttr []*pc.ChannelProductAttr) (attrVal []*et.CommonAttrValue) {
	for _, v := range productAttr {
		//属性值为空跳过
		if len(v.AttrValue) == 0 {
			continue
		}

		valueList := []*et.ValueList{}
		attrValueId := strings.Split(v.AttrValueId, ",")
		attrValue := strings.Split(v.AttrValue, ",")
		for kk, vv := range attrValueId {
			valueList = append(valueList, &et.ValueList{
				ValueId: cast.ToInt64(vv),
				Value:   attrValue[kk],
			})
		}

		attrVal = append(attrVal, &et.CommonAttrValue{
			AttrId:    cast.ToInt64(v.AttrId),
			AttrName:  v.AttrName,
			ValueList: valueList,
		})
	}

	return
}

// 美团渠道--批量同步任务
// 当选择商品及门店后点击“批量同步”时，如果商品在对应的门店美团商品库里不存在则进行新建，商品信息同步，建立快照;
// 如果商品在对应的门店已经存在，则按美团渠道商品库商品信息对门店美团商品库进行更新，同时更新快照
func (c *Product) BatchToMT(ctx context.Context, in *pc.BatchToMTRequest) (*pc.BatchBaseResponse, error) {
	//1、循环门店
	//2、循环商品
	//2.1、取商品价格和库存
	//3、信息同步到美团
	logPrefix := fmt.Sprintf("BatchToMT美团创建/更新商品====,product_id=%s|channel_id=%d|finance_code=%s|finance_code_list=%s", in.ProductId, in.ChannelId, in.FinanceCode, in.FinanceCodeList)
	glog.Info(logPrefix, "入参为%s", kit.JsonEncode(in))
	out := new(pc.BatchBaseResponse)
	out.Code = 400
	var userno string
	var finance_code_arr []string
	//判断是全部门店
	if in.IsAll == 1 {
		if len(in.UserNo) <= 0 {
			glog.Error(logPrefix, "用户编码不能为空")
			out.Message = "用户编码不能为空"
			out.Code = 400
			return out, nil
		}
		client := GetDataCenterClient()
		defer client.Close()

		var params dac.GetHospitalListByUserNoRequest
		userno = in.UserNo
		params.UserNo = in.UserNo
		//用户校验
		userInfo := loadLoginUserInfo(ctx)
		if userInfo.IsGeneralAccount {
			params.IsLogo = 1
		} else {
			params.IsLogo = 0
		}
		params.ChannelId = in.ChannelId
		params.Category = in.Category
		out_result, err := client.RPC.GetHospitalListByUserNo(client.Ctx, &params)
		if err != nil {
			glog.Error(logPrefix, "请求GetHospitalListByUserNo失败,err=", err.Error())
			out.Message = "请求GetHospitalListByUserNo失败"
			out.Error = err.Error()
			out.Code = 400
			return out, nil
		}
		for _, v := range out_result.Data {
			finance_code_arr = append(finance_code_arr, v.StructOuterCode)
		}
	} else {
		if len(in.FinanceCode) > 0 {
			finance_code_arr = strings.Split(in.FinanceCode, ",")
		} else {
			glog.Error(logPrefix, "财务编码或标识门店不能为空")
			out.Message = "财务编码或标识门店不能为空"
			out.Code = 400
			return out, nil
		}
	}
	redisConn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}
	session := NewDbConn().NewSession()
	defer session.Close()

	var productIdSlice []int32
	if len(in.ProductId) > 0 {
		for _, v := range strings.Split(in.ProductId, ",") {
			productIdSlice = append(productIdSlice, cast.ToInt32(v))
		}
	} else {
		glog.Error(logPrefix, "商品ID不能为空")
		out.Message = "商品ID不能为空"
		out.Code = 400
		return out, nil
	}

	//通过门店财务编码查询渠道门店id
	appPoiCodeMap := GetAppPoiCodeByFinanceCode(finance_code_arr, 2)
	appPoiCodeSlice := []string{}
	for k := range appPoiCodeMap {
		if len(k) > 0 {
			appPoiCodeSlice = append(appPoiCodeSlice, k)
		}
	}
	if len(appPoiCodeSlice) == 0 {
		glog.Error(logPrefix, "没有可用的渠道门店")
		out.Message = "没有可用的渠道门店"
		out.Code = 400
		return out, nil
	}
	//循环门店信息
	resList := make([][]string, 0)
	for _, appPoiCodeEle := range appPoiCodeSlice {
		//storeMasterId := GetAppChannelByStoreId(appPoiCodeEle)

		storeMasterId, err := GetAppChannelByFinanceCode(appPoiCodeMap[appPoiCodeEle])
		if err != nil {
			glog.Error(logPrefix, "BatchToMT,", "GetAppChannelByFinanceCode failed,", appPoiCodeMap[appPoiCodeEle], err)
			continue
		}

		for _, productId := range productIdSlice {
			//虚拟商品限制不让同步到mt
			product := models.Product{}
			session.SQL("select * from product where id = ?", productId).Get(&product)
			if product.ProductType == 2 {
				glog.Error(logPrefix, "虚拟商品限制不让同步到mt")
				out.Message = "虚拟商品限制不让同步到mt"
				return out, nil
			}

			//取快照信息
			var snapShots *pc.ChannelProductSnapshot
			if res, err := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
				ChannelId:   in.ChannelId,
				ProductId:   []int32{productId},
				FinanceCode: appPoiCodeMap[appPoiCodeEle],
			}); err != nil {
				glog.Error(logPrefix, err)
				out.Message = err.Error()
				return out, nil
			} else if res.Code != 200 || len(res.Details) == 0 {
				b2, _ := json.Marshal(res)
				glog.Error(logPrefix, "b2 ", string(b2))
				out.Message = "商品快照不存在，请先编辑商品"
				return out, nil
			} else {
				snapShots = res.Details[0]
			}

			if res, err := c.NewChannelProductSnapshot(ctx, &pc.ChannelProductSnapshot{
				Id:          snapShots.Id,
				ChannelId:   in.ChannelId,
				UserNo:      userno,
				ProductId:   snapShots.ProductId,
				JsonData:    snapShots.JsonData,
				FinanceCode: appPoiCodeMap[appPoiCodeEle],
			}); err != nil {
				glog.Error(logPrefix, err)
				out.Message = err.Error()
				return out, nil
			} else if res.Code != 200 {
				b2, _ := json.Marshal(res)
				glog.Info(logPrefix, "b2 ", string(b2))
				out.Message = "无法创建快照"
				return out, nil
			}

			//获取美团商品信息 -- 一个一个处理
			gmdr := pc.GetMtProductDataRequest{
				ProductId:   []int32{productId},
				ChannelId:   2,
				FinanceCode: []string{appPoiCodeMap[appPoiCodeEle]},
				Type:        1,
			}
			mtProductData, err := c.GetMtProductData(ctx, &gmdr)
			glog.Info(logPrefix, "获取美团分类数据：", kit.JsonEncode(mtProductData), "err:", err)
			if err != nil {
				glog.Error("处理格式化美团商品信息异常", err)
				out.Message = "处理格式化美团商品信息异常" + err.Error()
				return out, nil
			} else if mtProductData.Code != 200 || len(mtProductData.Data) == 0 {
				glog.Error(logPrefix, "处理格式化美团商品信息失败")
				out.Message = "处理格式化美团商品信息失败"
				return out, nil
			}
			foodData := []*et.RetailBatchinitdata{}
			if json.Unmarshal([]byte(mtProductData.Data), &foodData) != nil {
				glog.Error(logPrefix, "序列化美团商品信息失败", err)
				out.Message = "序列化美团商品信息失败"
				return out, nil
			}

			//查询库存
			skuCodes := []*ic.SkuCodeInfo{}
			for _, sku := range foodData[0].Skus {
				skuCode := &ic.SkuCodeInfo{}
				skuCode.FinanceCode = appPoiCodeMap[appPoiCodeEle]
				skuCode.Sku = sku.SkuId
				skuCodes = append(skuCodes, skuCode)
			}
			stockMap, _ := GetStockInfoBySkuCode(0, skuCodes, ChannelMtId, 1) //查询本地的库存

			// 批量操作后，无法判断商品是否美团已存在，需一条一条处理。 速度慢
			for k := range foodData {
				//二、美团业务
				OperateType := 0 //商品是否存在美团 -- 如果不确定的话，则更新库存为0
				//调用美团的商品信息
				clientMt := GetMtGlobalProductClient()
				retailGetRequest := et.RetailGetRequest{}
				retailGetRequest.AppPoiCode = appPoiCodeEle
				retailGetRequest.AppFoodCode = foodData[k].AppFoodCode
				retailGetRequest.StoreMasterId = storeMasterId

				retailRes, retailErr := clientMt.RPC.RetailGet(clientMt.Ctx, &retailGetRequest)
				if retailErr == nil {
					if retailRes.Code == 200 {
						OperateType = 2
					}
				}
				for i, sku := range foodData[k].Skus {

					//判断价格是否一样,是一样就不更新价格
					if OperateType == 2 {
						if cast.ToFloat32(sku.Price) == retailRes.Data.Price {
							foodData[k].Skus[i].Price = ""
						}
					} else {
						// 创建的时候需要构建价格
						getPrice, err1 := GetPrice(int(ChannelMtId), cast.ToString(sku.SkuId), appPoiCodeMap[appPoiCodeEle])
						if err1 != nil {
							glog.Error(logPrefix, "获取商品价格失败", err1)
						} else {
							foodData[k].Skus[0].Price = cast.ToString(getPrice)
						}
					}
					//单sku处理方式
					mapkey := appPoiCodeMap[appPoiCodeEle] + ":" + sku.SkuId
					if _, ok := stockMap[mapkey]; ok {
						if OperateType != 2 {
							sku.Stock = cast.ToString(stockMap[mapkey])
						} else {
							sku.Stock = ""
						}
					}
				}

				var retailBatchinitdataRequest = &et.RetailBatchinitdataRequest{
					AppPoiCode:    appPoiCodeEle,
					FoodData:      foodData,
					OperateType:   int32(OperateType),
					StoreMasterId: storeMasterId,
				}

				glog.Info(logPrefix, "RetailBatchinitdata request:", kit.JsonEncode(retailBatchinitdataRequest))
				initRes, err := clientMt.RPC.RetailBatchinitdata(context.Background(), retailBatchinitdataRequest)
				glog.Info(logPrefix, "4444美团创建/更新商品返回数据为", kit.JsonEncode(initRes), ",----------请求数据为", kit.JsonEncode(retailBatchinitdataRequest))
				// v7.0.11 同步第三方商品ID回来
				MtProductThirdId(enum.UpdateProductThirdIdFrom8, initRes, foodData, appPoiCodeMap[appPoiCodeEle], retailBatchinitdataRequest.OperateType)
				if err != nil {
					glog.Error(logPrefix, err)
					if in.IsCutStorehouse == 1 { // 切换仓库的时候同步商品失败直接退出
						glog.Error("切换仓库的时候同步商品失败err: ", appPoiCodeMap[appPoiCodeEle],
							cast.ToString(foodData[k].Skus[0].SkuId), initRes.Error.Msg)
						out.Message = err.Error()
						return out, nil
					}
				}

				if initRes.Code != 200 {
					row := []string{appPoiCodeMap[appPoiCodeEle], cast.ToString(foodData[k].Skus[0].SkuId), initRes.Error.Msg}
					resList = append(resList, row)

					if in.IsCutStorehouse == 1 { // 切换仓库的时候同步商品失败直接退出
						glog.Error(logPrefix, "切换仓库的时候同步商品失败直接退出: ", appPoiCodeMap[appPoiCodeEle],
							cast.ToString(foodData[k].Skus[0].SkuId), initRes.Error.Msg)
						out.Message = initRes.Error.Msg
						return out, nil
					}
				}

				//clientMt.Close()
			}
		}
	}
	//异步同步价格
	go func() {
		time.Sleep(10 * time.Second)
		syncMtPrice(finance_code_arr, productIdSlice)
	}()
	//更新任务列表信息
	if len(resList) > 0 && in.IsCutStorehouse == 0 {
		headRow := append([]string{}, "财务编码", "平台商品ID", "失败原因")
		errList := append([][]string{}, headRow)
		errList = append(errList, resList...)
		excelUrl, err := ExportProductErr(errList)
		out.QiniuUrl = excelUrl
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, nil
		}
		//取第一条数据的错误信息
		out.Message = resList[0][2]
	}
	if out.Message == "" {
		out.Message = "操作成功"
	}
	out.Code = 200
	return out, nil
}

// 价格同步--美团
func syncMtPrice(financeCodeArr []string, productIdSlice []int32) {

	product := new(Product)
	resp, err := product.GetChannelWarehouses(financeCodeArr, ChannelMtId)
	if err != nil {
		glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
	}
	warehouseMap := make(map[string]models.ChannelWarehouse, 0)
	for _, datum := range resp {
		warehouseMap[datum.WarehouseCode] = datum
	}

	for _, financeCode := range financeCodeArr {
		for _, v := range productIdSlice {
			if value, ok := warehouseMap[financeCode]; ok && value.Category == 3 {
				var sku_third models.SkuThird
				engine.Where("erp_id=4").And("product_id=?", v).Get(&sku_third)
				if sku_third.Id > 0 {
					UpdateZlPrice(financeCode, sku_third.SkuId, ChannelMtId)
					//价格同步--美团
				} else if value.Category == 4 || value.Category == 5 || value.Category == 1 {
					var sku_third models.SkuThird
					engine.Where("erp_id=2").And("product_id=?", v).Get(&sku_third)
					if sku_third.Id > 0 {
						UpdateQzPrice(financeCode, int32(warehouseMap[financeCode].WarehouseId), sku_third.SkuId, ChannelMtId)
					}
				}
			}
		}
	}
}

// 美团渠道--批量同步任务
// 已废弃？
func (c *Product) BatchToMTBak(ctx context.Context, in *pc.BatchToMTRequest) (*pc.BatchToMTResponse, error) {
	glog.Info("开始批量同步")
	out := new(pc.BatchToMTResponse)
	out.Code = 400
	var finance_code_arr []string
	//判断是全部门店
	if in.IsAll == 1 {
		if len(in.UserNo) <= 0 {
			out.Message = "用户编码不能为空"
			out.Code = 400
			return out, nil
		}
		client := GetDataCenterClient()
		defer client.Close()

		var params dac.GetHospitalListByUserNoRequest
		params.UserNo = in.UserNo
		//用户校验
		userInfo := loadLoginUserInfo(ctx)
		if userInfo.IsGeneralAccount {
			params.IsLogo = 1
		} else {
			params.IsLogo = 0
		}
		params.ChannelId = in.ChannelId
		params.Category = in.Category
		out_result, err := client.RPC.GetHospitalListByUserNo(client.Ctx, &params)
		if err != nil {
			out.Message = "请求GetHospitalListByUserNo失败"
			out.Error = err.Error()
			out.Code = 400
			return out, nil
		}
		for _, v := range out_result.Data {
			finance_code_arr = append(finance_code_arr, v.StructOuterCode)
		}
	} else {
		if len(in.FinanceCodeList) > 0 {
			finance_code_arr = strings.Split(in.FinanceCodeList, ",")
		} else {
			out.Message = "财务编码或标识门店不能为空"
			out.Code = 400
			return out, nil
		}
	}
	session := NewDbConn().NewSession()
	defer session.Close()

	var productIdSlice []int32
	if len(in.ProductId) > 0 {
		for _, v := range strings.Split(in.ProductId, ",") {
			productIdSlice = append(productIdSlice, cast.ToInt32(v))
		}
	} else {
		out.Message = "商品ID不能为空"
		out.Code = 400
		return out, nil
	}

	//通过门店财务编码查询渠道门店id
	appPoiCodeMap := GetAppPoiCodeByFinanceCode(finance_code_arr, 2)
	appPoiCodeSlice := []string{}
	for k := range appPoiCodeMap {
		if len(k) > 0 {
			appPoiCodeSlice = append(appPoiCodeSlice, k)
		}
	}
	if len(appPoiCodeSlice) == 0 {
		out.Message = "没有可用的渠道门店"
		out.Code = 400
		return out, nil
	}

	//价格同步--美团
	SyncMtPrice := func(finance_code_arr []string) {
		disclient := GetDispatchClient()
		defer disclient.Close()
		//处理门店仓的价格，判断仓库属性
		res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(context.Background(), &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: finance_code_arr})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
		}
		warehouseMap := make(map[string]*dc.WarehouseList, len(res.Data))
		//结果赋值
		for _, datum := range res.Data {
			warehouseMap[datum.Code] = datum
		}
		for _, financeCode := range finance_code_arr {
			for _, v := range productIdSlice {
				if value, ok := warehouseMap[financeCode]; ok && value.Category == 3 {
					var sku_third models.SkuThird
					engine.Where("erp_id=4").And("product_id=?", v).Get(&sku_third)
					if sku_third.Id > 0 {
						//价格同步--美团
						UpdateZlPrice(financeCode, sku_third.SkuId, ChannelMtId)
					}
				} else if value.Category == 4 {
					var sku_third models.SkuThird
					engine.Where("erp_id=2").And("product_id=?", v).Get(&sku_third)
					if sku_third.Id > 0 {
						UpdateQzPrice(financeCode, value.Id, sku_third.SkuId, ChannelMtId)
					}
				}
			}
		}
	}

	resList := make([][]string, 0)
	//单个操作走同步，批量操作走异步
	if in.OperateType == 1 {
		go func() {
			clientMt := GetMtProductClient()
			defer clientMt.Close()
			//查询新任务的ID
			params := &pc.GetTaskListRequest{
				Sort:        "createTimeAsc",
				TaskStatus:  1,
				ChannelId:   2,
				TaskContent: 13, //批量同步任务ID
				Status:      1,
				Page:        1,
				PageSize:    1,
				CreateId:    in.UserNo,
			}
			pd := new(Product)
			glog.Info("美团批量同步读取任务ID")
			result, err := pd.GetTaskList(ctx, params)
			if err != nil {
				glog.Error(err)
			}
			for _, appPoiCodeEle := range appPoiCodeSlice {

				storeMasterId := GetAppChannelByStoreId(appPoiCodeEle)

				gmdr := pc.GetMtProductDataRequest{
					ProductId:   productIdSlice,
					ChannelId:   2,
					FinanceCode: []string{appPoiCodeMap[appPoiCodeEle]},
					Type:        0,
					Category:    in.Category,
				}
				mtProductData, err := c.GetMtProductData(ctx, &gmdr)

				if err != nil {
					glog.Error(err)
					out.Message = err.Error()
					row := []string{appPoiCodeMap[appPoiCodeEle], "", err.Error()}
					resList = append(resList, row)
					continue
				} else if mtProductData.Code != 200 || len(mtProductData.Data) == 0 {
					out.Message = mtProductData.Message
					row := []string{appPoiCodeMap[appPoiCodeEle], "", mtProductData.Message}
					resList = append(resList, row)
					continue
				}

				foodData := []*et.RetailInitdataRequest{}
				if json.Unmarshal([]byte(mtProductData.Data), &foodData) != nil {
					glog.Error(err)
					row := []string{appPoiCodeMap[appPoiCodeEle], "", err.Error()}
					resList = append(resList, row)
					continue
				}
				//存储原来的上下架状态。key为商品ID，value为美团上下架状态
				statusMap := make(map[int32]int32)
				r := make([]MtMap, 0)
				session.Table("channel_store_product").Where("finance_code = ?", appPoiCodeMap[appPoiCodeEle]).And("channel_id = 2").In("product_id", productIdSlice).Find(&r)
				for _, v := range r {
					statusMap[v.Product_id] = v.Up_down_state
				}
				//库存赋值，上下级状态赋值
				for k, v := range foodData {
					for kk, sku := range v.Skus {
						stock, _ := GetStockInfoBySkuCodeAndShopId(2, cast.ToInt32(sku.SkuId), appPoiCodeMap[appPoiCodeEle])
						foodData[k].Skus[kk].Stock = cast.ToString(stock)
						if statusMap[cast.ToInt32(foodData[k].AppFoodCode)] == 0 {
							foodData[k].IsSoldOut = 1
						} else {
							foodData[k].IsSoldOut = 0
						}
					}
				}
				//限频每秒50次
				for _, v := range foodData {
					time.Sleep(time.Second / 50)
					//先创建,带库存
					v.OperateType = 1
					v.AppPoiCode = appPoiCodeEle
					v.StoreMasterId = storeMasterId
					res_1, err := clientMt.RPC.RetailInitdata(context.Background(), v)
					glog.Info("5555美团创建/更新商品返回数据为", kit.JsonEncode(res_1), "请求数据为", kit.JsonEncode(v))
					// v7.0.11 同步第三方商品ID回来
					MtProductThirdId(enum.UpdateProductThirdIdFrom9, res_1, []*et.RetailBatchinitdata{&et.RetailBatchinitdata{AppFoodCode: v.AppFoodCode}}, appPoiCodeMap[appPoiCodeEle], v.OperateType)
					if err != nil {
						glog.Error("5555美团创建/更新商品返回数据", err)
						continue
					} else {
						fmt.Println(res_1)
					}

					//再更新,不带库存
					v.OperateType = 2
					v.AppPoiCode = appPoiCodeEle
					for _, vv := range v.Skus {
						vv.Stock = ""
					}
					res_update, err := clientMt.RPC.RetailInitdata(context.Background(), v)
					glog.Info("6666美团创建/更新商品返回数据为", kit.JsonEncode(res_update), "----------请求数据为", kit.JsonEncode(v))
					// v7.0.11 同步第三方商品ID回来
					MtProductThirdId(enum.UpdateProductThirdIdFrom10, res_update, []*et.RetailBatchinitdata{&et.RetailBatchinitdata{AppFoodCode: v.AppFoodCode}}, appPoiCodeMap[appPoiCodeEle], 1)
					if err != nil {
						row := []string{appPoiCodeMap[appPoiCodeEle], v.AppFoodCode, err.Error()}
						resList = append(resList, row)
					} else if res_update.Code != 200 {
						row := []string{appPoiCodeMap[appPoiCodeEle], v.AppFoodCode, res_update.Error.Msg}
						resList = append(resList, row)
						continue
					} else {
						fmt.Println(res_update.Message)

					}

				}
			}
			//错误信息以excel形式上传至七牛云
			excelUrl := ""
			if len(resList) > 0 {
				headRow := append([]string{}, "财务编码", "商品ID", "结果")
				errList := append([][]string{}, headRow)
				errList = append(errList, resList...)
				excelUrl, err = ExportProductErr(errList)
				if err != nil {
					glog.Error("错误信息上传失败; err: " + err.Error())
				}
			}
			//更新任务状态为已完成
			updateModel := models.TaskList{
				TaskStatus:     3,
				TaskDetail:     "",
				ResulteFileUrl: excelUrl,
				ModifyTime:     time.Now(),
			}
			if len(resList) == 0 {
				updateModel.TaskDetail = "成功"
			} else {
				updateModel.TaskDetail = "失败商品:"
			}
			_, err = engine.Id(result.TaskList[0].Id).Update(updateModel)
			if err != nil {
				glog.Info("更新任务状态错误", err.Error())
			}
		}()
	} else {
		for _, appPoiCodeEle := range appPoiCodeSlice {
			gmdr := pc.GetMtProductDataRequest{
				ProductId:   productIdSlice,
				ChannelId:   2,
				FinanceCode: []string{appPoiCodeMap[appPoiCodeEle]},
				Type:        0,
			}
			mtProductData, err := c.GetMtProductData(ctx, &gmdr)

			if err != nil {
				glog.Error(err)
				out.Message = err.Error()
				out.Code = 400
				return out, err
			} else if mtProductData.Code != 200 || len(mtProductData.Data) == 0 {
				out.Message = mtProductData.Message
				out.Code = 400
				return out, nil
			}

			foodData := []*et.RetailInitdataRequest{}

			storeMasterId := GetAppChannelByStoreId(appPoiCodeEle)

			foodData[0].StoreMasterId = storeMasterId

			if json.Unmarshal([]byte(mtProductData.Data), &foodData) != nil {
				out.Code = 400
				return out, nil
			}
			//存储原来的上下架状态。key为商品ID，value为美团上下架状态
			statusMap := make(map[int32]int32)
			r := make([]MtMap, 0)
			session.Table("channel_store_product").Where("finance_code = ?", appPoiCodeMap[appPoiCodeEle]).And("channel_id = 2").In("product_id", productIdSlice).Find(&r)
			if len(r) > 0 {
				for _, v := range r {
					statusMap[v.Product_id] = v.Up_down_state
				}
			}
			for kk := range foodData[0].Skus {
				foodData[0].Skus[kk].Stock = ""
				if len(r) > 0 {
					if statusMap[cast.ToInt32(foodData[0].AppFoodCode)] == 0 {
						foodData[0].IsSoldOut = 1
					} else {
						foodData[0].IsSoldOut = 0
					}
				} else {
					foodData[0].IsSoldOut = 1
				}
			}
			clientMt := GetMtGlobalProductClient()
			//defer clientMt.Close()
			foodData[0].AppPoiCode = appPoiCodeEle
			foodData[0].OperateType = 2
			//请求美团参数写入日志
			gjson, _ := json.Marshal(foodData[0])
			glog.Info("美团更新接口参数：", string(gjson))

			res, err := clientMt.RPC.RetailInitdata(context.Background(), foodData[0])
			glog.Info("7777美团创建/更新商品返回数据为", kit.JsonEncode(res), "---------请求数据为", kit.JsonEncode(foodData[0]))
			// v7.0.11 同步第三方商品ID回来
			MtProductThirdId(enum.UpdateProductThirdIdFrom11, res, []*et.RetailBatchinitdata{&et.RetailBatchinitdata{AppFoodCode: foodData[0].AppFoodCode}}, appPoiCodeMap[appPoiCodeEle], 1)
			if err != nil {
				glog.Error(err)
				out.Message = err.Error()
				out.Code = 400
				return out, err
			}
			if res.Code != 200 && strings.Contains(res.Error.Msg, "不存在此商品") {
				for kk, sku := range foodData[0].Skus {
					stock, _ := GetStockInfoBySkuCodeAndShopId(2, cast.ToInt32(sku.SkuId), appPoiCodeMap[appPoiCodeEle])
					foodData[0].Skus[kk].Stock = cast.ToString(stock)
					if len(r) > 0 {
						if statusMap[cast.ToInt32(foodData[0].AppFoodCode)] == 0 {
							foodData[0].IsSoldOut = 1
						} else {
							foodData[0].IsSoldOut = 0
						}
					} else {
						foodData[0].IsSoldOut = 1
					}
				}
				foodData[0].OperateType = 1

				//请求美团参数写入日志
				gjson, _ := json.Marshal(foodData[0])
				glog.Info("美团更新接口参数1：", string(gjson))
				res_add, err := clientMt.RPC.RetailInitdata(context.Background(), foodData[0])
				glog.Info("8888美团创建/更新商品返回数据为：", kit.JsonEncode(res_add), "请求数据为：", kit.JsonEncode(foodData[0]))
				if err != nil {
					// v7.0.11 同步第三方商品ID回来
					MtProductThirdId(enum.UpdateProductThirdIdFrom12, res_add, []*et.RetailBatchinitdata{&et.RetailBatchinitdata{AppFoodCode: foodData[0].AppFoodCode}}, appPoiCodeMap[appPoiCodeEle], foodData[0].OperateType)
				}
				if err != nil {
					glog.Error(err)
					out.Message = err.Error()
					out.Code = 400
					return out, err
				} else if res_add.Code != 200 {
					out.Message = "美团接口失败，失败原因：" + res_add.Error.Msg
					out.Code = 400
					return out, nil
				}

			}

			go SyncMtPrice(finance_code_arr)
			out.Message = "单商品编辑同步美团成功"
			out.Code = 200
			return out, err
		}
	}
	go SyncMtPrice(finance_code_arr)
	out.Message = "操作成功"
	out.Code = 200
	return out, nil
}
