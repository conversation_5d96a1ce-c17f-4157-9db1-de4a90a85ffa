package services

import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/dc"
	"_/proto/et"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/golang/protobuf/ptypes/empty"

	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"github.com/tricobbler/mqgo"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ChannelProduct struct {
}

type fjStruct struct {
	Finance_code string
	Json_data    string
}

// 根据sku_id查找product_id（批量）
func (c *ChannelProduct) GetProductIdBySkuId(ctx context.Context, in *pc.GetProductIdBySkuIdRequest) (*pc.GetProductIdBySkuIdResponse, error) {
	list := []*models.Sku{}
	if in.IsSaas == 1 {

		if err := NewDbConn().Table("eshop.pro_sku").Select("id, product_id").In("id", in.SkuId).Find(&list); err != nil {
			return nil, err
		}

	} else {
		if err := NewDbConn().Select("id, product_id").In("id", in.SkuId).Find(&list); err != nil {
			return nil, err
		}
	}

	out := &pc.GetProductIdBySkuIdResponse{
		Data: map[int32]int32{},
	}
	for _, v := range list {
		out.Data[v.Id] = v.ProductId
	}

	return out, nil
}

// 从管家商品库认领商品到渠道商品库
func (c *Product) CopyGjProductToChannelProduct(ctx context.Context, in *pc.CopyGjProductToChannelProductRequest) (*pc.BaseResponse, error) {
	if len(in.ChannelId) == 0 {
		return nil, errors.New("渠道id为空不可认领")
	}
	out := new(pc.BaseResponse)
	glog.Info("管家单个认领或重新认领参数: ", in)
	str, err := copyGjProductToChannelProduct(ctx, in.ProductId, in.ChannelId, in.UpdateFields, in.IsBatch)

	if err != nil {
		return nil, err
	} else if str[0][0] != "认领成功" {
		out.Code = 400
		out.Message = "认领失败:" + str[0][3]
	} else {
		out.Code = 200
		out.Message = str[0][0]
	}
	return out, nil
}

// 根据商品ID查询第三方货号信息（渠道）
func (c *Product) QueryChannelSkuThird(ctx context.Context, in *pc.OneofIdRequest) (*pc.SkuThirdResponse, error) {
	out := new(pc.SkuThirdResponse)
	out.Code = 400

	session := NewDbConn().Table("channel_sku_third").
		Alias("a").
		Join("INNER", "erp", "a.erp_id=erp.id").
		Select("a.`id`, a.`sku_id`, a.`third_spu_id`,a.`third_sku_id`, a.`erp_id`, a.`product_id`,erp.`name` as erp_name")
	if len(in.GetSkuId().GetValue()) > 0 {
		session.In("a.sku_id", in.GetSkuId().GetValue())
	} else if len(in.GetProductId().GetValue()) > 0 {
		session.In("a.product_id", in.GetProductId().GetValue())
	} else if len(in.GetErpId().GetValue()) > 0 {
		session.In("a.erp_id", in.GetErpId().GetValue())
	}

	session.And("a.channel_id = ?", in.ChannelId)

	if err := session.Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 根据商品ID查询组合商品信息（渠道）
func (c *Product) QueryChannelSkuGrouop(ctx context.Context, in *pc.OneofIdRequest) (*pc.SkuGroupResponse, error) {
	out := new(pc.SkuGroupResponse)
	out.Code = 400
	session := NewDbConn().NewSession()
	defer session.Close()

	session.Select("a.id, a.product_id, a.sku_id, a.group_product_id, a.group_sku_id, a.count, "+
		"a.discount_value, a.discount_type, a.market_price, a.channel_id, b.name product_name, a.product_type").
		Table("channel_sku_group").
		Alias("a").
		Join("inner", "channel_product b", "b.Id = a.group_product_id AND b.channel_id = ?", in.ChannelId)
	if len(in.GetSkuId().GetValue()) != 0 {
		skuIds := in.GetSkuId().GetValue()
		session.In("a.sku_id", skuIds)
	} else {
		productIds := in.GetProductId().GetValue()
		session.In("a.product_id", productIds)
	}
	session.And("a.channel_id = ?", in.ChannelId)

	if err := session.Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}
	out.Code = 200
	return out, nil
}

// 查询渠道商品主体信息
func (c *Product) QueryChannelProductOnly(ctx context.Context, in *pc.OneofIdRequest) (*pc.ChannelProductResponse, error) {
	out := new(pc.ChannelProductResponse)
	out.Code = 400

	if in.ChannelId == 0 {
		out.Message = "渠道id不能为空"
		return out, nil
	}

	if err := NewDbConn().Table("channel_product").Select("`id`, `channel_id`, `category_id`,"+
		" `brand_id`, `name`, `short_name`, `code`, `bar_code`, `create_date`, `update_date`, `is_del`, "+
		"`is_group`, `pic`, `selling_point`, `video`, `content_pc`, `content_mobile`, "+
		"`is_discount`, `product_type`, `is_use`, `del_date`, `channel_category_id`, "+
		"`channel_tag_id`, `channel_name`, `last_edit_user`, "+
		"`channel_category_name`,`category_name`,`is_recommend`, `brand_name`, `group_type`, `term_type`, "+
		"`term_value`, `use_range`,virtual_invalid_refund").In("id", in.GetProductId().GetValue()).
		And("`channel_id` = ?", in.ChannelId).
		Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	out.Code = 200
	return out, nil
}

// 查询渠道商品详情信息
func (c *Product) QueryChannelProductDetail(ctx context.Context, in *pc.OneofIdRequest) (*pc.ChannelProductDetailResponse, error) {
	out := new(pc.ChannelProductDetailResponse)
	out.Code = 400

	//查询商品主库信息
	if res, err := c.QueryChannelProductOnly(ctx, in); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	} else if len(res.Details) == 0 {
		out.Message = "商品不存在"
		return out, nil
	} else {
		for _, v := range res.Details {
			out.Details = append(out.Details, &pc.ChannelProductDetail{
				Product: v,
			})
		}
	}

	for k, v := range out.Details {
		in = &pc.OneofIdRequest{ChannelId: in.ChannelId, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{v.Product.Id}}}}

		//查询商品属性
		if res, err := c.QueryChannelProductAttr(ctx, in); err != nil {
			glog.Error(err)
			continue
		} else {
			out.Details[k].ProductAttr = res.Details
		}

		//查询商品SKU
		var sku []*pc.Sku
		if res, err := c.QueryChannelSku(ctx, in); err != nil {
			glog.Error(err)
			continue
		} else {
			sku = res.Details
		}

		//查询SKU值
		var skuValue []*pc.SkuValue
		if res, err := c.QueryChannelSkuValue(ctx, in); err != nil {
			glog.Error(err)
			continue
		} else {
			skuValue = res.Details
		}
		//查询第三方货号
		var skuThird []*pc.SkuThird
		if res, err := c.QueryChannelSkuThird(ctx, in); err != nil {
			glog.Error(err)
			continue
		} else {
			skuThird = res.Details
		}

		//规格、规格值
		spec := make(map[int32]string)
		specValue := make(map[int32]string)
		var specID strings.Builder
		var specValueID strings.Builder
		for i, v := range skuValue {
			specID.WriteString(strconv.Itoa(int(v.SpecId)))
			specValueID.WriteString(strconv.Itoa(int(v.SpecValueId)))
			if i != len(skuValue)-1 {
				specID.WriteString(",")
				specValueID.WriteString(",")
			}
		}

		if res, err := c.QuerySpecSingle(ctx, &pc.IdRequest{Id: specID.String()}); err != nil {
			glog.Error(err)
		} else {
			for _, v := range res.Details {
				spec[v.Id] = v.Name
			}
		}

		if res, err := c.QuerySpecValue(ctx, &pc.IdRequest{Id: specValueID.String()}); err != nil {
			glog.Error(err)
		} else {
			for _, v := range res.Details {
				specValue[v.Id] = v.Value
			}
		}

		for _, s := range sku {
			skuInfo := &pc.SkuInfo{
				RetailPrice:   s.RetailPrice,
				SkuId:         s.Id,
				ProductId:     s.ProductId,
				MarketPrice:   s.MarketPrice,
				BarCode:       s.BarCode,
				WeightForUnit: s.WeightForUnit,
				WeightUnit:    s.WeightUnit,
			}
			//第三方货号
			for _, t := range skuThird {
				if t.SkuId == s.Id {
					skuInfo.SkuThird = append(skuInfo.SkuThird, t)
				}
			}

			//sku value
			for _, v := range skuValue {
				if v.SkuId == s.Id {
					skuv := *v
					skuv.SpecName = spec[skuv.SpecId]
					skuv.SpecValueValue = specValue[skuv.SpecValueId]
					skuInfo.Skuv = append(skuInfo.Skuv, &skuv)
				}
			}

			out.Details[k].SkuInfo = append(out.Details[k].SkuInfo, skuInfo)
		}
	}

	out.Code = 200
	return out, nil
}

// 商品库渠道已上架商品列表（渠道）
func (c *Product) QueryChannelProductUp(ctx context.Context, in *pc.QueryProductRequest) (*pc.ChannelProductResponse, error) {
	out := new(pc.ChannelProductResponse)
	out.Code = 400

	session := NewDbConn().NewSession()
	defer session.Close()

	session = session.Table("channel_product").Join("INNER", "(SELECT DISTINCT product_id FROM channel_store_product WHERE channel_id=1 AND finance_code IN('"+strings.Join(strings.Split(in.FinanceCode, ","), "','")+"')) channel_store_product_tmp", "channel_product.id=channel_store_product_tmp.product_id AND channel_product.channel_id=1").Where("1=1")

	// if in.UpDownState >= 0 {
	// 	session.And("channel_store_product.up_down_state = ?", in.UpDownState)
	// }

	// if in.CategoryId >= 0 {
	// 	session.And("category_id=?", in.CategoryId)
	// }
	// if in.BrandId != 0 {
	// 	session.And("brand_id=?", in.BrandId)
	// }

	// if in.FinanceCode != "" {
	// 	session.In("channel_store_product.finance_code", strings.Split(in.FinanceCode, ","))
	// }

	if in.ProductType != 0 {
		session.And("product_type=?", in.ProductType)
	}
	// if in.ChannelId != 0 {
	// 	session.And("channel_store_product.channel_id = ? AND channel_product.channel_id = ?", in.ChannelId, in.ChannelId)
	// }

	switch in.WhereType {
	case "":
		if in.Where != "" {
			session.And("channel_product.name like ? OR channel_product.id = ? OR channel_product.code = ? OR channel_product.bar_code = ?", "%"+in.Where+"%", in.Where, in.Where, in.Where)
		}
		break
	case "name":
		in.WhereType = "channel_product." + in.WhereType
		session.And(in.WhereType+" like ?", "%"+in.Where+"%")
		break
	case "code", "id", "bar_code":
		in.WhereType = "channel_product." + in.WhereType
		session.And(in.WhereType+"=?", in.Where)
		break
	case "third_spu_sku_id":
		var productID []int
		if err := session.Table("channel_sku_third").Select("product_id").Where("third_spu_sku_id LIKE ?", in.Where+"%").Find(&productID); err != nil {
			glog.Error(err)
		}

		if err := session.Table("channel_sku_third").Select("product_id").Where("third_spu_sku_id LIKE ?", "%"+in.Where).Find(&productID); err != nil {
			glog.Error(err)
		}

		session.In("id", productID)
		break
	default:
		out.Message = "查询条件类型有误"
		return out, nil
	}

	sessionCount := session.Clone()

	if rows, err := sessionCount.Count(); err != nil {
		glog.Error(err)
		return out, err
	} else if rows > 0 {
		out.TotalCount = int32(rows)

		if err := session.Select("channel_product.`id`,channel_product.`category_id`,channel_product.`brand_id`,channel_product.`name`,channel_product.`code`,channel_product.`bar_code`,channel_product.`create_date`,channel_product.`update_date`,channel_product.`is_del`,channel_product.`is_group`,channel_product.`pic`,channel_product.`selling_point`,channel_product.`video`,channel_product.`content_pc`,channel_product.`content_mobile`,channel_product.`is_discount`,channel_product.`product_type`,channel_product.`is_use`,channel_product.`channel_id`,channel_product.`channel_category_id`,channel_product.`channel_tag_id`,channel_product.`channel_name`,channel_product.`last_edit_user`,channel_product.`channel_category_name`,channel_product.`category_name`").Distinct().Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("id desc").Find(&out.Details); err != nil {
			glog.Error(err)
			return out, err
		} else if len(out.Details) > 0 {
			for i := 0; i < len(out.Details); i++ {
				sku, _ := c.QueryChannelSku(ctx, &pc.OneofIdRequest{ChannelId: in.ChannelId, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{out.Details[i].Id}}}})
				out.Details[i].Sku = append(out.Details[i].Sku, sku.Details...)
			}
		}
	}
	bt, _ := json.Marshal(out)
	println(string(bt))
	out.Code = 200
	return out, nil
}

func (c *Product) QueryChannelProduct(ctx context.Context, in *pc.QueryProductRequest) (*pc.ChannelProductResponse, error) {
	out := new(pc.ChannelProductResponse)
	out.Code = 400

	session := NewDbConn().Table("channel_product").Alias("a").Where("a.channel_id=?", in.ChannelId)

	//已上架商品
	if in.UpDownState == 1 {
		session.Join("INNER", "channel_store_product b", "a.id = b.product_id and a.channel_id=b.channel_id").
			And("b.up_down_state=1")
		if len(in.FinanceCode) > 0 {
			session.And("b.finance_code=?", in.FinanceCode)
		}
	}
	//已下架商品
	if in.UpDownState == 0 {
		if len(in.FinanceCode) > 0 {
			session.And("id NOT IN(SELECT product_id FROM channel_store_product WHERE channel_id=? AND finance_code=? AND up_down_state=1)", in.ChannelId, in.FinanceCode)
		} else {
			session.And("id NOT IN(SELECT product_id FROM channel_store_product WHERE channel_id=? AND up_down_state=1)", in.ChannelId)
		}
	}
	if in.CategoryId >= 0 {
		session.And("a.channel_category_id=?", in.CategoryId)
	}

	if in.BrandId != 0 {
		session.And("a.brand_id=?", in.BrandId)
	}
	if in.ProductType != 0 {
		switch in.ProductType {
		case 31: // 实实组合
			session.And("a.product_type = 3 and a.group_type = 1")
		case 33: // 实虚组合
			session.And("a.product_type = 3 and a.group_type = 3")
		default:
			session.And("a.product_type=?", in.ProductType)
		}
	}
	var productID []int
	switch in.WhereType {
	case "":
		if in.Where != "" {
			session.And("a.name like ? OR a.id = ? OR a.code = ? OR a.bar_code = ?", "%"+in.Where+"%", in.Where, in.Where, in.Where)
		}
	case "name":
		session.And("a."+in.WhereType+" like ?", "%"+in.Where+"%")
	case "code", "id":
		if len(in.Where) > 0 {
			session.And("a."+in.WhereType+"=?", in.Where)
		}
	case "bar_code":
		if in.Where != "" {
			if err := NewDbConn().Table("channel_sku").Select("product_id").Where("bar_code = ?", in.Where).Find(&productID); err != nil {
				glog.Error(err)
			}
			if len(productID) > 0 {
				session.In("a.id", productID)
			} else {
				out.Code = 200
				return out, nil
			}
		}
	case "third_spu_sku_id":
		if in.Where != "" {
			if err := NewDbConn().Table("channel_sku_third").Select("product_id").Where(" channel_id=? and third_spu_sku_id LIKE ?", in.ChannelId, "%"+in.Where+"%").Find(&productID); err != nil {
				glog.Error(err)
			}
			if len(productID) > 0 {
				session.In("a.id", productID)
			} else {
				out.Code = 200
				return out, nil
			}
		}

	case "sku_id":
		if err := NewDbConn().Table("channel_sku").Select("product_id").Where("id = ?", in.Where).Find(&productID); err != nil {
			glog.Error(err)
		}
		if len(productID) > 0 {
			session.In("a.id", productID)
		} else {
			out.Code = 200
			return out, nil
		}
	default:
		return out, errors.New("查询条件类型有误")
	}

	sessionCount := *session

	if rows, err := sessionCount.Distinct("a.id").Count(); err != nil {
		glog.Error(err)
		return out, err
	} else if rows > 0 {
		out.TotalCount = int32(rows)

		if err := session.Select("a.*").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("a.id desc").GroupBy("a.id").Find(&out.Details); err != nil {
			glog.Error(err)
			return out, err
		} else if len(out.Details) > 0 {
			productIdSlice := []int32{}
			for _, v := range out.Details {
				productIdSlice = append(productIdSlice, v.Id)
			}

			//批量查询商品sku信息
			skus, _ := c.QueryChannelSku(ctx, &pc.OneofIdRequest{ChannelId: in.ChannelId, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: productIdSlice}}})
			if len(skus.Details) > 0 {
				wg := new(sync.WaitGroup)

				for k := range out.Details {
					wg.Add(1)
					go func(k int) {
						defer wg.Done()

						for _, sku := range skus.Details {
							if sku.ProductId == out.Details[k].Id {
								out.Details[k].Sku = append(out.Details[k].Sku, sku)
								//TODO:目前一个spu对应一个sku，所以找到了spu对应的sku就可以break，如果未来一对多则不可以break
								break
							}
						}
					}(k)
				}

				wg.Wait()
			}
		}
	}

	pid := []int32{}
	for _, v := range out.Details {
		pid = append(pid, v.Id)
	}

	var datas []*pc.ChannelProduct
	res, _ := c.QuerySkuThird(ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: pid}}})
	if len(out.Details) > 0 && len(res.Details) > 0 {
		for _, v := range out.Details {
			for _, val := range res.Details {
				if v.Id == val.ProductId {
					v.SkuId = val.SkuId
					break
				}
			}
			datas = append(datas, v)
		}
		out.Details = datas
	}

	out.Code = 200
	return out, nil
}

// 根据商品ID查询渠道SKU信息（渠道）
func (c *Product) QueryChannelSku(ctx context.Context, in *pc.OneofIdRequest) (*pc.SkuResponse, error) {
	out := new(pc.SkuResponse)
	out.Code = 400

	if err := NewDbConn().
		Table("channel_sku").
		Alias("a").
		Select("a.*").
		Where("channel_id = ?", in.ChannelId).
		In("product_id", in.GetProductId().Value).
		Find(&out.Details); err != nil {
		glog.Error(err)
		return out, err
	}

	//查询SKU组合的值信息
	var skuValue []*pc.SkuValue
	if res, err := c.QueryChannelSkuValue(ctx, in); err != nil {
		glog.Error(err)
		return out, err
	} else {
		skuValue = res.Details
	}

	//第三方货号信息
	var skuThird []*pc.SkuThird
	if res, err := c.QueryChannelSkuThird(ctx, in); err != nil {
		glog.Error(err)
		return out, err
	} else {
		skuThird = res.Details
	}

	//组合商品信息
	var skuGroup []*pc.SkuGroup
	if res, err := c.QueryChannelSkuGrouop(ctx, in); err != nil {
		glog.Error(err)
		return out, err
	} else {
		skuGroup = res.Details
	}

	//获得specid,specvalueid
	var specID strings.Builder
	var specValueID strings.Builder
	for i, v := range skuValue {
		specID.WriteString(strconv.Itoa(int(v.SpecId)))
		specValueID.WriteString(strconv.Itoa(int(v.SpecValueId)))
		if i != len(skuValue)-1 {
			specID.WriteString(",")
			specValueID.WriteString(",")
		}
	}

	var spec map[int32]*pc.Spec
	var specValue map[int32]*pc.SpecValue
	if res, err := c.QuerySpecMap(ctx, &pc.IdRequest{Id: specID.String()}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		spec = res.Spec
	}
	if res, err := c.QuerySpecValueMap(ctx, &pc.IdRequest{Id: specValueID.String()}); err != nil {
		glog.Error(err)
		return out, err
	} else {
		specValue = res.SpecValue
	}

	for _, v := range out.Details {
		for _, s := range skuValue {
			if v.Id == s.SkuId {
				sv := *s
				if len(spec) == 0 {
					sv.SpecName = ""
				} else {
					sv.SpecName = spec[sv.SpecId].Name
				}
				if len(specValue) == 0 {
					sv.SpecValueValue = ""
				} else {
					sv.SpecValueValue = specValue[sv.SpecValueId].Value
				}
				v.SkuValue = append(v.SkuValue, &sv)
			}
		}

		for _, t := range skuThird {
			if v.Id == t.SkuId {
				v.SkuThird = append(v.SkuThird, t)
			}
		}

		for _, g := range skuGroup {
			if v.Id == g.SkuId {
				v.SkuGroup = append(v.SkuGroup, g)
			}
		}
	}

	out.Code = 200
	return out, nil
}

// 编辑渠道商品（渠道） 组合商品修改后的单编辑   周翔整理
func (c *Product) EditChannelProduct(ctx context.Context, in *pc.ChannelProductRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: 400}

	glog.Info("编辑单个商品")
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return out, nil
	}

	session := NewDbConn().NewSession()
	defer session.Close()
	session.Begin()
	//单门店
	if len(userInfo.FinancialCode) > 0 {
		storeCategory := 0
		product := new(Product)
		resp, err := product.GetChannelWarehouses([]string{userInfo.FinancialCode}, cast.ToInt32(in.Product.ChannelId))
		if err != nil {
			glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
			out.Message = "查询仓库绑定关系异常" + err.Error()
			return out, nil
		}
		if len(resp) <= 0 && cast.ToInt32(in.Product.ChannelId) == ChannelAwenId { // 如果是阿闻渠道并且查询阿闻外卖没有查询到关联关系则查询竖屏自提
			resp, err = product.GetChannelWarehouses([]string{userInfo.FinancialCode}, ChannelAwenPickUpId)
			if err != nil {
				out.Message = "查询阿闻外卖自提仓库绑定关系异常" + err.Error()
				return out, nil
			}
		}

		if len(resp) <= 0 {
			out.Message = "未查询到渠道仓库绑定信息"
			return out, nil
		}
		glog.Info("查询仓库关系返回数据：", kit.JsonEncode(resp))
		for _, v := range resp {
			storeCategory = v.Category
		}

		if storeCategory > 0 {
			glog.Info(in.FinanceCode+"，门店仓库类型：", storeCategory)
			retmes := "该商品不在应用范围内"
			switch storeCategory {
			case 3: //门店仓上架需要有子龙货号
				//多规格校验支持
				for _, vSku := range in.SkuInfo {
					for _, vThird := range vSku.SkuThird {
						var thirdSkuId string
						has, err := session.SQL("select third_sku_id from channel_sku_third where product_id = ?  and erp_id = 4  and  channel_id= ? and sku_id = ? ", in.Product.Id, in.Product.ChannelId, vThird.SkuId).Get(&thirdSkuId)
						if err != nil {
							session.Rollback()
							glog.Error(err)
							out.Message = fmt.Sprintf("%d", in.Product.Id) + err.Error()
							out.Code = 400
							return out, err
						}
						if !has {
							session.Rollback()
							if in.Product.ProductType != 3 {
								retmes = "查询不到子龙货号信息"
							}
							out.Message = fmt.Sprintf("%d", in.Product.Id) + retmes
							out.Code = 400
							return out, nil
						} else {
							if thirdSkuId == "" {
								session.Rollback()
								out.Message = fmt.Sprintf("%d", in.Product.Id) + "商品子龙货号为空"
								out.Code = 400
								return out, nil
							}
						}
					}
				}

			case 4, 5, 1: //前置仓上架需要有A8货号
				for _, vSku := range in.SkuInfo {
					for _, vThird := range vSku.SkuThird {
						var thirdSkuId string
						has, err := session.SQL("select third_sku_id from channel_sku_third where product_id =? and erp_id = 2 and  channel_id= ? and sku_id = ?", in.Product.Id, in.Product.ChannelId, vThird.SkuId).Get(&thirdSkuId)
						if err != nil {
							session.Rollback()
							glog.Error(err)
							out.Message = fmt.Sprintf("%d", in.Product.Id) + err.Error()
							out.Code = 400
							return out, err
						}
						if !has {
							session.Rollback()
							if in.Product.ProductType != 3 {
								retmes = "查询不到A8货号信息"
							}
							out.Message = fmt.Sprintf("%d", in.Product.Id) + retmes
							out.Code = 400
							return out, nil
						} else {
							if thirdSkuId == "" {
								session.Rollback()
								out.Message = fmt.Sprintf("%d", in.Product.Id) + "商品A8货号为空"
								out.Code = 400
								return out, nil
							}
						}
					}

				}
			default:
				out.Message = in.FinanceCode + "，门店仓库类型不正确"
				out.Code = 400
				return out, nil
			}
		}
	}
	in.Product.LastEditUser = userInfo.UserNo
	channelId := cast.ToInt32(in.Product.ChannelId)

	clientData := GetDataCenterClient()
	defer clientData.Close()

	platformChannel, err := clientData.RPC.QueryPlatformChannelById(clientData.Ctx, &dac.PlatformChannel{
		Id: channelId,
	})
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}
	if len(platformChannel.Data) == 0 {
		out.Message = "渠道未查到"
		return out, nil
	}

	in.Product.ChannelName = platformChannel.Data[0].Name
	in.Product.UpdateDate = time.Now().Format("2006-01-02 15:04:05")

	//递归查询渠道分类做冗余
	if res, err := c.QueryChannelCategoryRecursion(ctx, &pc.IdRequest{Id: strconv.Itoa(int(in.Product.ChannelCategoryId))}); err != nil {
		glog.Error(err)
	} else {
		in.Product.ChannelCategoryName = c.getCategoryName(0, res.Details)
		c.categoryNames = c.categoryNames[0:0]
	}

	//写入商品快照
	bt, err := json.Marshal(in)
	if err != nil {
		glog.Error(err)
	}
	snap := pc.ChannelProductSnapshot{
		ChannelId: cast.ToInt32(in.Product.ChannelId),
		ProductId: in.Product.Id,
		UserNo:    userInfo.UserNo,
		JsonData:  string(bt),
	}
	//查询快照信息
	var dataSnap models.ChannelProductSnapshot
	session.SQL("SELECT * FROM dc_product.channel_product_snapshot a WHERE a.finance_code = ? AND a.product_id = ? AND a.channel_id = ?;",
		in.FinanceCode, in.Product.Id, in.Product.ChannelId).Get(&dataSnap)

	bytes, err := json.Marshal(dataSnap)
	glog.Info("product--log", kit.JsonEncode(dataSnap))
	c.oldSnap = string(bytes)
	c.NewChannelProductSnapshot(ctx, &snap)
	c.UpdatePriceCommonBatch(ctx, []string{userInfo.FinancialCode}, in) // 改成异步处理，不然但商品关联组合商品多的话可能会有问题

	out.Code = 200
	return out, nil
}

// 废弃
// 编辑渠道商品（渠道）  周翔整理
func (c *Product) EditChannelProductOld(ctx context.Context, in *pc.ChannelProductRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: 400}

	glog.Info("编辑单个商品")
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return out, nil
	}

	session := NewDbConn().NewSession()
	defer session.Close()
	session.Begin()
	//单门店
	if len(userInfo.FinancialCode) > 0 {
		storeCategory := 0
		//根据财务编码获取仓库属性
		disclient := GetDispatchClient()
		defer disclient.Close()
		res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: []string{userInfo.FinancialCode}})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
		}
		if len(res.Data) > 0 {
			storeCategory = int(res.Data[0].Category)
		}

		if storeCategory > 0 {
			glog.Info(in.FinanceCode+"，门店仓库类型：", storeCategory)
			switch storeCategory {
			case 3: //门店仓上架需要有子龙货号
				var thirdSkuId string
				has, err := session.SQL("select third_sku_id from channel_sku_third where product_id =  " + fmt.Sprintf("%d", in.Product.Id) + " and erp_id = 4  and  channel_id=" + cast.ToString(in.Product.ChannelId)).Get(&thirdSkuId)
				if err != nil {
					session.Rollback()
					glog.Error(err)
					out.Message = fmt.Sprintf("%d", in.Product.Id) + err.Error()
					out.Code = 400
					return out, err
				}
				if !has {
					session.Rollback()
					out.Message = fmt.Sprintf("%d", in.Product.Id) + "查询不到子龙货号信息，无法上架"
					out.Code = 400
					return out, nil
				} else {
					if thirdSkuId == "" {
						session.Rollback()
						out.Message = fmt.Sprintf("%d", in.Product.Id) + "商品子龙货号为空，无法上架"
						out.Code = 400
						return out, nil
					}
				}
			case 4: //前置仓上架需要有A8货号
				var thirdSkuId string
				has, err := session.SQL("select third_sku_id from channel_sku_third where product_id =  " + fmt.Sprintf("%d", in.Product.Id) + " and erp_id = 2 and  channel_id=" + cast.ToString(in.Product.ChannelId)).Get(&thirdSkuId)
				if err != nil {
					session.Rollback()
					glog.Error(err)
					out.Message = fmt.Sprintf("%d", in.Product.Id) + err.Error()
					out.Code = 400
					return out, err
				}
				if !has {
					session.Rollback()
					out.Message = fmt.Sprintf("%d", in.Product.Id) + "查询不到A8货号信息，无法上架"
					out.Code = 400
					return out, nil
				} else {
					if thirdSkuId == "" {
						session.Rollback()
						out.Message = fmt.Sprintf("%d", in.Product.Id) + "商品A8货号为空，无法上架"
						out.Code = 400
						return out, nil
					}
				}
			default:
				out.Message = in.FinanceCode + "，门店仓库类型不正确"
				out.Code = 400
				return out, nil
			}
		}
	}
	in.Product.LastEditUser = userInfo.UserNo
	channelId := cast.ToInt32(in.Product.ChannelId)

	clientData := GetDataCenterClient()
	defer clientData.Close()

	platformChannel, err := clientData.RPC.QueryPlatformChannelById(clientData.Ctx, &dac.PlatformChannel{
		Id: channelId,
	})
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}
	if len(platformChannel.Data) == 0 {
		out.Message = "渠道未查到"
		return out, nil
	}

	in.Product.ChannelName = platformChannel.Data[0].Name
	in.Product.UpdateDate = time.Now().Format("2006-01-02 15:04:05")

	var sku []models.ChannelSku           //商品SKU
	var skuThird []models.ChannelSkuThird //第三方SKU货号
	var skuValue []models.ChannelSkuValue //商品SKU规格组合
	var attr []models.ChannelProductAttr  //商品自定义属性

	//递归查询渠道分类做冗余
	if res, err := c.QueryChannelCategoryRecursion(ctx, &pc.IdRequest{Id: strconv.Itoa(int(in.Product.ChannelCategoryId))}); err != nil {
		glog.Error(err)
	} else {
		in.Product.ChannelCategoryName = c.getCategoryName(0, res.Details)
		c.categoryNames = c.categoryNames[0:0]
	}

	//更新商品主表（此处用map更新是因为用结构体的时候如果遇到可以为0或空的字段时，会更新不成功）
	_, err = session.Table("channel_product").Where("id=? and channel_id=?", in.Product.Id, in.Product.ChannelId).Update(&map[string]interface{}{
		"brand_id":              in.Product.BrandId,
		"name":                  in.Product.Name,
		"pic":                   in.Product.Pic,
		"selling_point":         in.Product.SellingPoint,
		"video":                 in.Product.Video,
		"content_pc":            in.Product.ContentPc,
		"content_mobile":        in.Product.ContentMobile,
		"is_discount":           in.Product.IsDiscount,
		"last_edit_user":        in.Product.LastEditUser,
		"channel_category_id":   in.Product.ChannelCategoryId,
		"channel_category_name": in.Product.ChannelCategoryName,
		"channel_tag_id":        in.Product.ChannelTagId,
		"channel_name":          platformChannel.Data[0].Name,
		"is_recommend":          in.Product.IsRecommend,
		"update_date":           time.Now().Format("2006-01-02 15:04:05"),
		"brand_name":            in.Product.BrandName,
	})
	if err != nil {
		glog.Error(err)
		session.Rollback()
		out.Message = "事务更新失败，err:" + err.Error()
		return out, err
	}

	//删除ChannelSku相关记录
	if _, err := session.Where("product_id=?", in.Product.Id).And("channel_id=?", in.Product.ChannelId).Delete(&models.ChannelSku{}); err != nil {
		glog.Error(err)
		session.Rollback()
		out.Message = "事务删除失败，err:" + err.Error()
		return out, err
	}

	//删除ChannelSkuThird相关记录
	if _, err := session.Where("product_id=?", in.Product.Id).And("channel_id=?", in.Product.ChannelId).Delete(&models.ChannelSkuThird{}); err != nil {
		glog.Error(err)
		session.Rollback()
		out.Message = "事务删除失败，err:" + err.Error()
		return out, err
	}

	//删除ChannelSkuValue相关记录
	if _, err := session.Where("product_id=?", in.Product.Id).And("channel_id=?", in.Product.ChannelId).Delete(&models.ChannelSkuValue{}); err != nil {
		glog.Error(err)
		session.Rollback()
		out.Message = "事务删除失败，err:" + err.Error()
		return out, err
	}

	//商品自定义属性
	if len(in.ProductAttr) > 0 {
		for _, v := range in.ProductAttr {
			attr = append(attr, models.ChannelProductAttr{
				ProductId:   in.Product.Id,
				AttrId:      v.AttrId,
				AttrName:    v.AttrName,
				AttrValueId: v.AttrValueId,
				AttrValue:   v.AttrValue,
				ChannelId:   channelId,
			})
		}

		//删除之前属性记录
		if _, err := session.Where("product_id=?", in.Product.Id).And("channel_id=?", in.Product.ChannelId).Delete(&models.ChannelProductAttr{}); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务删除失败，err:" + err.Error()
			return out, err
		}

		//批量添加新的属性
		if _, err := session.Insert(&attr); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务新增失败，err:" + err.Error()
			return out, err
		}
	}

	//规格排序
	skuValuesort := int32(1)

	//SKU相关信息整理
	for _, v := range in.SkuInfo {
		var skuId int32
		skuId = v.SkuId

		sku = append(sku, models.ChannelSku{
			Id:            skuId,
			ProductId:     in.Product.Id,
			MarketPrice:   v.MarketPrice,
			PriceUnit:     v.PriceUnit,
			RetailPrice:   v.RetailPrice,
			ChannelId:     channelId,
			IsUse:         v.IsUse,
			WeightForUnit: v.WeightForUnit,
			WeightUnit:    v.WeightUnit,
			MinOrderCount: v.MinOrderCount,
			BarCode:       v.BarCode,
			PreposePrice:  v.PreposePrice,
			StorePrice:    v.StorePrice,
		})

		//第三方货号
		for _, t := range v.SkuThird {
			third := models.ChannelSkuThird{
				SkuId:      skuId,
				ThirdSkuId: t.ThirdSkuId,
				ThirdSpuId: t.ThirdSpuId,
				ErpId:      t.ErpId,
				ProductId:  in.Product.Id,
				ChannelId:  channelId,
				IsUse:      t.IsUse,
			}

			if t.ThirdSkuId != "" || t.ThirdSpuId != "" {
				third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", t.ThirdSpuId, t.ThirdSkuId)
			} else {
				continue
			}

			skuThird = append(skuThird, third)
		}

		//SKU规格组合
		for _, s := range v.Skuv {
			skuValue = append(skuValue, models.ChannelSkuValue{
				SpecId:      s.SpecId,
				SpecValueId: s.SpecValueId,
				SkuId:       skuId,
				ProductId:   in.Product.Id,
				Pic:         s.Pic,
				Sort:        skuValuesort,
				ChannelId:   channelId,
			})
			skuValuesort++
		}
	}

	if len(sku) > 0 {
		if _, err := session.Insert(&sku); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务新增失败，err:" + err.Error()
			return out, err
		}
	}

	//第三方SKU货号
	if len(skuThird) > 0 {
		if _, err := session.Insert(&skuThird); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务新增失败，err:" + err.Error()
			return out, err
		}
	}

	//商品SKU规格组合
	if len(skuThird) > 0 {
		if _, err := session.Insert(&skuValue); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务新增失败，err:" + err.Error()
			return out, err
		}
	}

	//只有总账号和多门店账号更新渠道商品信息
	var shopList []string
	if len(userInfo.FinancialCode) == 0 {
		if userInfo.IsGeneralAccount {
			if err := session.Commit(); err != nil {
				session.Rollback()
				glog.Error(err)
				out.Message = "事务提交失败，err:" + err.Error()
				return out, err
			}
		} else {
			session.Rollback()
		}
		var isLogo int32
		if userInfo.IsGeneralAccount {
			isLogo = 1
		}
		out_result, err := clientData.RPC.GetHospitalListByUserNo(clientData.Ctx, &dac.GetHospitalListByUserNoRequest{
			UserNo:    userInfo.UserNo,
			ChannelId: channelId,
			IsLogo:    isLogo,
		})
		if err != nil {
			out.Message = "请求GetHospitalListByUserNo失败"
			out.Error = err.Error()
			out.Code = 400
			return out, nil
		}
		for _, v := range out_result.Data {
			shopList = append(shopList, v.StructOuterCode)
		}
	} else {
		session.Rollback()
	}

	//写入商品快照
	bt, err := json.Marshal(in)
	if err != nil {
		glog.Error(err)
	}
	snap := pc.ChannelProductSnapshot{
		ChannelId: cast.ToInt32(in.Product.ChannelId),
		ProductId: in.Product.Id,
		UserNo:    userInfo.UserNo,
		JsonData:  string(bt),
	}

	//京东渠道总账更改快照时不需要更改前置仓价格和门店仓价格，而多门店账号需要
	if userInfo.FinancialCode == "" {
		if len(shopList) > 0 {
			//优化sql
			var channel_product_snapshot_list []models.ChannelProductSnapshot
			engine.Where("channel_id=?", channelId).And("product_id=?", in.Product.Id).In("finance_code", shopList).Cols("finance_code,json_data").Find(&channel_product_snapshot_list)
			channel_product_snapshot_map := make(map[string]string, len(channel_product_snapshot_list))
			for _, v := range channel_product_snapshot_list {
				channel_product_snapshot_map[v.FinanceCode] = v.JsonData
			}
			for _, v := range shopList {

				if v == "CX0004" {
					fmt.Println("测试")
				}

				var jsonData string
				var channelProductReq pc.ChannelProductRequest
				if bt, err := json.Marshal(in); err != nil {
					glog.Error(err)
				} else {
					snap.JsonData = string(bt)
				}
				if res, ok := channel_product_snapshot_map[v]; ok {
					jsonData = res
				} else {
					snap.FinanceCode = v
					c.NewChannelProductSnapshot(ctx, &snap)
					continue
				}
				err = json.Unmarshal([]byte(jsonData), &channelProductReq)
				if err != nil {
					glog.Error(err)
				}
				//京东渠道总账号保留每家门店的价格
				if userInfo.IsGeneralAccount && channelId == ChannelJddjId {
					in.SkuInfo[0].PreposePrice = channelProductReq.SkuInfo[0].PreposePrice
					in.SkuInfo[0].StorePrice = channelProductReq.SkuInfo[0].StorePrice
				}
				if bt, err := json.Marshal(in); err != nil {
					glog.Error(err)
				} else {
					snap.JsonData = string(bt)
				}
				snap.FinanceCode = v
				c.NewChannelProductSnapshot(ctx, &snap)
			}
			//京东渠道多账号更新价格
			if !userInfo.IsGeneralAccount && channelId == ChannelJddjId {
				financeCodeStr := strings.Join(shopList, ",")
				priceIn := &pc.SyncJddjPriceRequest{
					OutSkuId:     in.SkuInfo[0].SkuId,
					StorePrice:   in.SkuInfo[0].StorePrice,
					PreposePrice: in.SkuInfo[0].PreposePrice,
					FinanceCode:  financeCodeStr,
				}
				res, err := c.SyncJddjPrice(ctx, priceIn)
				if err != nil {
					glog.Error(err)
				} else if res.Code != 200 {
					glog.Info(res.Message)
				} else {
					glog.Info("批量同步完成")
				}
			}
		} else {
			c.NewChannelProductSnapshot(ctx, &snap)
		}
		//价格同步
		c.UpdatePriceCommonBatch(ctx, shopList, in)
		out.Code = 200
		return out, nil
	}

	if len(shopList) > 0 {
		c.UpdatePriceCommonBatch(ctx, shopList, in)
		for _, v := range shopList {
			snap.FinanceCode = v
			c.NewChannelProductSnapshot(ctx, &snap)
		}
	} else {
		//if channelId != 1 { //add by csf上面的价格已经是北京的价格了，阿闻渠道这边不需要再用界面过来的价格更新快照，在不影响其他渠道代码逻辑的情况下排除掉阿闻渠道
		c.NewChannelProductSnapshot(ctx, &snap) //先更新本地的价格
		//}
		//再更新北京来的数据
		c.UpdatePriceCommonBatch(ctx, []string{userInfo.FinancialCode}, in)
	}
	out.Code = 200
	return out, nil
}

// 批量编辑改价公共模块
func (c *Product) UpdatePriceCommonBatch(ctx context.Context, FinanceCode []string, in *pc.ChannelProductRequest) {
	for _, shop := range FinanceCode {

		for _, v_sku := range in.SkuInfo { // 兼容多规格处理
			var channelProduct ChannelProductPriceSync
			channelProduct.oldJsonData = c.oldSnap
			channelProduct.ChannelId = cast.ToInt(in.Product.ChannelId)
			channelProduct.ProductId = cast.ToString(in.Product.Id)
			channelProduct.FinanceCode = shop
			channelProduct.ProductSkuId = cast.ToString(v_sku.SkuId)
			channelProduct.SyncPrice()
		}

	}
}

// 更新前置仓已经上架的商品价格，根据前置仓的价格表
func (c *Product) SyncGoodsQzcPrice(ctx context.Context, in *pc.PreposePriceUpInfo) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{
		Code: 400,
	}
	defer func() {
		if err := recover(); err != nil {
			glog.Error(err)
		}
	}()
	finance_code_list := make([]string, 0)
	finance_code_map := make(map[string]int) //初始化map

	client := GetDispatchClient()
	defer client.Close()
	//查询前置仓门店
	res, err := client.RPC.GetWarehouseRelation(context.Background(), &dc.Empty{})
	if err != nil {
		glog.Error("批量更新前置仓价格出错： " + err.Error())
		out.Message = "批量更新前置仓价格出错，err: " + err.Error()
		return out, nil
	}

	//获取美团和财务编码的对应关系
	//1、获取redis中 财务编码和子龙id的对应关系   携程里面的方法提取出来
	redisConn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}
	maplist := redisConn.HGetAll("store:relation:dctomt").Val()
	if len(maplist) == 0 {
		glog.Error("查询不到财务编码与子龙关系数据，请增加子龙门店和财务编码关系信息")
	}

	//说明是指定了只跑哪个店的，否则全部跑
	if in.FinanceCode != "" && in.FinanceCode != "Hello" {

		for _, v := range res.WarehouseRelationAarray {
			if v.ShopId == in.FinanceCode {
				finance_code_map[v.ShopId] = int(v.Id)
				finance_code_list = append(finance_code_list, in.FinanceCode)
				break
			}

		}
	} else {

		for _, v := range res.WarehouseRelationAarray {
			finance_code_map[v.ShopId] = int(v.Id)
			finance_code_list = append(finance_code_list, v.ShopId)
		}

	}
	glog.Info("SyncGoodsQzcPrice 开始跑前置仓价格" + time.Now().Format("2006-01-02 15:04:05"))
	db := NewDbConn()

	//for _, shop := range finance_code_list {
	//
	//}
	//var wg1 sync.WaitGroup //定义一个同步等待的组
	//go func() {
	//wg1.Add(1)
	l := 5 //这里的5，指的是5家门店的数量
	for {
		if len(finance_code_list) < l {
			l = len(finance_code_list)
		}
		_list := finance_code_list[:l]

		var wg sync.WaitGroup //定义一个同步等待的组

		for _, l := range _list {
			wg.Add(1)
			go func(shop string) {
				redisHandle := GetRedisConn()
				if !kit.EnvCanCron() {
					defer redisHandle.Close()
				}
				if _, ok := finance_code_map[shop]; ok {

					qzc_price_sync_list := make([]models.QzcPriceSync, 0)
					err = db.Where("warehouse_id=?", finance_code_map[shop]).Find(&qzc_price_sync_list)
					if err != nil {
						glog.Error("查询前置仓价格失败 " + shop + " " + err.Error())
					}
					price_map := make(map[int]int) //初始化map
					for _, product_price := range qzc_price_sync_list {
						price_map[product_price.ProductId] = product_price.Price
					}

					//如果包含阿闻，才跑
					if strings.Contains(in.ChannelIds, cast.ToString(ChannelAwenId)) {
						glog.Info("阿闻开始跑前置仓价格 门店 " + shop)
						channel_store_product_list := make([]models.ChannelProductSnapshot, 0)
						err := db.Where("finance_code=? AND channel_id=?", shop, ChannelAwenId).Find(&channel_store_product_list)
						if err != nil {
							glog.Error("阿闻前置仓价格查询上架商品失败 " + shop + " " + err.Error())
							//out.Message="前置仓价格查询上架商品失败 "+shop+" "+ err.Error()
						}
						//一个门店一个门店的处理
						for _, store_product := range channel_store_product_list {

							var request pc.ChannelProductRequest
							err := json.Unmarshal([]byte(store_product.JsonData), &request)
							if err != nil {
								glog.Error("阿闻前置价格 快照解析失败 " + cast.ToString(store_product.Id) + " " + err.Error())
							}

							if _, ok := price_map[int(store_product.ProductId)]; ok {
								if int(request.SkuInfo[0].MarketPrice) != price_map[int(store_product.ProductId)] {
									var channelProduct ChannelProductPriceSync
									channelProduct.ChannelId = ChannelAwenId
									channelProduct.ProductId = cast.ToString(store_product.ProductId)
									channelProduct.FinanceCode = shop
									channelProduct.ProductSkuId = cast.ToString(request.SkuInfo[0].SkuId)
									//如果是批量的处理，用一次性查询出来的对应关系去查询价格

									byt, err := json.Marshal(&store_product)
									if err != nil {
										glog.Error("美图前置价格 快照解析失败 " + cast.ToString(store_product.Id) + " " + err.Error())
									}

									channelProduct.JsonData = string(byt)
									channelProduct.WarehouseId = finance_code_map[shop]
									channelProduct.WarehouseCategory = 4
									channelProduct.ProductSyncPrice = price_map[int(store_product.ProductId)]
									channelProduct.QzcUpPrice()
								} else {
									//glog.Info(fmt.Sprintf("商品SKUID %d 前置仓价格相同跳过执行 门店 %s", store_product.SkuId,shop))
								}

							} else {
								//glog.Info(fmt.Sprintf("商品SKUID %d 前置仓价格未找到 门店 %s", store_product.SkuId,shop))
							}

						}
						glog.Info("阿闻跑前置仓价格结束 门店 " + shop)
					}

					//如果包含美团，才跑
					if strings.Contains(in.ChannelIds, cast.ToString(ChannelMtId)) {
						if _, ok := maplist[shop]; ok {
							glog.Info("美团跑前置仓价格开始 门店 " + shop)
							channel_store_product_list_mt := make([]models.ChannelProductSnapshot, 0)
							err := db.Where("finance_code=? AND channel_id=?", shop, ChannelMtId).Find(&channel_store_product_list_mt)
							if err != nil {
								glog.Error("阿闻前置仓价格查询上架商品失败 " + shop + " " + err.Error())
							}

							//一个门店一个门店的处理
							for _, store_product := range channel_store_product_list_mt {
								var request pc.ChannelProductRequest
								err := json.Unmarshal([]byte(store_product.JsonData), &request)
								if err != nil {
									glog.Error("美图前置价格 快照解析失败 " + cast.ToString(store_product.Id) + " " + err.Error())
								}
								if _, ok := price_map[int(store_product.ProductId)]; ok {
									if int(request.SkuInfo[0].MarketPrice) != price_map[int(store_product.ProductId)] {
										var channelProduct ChannelProductPriceSync
										channelProduct.ChannelId = ChannelMtId
										channelProduct.ProductId = cast.ToString(store_product.ProductId)
										channelProduct.FinanceCode = shop
										channelProduct.ProductSkuId = cast.ToString(request.SkuInfo[0].SkuId)
										//如果是批量的处理，用一次性查询出来的对应关系去查询价格

										byt, err := json.Marshal(&store_product)
										if err != nil {
											glog.Error("美图前置价格 快照解析失败 " + cast.ToString(store_product.Id) + " " + err.Error())
										}

										channelProduct.JsonData = string(byt)
										channelProduct.WarehouseId = finance_code_map[shop]
										channelProduct.WarehouseCategory = 4
										channelProduct.ProductSyncPrice = price_map[int(store_product.ProductId)]
										channelProduct.ChannelFinanaceCode = maplist[shop]
										channelProduct.QzcUpPrice()
									} else {
										//glog.Info(fmt.Sprintf("商品SKUID %d 前置仓价格相同跳过执行 门店 %s", store_product.SkuId,shop))
									}

								} else {
									//glog.Info(fmt.Sprintf("商品SKUID %d 前置仓价格未找到 门店 %s", store_product.SkuId,shop))
								}

							}
							glog.Info("美团跑前置仓价格结束 门店 " + shop)

						} else {
							glog.Info("门店 " + shop + "未找美团对应关系")
						}
					}

				} else {
					glog.Info("门店 " + shop + "未找到对应前置仓关系")
				}
				glog.Info("跑前置仓价格成功 门店 " + shop)

				wg.Done()

			}(l)
		}
		wg.Wait()
		finance_code_list = finance_code_list[l:]
		if len(finance_code_list) == 0 {
			//wg1.Done()
			break
		}
	}
	//wg1.Wait()
	glog.Info(" SyncGoodsQzcPrice 结束跑前置仓价格" + time.Now().Format("2006-01-02 15:04:05"))
	//}()
	out.Code = 200
	out.Message = "启动跑前置仓价格成功"
	return out, nil

}

// 更新当前门店当前商品的组合商品价格
func UpdateGroupProductPrice(financeCode string, channelId, category, productId, price int32, groupMasterProIds []int32, skuId int32) {
	glog.Info("更新当前门店当前商品的组合商品价格: ", financeCode, " channel : ", channelId, " category : ", category, " product : ", productId,
		" price :", price, " groupMasterProids : ", groupMasterProIds, "sku_id : ", skuId)

	defer func() {
		// 是否有未捕获的异常
		if err := recover(); err != nil {
			glog.Error("周翔错误捕获3", err)
		}
	}()

	var snapshotList []models.ChannelProductSnapshot
	session := engine.Select("distinct a.*").Table("channel_product_snapshot").Alias("a").
		Join("inner", "channel_sku_group b", "a.product_id = b.product_id").
		Where("a.channel_id = ?", channelId).And("a.finance_code = ?", financeCode).
		And("b.group_product_id = ?", productId).And("b.channel_id = ? ", channelId)
	if len(groupMasterProIds) > 0 {
		session.In("a.product_id", groupMasterProIds)
	}
	err := session.Find(&snapshotList)
	if err != nil {
		glog.Error("查询门店组合商品信息失败: ", err)
		return
	}
	// todo 虚拟商品的时候如果是折扣的话直接取channel_sku的价格

	for _, snap := range snapshotList {
		var snapshot pc.ChannelProductRequest
		err := json.Unmarshal([]byte(snap.JsonData), &snapshot)
		if err != nil {
			glog.Error("解析组合商品快照失败；", err)
			continue
		}
		// 组合商品价格
		var groupPrice int32
		var isUpdate = false

		for _, v_sku := range snapshot.SkuInfo {
			for _, v_group := range v_sku.SkuGroup {
				// 当前子商品价格
				if v_group.GroupProductId == productId && v_group.MarketPrice != price && v_group.GroupSkuId == skuId {
					isUpdate = true
					v_group.MarketPrice = price
				}
				if v_group.DiscountType == 1 {
					// 将单价四舍五入
					var discountPrice = decimal.NewFromInt(int64(v_group.MarketPrice)).
						Mul(decimal.NewFromInt(int64(v_group.DiscountValue))).
						DivRound(decimal.NewFromInt(100), 0).IntPart()
					// 子商品的价格综合 单价*数量
					groupPrice = groupPrice + int32(discountPrice)*v_group.Count
				}
			}
		}

		if groupPrice > 0 {
			for _, v_sku := range snapshot.SkuInfo {
				v_sku.MarketPrice = groupPrice
			}
		}

		// 若子商品价格修改则更新组合商品快照
		if isUpdate {
			if category == 3 || category == 1 {
				for _, v := range snapshot.SkuInfo {
					v.StorePrice = v.MarketPrice
				}
			} else if category == 4 || category == 5 {
				for _, v := range snapshot.SkuInfo {
					v.PreposePrice = v.MarketPrice
				}
			}

			bt, _ := json.Marshal(&snapshot)
			snap.JsonData = string(bt)
			_, err = engine.ID(snap.Id).Update(&snap)
			if err != nil {
				glog.Error("更新组合商品快照失败: ", err)
				return
			}
			for _, v_sku := range snapshot.SkuInfo {

				glog.Info("批量修改子商品的价格信息", kit.JsonEncode(v_sku), " channel_id : ", channelId, productId, skuId)

				engine.Where("snapshot_id = ?", snap.Id).Update(&models.ChannelStoreProduct{MarketPrice: int(v_sku.MarketPrice)})

				// todo 子商品对应的第三方平台的组合商品 在这里同步价格到第三方渠道
				switch channelId {
				case ChannelMtId:
					glog.Info("更新组合商品的mt价格", financeCode, " 仓库类型：", category)
					UpdateToMt(financeCode, category, v_sku)
				case ChannelElmId:
					glog.Info("更新组合商品的ele-me价格", financeCode, " 仓库类型： ", category)
					updatePriceToEle(financeCode, category, v_sku)
				case ChannelJddjId:
					glog.Info("更新组合商品的jddj价格", financeCode, "仓库类型： ", category)
					updatePriceToJDdj(financeCode, category, v_sku, true)
				}
			}
		}
	}
}

func UpdateToMt(financeCode string, category int32, v_sku *pc.SkuInfo) {

	glog.Info("UpdateToMt run。。 ")
	//通过门店财务编码查询渠道门店id
	appPoiCodeMap := GetAppPoiCodeByFinanceCode([]string{financeCode}, 2)
	appPoiCodeSlice := []string{}
	for k := range appPoiCodeMap {
		if len(k) > 0 {
			appPoiCodeSlice = append(appPoiCodeSlice, k)
		}
	}
	if len(appPoiCodeSlice) <= 0 {
		return
	}

	//调用美团的更新商品接口，更新商品信息
	clientMt := et.GetExternalClient()
	defer clientMt.Close()
	var food_data_list []et.FoodDataMt
	var food_data et.FoodDataMt
	var skus et.FoodSku
	skus.SkuId = cast.ToString(v_sku.SkuId)
	var priceInt int32

	switch category {
	case 3:
		priceInt = v_sku.StorePrice
	case 4, 5:
		priceInt = v_sku.PreposePrice
	}
	f, err := cast.ToFloat64E(priceInt)
	if err != nil {
		glog.Error("美团价格同步价格转换失败，失败原因：" + err.Error())
		return
	}
	priceStr := f / 100
	skus.Price = cast.ToString(priceStr)
	food_data.AppFoodCode = cast.ToString(v_sku.ProductId)
	food_data.Skus = append(food_data.Skus, &skus)
	food_data_list = append(food_data_list, food_data)
	str, err := json.Marshal(food_data_list)
	if err != nil {
		glog.Error("美团价格同步反序列化失败，失败原因：" + err.Error())
		return
	}
	foodData := et.MtRetailSkuPriceRequest{}
	foodData.AppPoiCode = appPoiCodeSlice[0]
	foodData.FoodData = string(str)
	storeMasterId, err := GetAppChannelByFinanceCode(financeCode)
	if err != nil {
		glog.Error("syncPriceToMt.", "GetAppChannelByFinanceCode failed：", financeCode, err)
		return
	}
	foodData.StoreMasterId = storeMasterId
	if !CanCallThirdApi(cast.ToInt(v_sku.ProductId), ChannelMtId, financeCode) {
		glog.Error("没有第三方商品id====15,商品id:", cast.ToInt(v_sku.ProductId), "财务编码：", financeCode)

		return
	}

	//todo tp mt
	glog.Info("mt-价格同步参数", kit.JsonEncode(foodData))
	res, err := clientMt.RPC.MtRetailSkuPrice(clientMt.Ctx, &foodData)
	glog.Info("同步价格到第三方返回数据：：", kit.JsonEncode(res), "入参：", kit.JsonEncode(foodData), "err为：", kit.JsonEncode(err))
	if err != nil {
		glog.Info("美团价格同步接口失败，失败原因：" + err.Error())
		return
	}
	errMsg := ""
	if res.Code != 200 {
		errMsg = res.Message
	}
	UpdateProductThirdSyncErr(cast.ToInt(v_sku.ProductId), ChannelMtId, financeCode, errMsg)
	if res.Code != 200 {

		glog.Info("美团价格同步接口失败，失败原因：" + res.Message)
		return
	}

}

func updatePriceToEle(financeCode string, category int32, v_sku *pc.SkuInfo) {
	clientElm := GetMtProductClient()
	defer clientElm.Close()

	//通过门店财务编码查询渠道门店id
	appPoiCodeMap := GetAppPoiCodeByFinanceCode([]string{financeCode}, 3)
	appPoiCodeSlice := []string{}
	for k := range appPoiCodeMap {
		if len(k) > 0 {
			appPoiCodeSlice = append(appPoiCodeSlice, k)
		}
	}
	if len(appPoiCodeSlice) == 0 {
		msg := "更新ele组合商品的价格没有可用的渠道门店"
		glog.Error("同步子商品的组合商品", msg)
	}

	appChannel, err := GetAppChannelByFinanceCode(financeCode)
	if err != nil || appChannel == 0 {
		glog.Error("更新ele组合商品的价格获取门店的appChannel出错", appChannel)
	}

	var elmParams et.SkuPriceUpdateOneRequest
	if category == 3 {
		elmParams.SkuidPrice = fmt.Sprintf("%d:%d,%d", v_sku.SkuId, v_sku.StorePrice, v_sku.StorePrice)
	} else if category == 4 || category == 5 {
		//价格同步前置仓
		elmParams.SkuidPrice = fmt.Sprintf("%d:%d,%d", v_sku.SkuId, v_sku.PreposePrice, v_sku.PreposePrice)
	}
	elmParams.ShopId = appPoiCodeSlice[0]
	elmParams.AppChannel = appChannel
	if !CanCallThirdApi(cast.ToInt(v_sku.ProductId), ChannelElmId, financeCode) {

		glog.Error("没有第三方商品id====16,商品id:", v_sku.ProductId, "财务编码：", financeCode)
		return

	}
	glog.Info("更新ele组合商品的价格子商品的组合商品饿了么更新价格参数：", elmParams)
	res, err := clientElm.ELMPRODUCT.SkuPriceUpdateOne(context.Background(), &elmParams)
	glog.Info("饿了么更新价格响应结果：", res, "饿了么更新价格参数", elmParams, "err为:", kit.JsonEncode(err))

	if err != nil {
		glog.Info("更新ele组合商品的价格同步子商品的组合商品的价格失败", err.Error())
	}
	UpdateProductThirdSyncErr(cast.ToInt(v_sku.ProductId), ChannelElmId, financeCode, res.Error)

}

func updatePriceToJDdj(financeCode string, category int32, v_sku *pc.SkuInfo, limitFrequency bool) {

	dcClinet := GetDispatchClient()
	defer dcClinet.Close()
	client := et.GetExternalClient()
	defer client.Close()
	redisConn := GetRedisConn()
	pip := redisConn.Pipeline()
	defer func() {
		pip.Close()
		if kit.EnvCanCron() {
			redisConn.Close()
		}
	}()
	financeCodeSlice := strings.Split(financeCode, ",")
	//批量获取京东到家编码
	jddjMap := map[string]string{}
	err := func() error {
		for _, v := range financeCodeSlice {
			pip.HGet("store:relation:dctojddj", v)
		}
		cmders, _ := pip.Exec()
		for _, c := range cmders {
			jddjMap[cast.ToString(c.Args()[2])] = c.(*redis.StringCmd).Val()
		}
		return nil
	}()
	if err != nil {
		glog.Error(utils.RunFuncName(), "获取门店京东到家编码失败")
		return
	}
	glog.Info("jddjMap: ", jddjMap)

	//限频0.6秒一次
	if limitFrequency {
		time.Sleep(600 * time.Millisecond)
	}

	etReq := new(et.UpdateStationPriceRequest)
	etReq.StationNo = jddjMap[financeCode]
	etReq.OutStationNo = financeCode
	subData := new(et.JddjSkuPriceInfo)
	subData.OutSkuId = cast.ToString(v_sku.SkuId)

	// 添加到请求信息
	storeMasterId, err := GetAppChannelByFinanceCode(financeCode)
	if err != nil {
		glog.Error("syncPriceToJd,", "GetAppChannelByFinanceCode,", financeCode, err)
		return
	}

	etReq.StoreMasterId = storeMasterId

	switch category {
	case 3:
		subData.Price = int64(v_sku.StorePrice)
	case 4, 5:
		subData.Price = int64(v_sku.PreposePrice)
	}
	etReq.SkuPriceInfoList = append(etReq.SkuPriceInfoList, subData)
	glog.Info("跟新jddj的组合商品的价格", kit.JsonEncode(etReq))
	if res, err := client.JddjProduct.UpdateStationPrice(context.Background(), etReq); err != nil {
		err = errors.New(utils.RunFuncName() + "，更新jddj组合商品的价格调用UpdateStationPrice失败，" + err.Error())
		glog.Error(err)
		return
	} else if res.Code != "0" {
		err = errors.New(utils.RunFuncName() + "，更新jddj组合商品的价格调用UpdateStationPrice失败，" + res.Msg)
		glog.Error(err)
	} else {
		glog.Info("跟新jddj的组合商品的价格 res信息", kit.JsonEncode(res))
	}

}

// 批量同步京东到家门店商品价格
func (c *Product) SyncJddjPrice(ctx context.Context, in *pc.SyncJddjPriceRequest) (*pc.BaseResponse, error) {
	//京东 TODO DONE:更新到京东门店价格

	redisConn := GetRedisConn()
	pip := redisConn.Pipeline()
	defer func() {
		pip.Close()
		if kit.EnvCanCron() {
			redisConn.Close()
		}
	}()

	financeCodeSlice := strings.Split(in.FinanceCode, ",")
	//批量获取京东到家编码
	jddjMap := map[string]string{}
	err := func() error {
		for _, v := range financeCodeSlice {
			pip.HGet("store:relation:dctojddj", v)
		}
		cmders, _ := pip.Exec()
		for _, c := range cmders {
			jddjMap[cast.ToString(c.Args()[2])] = c.(*redis.StringCmd).Val()
		}
		return nil
	}()
	if err != nil {
		return nil, status.Error(codes.Internal, "获取门店京东到家编码失败")
	}
	glog.Info("jddjMap: ", jddjMap)
	//client := et.GetExternalClient()
	//defer client.Close()
	dcClinet := GetDispatchClient()
	defer dcClinet.Close()

	//backCont := context.Background()
	//var sku models.SkuThird
	//engine.Where("erp_id=4").And("sku_id=?", in.OutSkuId).Get(&sku)

	//disclient := GetDispatchClient()
	//defer disclient.Close()
	//res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: financeCodeSlice})
	//if err != nil {
	//	glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
	//}

	//warehouseMap := make(map[string]*dc.WarehouseList, len(res.Data))
	//
	////var financeCode_list []string
	//for _, v := range res.Data {
	//	warehouseMap[v.Code] = v
	//}

	product := new(Product)
	resp, err := product.GetChannelWarehouses(financeCodeSlice, ChannelJddjId)

	if err != nil {
		glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
	}

	warehouseMap := make(map[string]models.ChannelWarehouse, 0)
	for i := range resp {
		warehouse := resp[i]
		warehouseMap[warehouse.ShopId] = warehouse
	}

	glog.Info("获取京东到家的渠道关联关系：", kit.JsonEncode(warehouseMap))

	//var params_list []models.PriceSync
	////取本地，本地不存在取北京
	//engine.Where("sku=?", in.OutSkuId).In("finance_code", financeCode_list).Find(&params_list)

	//params_list_map := make(map[string]models.PriceSync, len(params_list))
	//for _, v := range params_list {
	//	params_list_map[v.FinanceCode] = v
	//}

	for _, financeCodeEle := range financeCodeSlice {
		//限频0.6秒一次
		time.Sleep(600 * time.Millisecond)

		storeMasterId, err := GetAppChannelByFinanceCode(financeCodeEle)
		if err != nil {
			err = errors.New(utils.RunFuncName() + "调用GetAppChannelByFinanceCode根据财务编码查询店铺主体信息失败：" + err.Error() + financeCodeEle)
			glog.Error(err)
			continue
		}

		etReq := new(et.UpdateStationPriceRequest)
		etReq.StoreMasterId = storeMasterId
		etReq.StationNo = jddjMap[financeCodeEle]
		etReq.OutStationNo = financeCodeEle
		subData := new(et.JddjSkuPriceInfo)
		subData.OutSkuId = cast.ToString(in.OutSkuId)

		//根据财务编码获取门店对应的仓库 -- 便于取商品价格
		//warehouse, err := dcClinet.RPC.GetWarehouseInfoByFanceCode(backCont, &dc.GetWarehouseInfoByFanceCodeRequest{FinanceCode: financeCodeEle})
		//glog.Info("GetWarehouseInfoByFanceCode根据财务编码查询仓库类型：", financeCodeEle, "，返回值：", warehouse)
		//if err != nil {
		//	err = errors.New(utils.RunFuncName() + "调用GetWarehouseInfoByFanceCode根据财务编码查询仓库类型失败：" + err.Error())
		//	glog.Error(err)
		//	continue
		//}
		//if warehouse.Code != 200 {
		//	err = errors.New(utils.RunFuncName() + "调用GetWarehouseInfoByFanceCode根据财务编码查询仓库类型失败：" + warehouse.Message)
		//	glog.Error(err)
		//	continue
		//}

		if value, ok := warehouseMap[financeCodeEle]; ok {
			switch value.Category {
			case 3:
				subData.Price = int64(in.StorePrice)
			case 4, 5:
				subData.Price = int64(in.PreposePrice)
			}
		} else {
			glog.Error("没有获取到门店的仓库关系", err.Error())
			continue
		}

		etReq.SkuPriceInfoList = append(etReq.SkuPriceInfoList, subData)
		//if res, err := client.JddjProduct.UpdateStationPrice(backCont, etReq); err != nil {
		//	err = errors.New(utils.RunFuncName() + "，调用UpdateStationPrice失败，" + err.Error())
		//	glog.Error(err)
		//	continue
		//} else if res.Code != "0" {
		//	err = errors.New(utils.RunFuncName() + "，调用UpdateStationPrice失败，" + res.Msg)
		//	glog.Error(err)
		//	continue
		//}

		//价格同步
		if value, ok := warehouseMap[financeCodeEle]; ok && value.Category == 3 {
			UpdateZlPrice(financeCodeEle, in.OutSkuId, ChannelJddjId)

		} else if value.Category == 4 || value.Category == 5 {
			UpdateQzPrice(financeCodeEle, int32(value.WarehouseId), in.OutSkuId, ChannelJddjId)
		}
	}
	return &pc.BaseResponse{
		Code:    200,
		Message: "同步价格完成，请到京东查看结果",
	}, nil
}

// 查询渠道门店商品（渠道）
func (c *Product) QuerySaaSChannelStoreProduct(ctx context.Context, in *pc.ChannelStoreProductRequest) (*pc.ChannelStoreProductResponse, error) {
	out := new(pc.ChannelStoreProductResponse)
	out.Code = 400

	glog.Info("QuerySaaSChannelStoreProduct参数：", in)
	session := NewDbConn().Table("eshop.pro_product_store_info").Where("channel_id=?", in.ChannelId)

	if len(in.FinanceCode) != 0 {
		session.In("store_id", in.FinanceCode)
	}

	if len(in.ProductId) != 0 {
		session.In("product_id", in.ProductId)
	}
	if in.UpDownState != -1 {
		session.And("up_down_state = ?", in.UpDownState)
	}

	sessionCount := *session

	out.TotalCount, _ = sessionCount.Count()

	if err := session.Select("`id`, `channel_id`, store_id as finance_code, `product_id`, `up_down_state`").
		Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
		Find(&out.Details); err != nil {
		glog.Error("QueryChannelStoreProduct出错:", err)
		return nil, err
	}

	out.Code = 200
	return out, nil
}

// 查询渠道门店商品（渠道）
func (c *Product) QueryChannelStoreProduct(ctx context.Context, in *pc.ChannelStoreProductRequest) (*pc.ChannelStoreProductResponse, error) {
	out := new(pc.ChannelStoreProductResponse)
	out.Code = 400

	glog.Info("QueryChannelStoreProduct参数：", in)
	session := NewDbConn().Table("channel_store_product").Where("channel_id=?", in.ChannelId)

	if in.OrderBy != "" {
		session.OrderBy(in.OrderBy)
	}

	if len(in.FinanceCode) != 0 {
		session.In("finance_code", in.FinanceCode)
	}

	if len(in.ProductId) != 0 {
		session.In("product_id", in.ProductId)
	}
	if len(in.SkuId) != 0 {
		session.In("sku_id", in.SkuId)
	}
	if in.UpDownState != -1 {
		session.And("up_down_state = ?", in.UpDownState)
		if in.UpDownState == 0 {
			session.And("down_type not in (5,6)")
		}
	}

	sessionCount := *session

	out.TotalCount, _ = sessionCount.Count()

	if err := session.Select("`id`, `channel_id`, `finance_code`, `product_id`, `up_down_state`, `create_date`, `update_date`,`snapshot_id`,`sku_id`").
		Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
		Find(&out.Details); err != nil {
		glog.Error("QueryChannelStoreProduct出错:", err)
		return nil, err
	}

	out.Code = 200
	return out, nil
}

// 查询门店推荐商品
func (c *Product) QueryStoreRecommendProduct(ctx context.Context, in *pc.ChannelStoreProductRequest) (*pc.QueryStoreRecommendProductResponse, error) {
	out := &pc.QueryStoreRecommendProductResponse{}
	out.Data = make([]*pc.StoreRecommendProduct, len(in.FinanceCode))

	cancelCtx, cancel := context.WithCancel(context.Background())
	eg, errCtx := errgroup.WithContext(cancelCtx)

	//控制协程数量
	ch := make(chan int8, runtime.NumCPU())
	for k, financeCode := range in.FinanceCode {
		ch <- 1
		k := k
		financeCode := financeCode
		eg.Go(func() error {
			recommends := &pc.StoreRecommendProduct{
				FinanceCode: financeCode,
			}
			defer func() {
				out.Data[k] = recommends
				<-ch
			}()

			select {
			case <-errCtx.Done():
				return errCtx.Err()
			default:
			}

			model := []models.ChannelStoreProduct{}
			if err := NewDbConn().Select("a.product_id,a.snapshot_id").Table("channel_store_product").Alias("a").
				Join("LEFT", "channel_product b", "a.product_id=b.id and a.channel_id=b.channel_id").
				Where("a.channel_id=?", in.ChannelId).
				And("a.finance_code = ?", financeCode).
				And("a.up_down_state = 1").
				And("a.is_recommend=1").
				Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
				OrderBy("a.update_date desc").
				Find(&model); err != nil {
				//数据库错误取消后续协程
				cancel()
				return status.Error(codes.Internal, GetDBError(err).Error())
			}

			//glog.Info("QueryStoreRecommendProduct：", model)
			snapshotIdSlice := []int32{}
			for _, v := range model {
				snapshotIdSlice = append(snapshotIdSlice, int32(v.SnapshotId))
			}

			if len(snapshotIdSlice) == 0 {
				return nil
			}

			details := []*pc.ChannelProductDetail{}
			if res, err := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
				Ids: snapshotIdSlice,
			}); err != nil {
				glog.Error(err)
			} else if res.Code != 200 {
				glog.Errorf(res.Message)
			} else {
				//按快照顺序排序
				for _, v := range snapshotIdSlice {
					for _, vv := range res.Details {
						if v == vv.Id {
							detail := new(pc.ChannelProductDetail)
							if err := json.Unmarshal([]byte(vv.JsonData), detail); err != nil {
								glog.Error(err)
								continue
							}
							details = append(details, detail)
							break
						}
					}
				}
			}

			recommends.Details = details

			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		glog.Error(err)
		return nil, err
	}
	close(ch)

	return out, nil
}

// 查询门店最新上架商品
func (c *Product) QueryStoreLatestProduct(ctx context.Context, in *pc.ChannelStoreProductRequest) (*pc.QueryStoreRecommendProductResponse, error) {
	out := &pc.QueryStoreRecommendProductResponse{}
	out.Data = make([]*pc.StoreRecommendProduct, len(in.FinanceCode))

	cancelCtx, cancel := context.WithCancel(context.Background())
	eg, errCtx := errgroup.WithContext(cancelCtx)

	//控制协程数量
	ch := make(chan int8, runtime.NumCPU())
	for k, financeCode := range in.FinanceCode {
		ch <- 1
		k := k
		financeCode := financeCode
		eg.Go(func() error {
			recommends := &pc.StoreRecommendProduct{
				FinanceCode: financeCode,
			}
			defer func() {
				out.Data[k] = recommends
				<-ch
			}()

			select {
			case <-errCtx.Done():
				return errCtx.Err()
			default:
			}

			model := []models.ChannelStoreProduct{}

			if err := NewDbConn().Select("a.product_id,a.snapshot_id").Table("channel_store_product").Alias("a").
				Join("LEFT", "channel_product b", "a.product_id=b.id and a.channel_id=b.channel_id").
				Where("a.channel_id=?", in.ChannelId).
				And("a.finance_code = ?", financeCode).
				And("a.up_down_state = 1").
				Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
				OrderBy("a.update_date desc").
				Find(&model); err != nil {
				//数据库错误取消后续协程
				cancel()
				return status.Error(codes.Internal, GetDBError(err).Error())
			}

			snapshotIdSlice := []int32{}
			for _, v := range model {
				snapshotIdSlice = append(snapshotIdSlice, int32(v.SnapshotId))
			}

			if len(snapshotIdSlice) == 0 {
				return nil
			}

			details := []*pc.ChannelProductDetail{}
			if res, err := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
				Ids: snapshotIdSlice,
			}); err != nil {
				glog.Error(err)
			} else if res.Code != 200 {
				glog.Errorf(res.Message)
			} else {
				//按快照顺序排序
				for _, v := range snapshotIdSlice {
					for _, vv := range res.Details {
						if v == vv.Id {
							detail := new(pc.ChannelProductDetail)
							if err := json.Unmarshal([]byte(vv.JsonData), detail); err != nil {
								glog.Error(err)
								continue
							}
							details = append(details, detail)
							break
						}
					}
				}
			}

			recommends.Details = details

			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		glog.Error(err)
		return nil, err
	}
	close(ch)

	return out, nil
}

// 查询宠物saas门店最新上架商品
func (c *Product) QueryEshopStoreLatestProduct(ctx context.Context, in *pc.ChannelStoreProductRequest) (*pc.QueryStoreRecommendProductResponse, error) {
	out := &pc.QueryStoreRecommendProductResponse{}
	out.Data = make([]*pc.StoreRecommendProduct, len(in.FinanceCode))

	cancelCtx, cancel := context.WithCancel(context.Background())
	eg, errCtx := errgroup.WithContext(cancelCtx)

	//控制协程数量
	ch := make(chan int8, runtime.NumCPU())
	for k, financeCode := range in.FinanceCode {
		ch <- 1
		k := k
		financeCode := financeCode
		eg.Go(func() error {
			recommends := &pc.StoreRecommendProduct{
				FinanceCode: financeCode,
			}
			defer func() {
				out.Data[k] = recommends
				<-ch
			}()

			select {
			case <-errCtx.Done():
				return errCtx.Err()
			default:
			}

			productIds := make([]int32, 0)

			if err := NewDbConn().Select("a.id as product_id").Table("eshop.pro_product").Alias("a").
				Join("inner", "eshop.pro_product_store_info b", "a.id=b.product_id").
				Where("b.channel_id=?", in.ChannelId).
				And("b.store_id = ?", financeCode).
				And("b.up_down_state = 1").
				Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
				OrderBy("b.update_date desc").
				Find(&productIds); err != nil {
				//数据库错误取消后续协程
				cancel()
				return status.Error(codes.Internal, GetDBError(err).Error())
			}

			//获取商品信息
			productDetail, err := c.GetEshopProductSnapshotBySpuOrSku(ctx, &pc.GetChannelProductSnapshotBySpuOrSkuRequest{ChannelId: in.ChannelId, FinanceCode: financeCode, ProductId: productIds})
			if err != nil {
				glog.Error("查询宠物saas门店最新上架商品失败，err=", err.Error())
			}
			if productDetail.Code != 200 {
				glog.Error("查询宠物saas门店最新上架商品失败；err=", productDetail.Error, "message=", productDetail.Message)
			}
			details := []*pc.ChannelProductDetail{}
			for _, id := range productIds {
				for _, v := range productDetail.Details {
					if v.Product.Id == id {
						details = append(details, v)
					}
				}
			}

			recommends.Details = details

			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		glog.Error(err)
		return nil, err
	}
	close(ch)

	return out, nil
}

// 更新快照
func (c *Product) UpdateSnapShot(ctx context.Context, in *pc.UpdateSnapShotRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{
		Code: 400,
	}

	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return out, nil
	}

	clientData := GetDataCenterClient()
	defer clientData.Close()

	res, err := clientData.RPC.QueryStoreUserAuthority(ctx, &dac.StoreUserAuthorityRequest{
		FinanceCode: in.FinanceCode,
	})
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}
	if len(res.Details) == 0 {
		out.Message = "用户不存在"
		return out, nil
	}

	//循环当前账号的门店信息列表
	for _, v := range res.Details {
		//1、查出当前门店和渠道、商品的快照信息
		//2、判断商品是否上架，如果已上架，则不获取价格信息(价格是根据门店不同而不同)
		//3、更新快照信息
		for _, productId := range in.ProductId {
			//获取快照
			res, _ := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
				ChannelId:   in.ChannelId,
				UserNo:      userInfo.UserNo,
				ProductId:   []int32{productId},
				FinanceCode: v.FinanceCode,
			})
			if len(res.Details) > 0 {
				//先创建
				snapShot := res.Details[0]
				//todo 根据商品id和门店修改价格
				_, err := c.NewChannelProductSnapshot(clientData.Ctx, &pc.ChannelProductSnapshot{
					ChannelId:   snapShot.ChannelId,
					UserNo:      snapShot.UserNo,
					ProductId:   snapShot.ProductId,
					JsonData:    snapShot.JsonData,
					FinanceCode: snapShot.FinanceCode,
				})
				if err != nil {
					out.Message = err.Error()
					return out, nil
				}
				//再查询
				model := models.ChannelProductSnapshot{}
				if _, err = NewDbConn().
					SQL("SELECT id,channel_id,user_no,finance_code,product_id,json_data,create_date FROM dc_product.channel_product_snapshot WHERE finance_code = ? AND product_id = ?;", v.FinanceCode, productId).
					Get(&model); err != nil {
					glog.Error(err)
					continue
				}
				if model.Id == 0 {
					continue
				}

				//处理更新渠道商品分类和推荐字段无法更新到小程序问题
				var newSnap = pc.ChannelProductRequest{
					Product:     &pc.ChannelProduct{},
					ProductAttr: nil,
				}
				err = json.Unmarshal([]byte(snapShot.JsonData), &newSnap)
				if err != nil {
					glog.Error("NewChannelStoreProductTask方法查询快照信息失败,", err)
					continue
				}

				if _, err := NewDbConn().Where("channel_id=? and finance_code=? and product_id=?", in.ChannelId, v.FinanceCode, productId).Cols("channel_category_id,is_recommend,snapshot_id").Update(&models.ChannelStoreProduct{
					SnapshotId:        model.Id,
					ChannelCategoryId: int(newSnap.Product.ChannelCategoryId),
					IsRecommend:       int(newSnap.Product.IsRecommend),
				}); err != nil {
					glog.Error(err)
					continue
				}
			}
		}
	}

	out.Code = 200
	return out, nil
}

// 更新快照[v3.1]
func (c *Product) UpdateSnapShotNew(ctx context.Context, in *pc.UpdateSnapShotRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	clientData := GetDataCenterClient()
	defer clientData.Close()

	//循环当前账号的门店信息列表
	for _, v := range in.FinanceCode {
		//1、查出当前门店和渠道、商品的快照信息
		//2、判断商品是否上架，如果已上架，则不获取价格信息(价格是根据门店不同而不同)
		//3、更新快照信息
		for _, productId := range in.ProductId {
			//获取快照
			res, _ := c.QueryChannelProductSnapshot(ctx, &pc.ChannelProductSnapshotRequest{
				ChannelId:   in.ChannelId,
				ProductId:   []int32{productId},
				FinanceCode: v,
			})
			if len(res.Details) > 0 {
				//先创建
				snapShot := res.Details[0]
				//todo 根据商品id和门店修改价格
				_, err := c.NewChannelProductSnapshot(ctx, &pc.ChannelProductSnapshot{
					ChannelId:   snapShot.ChannelId,
					UserNo:      snapShot.UserNo,
					ProductId:   snapShot.ProductId,
					JsonData:    snapShot.JsonData,
					FinanceCode: snapShot.FinanceCode,
				})
				if err != nil {
					out.Message = err.Error()
					return out, nil
				}
				//再查询
				model := models.ChannelProductSnapshot{}
				if _, err = NewDbConn().
					SQL("SELECT id,channel_id,user_no,finance_code,product_id,json_data,create_date FROM dc_product.channel_product_snapshot WHERE channel_id=? and finance_code = ? AND product_id = ?;", in.ChannelId, v, productId).
					Get(&model); err != nil {
					glog.Error(err)
					continue
				}
				if model.Id == 0 {
					continue
				}

				//处理更新渠道商品分类和推荐字段无法更新到小程序问题
				var newSnap = pc.ChannelProductRequest{
					Product:     &pc.ChannelProduct{},
					ProductAttr: nil,
				}
				err = json.Unmarshal([]byte(snapShot.JsonData), &newSnap)
				if err != nil {
					glog.Error("NewChannelStoreProductTask方法查询快照信息失败,", err)
					continue
				}

				if _, err := NewDbConn().Where("channel_id=? and finance_code=? and product_id=?", in.ChannelId, v, productId).Cols("channel_category_id,is_recommend,snapshot_id").Update(&models.ChannelStoreProduct{
					SnapshotId:        model.Id,
					ChannelCategoryId: int(newSnap.Product.ChannelCategoryId),
					IsRecommend:       int(newSnap.Product.IsRecommend),
				}); err != nil {
					glog.Error(err)
					continue
				}
			}

			getProductInfo := c.NewGetChannelProductSnapshot([]int32{cast.ToInt32(productId)}, cast.ToInt(in.ChannelId), v)
			if len(getProductInfo) > 0 {
				ProductInfo := getProductInfo[0]
				var channelProductSnap pc.ChannelProductRequest
				ProductInfo.Product.ChannelId = fmt.Sprintf("%d", in.ChannelId)
				channelProductSnap.Product = ProductInfo.Product
				channelProductSnap.FinanceCode = v
				channelProductSnap.SkuInfo = ProductInfo.SkuInfo
				channelProductSnap.ProductAttr = ProductInfo.ProductAttr
				c.UpdatePriceCommonBatch(context.Background(), in.FinanceCode, &channelProductSnap)
			}
		}
	}
	out.Code = 200
	return out, nil
}

// 新增门店关联的商品
func (c *Product) NewChannelStoreProduct(ctx context.Context, in *pc.NewChannelStoreProductRequest) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	if len(in.Info) == 0 {
		return out, errors.New("无任何更新信息")
	}

	if len(in.Info) >= 10 {
		//分块推送
		blockSize := 10
		blockNum := int(math.Ceil(float64(len(in.Info)) / float64(blockSize)))

		for i := 0; i < blockNum; i++ {
			low := blockSize * i
			if low > len(in.Info) {
				break
			}
			high := blockSize * (i + 1)
			if high > len(in.Info) {
				high = len(in.Info)
			}

			//推送到MQ
			bt, _ := json.Marshal(in.Info[low:high])
			m := mqgo.SyncMqInfo{
				Exchange: DatacenterExchange,
				Queue:    SaveChannelStoreProductQueue,
				RouteKey: SaveChannelStoreProductQueue,
				Request:  string(bt),
			}
			if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
				glog.Error(err)
				out.Message = err.Error()
				return out, nil
			}
		}
	} else {
		NewChannelStoreProductTask(in.Info)
	}

	out.Code = 200
	return out, nil
}

// 根据商品id分组查询权限范围内的渠道门店商品上架门店的数量（渠道）
func (c *Product) QueryChannelStoreProductGroupByFinanceCodeCount(ctx context.Context, in *pc.ChannelProductGroupByFinanceCodeCountRequest) (*pc.ChannelStoreProductResponse, error) {
	out := new(pc.ChannelStoreProductResponse)
	out.Code = 400
	session := NewDbConn().Table("channel_store_product").Select("product_id,GROUP_CONCAT(finance_code) as finance_code").GroupBy("product_id").Where("channel_id = ?", in.ChannelId)

	if len(in.ProductId) > 0 {
		session.In("product_id", in.ProductId)
	}

	if len(in.FinanceCode) > 0 {
		session.In("finance_code", in.FinanceCode)
	}

	if err := session.Find(&out.Details); err != nil {
		glog.Error(err)
		return nil, err
	}

	out.Code = 200
	return out, nil
}

// 新增一个商品快照（渠道） 如果已存在，则编辑
func (c *Product) NewChannelProductSnapshot(ctx context.Context, in *pc.ChannelProductSnapshot) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	//用户校验
	var financialCode string
	if len(in.FinanceCode) <= 0 {
		user := loadLoginUserInfo(ctx)
		glog.Info("user:", user)
		if user == nil {
			out.Message = "用户不存在"
			return out, nil
		}
		in.UserNo = user.UserNo

		financialCode = in.FinanceCode
		if financialCode == "" {
			financialCode = user.FinancialCode
		}
	} else {
		financialCode = in.FinanceCode
	}

	if financialCode == "" {
		out.Message = "快照门店信息不能为空"
		return out, nil
	}

	if in.JsonData == "" {
		out.Message = "快照内容为空"
		return out, nil
	}

	product := new(Product)
	resp, err := product.GetChannelWarehouses([]string{financialCode}, cast.ToInt32(in.ChannelId))
	if err != nil {
		glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
		out.Message = "查询仓库绑定关系异常" + err.Error()
		return out, nil
	}
	//if len(resp) <= 0 && cast.ToInt32(in.ChannelId) == ChannelAwenId { // 如果是阿闻渠道并且查询阿闻外卖没有查询到关联关系则查询竖屏自提
	//	resp, err = product.GetChannelWarehouses([]string{financialCode}, ChannelAwenPickUpId)
	//	if err != nil {
	//		out.Message = "查询阿闻外卖自提仓库绑定关系异常" + err.Error()
	//		return out, nil
	//	}
	//}
	if len(resp) <= 0 {
		out.Message = "未查询到渠道仓库绑定信息"
		return out, nil
	}
	var tempSnap pc.ChannelProductRequest
	err = json.Unmarshal([]byte(in.JsonData), &tempSnap)
	if err != nil {
		glog.Error(utils.RunFuncName()+"，json解析失败，", err, "，json：", in.JsonData)
		return out, err
	}
	var skuGroup []models.ChannelSkuGroup
	err = NewDbConn().Where("product_id = ?", in.ProductId).Find(&skuGroup)
	if err != nil {

	}
	glog.Info("test in: ", kit.JsonEncode(in))
	switch resp[0].Category {
	case 3:
		for _, vSku := range tempSnap.SkuInfo {
			vSku.MarketPrice = vSku.StorePrice
		}
	case 4, 5:
		for _, vSku := range tempSnap.SkuInfo {
			vSku.MarketPrice = vSku.PreposePrice
		}
	default:
		for _, vSku := range tempSnap.SkuInfo {
			vSku.MarketPrice = vSku.StorePrice
		}
	}
	NewJsonData, err := json.Marshal(tempSnap)
	if err != nil {
		glog.Error(err)
		return out, err
	}

	var model models.ChannelProductSnapshot
	//如果已存在，则编辑
	_, err = NewDbConn().SQL("SELECT id,channel_id,user_no,finance_code,product_id,json_data,create_date FROM "+
		"dc_product.channel_product_snapshot WHERE finance_code = ? AND product_id = ? AND channel_id=?;",
		financialCode, in.ProductId, in.ChannelId).Get(&model)

	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	if model.Id > 0 {
		if _, err := NewDbConn().Id(model.Id).Update(&models.ChannelProductSnapshot{
			ChannelId:   int(in.ChannelId),
			UserNo:      in.UserNo,
			FinanceCode: financialCode,
			ProductId:   in.ProductId,
			JsonData:    string(NewJsonData),
		}); err != nil {
			glog.Error(err)
			return nil, err
		}
		out.Details = append(out.Details, cast.ToString(model.Id))
	} else {
		model := &models.ChannelProductSnapshot{
			ChannelId:   int(in.ChannelId),
			UserNo:      in.UserNo,
			FinanceCode: financialCode,
			ProductId:   in.ProductId,
			JsonData:    string(NewJsonData),
		}
		if _, err := NewDbConn().Insert(model); err != nil {
			glog.Error(err)
			return nil, err
		} else {
			out.Details = append(out.Details, cast.ToString(model.Id))
		}
	}

	//todo 编辑的时候，对channel_store_product表的分类进行修改
	var channel_store_product models.ChannelStoreProduct
	if _, err = NewDbConn().Where("product_id=?", in.ProductId).And("channel_id=?", in.ChannelId).
		And("finance_code=?", financialCode).Get(&channel_store_product); err != nil {
		out.Message = err.Error()
		return out, nil
	}
	if channel_store_product.Id > 0 {
		channel_store_product.ChannelCategoryName = tempSnap.Product.ChannelCategoryName
		channel_store_product.Name = tempSnap.Product.Name
		channel_store_product.ChannelCategoryId = int(tempSnap.Product.ChannelCategoryId)
		channel_store_product.IsRecommend = int(tempSnap.Product.IsRecommend)
		_, err := NewDbConn().Id(channel_store_product.Id).Cols("channel_category_name,channel_category_id,is_recommend,name").Update(&channel_store_product)
		glog.Info("对channel_store_product的推荐商品修改： ", kit.JsonEncode(channel_store_product))
		if err != nil {
			glog.Error("修改channel_store_product失败，err:", err)
		}
	}
	out.Code = 200
	return out, nil
}

// 批量新增一个商品到多门店快照（渠道） 如果已存在，则编辑,重载上面方法，不需要单独查仓库类型
func newChannelProductSnapshot(ctx context.Context, in *pc.ChannelProductSnapshot, wareCate int32) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400

	//用户校验
	user := loadLoginUserInfo(ctx)
	if user == nil {
		out.Message = "用户不存在"
		return out, nil
	}
	in.UserNo = user.UserNo

	financialCode := in.FinanceCode
	if financialCode == "" {
		financialCode = user.FinancialCode
	}

	if financialCode == "" {
		out.Message = "快照门店信息不能为空"
		return out, nil
	}

	if in.JsonData == "" {
		out.Message = "快照内容为空"
		return out, nil
	}

	var tempSnap pc.ChannelProductRequest
	err := json.Unmarshal([]byte(in.JsonData), &tempSnap)
	if err != nil {
		glog.Error(utils.RunFuncName()+"，json解析失败，", err, "，json：", in.JsonData)
		return out, err
	}
	switch wareCate {
	case 3:
		//tempSnap.SkuInfo[0].MarketPrice = tempSnap.SkuInfo[0].StorePrice
		for _, vSku := range tempSnap.SkuInfo {
			vSku.MarketPrice = vSku.StorePrice
		}
	case 4, 5:
		//tempSnap.SkuInfo[0].MarketPrice = tempSnap.SkuInfo[0].PreposePrice
		for _, vSku := range tempSnap.SkuInfo {
			vSku.MarketPrice = vSku.PreposePrice
		}
	default:
		//tempSnap.SkuInfo[0].MarketPrice = tempSnap.SkuInfo[0].StorePrice
		for _, vSku := range tempSnap.SkuInfo {
			vSku.MarketPrice = vSku.StorePrice
		}
	}
	NewJsonData, err := json.Marshal(tempSnap)
	if err != nil {
		glog.Error(err)
		return out, err
	}

	var model models.ChannelProductSnapshot
	//如果已存在，则编辑
	_, err = NewDbConn().SQL("SELECT id,channel_id,user_no,finance_code,product_id,json_data,create_date FROM dc_product.channel_product_snapshot WHERE finance_code = ? AND product_id = ? AND channel_id=?;", financialCode, in.ProductId, in.ChannelId).Get(&model)

	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	if model.Id > 0 {
		if _, err := NewDbConn().Id(model.Id).Update(&models.ChannelProductSnapshot{
			ChannelId:   int(in.ChannelId),
			UserNo:      in.UserNo,
			FinanceCode: financialCode,
			ProductId:   in.ProductId,
			JsonData:    string(NewJsonData),
		}); err != nil {
			//glog.Error(err)
			return nil, err
		}
		out.Details = append(out.Details, cast.ToString(model.Id))
	} else {
		model := &models.ChannelProductSnapshot{
			ChannelId:   int(in.ChannelId),
			UserNo:      in.UserNo,
			FinanceCode: financialCode,
			ProductId:   in.ProductId,
			JsonData:    string(NewJsonData),
		}
		if _, err := NewDbConn().Insert(model); err != nil {
			//glog.Error(err)
			return nil, err
		} else {
			out.Details = append(out.Details, cast.ToString(model.Id))
		}
	}

	//todo 编辑的时候，对channel_store_product表的分类进行修改
	var channel_store_product models.ChannelStoreProduct
	_, err = NewDbConn().Where("product_id=?", in.ProductId).And("channel_id=?", in.ChannelId).And("finance_code=?", financialCode).Get(&channel_store_product)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	if channel_store_product.Id > 0 {
		channel_store_product.ChannelCategoryName = tempSnap.Product.ChannelCategoryName
		channel_store_product.ChannelCategoryId = int(tempSnap.Product.ChannelCategoryId)
		_, err := NewDbConn().Id(channel_store_product.Id).Update(&channel_store_product)
		if err != nil {
			glog.Error("修改channel_store_product失败，err:", err)
		}
	}

	out.Code = 200
	return out, nil
}

// 京东到家新增渠道  //todo 周翔整理无用方法，测试后删除
func (c *Product) JddjNewChannelProductSnapshot(ctx context.Context, in *pc.ChannelProductSnapshot) (*pc.BaseResponse, error) {
	out := new(pc.BaseResponse)
	out.Code = 400
	//
	//if in.JsonData == "" {
	//	out.Message = "快照内容为空"
	//	return out, nil
	//}
	//
	//var model models.ChannelProductSnapshot
	////如果已存在，则编辑
	//_, err := NewDbConn().SQL("SELECT id,channel_id,user_no,finance_code,product_id,json_data,create_date FROM dc_product.channel_product_snapshot WHERE finance_code = ? AND product_id = ? AND channel_id=?;", in.FinanceCode, in.ProductId, in.ChannelId).Get(&model)
	//
	//if err != nil {
	//	out.Message = err.Error()
	//	return out, nil
	//}
	//if model.Id > 0 {
	//	if _, err := NewDbConn().Id(model.Id).Update(&models.ChannelProductSnapshot{
	//		ChannelId:   int(in.ChannelId),
	//		UserNo:      in.UserNo,
	//		FinanceCode: in.FinanceCode,
	//		ProductId:   in.ProductId,
	//		JsonData:    in.JsonData,
	//	}); err != nil {
	//		glog.Error(err)
	//		return nil, err
	//	}
	//	out.Details = append(out.Details, cast.ToString(model.Id))
	//} else {
	//	model := &models.ChannelProductSnapshot{
	//		ChannelId:   int(in.ChannelId),
	//		UserNo:      in.UserNo,
	//		FinanceCode: in.FinanceCode,
	//		ProductId:   in.ProductId,
	//		JsonData:    in.JsonData,
	//	}
	//	if _, err := NewDbConn().Insert(model); err != nil {
	//		glog.Error(err)
	//		return nil, err
	//	} else {
	//		out.Details = append(out.Details, cast.ToString(model.Id))
	//	}
	//}
	//
	//out.Code = 200
	out.Error = "周翔整理无用方法"
	return out, nil
}

// 查询渠道商品编辑快照
func (c *Product) QueryChannelProductSnapshot(ctx context.Context, in *pc.ChannelProductSnapshotRequest) (*pc.ChannelProductSnapshotResponse, error) {

	out := new(pc.ChannelProductSnapshotResponse)
	out.Code = 400
	session := NewDbConn().Where("1=1")
	var ids []int32
	//根据id查快照
	if in.Id > 0 {
		ids = append(ids, in.Id)
	} else if len(in.Ids) > 0 {
		ids = append(ids, in.Ids...)
	} else {
		if in.FinanceCode == "" {
			userInfo := loadLoginUserInfo(ctx)
			if userInfo == nil {
				out.Message = "用户不存在"
				return out, nil
			}

			if userInfo.FinancialCode != "" {
				in.FinanceCode = userInfo.FinancialCode
			}
		}

		if in.FinanceCode != "" {
			session.And("finance_code = ?", in.FinanceCode)
		} else {
			out.Message = "门店编码不能为空"
			return out, nil
		}

		if in.ChannelId > 0 {
			session.And("channel_id = ?", in.ChannelId)
		}

		if len(in.SkuId) > 0 && len(in.ProductId) == 0 {
			if err := NewDbConn().Table("dc_product.sku").In("id", in.SkuId).Select("Distinct product_id").Find(&in.ProductId); err != nil {
				glog.Info("QueryChannelProductSnapshot" + err.Error())
				out.Message = err.Error()
				return out, err
			} else if len(in.ProductId) == 0 {
				return out, errors.New(fmt.Sprintf("%v 查询不到商品信息", in.SkuId))
			}
		}

		if len(in.ProductId) > 0 {
			session.In("product_id", in.ProductId)
		}

		var res []*pc.ChannelProductSnapshot
		if err := session.Select("id").Find(&res); err != nil {
			glog.Error(err)
			out.Message = err.Error()
			return out, err
		}
		for _, v := range res {
			ids = append(ids, v.Id)
		}
	}
	if err := session.Select("`id`, `channel_id`, `user_no`, `finance_code`,`product_id`, `json_data`, `create_date`,`product_third_id`").
		In("id", ids).Find(&out.Details); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, err
	}
	//判断是否是总账号标识或多账号问题
	if len(out.Details) > 0 {
		userInfo := loadLoginUserInfo(ctx)
		if userInfo != nil {
			if userInfo.FinancialCode == "" && in.FinanceCode == "" {
				out.Details = []*pc.ChannelProductSnapshot{}
			}
		}
	}
	// productlist:=make([]int32,0)

	if len(out.Details) != len(in.ProductId) {
		for _, id := range in.ProductId {
			ishave := false
			for _, x := range out.Details {
				if x.ProductId == id {
					ishave = true
					break
				}
			}
			if !ishave {
				var newSnap pc.ChannelProductRequest
				req := &pc.OneofIdRequest{ChannelId: in.ChannelId, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{id}}}}

				//查询商品主库信息
				if res, err := c.QueryChannelProductOnly(ctx, req); err != nil {
					out.Message = err.Error()
					return out, err
				} else if len(res.Details) == 0 {
					out.Message = cast.ToString(id) + "，查询不到商品信息"
					return out, errors.New(cast.ToString(id) + "，查询不到商品信息")
				} else {
					newSnap.Product = res.Details[0]
				}

				//查询商品属性
				if res, err := c.QueryChannelProductAttr(ctx, req); err != nil {
					out.Message = err.Error()
					return out, err
				} else {
					newSnap.ProductAttr = res.Details
				}

				//查询商品SKU
				var sku []*pc.Sku
				var skuValue []*pc.SkuValue
				var skuThird []*pc.SkuThird
				var skuGroup []*pc.SkuGroup
				if res, err := c.QueryChannelSku(ctx, req); err != nil {
					out.Message = err.Error()
					return out, err
				} else {
					sku = res.Details
					for _, v := range res.Details {
						skuValue = append(skuValue, v.SkuValue...)
						skuThird = append(skuThird, v.SkuThird...)
						skuGroup = append(skuGroup, v.SkuGroup...)
					}
				}

				for _, s := range sku {
					skuInfo := &pc.SkuInfo{
						RetailPrice:   s.RetailPrice,
						SkuId:         s.Id,
						ProductId:     s.ProductId,
						MarketPrice:   s.MarketPrice,
						BarCode:       s.BarCode,
						ChannelId:     s.ChannelId,
						IsUse:         s.IsUse,
						WeightForUnit: s.WeightForUnit,
						WeightUnit:    s.WeightUnit,
						MinOrderCount: s.MinOrderCount,
						PriceUnit:     s.PriceUnit,
						PreposePrice:  s.PreposePrice,
						StorePrice:    s.StorePrice,
					}
					//第三方货号
					for _, t := range skuThird {
						if t.SkuId == s.Id {
							skuInfo.SkuThird = append(skuInfo.SkuThird, t)
						}
					}

					//sku value
					for _, v := range skuValue {
						if v.SkuId == s.Id {
							skuInfo.Skuv = append(skuInfo.Skuv, v)
						}
					}

					for _, g := range skuGroup {
						if g.SkuId == s.Id {
							skuInfo.SkuGroup = append(skuInfo.SkuGroup, g)
						}
					}

					newSnap.SkuInfo = append(newSnap.SkuInfo, skuInfo)
				}
				bt, _ := json.Marshal(newSnap)
				channelProductSnapshot := pc.ChannelProductSnapshot{
					Id:          0,
					ChannelId:   in.ChannelId,
					ProductId:   id,
					JsonData:    string(bt),
					FinanceCode: in.FinanceCode,
				}
				out.Details = append(out.Details, &channelProductSnapshot)
			}
		}
	}
	out.Code = 200
	return out, nil
}

// 根据商品id和渠道id获取该商品在哪些门店上架的信息(集合)
func (c *Product) QueryStoreRecommendProductOnstatus(ctx context.Context, in *pc.QueryStoreRecommendProductOnstatusRequest) (*pc.QueryStoreRecommendProductOnstatusResponse, error) {
	out := new(pc.QueryStoreRecommendProductOnstatusResponse)
	out.Code = 400
	var list []models.ChannelStoreProduct
	NewDbConn().Where("channel_id=?", in.ChannelId).And("product_id=?", in.ProductId).And("up_down_state=1").Find(&list)
	for _, v := range list {
		var model pc.ChannelStoreProductModel
		model.Id = int32(v.Id)
		model.ChannelId = int32(v.ChannelId)
		model.FinanceCode = v.FinanceCode
		model.ProductId = int32(v.ProductId)
		model.ChannelCategoryId = int32(v.ChannelCategoryId)
		model.IsRecommend = int32(v.IsRecommend)
		model.UpDownState = int32(v.UpDownState)
		model.SnapshotId = int32(v.SnapshotId)
		model.CreateDate = cast.ToString(v.CreateDate)
		model.UpdateDate = cast.ToString(v.UpdateDate)
		out.Details = append(out.Details, &model)
	}
	return out, nil
}

// 下单增加门店商品销售量
func (c *Product) UpdateStoreProductSalesVolume(ctx context.Context, in *pc.UpdateSalesVolumeRequest) (*pc.BaseResponse, error) {
	conn := NewDbConn()

	out := &pc.BaseResponse{
		Code:    200,
		Message: "ok",
	}
	for _, v := range in.ProductList {
		if _, err := conn.Exec("update channel_store_product set sales_volume = sales_volume + ? where channel_id = ? and finance_code = ? and product_id = ?",
			v.Num, in.ChannelId, in.ShopId, v.ProductId); err != nil {
			glog.Errorf("更新s2b2c门店商品销量失败, 门店：%s, 商品：%d, 数量：%d", in.ShopId, v.ProductId, v.Num)
		}
	}
	return out, nil
}

/*******************************************  私有方法分割线  *******************************************/

func GetSkuIDByPID(engine *xorm.Engine, pid []int32) map[int32]int32 {
	type SkuIDRow struct {
		Product_id int32 `xorm:""`
		Id         int32 `xorm:""`
	}
	rows := make([]SkuIDRow, 0)
	tempMap := make(map[int32]int32)
	strParam := ""
	for k, v := range pid {
		if k < len(pid)-1 {
			strParam += cast.ToString(v) + ","
		} else {
			strParam += cast.ToString(v)
		}
	}
	fmt.Println(strParam)
	engine.Table("gj_sku").Select("product_id,id").Where("product_id in (" + strParam + ")").Find(&rows)
	//fmt.Println(rows)
	for _, v := range rows {
		tempMap[v.Product_id] = v.Id
	}
	return tempMap
}
func GetNameByPID(engine *xorm.Engine, pid []int32) map[int32]string {
	type NameRow struct {
		Id   int32  `xorm:""`
		Name string `xorm:""`
	}
	rows := make([]NameRow, 0)
	tempMap := make(map[int32]string)
	strParam := ""
	for k, v := range pid {
		if k < len(pid)-1 {
			strParam += cast.ToString(v) + ","
		} else {
			strParam += cast.ToString(v)
		}
	}
	fmt.Println(strParam)
	engine.SQL("select id,name from gj_product where id in (" + strParam + ") ").Find(&rows)
	for _, v := range rows {
		tempMap[v.Id] = v.Name
	}
	fmt.Println(rows)
	return tempMap
}

// 获取gj第三方分类
func GetThirdCategory(productStr string) (map[string]string, error) {

	glog.Info("GetThirdCategory参数： ", productStr)

	var out = make(map[string]string, 0)
	conn := NewDbConn()

	channels := make([]models.GjProductChannel, 0)
	sql := "select * from dc_product.gj_product_channel where product_id IN (" + productStr + ")"
	err := conn.SQL(sql).Find(&channels)
	if err != nil {
		glog.Error("查询gj的第三方分类id异常")
	}

	for _, e := range channels {
		product := e
		str := strconv.Itoa(int(product.ProductId)) + ":" + strconv.Itoa(int(product.ChannelId))
		out[str] = strconv.Itoa(int(product.CategoryId))
	}
	glog.Info(kit.JsonEncode(out))
	return out, nil

}

// 同步gj的分类id和属性到渠道表
func updateThirdCategoryAndAttr(tmpProductId []int32, channel_id int32, outMap map[string]string) error {

	// 所有符合商品id
	productIdSlice := make([]string, len(tmpProductId))
	for k, v := range tmpProductId {
		productIdSlice[k] = cast.ToString(v)
	}
	productIdStr := strings.Join(productIdSlice, ",")

	glog.Info("updateThirdCategoryAndAttr参数 : ", kit.JsonEncode(tmpProductId), kit.JsonEncode(outMap), channel_id)
	conn := NewDbConn().NewSession()
	defer conn.Close()
	for _, v := range tmpProductId {
		producId := v
		sql := "update dc_product.channel_product set channel_tag_id = ? where id=? and channel_id = ?"
		category, ok := outMap[strconv.Itoa(int(producId))+":"+strconv.Itoa(int(channel_id))]
		if ok {
			_, err := conn.Exec(sql, category, producId, channel_id)
			if err != nil {
				glog.Error("更新gj的第三方分类id异常", producId, channel_id)
			}
		}
	}
	// 将gj的属性同步到渠道中只有渠道是美团的才需要同步
	if channel_id == ChannelMtId {
		// 先删除在新增
		if _, err := conn.Where("channel_id=?", channel_id).In("product_id", tmpProductId).Delete(models.ChannelProductAttr{}); err != nil {
			conn.Rollback()
			glog.Error("渠道属性数据库删除失败，", err.Error())

		}
		sql := "insert into dc_product.channel_product_attr (product_id, channel_id, attr_id, attr_name,attr_value_id, attr_value) " +
			"select product_id, channel_id, attr_id,attr_name, attr_value_id, attr_value from dc_product.gj_product_channel_attr " +
			"where product_id in (" + productIdStr + ")" + "and channel_id = " + cast.ToString(channel_id)
		if _, err := conn.Exec(sql); err != nil {
			conn.Rollback()
			glog.Error("渠道属性数据库添加失败，", err.Error())

		}
	}

	return nil
}

// 剔除主表中没有或者新增加的字段
func SplitUpdateFields(updateFields string) (mainUpdateFields, otherFields string, err error) {
	// 美团分类、饿了么分类、京东分类
	otherFieldsData := "mt_id,jd_id,ele_id,custom_area"
	mainUpdateFieldsSlice := make([]string, 0)
	otherFieldsSlice := make([]string, 0)
	if len(updateFields) > 0 {
		split := strings.Split(updateFields, ",")
		for _, v := range split {
			st := v
			if strings.Contains(otherFieldsData, st) {
				otherFieldsSlice = append(otherFieldsSlice, st)
			} else {
				mainUpdateFieldsSlice = append(mainUpdateFieldsSlice, st)
			}
		}
	}
	// 如果有有效期则需要同步修改term_type字段
	if strings.Contains(updateFields, "term_value") {
		mainUpdateFieldsSlice = append(mainUpdateFieldsSlice, "term_type")
	}
	return strings.Join(mainUpdateFieldsSlice, ","), strings.Join(otherFieldsSlice, ","), nil
}

// 更新管家的第三方id到渠道
func UpdateGjCategoryChannel(tmpProductId []int32, cid int32) {
	conn := NewDbConn()
	channel := models.GjProductChannel{}
	conn.SQL("select * from gj_product_channel where product_id=? and channel_id = ?  ", tmpProductId[0], cid).Get(&channel)
	product := models.ChannelProduct{
		ChannelTagId: channel.CategoryId,
	}
	_, err := conn.Where("id = ? and channel_id = ? ", tmpProductId[0], cid).Cols("channel_tag_id").Update(&product)
	if err != nil {
		glog.Error("更新管家的第三方id到渠道失败", err.Error())
	}
}

// 管家批量认领商品,以二维切片形式输出错误信息
func copyGjProductToChannelProduct(ctx context.Context, productId, channelId []int32, updateFields string, isBatchCopy bool) ([][]string, error) {
	inSet := utils.NewSet(productId...)
	out := make([][]string, 0)
	engine := NewDbConn()
	var (
		passPID  []int32
		failPID0 []int32 // 没有货号的商品
		failPID1 []int32 // 商品名称或商品图片为空的商品
		failPID2 []int32 // 价格或重量为空的商品
		failPID3 []int32 // 无阿闻分类的商品
		failPID4 []int32 // 过期的虚拟商品
		failPID5 []int32 // 明细存在虚拟商品过期的组合商品
		//failPID6 []int32 // 已停用或只适用于电商的组合商品
		failPID7 []int32 // 认领到第三方平台的时候不存在第三方分类
		//failPID8 []int32 // 互联网医疗商品限制认领
		err      error
		funcName = utils.RunFuncName()
	)
	_updateFields := updateFields //需要更新的字段数据

	//获取没有子龙或A8货号的商品
	successPID := make([]int32, 0)
	err = engine.Table("gj_sku_third").Select("product_id").
		In("erp_id", []int{2, 4}).
		In("product_id", productId).
		GroupBy("product_id").
		Find(&successPID)
	successSet := utils.NewSet(successPID...)
	failPID0 = inSet.Minus(successSet).List()
	if err != nil {
		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		glog.Error(err)
		return out, err
	}
	glog.Info(funcName, "，批量认领，没有货号的商品为，", failPID0)

	SkuMap := GetSkuIDByPID(engine, productId)
	NameMap := GetNameByPID(engine, productId)
	for _, v := range failPID0 {
		row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "货号为空"}
		out = append(out, row)
	}

	//获取商品名称、商品图片为空的商品
	err = engine.Table("gj_product").Select("id").
		Where("category_id=0 or category_id is null").
		Or("name = '' or name is null").
		Or("pic = '' or pic is null").
		In("id", productId).
		Find(&failPID1)
	if err != nil {
		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		glog.Error(err)
		return out, err
	}
	glog.Info(funcName, "，批量认领，商品信息不符合的商品为，", failPID1)
	for _, v := range failPID1 {
		row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "商品名称或商品图片为空"}
		out = append(out, row)
	}
	//获取价格、重量为空的商品
	err = engine.Select("product_id").Table("gj_sku").Alias("a").
		Join("inner", "gj_product b", "a.product_id = b.id").
		Where("((a.weight_for_unit=0 or a.weight_for_unit is null) and b.product_type != 2) or (a.prepose_price=0 or a.store_price=0 or a.prepose_price is null or a.store_price is null)").
		In("a.product_id", productId).
		Find(&failPID2)
	if err != nil {
		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		glog.Error(err)
		return out, err
	}

	glog.Info(funcName, "，批量认领，商品价格、重量不符合的商品为，", failPID2)
	for _, v := range failPID2 {
		row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "价格或重量为空"}
		out = append(out, row)
	}

	//查询所有过期的虚拟商品
	err = engine.Select("id").Table("gj_product").
		Where("(product_type=2) AND term_type=1 AND  FROM_UNIXTIME(term_value)<NOW()").In("id", productId).
		//err = engine.SQL("select id from gj_product where (product_type=2) AND term_type=1 AND  FROM_UNIXTIME(term_value)<NOW()").In("id",productId).
		Find(&failPID4)
	if err != nil {
		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		glog.Error(err)
		return out, err
	}
	//判断所有虚拟商品已经过期的商品
	glog.Info(funcName, "，虚拟商品已经过期，", failPID4)
	for _, v := range failPID4 {
		row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "虚拟商品已经过期"}
		out = append(out, row)
	}

	//获取无阿闻分类的商品
	var categoryPassId []int32
	err = engine.Select("a.id").Table("gj_product").Alias("a").
		Join("inner", "channel_category b", "a.category_id = b.id").
		Where("b.channel_id = 1").
		In("a.id", productId).
		Find(&categoryPassId)
	categoryPassSet := utils.NewSet(categoryPassId...)
	failPID3 = inSet.Minus(categoryPassSet).List()
	if err != nil {
		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		glog.Error(err)
		return out, err
	}

	glog.Info(funcName, "，批量认领，无阿闻分类的商品为，", failPID3)
	for _, v := range failPID3 {
		row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "无阿闻分类"}
		out = append(out, row)
	}

	//认领到第三方却没有第三方分类的商品
	setsChannel := utils.NewSet(channelId...)
	setsChannelNew := setsChannel.Minus(utils.NewSet(ChannelAwenId, ChannelDigitalHealth))
	glog.Info("setsChannelNew", setsChannelNew.List())

	for _, product := range productId {
		var failPID []int32

		err = engine.Table("gj_product_channel").Select(" distinct id").
			Where("product_id = ? ", product).
			In("channel_id ", setsChannelNew.List()).Find(&failPID)
		if err != nil {
			err = errors.New(funcName + "，数据库查询失败，" + err.Error())
			glog.Error(err)
			return out, err
		}
		if len(failPID) < len(setsChannelNew.List()) {
			glog.Info("无第三方分类的：", failPID)
			failPID7 = append(failPID7, product)
		}
	}

	for _, v := range failPID7 {
		row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "认领到第三方却没有第三方分类"}
		out = append(out, row)
	}

	faiSet0 := utils.NewSet(failPID0...)
	failSet1 := utils.NewSet(failPID1...)
	failSet2 := utils.NewSet(failPID2...)
	failSet3 := utils.NewSet(failPID3...)
	failSet4 := utils.NewSet(failPID4...)
	failSet7 := utils.NewSet(failPID7...)

	passSet := inSet.Minus(faiSet0).Minus(failSet1).Minus(failSet2).Minus(failSet3).Minus(failSet4).Minus(failSet7)
	passPID = passSet.List()

	// 筛选出组合商品id
	var groupProductId []int32
	err = engine.Table("gj_product").Alias("a").Select("a.id").
		Where("a.product_type = 3").
		In("a.id", passPID).
		Find(&groupProductId)
	if err != nil {
		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		glog.Error(err)
	}

	//筛选出组合商品里面有明细已经过期的
	err = engine.Select("a.id").Table("gj_product").Alias("a").
		Join("inner", "gj_sku_group b", "a.id = b.product_id").
		Join("inner", "gj_product c", "c.id = b.group_product_id").
		Where("c.term_type=1 AND  FROM_UNIXTIME(c.term_value)<NOW()").
		In("a.id", groupProductId).
		Find(&failPID5)

	if err != nil {
		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		glog.Error(err)
	}
	glog.Info(funcName, "，组合商品中存在虚拟商品已经过期的，", failPID5)
	for _, v := range failPID5 {
		row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "子商品有过期的"}
		out = append(out, row)
	}
	failSet5 := utils.NewSet(failPID5...)

	//筛选掉所有不符合的后的商品ID集合
	passPID = passSet.Minus(failSet5).List()
	if len(passPID) == 0 {
		return out, nil
	}
	//client := et.GetExternalClient()
	//defer client.Close()

	session := engine.NewSession()
	defer session.Close()
	//对符合条件的商品遍历渠道认领
	jdFailID := make([]int32, 0)
	for _, cid := range channelId {
		//一个渠道一个事务
		session.Begin()
		tmpProductId := passPID
		//批量认领排除已存在的
		if isBatchCopy {
			sub_passPID := []int32{}
			err = session.Table("gj_product").Alias("a").Select("a.id").
				Join("left", "channel_product b", "a.id=b.id and b.channel_id=?", cid).
				In("a.id", tmpProductId).
				And("b.id is null").
				Find(&sub_passPID)
			if err != nil {
				err = errors.New(funcName + "，数据库查询失败，" + err.Error())
				glog.Error(err)
				continue
			}
			glog.Info(funcName, "，批量认领: ", cid, "，商品为，", sub_passPID)
			tmpProductId = sub_passPID
			if len(tmpProductId) == 0 {
				glog.Info("channel_id：", cid, "，认领商品都已存在")
				continue
			}
		}

		// 未认领的组合商品
		var groupFailProductId []int32
		err = engine.Select("a.id").Table("gj_product").Alias("a").
			Join("inner", "gj_sku_group b", "a.id = b.product_id").
			Join("inner", "gj_product c", "c.id = b.group_product_id").
			Where("c.channel_id not like '%"+cast.ToString(cid)+"%'").
			In("a.id", groupProductId).
			Find(&groupFailProductId)
		if err != nil {
			err = errors.New(funcName + "，数据库查询失败，" + err.Error())
			glog.Error(err)
			continue
		}

		//所有组合商品的ID
		groupSet := utils.NewSet(groupProductId...)
		//未认领的组合ID
		groupFailSet := utils.NewSet(groupFailProductId...)

		//已认领的组合商品id
		groupProductId := groupSet.Minus(groupFailSet).List()

		glog.Info(funcName, "，组合明细中存在未认领商品的组合商品，", groupFailProductId)
		for _, v := range groupFailProductId {
			row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "子商品未认领"}
			out = append(out, row)
		}

		// 美团、饿了么、京东不能认领为药品的商品
		var drugsFailProductId []int32
		//if cid == ChannelMtId || cid == ChannelElmId || cid == ChannelJddjId {
		//	err = engine.Select("a.id").Table("gj_product").Alias("a").
		//		Where("is_drugs = 1").In("a.id", tmpProductId).
		//		Find(&drugsFailProductId)
		//	if err != nil {
		//		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		//		glog.Error(err)
		//		continue
		//	}
		//}
		//未认领的组合ID
		drugsFailSet := utils.NewSet(drugsFailProductId...)

		glog.Info(funcName, "美团、饿了么、京东，不能认领药品商品，", drugsFailProductId)
		for _, v := range drugsFailProductId {
			row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], ChannelName[cid] + "不能认领药品"}
			out = append(out, row)
		}

		// 筛选出符合条件的商品
		tmpSet := utils.NewSet(tmpProductId...)

		//if cid == ChannelDigitalHealth { // 是医疗互联网的排除不是互联网商品的数据
		//
		//	err = engine.Table("gj_product").Select("id").Where("is_intel_goods = 0").In("id", tmpProductId).Find(&failPID8)
		//	if err != nil {
		//		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		//		glog.Error(err)
		//		return out, err
		//	}
		//
		//	for _, v := range failPID8 {
		//		row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "商品不是互联网医疗产品，无法认领到医疗互联网渠道"}
		//		out = append(out, row)
		//	}
		//
		//} else { // 不是医疗互联网的渠道排除医疗互联网的商品
		//	err = engine.Table("gj_product").Select("id").Where("is_intel_goods = 1").In("id", tmpProductId).Find(&failPID8)
		//	if err != nil {
		//		err = errors.New(funcName + "，数据库查询失败，" + err.Error())
		//		glog.Error(err)
		//		return out, err
		//	}
		//
		//	for _, v := range failPID8 {
		//		row := []string{cast.ToString(v), cast.ToString(SkuMap[v]), NameMap[v], "商品为互联网医疗产品，无法认领到非医疗互联网渠道"}
		//		out = append(out, row)
		//	}
		//}
		//failSet8 := utils.NewSet(failPID8...)

		tmpProductId = tmpSet.Minus(groupFailSet).Minus(drugsFailSet).List()

		// 所有符合商品id
		productIdSlice := make([]string, len(tmpProductId))
		for k, v := range tmpProductId {
			productIdSlice[k] = cast.ToString(v)
		}
		productIdStr := strings.Join(productIdSlice, ",")

		// 组合商品id
		groupProductIdSlice := make([]string, len(groupProductId))
		for k, v := range groupProductId {
			groupProductIdSlice[k] = cast.ToString(v)
		}
		groupProductIdStr := strings.Join(groupProductIdSlice, ",")
		// 若商品为空直接返回
		if len(tmpProductId) == 0 {
			return out, nil
		}
		//非重新认领,先删除，再插入
		cidStr := cast.ToString(cid)
		// 认领操作的时候字段传递claim(主要是为了区分现在的重新认领没有默认字段),重新认领的时候传递重新认领的字段
		if updateFields == "claim" {
			var (
				cpm  = new(models.ChannelProduct)
				csm  = new(models.ChannelSku)
				cstm = new(models.ChannelSkuThird)
				csvm = new(models.ChannelSkuValue)
				csg  = new(models.ChannelSkuGroup)
			)

			//  删除这里将虚拟商品全部排除了，此次需要放开虚拟商品的限制
			//if cid != 1 {
			//	var virtualProductId []int32
			//	err = session.Select("id").Table("gj_product").
			//		Where("product_type = 2").In("id", tmpProductId).Find(&virtualProductId)
			//	if err != nil {
			//		session.Rollback()
			//		glog.Error(funcName, "，数据库删除失败，", err)
			//	}
			//
			//	tmpSet := utils.NewSet(tmpProductId...)
			//	virtualFailSet := utils.NewSet(virtualProductId...)
			//	tmpProductId = tmpSet.Minus(virtualFailSet).List()
			//}

			//批量认领不做删除操作
			if _, err = session.Where("channel_id=?", cid).In("id", tmpProductId).Delete(cpm); err != nil {
				session.Rollback()
				glog.Error(funcName, "，数据库删除失败，", err)

			}
			if _, err = session.Where("channel_id=?", cid).In("product_id", tmpProductId).Delete(csm); err != nil {
				session.Rollback()
				glog.Error(funcName, "，数据库删除失败，", err)

			}
			if _, err = session.Where("channel_id=?", cid).In("product_id", tmpProductId).Delete(cstm); err != nil {
				session.Rollback()
				glog.Error(funcName, "，数据库删除失败，", err)

			}
			if _, err = session.Where("channel_id=?", cid).In("product_id", tmpProductId).Delete(csvm); err != nil {
				session.Rollback()
				glog.Error(funcName, "，数据库删除失败，", err)
			}
			if _, err = session.Where("channel_id=?", cid).In("product_id", groupProductId).Delete(csg); err != nil {
				session.Rollback()
				glog.Error(funcName, "，数据库删除失败，", err)
			}
			//复制gj_product
			paramStr := ""
			elmParam := ""
			//京东分类默认值,同步京东品牌库时需要该值，其它渠道不需要。
			if cid == ChannelJddjId {
				paramStr = "24479 as"
			} else {
				paramStr = "0 as"
			}
			//饿了么重量单位为克
			if cid == ChannelElmId {
				elmParam = "*1000 as weight_for_unit"
			}
			if _, err := session.Exec("INSERT INTO `channel_product`(`id`, `category_id`,`channel_category_id`, `brand_id`, `name`, `short_name`, `code`, `bar_code`, `create_date`, `update_date`, `is_del`, `is_group`, `pic`, `selling_point`, `video`, `content_pc`, `content_mobile`, `is_discount`, `product_type`, `is_use`, `del_date`, `channel_id`,`category_name`, `channel_category_name`,`channel_tag_id`, `group_type`, `term_type`, `term_value`, `use_range`, `virtual_invalid_refund`) SELECT `id`, `category_id`,`category_id`,`brand_id`, `name`, `short_name`,`code`, `bar_code`, `create_date`, `update_date`, `is_del`, `is_group`, `pic`, `selling_point`, `video`, `content_pc`, `content_mobile`, `is_discount`, `product_type`, `is_use`, `del_date`, " + cidStr + " as `channel_id`,`category_name`,`category_name`," + paramStr + " `channel_tag_id`, `group_type`, `term_type`, `term_value`, `use_range`, `virtual_invalid_refund` FROM `gj_product` WHERE id IN(" + productIdStr + ")"); err != nil {
				session.Rollback()
				glog.Error(funcName, "，数据库插入失败，", err)
				row := []string{productIdStr, "", "", err.Error()}
				out = append(out, row)
				continue
			}
			//复制gj_sku
			if _, err := session.Exec("INSERT INTO `channel_sku`(`id`, `product_id`, `market_price`, `retail_price`,`channel_id`,`bar_code`,`weight_for_unit`,`prepose_price`,`store_price`) SELECT `id`, `product_id`, `market_price`, `retail_price`, " + cidStr + " as `channel_id`,`bar_code`,`weight_for_unit`" + elmParam + ",`prepose_price`,`store_price` FROM gj_sku WHERE product_id IN(" + productIdStr + ")"); err != nil {
				session.Rollback()
				glog.Error(funcName, "，数据库插入失败，", err)
				row := []string{productIdStr, "", "", err.Error()}
				out = append(out, row)
				continue
			}

			//复制gj_sku_third
			if _, err := session.Exec("INSERT INTO `channel_sku_third`(`product_id`, `sku_id`, `third_spu_id`, `third_sku_id`, `third_spu_sku_id`, `erp_id`,`channel_id`) SELECT `product_id`, `sku_id`, `third_spu_id`, `third_sku_id`, `third_spu_sku_id`, `erp_id`, " + cidStr + " as `channel_id` FROM gj_sku_third WHERE product_id IN(" + productIdStr + ")"); err != nil {
				session.Rollback()
				glog.Error(funcName, "，数据库插入失败，", err)
				row := []string{productIdStr, "", "", err.Error()}
				out = append(out, row)
				continue
			}

			//复制gj_sku_value
			if _, err := session.Exec("INSERT INTO `channel_sku_value`(`spec_id`, `spec_value_id`, `sku_id`, `product_id`, `pic`, `sort`, `channel_id`) SELECT `spec_id`, `spec_value_id`, `sku_id`, `product_id`, `pic`, `sort` , " + cidStr + " as `channel_id`FROM `gj_sku_value` WHERE product_id IN(" + productIdStr + ")"); err != nil {
				session.Rollback()
				glog.Error(funcName, "，数据库插入失败，", err)
				row := []string{productIdStr, "", "", err.Error()}
				out = append(out, row)
				continue
			}

			//复制gj_sku_group
			if len(groupProductIdStr) > 0 {
				if _, err := session.Exec("INSERT INTO `channel_sku_group`(`product_id`, `sku_id`, `group_product_id`, `group_sku_id`, `count`, `discount_value`,`discount_type`, `market_price`, `channel_id`, `product_type`) SELECT `product_id`, `sku_id`, `group_product_id`, `group_sku_id`, `count`, `discount_value`, `discount_type`, `market_price`, " + cidStr + " AS `channel_id`, `product_type` FROM gj_sku_group WHERE product_id IN(" + groupProductIdStr + ")"); err != nil {
					session.Rollback()
					glog.Error(funcName, "，数据库插入失败，", err)
					row := []string{productIdStr, "", "", err.Error()}
					out = append(out, row)
					continue
				}
			}

		} else { //重新认领，选择字段进行更新
			var (
				// 兼容多规格
				cpm = new(models.ChannelProduct)
				csm = make([]models.ChannelSku, 0)
				//a8货号model
				cstm_a8 = make([]models.ChannelSkuThird, 0)
				//子龙货号model
				cstm_zilong = make([]models.ChannelSkuThird, 0)
				csvm        = make([]models.ChannelSkuValue, 0)
			)

			// 修改兼容多规格  // 主商品的useRnage字段注意
			session.SQL("select id,category_id,category_name,brand_id,name,short_name,code,bar_code,create_date,update_date,is_del,is_group,pic,selling_point,video,content_pc,content_mobile,is_discount,product_type,is_use,del_date,is_drugs,brand_name,use_range,group_type,term_type,term_value,virtual_invalid_refund,warehouse_type from gj_product where id = ?", tmpProductId[0]).Omit("channel_id").Get(cpm)
			session.SQL("select * from gj_sku where product_id = ? ", tmpProductId[0]).Find(&csm)
			session.SQL("select * from gj_sku_third where product_id = ? and erp_id = 2", tmpProductId[0]).Find(&cstm_a8)
			session.SQL("select * from gj_sku_third where product_id = ? and erp_id = 4", tmpProductId[0]).Find(&cstm_zilong)
			glog.Info(kit.JsonEncode(cstm_zilong), kit.JsonEncode(cstm_a8))
			session.SQL("select * from gj_sku_value where product_id = ? ", tmpProductId[0]).Find(&csvm)
			//fmt.Println(cpm)
			// todo 修改主表 修改之前需要先剔除主表中不存在的字段列如第三方的分类字段
			if updateFields != "" {
				mainUpdateFields, otherFields, err := SplitUpdateFields(updateFields)
				glog.Info("拆分更新字段之后：", mainUpdateFields, " otherFields: ", otherFields)
				if err != nil {
					return out, nil
				}
				session.Where("id = ? and channel_id = ?", tmpProductId[0], cid).Cols(mainUpdateFields).Update(cpm)
			}

			//修改规格的情况
			if strings.Contains(updateFields, "spec_value_id") {
				for _, v := range csvm {
					csvm_data := v
					has, _ := session.Table("channel_sku_value").
						Where("product_id = ? and channel_id = ?  and sku_id = ? ", tmpProductId[0], cid, csvm_data.SkuId).Exist()
					// 有则更新没有则新增
					if has {
						_, err = session.Where("product_id = ? and channel_id = ? and sku_id = ? ", tmpProductId[0], cid, v.SkuId).Update(&v)
						if err != nil {
							row := []string{cast.ToString(tmpProductId[0]), "", "", "修改规格失败"}
							out = append(out, row)
							glog.Error("修改规格失败", v.SkuId, v.ProductId, err.Error())
						}
					} else {
						_, err := session.Insert(csvm_data)
						if err != nil {
							row := []string{cast.ToString(tmpProductId[0]), "", "", "新增规格失败"}
							out = append(out, row)
							glog.Error("新增规格失败", v.SkuId, v.ProductId, err.Error())
						}
					}

				}
			}
			//修改分类的情况，需再修改分类名和渠道店内分类ID
			if strings.Contains(updateFields, "category_id") && cpm.CategoryId > 0 {
				session.Where("id = ? and channel_id = ? ", tmpProductId[0], cid).Cols("category_name").Update(cpm)
				//使用管家商品category_id作为channel_category_id
				session.Table("channel_product").Where("id = ? and channel_id = ? ", tmpProductId[0], cid).Update(&models.ChannelProduct{ChannelCategoryId: cpm.CategoryId})
				var channel_store_product []models.ChannelStoreProduct

				//todo 重新认领的时候，对channel_store_product表的分类进行修改
				err = NewDbConn().Where("product_id=?", tmpProductId[0]).And("channel_id=?", cid).Find(&channel_store_product)
				if err != nil {
					glog.Error("渠道商品认领修改分类失败，err:", err)
				}
				for _, v := range channel_store_product {
					if v.Id > 0 {
						v.ChannelCategoryName = cpm.CategoryName
						v.ChannelCategoryId = int(cpm.CategoryId)
						_, err := session.Id(v.Id).Update(&v)
						if err != nil {
							glog.Error("修改channel_store_product失败，err:", err)
						}
					}
				}
			}
			//修改A8货号的情况
			if strings.Contains(updateFields, "a8") {
				// 清空a8货号
				if len(cstm_a8) == 0 {
					session.Exec("delete from channel_sku_third where product_id = ? and channel_id = ? and erp_id = 2", tmpProductId[0], cid)
				} else {
					//有则更新，无则插入
					for _, v := range cstm_a8 {
						cstmData := v
						has, _ := session.Table("channel_sku_third").Where("product_id = ? and channel_id = ? and erp_id = 2 and sku_id = ? ", tmpProductId[0], cid, cstmData.SkuId).Exist()
						if has {
							session.Where("product_id = ? and channel_id = ? and erp_id = 2 and  sku_id = ? ", tmpProductId[0], cid, cstmData.SkuId).Cols("third_sku_id", "third_spu_sku_id").Update(&cstmData)
						} else {
							cstmData.ChannelId = cid
							cstmData.Id = 0
							_, err := session.Insert(&cstmData)
							if err != nil {
								row := []string{cast.ToString(tmpProductId[0]), "", "", "修改A8货号失败"}
								out = append(out, row)
								continue
							}
						}
					}
				}
			}
			//修改子龙货号的情况
			if strings.Contains(updateFields, "zilong") {
				// 清空子龙货号
				if len(cstm_zilong) == 0 {
					session.Exec("delete from channel_sku_third where product_id = ? and channel_id = ? and erp_id = 4", tmpProductId[0], cid)
				} else {
					//有则更新，无则插入
					for _, v := range cstm_zilong {
						cstm_zilng_data := v
						has, _ := session.Table("channel_sku_third").
							Where("product_id = ? and channel_id = ? and erp_id = 4 and sku_id = ? ", tmpProductId[0], cid, cstm_zilng_data.SkuId).Exist()
						if has {
							session.Where("product_id = ? and channel_id = ? and erp_id = 4 and sku_id = ?", tmpProductId[0], cid, cstm_zilng_data.SkuId).Cols("third_sku_id", "third_spu_sku_id").Update(&cstm_zilng_data)
						} else {
							cstm_zilng_data.ChannelId = cid
							cstm_zilng_data.Id = 0
							_, err := session.Insert(cstm_zilng_data)
							if err != nil {
								row := []string{cast.ToString(tmpProductId[0]), "", "", "修改子龙货号失败"}
								out = append(out, row)
								continue
							}
						}
					}
				}

			}
			//修改SKUID的情况，对应修改channel_sku的id字段
			if strings.Contains(updateFields, "sku_id") {
				//修改重量，饿了么渠道单位为克，需要乘1000
				for _, v := range csm {
					csm_data := v
					csm_data.ChannelId = cid
					if cid == ChannelElmId {
						csm_data.WeightForUnit = csm_data.WeightForUnit * 1000
					}
					has, _ := session.Table("channel_sku").Where("product_id = ? and id = ?  and channel_id = ?",
						tmpProductId[0], csm_data.Id, cid).Exist()
					if has {
						session.Where("product_id = ? and id = ?  and channel_id = ?", tmpProductId[0], csm_data.Id, cid).Cols("id").Update(csm_data)
					} else {
						_, err := session.Insert(csm_data)
						if err != nil {
							row := []string{cast.ToString(tmpProductId[0]), "", "", "修改channel_sku失败"}
							out = append(out, row)
							continue
						}
					}

				}

			}
			// 更新重量
			if strings.Contains(updateFields, "weight_for_unit") {
				for _, v := range csm {
					csm_data := v
					if cid == ChannelElmId {
						csm_data.WeightForUnit = csm_data.WeightForUnit * 1000
					}
					session.Where("product_id = ? and id = ? and channel_id = ?", tmpProductId[0], csm_data.Id, cid).Cols("weight_for_unit").Update(csm_data)
				}
			}

			// 更新bar_code
			if strings.Contains(updateFields, "bar_code") {
				for _, v := range csm {
					csm_data := v
					session.Where("product_id = ? and id = ? and channel_id = ? ", tmpProductId[0], csm_data.Id, cid).Cols("bar_code").Update(csm_data)
				}
			}

			if strings.Contains(updateFields, "custom_area") {
				//只有美团有定制区域
				if cid == ChannelMtId {
					session.Exec("delete from channel_product_attr where product_id = ? and channel_id = ?", tmpProductId[0], cid)
					attrs := make([]models.ChannelProductAttr, 0)
					session.SQL("select * from gj_product_channel_attr where product_id = ? and channel_id = ? ", tmpProductId[0], cid).Find(&attrs)
					_, err := session.Insert(attrs)
					if err != nil {
						row := []string{cast.ToString(tmpProductId[0]), "", "", "跟新美团有定制区域失败"}
						out = append(out, row)
						glog.Error("custom_area : ", err.Error())
					}
				}

			}

			// 组合商品的重新认领使用的此字段
			//if updateFields != "update" {
			//	session.Where("product_id = ? and channel_id = ?", tmpProductId[0], cid).Cols(updateFields).Update(csm)
			//} else {
			//	session.Where("id = ? and channel_id = ?", tmpProductId[0], cid).Update(cpm)
			//	session.Where("product_id = ? and channel_id = ?", tmpProductId[0], cid).Update(csm)
			//}
		}
		if cid == ChannelJddjId {
			var jdCategoryId int64 = int64(JDCategoryId) // 获取jd分类
			channel := models.GjProductChannel{}
			session.SQL("select * from gj_product_channel where product_id=? and channel_id = ?  ", tmpProductId[0], cid).Get(&channel)
			if channel.Id > 0 {
				jdCategoryId = int64(channel.CategoryId)
			}
			//同步到京东品牌库
			rows := make([]models.GJSku, 0)
			newSession := NewDbConn().NewSession()
			defer newSession.Close()

			if err = newSession.Table("gj_product").Alias("a").
				Select("a.*,b.bar_code,b.id sku_id,b.weight_for_unit AS weight_for_unit,b.store_price market_price,c.category_id third_category_id,c.channel_store_id store_master_id").
				Join("inner", "gj_sku b", "a.id = b.product_id").
				Join("left", "channel_category_thirdid c", "a.category_id = c.id and c.channel_id = ?", ChannelJddjId).
				In("a.id", tmpProductId).
				GroupBy("c.category_id").
				Find(&rows); err != nil {
				glog.Error(funcName + "，数据库查询失败，" + err.Error())
				return out, errors.New("查询数据库商品信息失败")
			}

			//v6.6.5,雀巢京东新店，只有可上架的商品可以认领，其他的商品不让它认领过去
			agencyConfig, err := new(Product).GetAgencyConfig(context.Background(), &empty.Empty{})
			if err != nil {
				glog.Error("获取可上架配置异常", err.Error())
				return out, err
			}

			var req et.AddSkuRequest
			for _, v := range rows {
				// 组合商品的gjdj类目固定 upc为空，不然会报错 xorm的坑取得是第一个空的barcode
				if v.ProductType == 3 {
					v.BarCode = ""
				}
				if v.ProductType == 2 {
					continue // 虚拟商品不同步到jddj
				}

				storeMasterId, _ := strconv.ParseInt(v.StoreMasterId, 10, 32)
				if storeMasterId == 0 {
					storeMasterId = 1
				}

				// 判断是否认领到雀巢jd渠道
				var flag bool
				for i := range agencyConfig.ConfigData {
					excludeStoreMasterId := agencyConfig.ConfigData[i]
					if storeMasterId == cast.ToInt64(excludeStoreMasterId) {
						flag = true
						break
					}
				}

				if !flag {
					// 获取雀巢配置的可上架商品的sku_id的map集合
					skuIdsMap, err := new(Product).IsUpProduct(context.Background(), cast.ToInt32(storeMasterId), 0)
					if err != nil {
						glog.Error("获取雀巢配置的可上架商品的product_id的map集合异常 ", err.Error())
						return out, err
					}
					if _, ok := skuIdsMap[cast.ToInt(v.SkuId)]; !ok {
						// 不是瑞鹏和代运营的app-channel,但是没有在可上架的商品配置里面，直接不认领，continue
						continue
					}
				}
				//京东到家接口限流每次隔0.6秒
				time.Sleep(600 * time.Millisecond)
				req = et.AddSkuRequest{
					OutSkuId: v.SkuId,
					TraceId:  v.Id + "-" + cast.ToString(time.Now().Nanosecond()),
					//CategoryId:      22982,                      //默认京东到家分类，其他宠物用具
					CategoryId:      jdCategoryId,               //默认京东到家分类，其他宠物用具
					ShopCategories:  []int64{v.ThirdCategoryId}, //店内分类
					BrandId:         v.BrandId,
					SkuName:         v.Name,
					SkuPrice:        v.MarketPrice,
					Weight:          v.WeightForUnit,
					SloganStartTime: "2020-01-01 00:00:00",
					SloganEndTime:   "2050-01-01 00:00:00",
					FixedStatus:     1,         //上架状态
					IsSale:          false,     //不可售状态
					Upc:             v.BarCode, //6922636800257 可用
					Slogan:          v.SellingPoint,
					StoreMasterId:   int32(storeMasterId),
				}

				//默认使用其他品牌
				if req.BrandId == 0 {
					req.BrandId = int64(JDBrandId)
				}

				//默认价格（单位分）
				if req.SkuPrice == 0 {
					req.SkuPrice = 100000
				}

				if len(v.Pic) > 0 {
					for _, v1 := range strings.Split(v.Pic, ",") {
						if v1 != "" {
							req.Images = append(req.Images, v1)
						}
					}
				}

				glog.Info("商品同步到京东品牌库，接口输入:", kit.JsonEncode(req))
				client := et.GetExternalClient()
				if res, err := client.JddjProduct.AddSku(client.Ctx, &req); err != nil {
					glog.Error("调用JddjAddSku失败，"+v.Id+"同步失败", err)
					row := []string{cast.ToString(v.Id), "", "", "调用同步京东接口失败"}
					out = append(out, row)
					jdFailID = append(jdFailID, cast.ToInt32(v.Id))
					continue
				} else {
					glog.Info("商品同步到京东品牌库，接口输入:", kit.JsonEncode(req), ",接口返回结果：", kit.JsonEncode(res))
					if strings.Contains(res.Message, "失败") && !strings.Contains(res.Message, "1001030") {
						glog.Error(v.Id + "同步京东失败:" + res.Message)
						row := []string{cast.ToString(v.Id), "", "", v.Id + "同步京东失败:" + res.Message}
						out = append(out, row)
						jdFailID = append(jdFailID, cast.ToInt32(v.Id))
						continue
					} else if strings.Contains(res.Message, "1001030") {
						glog.Info("京东品牌库已存在该商品，执行更新字段同步接口")
						in := &et.JddjUpdateGoodsListRequest{
							TraceId:        v.Id + "-" + cast.ToString(time.Now().Nanosecond()),
							OutSkuId:       v.SkuId,
							ShopCategories: []string{cast.ToString(v.ThirdCategoryId)},
							//CategoryId:     24479,
							CategoryId:    jdCategoryId,         //京东商品类目--阿闻管家商品库分类
							SkuName:       v.Name,               //商品名称--商品名称
							Slogan:        v.SellingPoint,       // 商品卖点--商品广告词
							SkuPrice:      int32(v.MarketPrice), //门店仓价格--商品价格
							ProductDesc:   v.ContentPc,
							Weight:        v.WeightForUnit,
							BrandId:       v.BrandId,
							FixedStatus:   1,
							StoreMasterId: int32(storeMasterId),
						}

						if len(v.Pic) > 0 {
							for _, v := range strings.Split(v.Pic, ",") { //商品图片--商品管理
								if v != "" {
									in.Images = append(in.Images, v)
								}
							}
						}

						glog.Info("调用JddjUpdateGoodsList接口,输入:", in)

						res, err := client.JddjProduct.JddjUpdateGoodsList(client.Ctx, in)
						if err != nil {
							glog.Error("调用JddjUpdateGoodsList接口失败")
							row := []string{cast.ToString(v.Id), "", "", "调用京东更新接口失败"}
							out = append(out, row)
							jdFailID = append(jdFailID, cast.ToInt32(v.Id))
							continue
						} else if strings.Contains(res.Message, "失败") {
							glog.Error("调用JddjUpdateGoodsList接口,返回", res)
							row := []string{cast.ToString(v.Id), "", "", "京东更新失败"}
							out = append(out, row)
							jdFailID = append(jdFailID, cast.ToInt32(v.Id))
							continue
						}
					}
				}
			}
		}

		//更新认领状态
		var p Product
		jdFailSet := utils.NewSet(jdFailID...)
		set := utils.NewSet(tmpProductId...)
		finalPassSet := set.Minus(jdFailSet)
		for _, v := range finalPassSet.List() {
			p.EditProductIsUse(session, ctx, &pc.ProductRequest{Product: &pc.Product{Id: cast.ToInt32(v), ChannelId: cast.ToString(cid)}})
		}

		if len(jdFailID) > 0 && cid == ChannelJddjId { // jddj更新sku异常 退出不执行
			continue
		}
		if err := session.Commit(); err != nil {
			glog.Error(funcName+"渠道:"+cast.ToString(cid)+"商品:"+cast.ToString(tmpProductId[0])+"，事务提交失败，", err)
			return out, err
		}

		if updateFields == "claim" { // 前面事务处理，放到commit后面更新
			// 复制gj的第三方id和属性到渠道表中
			outMap, _ := GetThirdCategory(productIdStr)
			updateThirdCategoryAndAttr(tmpProductId, cid, outMap)
		} else {
			//更新第三方属性和第三方的id
			if strings.Contains(updateFields, "mt_id") {
				UpdateGjCategoryChannel(tmpProductId, ChannelMtId)
			}
			if strings.Contains(updateFields, "jd_id") {
				UpdateGjCategoryChannel(tmpProductId, ChannelJddjId)
			}
			if strings.Contains(updateFields, "ele_id") {
				UpdateGjCategoryChannel(tmpProductId, ChannelElmId)
			}
		}

		glog.Info("渠道:" + cast.ToString(cid) + "商品:" + cast.ToString(tmpProductId[0]) + "认领事务提交成功")
	}

	var p Product
	channelIdSlice := make([]string, len(channelId))
	for k, cid := range channelId {
		channelIdSlice[k] = cast.ToString(cid)

		//更新快照
		for _, pid := range productId {
			var newSnap pc.ChannelProductRequest
			req := &pc.OneofIdRequest{
				ChannelId: cid,
				Id: &pc.OneofIdRequest_ProductId{
					ProductId: &pc.ArrayIntValue{Value: []int32{pid}},
				},
			}

			//查询商品主库信息
			if res, err := p.QueryChannelProductOnly(ctx, req); err != nil {
				glog.Error(funcName, "，调用QueryChannelProductOnly失败，", err, "，productId：", pid)
				continue
			} else if len(res.Details) == 0 {
				glog.Error(funcName, "，调用QueryChannelProductOnly失败，未查询到商品信息，", "，productId：", pid)
				continue
			} else {
				newSnap.Product = res.Details[0]
			}

			//查询商品属性
			if res, err := p.QueryChannelProductAttr(ctx, req); err != nil {
				glog.Error(funcName, "，调用QueryChannelProductAttr失败，", err, "，productId：", pid)
				continue
			} else {
				newSnap.ProductAttr = res.Details
			}

			//查询商品SKU
			var sku []*pc.Sku
			var skuValue []*pc.SkuValue
			var skuThird []*pc.SkuThird
			var skuGroup []*pc.SkuGroup
			if res, err := p.QueryChannelSku(ctx, req); err != nil {
				glog.Error(funcName, "，调用QueryChannelSku失败，", err, "，productId：", pid)
				continue
			} else {
				sku = res.Details
				for _, v := range res.Details {
					skuValue = append(skuValue, v.SkuValue...)
					skuThird = append(skuThird, v.SkuThird...)
					skuGroup = append(skuGroup, v.SkuGroup...)
				}
			}

			for _, s := range sku {
				skuInfo := &pc.SkuInfo{
					RetailPrice:   s.RetailPrice,
					SkuId:         s.Id,
					ProductId:     s.ProductId,
					MarketPrice:   s.MarketPrice,
					BarCode:       s.BarCode,
					ChannelId:     s.ChannelId,
					IsUse:         s.IsUse,
					WeightForUnit: s.WeightForUnit,
					WeightUnit:    s.WeightUnit,
					MinOrderCount: s.MinOrderCount,
					PriceUnit:     s.PriceUnit,
					PreposePrice:  s.PreposePrice,
					StorePrice:    s.StorePrice,
				}
				//第三方货号
				for _, t := range skuThird {
					if t.SkuId == s.Id {
						skuInfo.SkuThird = append(skuInfo.SkuThird, t)
					}
				}

				//sku value
				for _, v := range skuValue {
					if v.SkuId == s.Id {
						skuInfo.Skuv = append(skuInfo.Skuv, v)
					}
				}

				for _, g := range skuGroup {
					if g.SkuId == s.Id {
						skuInfo.SkuGroup = append(skuInfo.SkuGroup, g)
					}
				}

				newSnap.SkuInfo = append(newSnap.SkuInfo, skuInfo)
			}

			//查询出有该商品ID和渠道ID的快照的门店编号
			financeCodeSlice := make([]string, 0)
			engine.Table("channel_product_snapshot").Select("finance_code").
				Where("product_id=?", pid).And("channel_id=?", cid).
				Find(&financeCodeSlice)
			glog.Infof("商品【%v】有快照的门店：%v", pid, financeCodeSlice)
			wg := &sync.WaitGroup{}
			ch := make(chan int, 10)
			//一次查询出所有门店对应的快照
			fjRes := make([]fjStruct, 0)
			err := engine.Table("channel_product_snapshot").Select("finance_code,json_data").
				Where("channel_id=? and product_id=? ", cid, pid).In("finance_code", financeCodeSlice).Find(&fjRes)
			if err != nil {
				glog.Error(err)
				continue
			}

			warehouseCategoryMap := make(map[string]int32)
			var financialCodes []string
			for _, f := range fjRes {
				financialCodes = append(financialCodes, f.Finance_code)
			}
			//根据财务编码获取门店对应的仓库
			//dcClinet := GetDispatchClient() //一个商品一个连接(控制连接数量，如果商品数量过多，还得进一步优化，目前重新认领是单个商品)
			//defer dcClinet.Close()
			//warehouseList, err := dcClinet.RPC.GetWarehouseInfoByFanceCodes(context.Background(), &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: financialCodes})
			//glog.Info("GetWarehouseInfoByFanceCodes根据财务编码批量查询仓库类型：", financialCodes)
			//if err != nil {
			//	err = errors.New(utils.RunFuncName() + "调用GetWarehouseInfoByFanceCode根据财务编码查询仓库类型失败：" + err.Error())
			//	glog.Info(err)
			//	continue
			//}
			product := new(Product)
			resp, err := product.GetChannelWarehouses(financialCodes, cid)
			if err != nil {
				glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
				continue
			}
			if cid == ChannelAwenId && len(resp) == 0 {
				data, err := product.GetChannelWarehouses(financialCodes, ChannelAwenPickUpId)
				if err != nil {
					glog.Error(utils.RunFuncName()+"GetFinanceCodeWarehouseRelation，", err)
				}
				resp = append(resp, data...)
			}
			for _, ware := range resp {
				warehouseCategoryMap[ware.ShopId] = int32(ware.Category)
			}

			for _, v := range fjRes {
				financeCode := v.Finance_code
				jsonData := v.Json_data
				channelProductReq := new(pc.ChannelProductRequest)
				err = json.Unmarshal([]byte(jsonData), channelProductReq)
				if err != nil {
					glog.Error(funcName, "，json解析失败，", err, "，json：", jsonData)
					continue
				}
				//glog.Infof("newSnap：%+v", newSnap.Product.Name)
				//newSnap.SkuInfo[0].PreposePrice = channelProductReq.SkuInfo[0].PreposePrice
				//处理当前字段个数

				for _, v := range strings.Split(_updateFields, ",") {
					switch v {
					case "category_id":
						//todo 管家商品库认领的时候获取的是渠道商品库分类 -- 具体逻辑不知道，页面显示的是这样的。
						channelProductReq.Product.ChannelCategoryId = newSnap.Product.ChannelCategoryId
						break
					case "name":
						channelProductReq.Product.Name = newSnap.Product.Name
					case "short_name":
						channelProductReq.Product.ShortName = newSnap.Product.ShortName
					case "selling_point":
						channelProductReq.Product.SellingPoint = newSnap.Product.SellingPoint
						break
					case "pic":
						channelProductReq.Product.Pic = newSnap.Product.Pic
						break
					case "video":
						channelProductReq.Product.Video = newSnap.Product.Video
						break
					case "brand_id":
						channelProductReq.Product.BrandId = newSnap.Product.BrandId
						break
					case "sku_id":
						channelProductReq.Product.SkuId = newSnap.Product.SkuId

						break
					case "use_range": // 组合商品的应用范围
						channelProductReq.Product.UseRange = newSnap.Product.UseRange
						break
					case "spec_value_id":
						// todo 单规格处理,多规格会出问题
						//channelProductReq.SkuInfo[0].Skuv[0].SpecValueId = newSnap.SkuInfo[0].Skuv[0].SpecValueId
						//如果规格新增增加或者减少了需要处理
						old_map := make(map[string]*pc.SkuInfo, 0)
						new_map := make(map[string]*pc.SkuInfo, 0)
						for _, v1 := range channelProductReq.SkuInfo {
							st_data := v1
							st1 := cast.ToString(st_data.ProductId) + ":" + cast.ToString(st_data.SkuId)
							old_map[st1] = st_data
						}
						for _, v2 := range newSnap.SkuInfo {
							st_data := v2
							st1 := cast.ToString(st_data.ProductId) + ":" + cast.ToString(st_data.SkuId)
							new_map[st1] = st_data
						}
						now_map := old_map
						for k, _ := range old_map { // 删除
							if _, ok := new_map[k]; !ok {
								delete(now_map, k)
							}
						}
						for k, _ := range new_map { // 新增加sku
							if _, ok := old_map[k]; !ok {
								now_map[k] = new_map[k]
							}
						}

						// 最新的规格
						channelProductReq.SkuInfo = nil
						for _, v := range now_map {
							channelProductReq.SkuInfo = append(channelProductReq.SkuInfo, v)
						}
						// 多规格循环处理
						for _, v1 := range channelProductReq.SkuInfo {
							for _, v2 := range newSnap.SkuInfo {
								if v1.ProductId == v2.ProductId && v1.SkuId == v2.SkuId {
									for _, v11 := range v1.Skuv {
										for _, v22 := range v2.Skuv {
											if v11.ProductId == v22.ProductId && v11.SkuId == v22.SkuId && v11.SpecId == v22.SpecId {
												v11.SpecValueId = v22.SpecValueId
											}
										}
									}
								}
							}
						}

						break
					case "a8":
						cnt := 0
						for _, v := range channelProductReq.SkuInfo[0].SkuThird {
							if v.ErpId == 2 {
								cnt++
								for _, m := range newSnap.SkuInfo[0].SkuThird {
									if m.ErpId == 2 {
										v.ThirdSkuId = m.ThirdSkuId
									}
								}
							}
						}
						//无则新增
						if cnt == 0 {
							for _, v := range newSnap.SkuInfo[0].SkuThird {
								if v.ErpId == 2 {
									channelProductReq.SkuInfo[0].SkuThird = append(channelProductReq.SkuInfo[0].SkuThird, v)
								}
							}
						}
						break
					case "zilong":
						cnt := 0
						for _, v := range channelProductReq.SkuInfo[0].SkuThird {
							if v.ErpId == 4 {
								cnt++
								for _, m := range newSnap.SkuInfo[0].SkuThird {
									if m.ErpId == 4 {
										v.ThirdSkuId = m.ThirdSkuId
									}
								}
							}
						}
						//无则新增
						if cnt == 0 {
							for _, v := range newSnap.SkuInfo[0].SkuThird {
								if v.ErpId == 4 {
									channelProductReq.SkuInfo[0].SkuThird = append(channelProductReq.SkuInfo[0].SkuThird, v)
								}
							}
						}
						break
					case "weight_for_unit":
						//channelProductReq.SkuInfo[0].WeightForUnit = newSnap.SkuInfo[0].WeightForUnit
						for _, v1 := range channelProductReq.SkuInfo {
							for _, v2 := range newSnap.SkuInfo {
								if v1.ProductId == v2.ProductId && v1.SkuId == v2.SkuId {
									v1.WeightForUnit = v2.WeightForUnit
								}
							}
						}
						break
					//case "prepose_price":
					//不需要修改快照商品价格信息
					//channelProductReq.SkuInfo[0].PreposePrice = newSnap.SkuInfo[0].PreposePrice
					//break
					//case "store_price":
					//不需要修改快照商品价格信息
					//channelProductReq.SkuInfo[0].StorePrice = newSnap.SkuInfo[0].StorePrice
					//break
					case "bar_code":
						channelProductReq.Product.BarCode = newSnap.Product.BarCode
						//channelProductReq.Product.BarCode = newSnap.SkuInfo[0].BarCode
						//channelProductReq.SkuInfo[0].BarCode = newSnap.SkuInfo[0].BarCode
						for _, v1 := range channelProductReq.SkuInfo {
							for _, v2 := range newSnap.SkuInfo {
								if v1.ProductId == v2.ProductId && v1.SkuId == v2.SkuId {
									v1.BarCode = v2.BarCode
								}
							}
						}
						break
					case "content_pc":
						channelProductReq.Product.ContentPc = newSnap.Product.ContentPc
						break
					// 定制区域
					case "custom_area":
						//就是属性
						channelProductReq.ProductAttr = newSnap.ProductAttr
						break
					case "mt_id":
						if cid == cast.ToInt32(channelProductReq.Product.ChannelId) && cid == ChannelMtId {
							channelProductReq.Product.ChannelTagId = newSnap.Product.ChannelTagId
						}
						break
					case "jd_id":
						if cid == cast.ToInt32(channelProductReq.Product.ChannelId) && cid == ChannelJddjId {
							channelProductReq.Product.ChannelTagId = newSnap.Product.ChannelTagId
						}
						break
					case "ele_id":
						if cid == cast.ToInt32(channelProductReq.Product.ChannelId) && cid == ChannelElmId {
							channelProductReq.Product.ChannelTagId = newSnap.Product.ChannelTagId
						}
						break

						// 有效期
					case "term_value":
						channelProductReq.Product.TermType = newSnap.Product.TermType
						channelProductReq.Product.TermValue = newSnap.Product.TermValue
						break
						// 是否支持退款
					case "virtual_invalid_refund":
						channelProductReq.Product.VirtualInvalidRefund = newSnap.Product.VirtualInvalidRefund
						break
					case "update": // 批量的直接忽略
						// 名称
						channelProductReq.Product.Name = newSnap.Product.Name
						// 分类
						channelProductReq.Product.ChannelCategoryId = newSnap.Product.ChannelCategoryId
						// 卖点
						channelProductReq.Product.SellingPoint = newSnap.Product.SellingPoint
						// 图片
						channelProductReq.Product.Pic = newSnap.Product.Pic
						// 视频
						channelProductReq.Product.Video = newSnap.Product.Video
						// 组合商品应用范围
						channelProductReq.Product.UseRange = newSnap.Product.UseRange
						// 重量
						channelProductReq.SkuInfo[0].WeightForUnit = newSnap.SkuInfo[0].WeightForUnit
						// 条码
						channelProductReq.Product.BarCode = newSnap.SkuInfo[0].BarCode
						channelProductReq.SkuInfo[0].BarCode = newSnap.SkuInfo[0].BarCode
						// 商品详情
						channelProductReq.Product.ContentPc = newSnap.Product.ContentPc
						// 货号
						channelProductReq.SkuInfo[0].SkuThird = newSnap.SkuInfo[0].SkuThird
						break
					}
				}

				bt, _ := json.Marshal(channelProductReq)
				channelProductSnapshot := pc.ChannelProductSnapshot{
					ChannelId:   cid,
					ProductId:   pid,
					FinanceCode: financeCode,
					JsonData:    string(bt),
				}

				wg.Add(1)
				ch <- 1
				go func(cps pc.ChannelProductSnapshot, warecate int32) {
					defer func() {
						<-ch
						wg.Done()
					}()
					//if _, err = p.NewChannelProductSnapshot(ctx, &cps); err != nil {
					if _, err = newChannelProductSnapshot(ctx, &cps, warecate); err != nil {
						//glog.Error(funcName, "，调用NewChannelProductSnapshot失败，", err)  //mod by csf 解决上千个门店循环起协程，导致日志服务grpc连接一次性过多，导致rpc transport is closing
					}
					// glog.Info(funcName, "，批量认领，新增快照成功，", channelProductSnapshot)
				}(channelProductSnapshot, warehouseCategoryMap[channelProductSnapshot.FinanceCode])
			}
			wg.Wait()
		}
	}
	if len(out) == 0 {
		return [][]string{{"认领成功"}}, nil
	} else {
		return out, nil
	}
}

func checkNewChannelCategoryParam(in *pc.Category) error {
	if len(in.Name) == 0 {
		return status.Error(codes.Internal, "分类名称不能为空")
	}

	if in.Id > 0 && in.Id == in.ParentId {
		return status.Error(codes.Internal, "分类自身不可作父类")
	}

	//名称是否超长
	if len([]rune(in.Name)) > 8 {
		return status.Error(codes.Internal, "分类名称不能超过8个字符")
	}

	//名称是否重复
	if c, err := NewDbConn().Where("name=? and id!=? and channel_id=?", in.Name, in.Id, in.ChannelId).Count(new(models.ChannelCategory)); err != nil {
		glog.Error(err)
		return status.Error(codes.Internal, GetDBError(err).Error())
	} else if c > 0 {
		return status.Error(codes.Internal, "分类名称重复")
	}

	//parent_id是否存在
	if in.ParentId > 0 {
		if has, err := NewDbConn().Where("id=?", in.ParentId).Get(new(models.ChannelCategory)); err != nil {
			return status.Error(codes.Internal, GetDBError(err).Error())
		} else if !has {
			return status.Error(codes.Internal, "分类父类不存在，parent_id: "+cast.ToString(in.ParentId))
		}
	}

	// v6.4.0 有分类的异步任务的时候退出不让编辑修改
	if in.Id > 0 { // 不让编辑
		var num int32
		engine.SQL("select count(1) from dc_product.task_list tl where task_content = ? "+
			"and task_status in (1,2) and category  = ? ", SyncCategoryTaskContent, in.Id).Get(&num)
		if num > 0 {
			return status.Error(codes.Internal, "分类已经在任务列表中不允许编辑,id: "+cast.ToString(in.Id))
		}
	}
	if in.ParentId > 0 { // 新增编辑二级分类的时候，看是否有一级分类在任务中
		var parentNum int32
		engine.SQL("select count(1) from dc_product.task_list tl where task_content = ? "+
			"and task_status in (1,2) and category  = ? ", SyncCategoryTaskContent, in.ParentId).Get(&parentNum)
		if parentNum > 0 {
			return status.Error(codes.Internal, "一级分类已经在任务列表中,二级分类不允许增删改，一级分类id: "+cast.ToString(in.ParentId))
		}
	}

	//v6.6.0同一层级的排序值不能相同

	if in.Id > 0 { // 编辑
		var sonCategory []models.ChannelCategory
		engine.SQL("select * from dc_product.channel_category cc where parent_id = ? and channel_id = ?  and id != ?  ;",
			in.ParentId, in.ChannelId, in.Id).Find(&sonCategory)
		for i := range sonCategory {
			if in.Sort == sonCategory[i].Sort {
				return status.Error(codes.Internal, "分类排序在同一层级中已经存在，请重新填写排序值: "+cast.ToString(in.Sort))
			}
		}

	} else { // 新增
		var sonCategory []models.ChannelCategory
		engine.SQL("select * from dc_product.channel_category cc where parent_id = ? and channel_id = ?;",
			in.ParentId, in.ChannelId).Find(&sonCategory)
		for i := range sonCategory {
			if in.Sort == sonCategory[i].Sort {
				return status.Error(codes.Internal, "分类排序在同一层级中已经存在，请重新填写排序值: "+cast.ToString(in.Sort))
			}
		}
	}

	return nil
}

// 创建或更新渠道门店商品信息表
func NewChannelStoreProductTask(info []*pc.ChannelStoreProduct) {
	insertData := []*models.ChannelStoreProduct{}
	sunccessInfo := []*pc.ChannelStoreProduct{}
	for _, v := range info {
		isExist := &models.ChannelStoreProduct{}
		//处理更新渠道商品分类和推荐字段无法更新到小程序问题
		var newSnap = pc.ChannelProductRequest{
			Product:     &pc.ChannelProduct{},
			ProductAttr: nil,
		}

		var snapShot models.ChannelProductSnapshot
		if v.SnapshotId != 0 {
			_, err := NewDbConn().SQL("SELECT id,channel_id,user_no,finance_code,product_id,json_data,create_date FROM dc_product.channel_product_snapshot WHERE id = ?;", v.SnapshotId).Get(&snapShot)
			err = json.Unmarshal([]byte(snapShot.JsonData), &newSnap)
			if err != nil {
				glog.Error("NewChannelStoreProductTask方法查询快照信息失败,", err)
				continue
			}
		} else if v.ChannelId != 0 && len(v.FinanceCode) > 0 && v.ProductId != 0 {
			_, err := NewDbConn().Where("channel_id = ?", v.ChannelId).And("finance_code=?", v.FinanceCode).And("product_id=?", v.ProductId).Get(&snapShot)
			err = json.Unmarshal([]byte(snapShot.JsonData), &newSnap)
			if err != nil {
				glog.Error("NewChannelStoreProductTask方法查询快照信息失败,", err)
				continue
			}
		}
		if has, err := NewDbConn().
			Where("channel_id=? and finance_code=? and product_id=?", v.ChannelId, v.FinanceCode, cast.ToInt(v.ProductId)).
			Get(isExist); err != nil {
			glog.Error("查询门店商品表数据失败", err)
			continue
		} else if has {
			isExist.UpDownState = int(v.UpDownState)
			isExist.ChannelCategoryId = int(newSnap.Product.ChannelCategoryId)
			isExist.IsRecommend = int(newSnap.Product.IsRecommend)

			isWeightOk, err := JudgeChannelStoreProductWeight(newSnap)
			if !isWeightOk {
				glog.Error("NewChannelStoreProductTask方法判断商品重量失败，", err)
				continue
			}

			if _, err := NewDbConn().Id(isExist.Id).Cols("up_down_state, update_date,channel_category_id,is_recommend").Update(isExist); err != nil {
				glog.Error("更新门店商品表数据失败，", err)
				continue
			}
		} else {
			isWeightOk, err := JudgeChannelStoreProductWeight(newSnap)
			if !isWeightOk {
				glog.Error("NewChannelStoreProductTask方法判断商品重量失败，", err)
				continue
			}
			skuid := int(newSnap.Product.SkuId)
			if skuid == 0 {
				var model models.SkuThird
				engine.Where("product_id=?", v.ProductId).Get(&model)
				if model.Id > 0 {
					skuid = int(model.SkuId)
				}
			}
			insertData = append(insertData, &models.ChannelStoreProduct{
				ChannelId:           int(v.ChannelId),
				FinanceCode:         v.FinanceCode,
				ProductId:           cast.ToInt(v.ProductId),
				UpDownState:         int(v.UpDownState),
				SnapshotId:          snapShot.Id,
				IsRecommend:         int(newSnap.Product.IsRecommend),
				ChannelCategoryId:   int(newSnap.Product.ChannelCategoryId),
				ChannelCategoryName: newSnap.Product.ChannelCategoryName,
				Name:                newSnap.Product.Name,
				SkuId:               skuid,
				MarketPrice:         int(newSnap.SkuInfo[0].MarketPrice),
			})
		}
		sunccessInfo = append(sunccessInfo, v)
	}

	//门店上架成功商品写入数据库
	if len(insertData) > 0 {
		if _, err := NewDbConn().Insert(insertData); err != nil {
			glog.Error("门店上架成功商品写入数据库", err)
		}
	}

	if len(sunccessInfo) > 0 {
		//推送到MQ
		bt, _ := json.Marshal(sunccessInfo)
		m := mqgo.SyncMqInfo{
			Exchange: DatacenterExchange,
			Queue:    ChannelProductToEs,
			RouteKey: ChannelProductToEs,
			Request:  string(bt),
		}
		if err := mqgo.NewMq(config.GetString("mq.oneself"), engine).Publish(m); err != nil {
			glog.Error("上架成功后推送到MQ", err)
		}
	}
}

// 把门店下架的商品更新成上架
func (c *Product) DealChannelProductUpsale(ctx context.Context, in *pc.ChannelProductUnsaleRequest) (*pc.BatchBaseResponse, error) {
	glog.Info("DealChannelProductUpsale参数：", in)
	out := &pc.BatchBaseResponse{
		Code: 400,
	}
	//获取用户信息
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return out, nil
	}

	//门店信息列表--财务编码
	var financeCodeMap = make(map[string]interface{}, 0)

	if in.FinanceCodeList == "" {
		//1、根据当前用户获取门店信息列表
		dcClientData := GetDataCenterClient()
		defer dcClientData.Close()

		dcOut, err := dcClientData.RPC.GetHospitalListByUserNo(dcClientData.Ctx, &dac.GetHospitalListByUserNoRequest{
			UserNo:    userInfo.UserNo,
			ChannelId: in.ChannelId,
			Category:  in.Category,
		})
		if err != nil {
			out.Message = err.Error()
			return out, nil
		}
		for _, v := range dcOut.Data {
			financeCodeMap[v.StructOuterCode] = v.StructOuterCode
		}
	} else {
		financeCodeArry := strings.Split(in.FinanceCodeList, ",")
		for _, v := range financeCodeArry {
			financeCodeMap[v] = v
		}
	}
	//查询当前渠道的所有已下架的商品信息列表
	session := NewDbConn().NewSession()
	defer session.Close()

	session = session.Table("dc_product.channel_store_product").Select(" id,finance_code,product_id,snapshot_id ").Where(" up_down_state = 0 AND channel_id = 1 ")
	if len(in.ProductIds) > 0 {
		session.In("product_id", in.ProductIds)
	}
	var list []models.ChannelStoreProduct
	err := session.Find(&list)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	glog.Info("所有需要上架的商品数据：", list)

	//优化查询
	var Result_model_list []models.SkuThird
	l := 20
	for {
		if len(list) < l {
			l = len(list)
		}
		_list := list[:l]
		var ProductIdlist []string
		for _, v := range _list {
			ProductIdlist = append(ProductIdlist, fmt.Sprintf("%d", v.ProductId))
		}
		ProductIdStr := strings.Join(ProductIdlist, "','")
		var model_list []models.SkuThird
		session.Where("erp_id=4").And("product_id in ('" + ProductIdStr + "')").Find(&model_list)
		for _, v := range model_list {
			Result_model_list = append(Result_model_list, v)
		}
		list = list[l:]
		if len(list) <= 0 {
			break
		}
	}
	Result_model_list_map := make(map[int32]models.SkuThird, 0)
	for _, v := range Result_model_list {
		Result_model_list_map[v.ProductId] = v
	}
	redis := GetRedisConn()
	if kit.EnvCanCron() {
		defer redis.Close()
	}
	//添加仓库属性判断
	var financeCodelist []string
	for s, _ := range financeCodeMap {
		financeCodelist = append(financeCodelist, s)
	}
	warehouseMap := make(map[string]int32)
	if len(financeCodelist) > 0 {
		disclient := GetDispatchClient()
		defer disclient.Close()
		res, err := disclient.RPC.GetWarehouseInfoByFanceCodes(disclient.Ctx, &dc.GetWarehouseInfoByFanceCodesRequest{FinanceCode: financeCodelist})
		if err != nil {
			glog.Error(utils.RunFuncName()+"调用GetWarehouseInfoByFanceCodes失败，", err)
		}
		for _, v := range res.Data {
			if v.Category == 3 {
				warehouseMap[v.Code] = v.Category
			}
		}
	}

	//循环所有下架的商品信息，并记录需要更新的数据集合
	var channelStoreProductIds []int
	var productErrList [][]string
	for _, v := range list {
		if _, ok := financeCodeMap[v.FinanceCode]; ok {
			if res, ok := Result_model_list_map[int32(v.ProductId)]; ok {
				if len(warehouseMap) > 0 && warehouseMap[v.FinanceCode] == 3 {
					//todo 价格同步
					var price_params pc.UpdatePriceToChannelRequest
					price_params.FinanceCode = v.FinanceCode
					price_params.ProductId = cast.ToInt32(v.ProductId)
					price_params.ChannelId = cast.ToInt32(v.ChannelId)
					price_params.SkuId = cast.ToInt32(v.SkuId)
					price_params.ProductCode = res.ThirdSkuId
					_, err := c.UpdatePriceToChannel(ctx, &price_params)
					if err != nil {
						glog.Error("批量上架，价格同步失败，", err, "请求数据为", price_params)
					}
				}
			}

			var productErr []string
			productErr = append(productErr, cast.ToString(v.ProductId))
			productErr = append(productErr, v.FinanceCode)
			//判断库存是否足够
			var channelSkuThirds []models.ChannelSkuThird
			session.SQL("SELECT id,product_id,sku_id,third_spu_id,third_sku_id,erp_id,channel_id,third_spu_sku_id,is_use FROM dc_product.channel_sku_third WHERE channel_id = 1 AND product_id = ?", v.ProductId).Find(&channelSkuThirds)

			glog.Info("所有需要上架的商品数据（ChannelSkuThird）：", channelSkuThirds)
			StringCmd := redis.HGet("store:relation:dctozl", v.FinanceCode)
			zlId := cast.ToInt32(StringCmd.Val())
			if zlId == 0 {
				productErr = append(productErr, "渠道不存在SKU信息")
				productErrList = append(productErrList, productErr)
				out.FailNum = out.FailNum + 1
				continue
			}
			skuId := "0"
			var skuCodeInfos []*ic.SkuCodeInfo
			if len(channelSkuThirds) > 0 {
				for _, _sku := range channelSkuThirds {
					skuId = cast.ToString(_sku.SkuId)
					skuCodeInfo := &ic.SkuCodeInfo{
						FinanceCode: v.FinanceCode,
						Sku:         skuId,
						Spu:         _sku.ThirdSpuId,
						ThirdSkuid:  _sku.ThirdSkuId,
						ZlId:        zlId,
						ErpId:       _sku.ErpId,
					}
					skuCodeInfos = append(skuCodeInfos, skuCodeInfo)
				}
			}
			//查询子龙的库存
			//如果子龙库存=0，则跳过
			stockMap, _ := GetStockInfoBySkuCode(0, skuCodeInfos, in.ChannelId)
			keyStr := fmt.Sprintf("%s:%s", v.FinanceCode, skuId)
			glog.Info("批量上架：核实库存结果：", stockMap, ";对应的库存key值：", keyStr)
			if stockMap[keyStr] <= 0 {
				productErr = append(productErr, "库存不足")
				productErrList = append(productErrList, productErr)
				out.FailNum = out.FailNum + 1
				continue
			}
			glog.Info("判断重量是否符合标准，参数：", v.SnapshotId)
			//判断重量是否符合标准
			var newSnap pc.ChannelProductRequest
			var snapShot models.ChannelProductSnapshot
			_, err = NewDbConn().SQL("SELECT id,channel_id,user_no,finance_code,product_id,json_data,create_date FROM dc_product.channel_product_snapshot  WHERE id  = ?;", v.SnapshotId).Get(&snapShot)

			if err != nil {
				glog.Error("查询快照信息失败：", err)
				productErr = append(productErr, "查询快照信息失败")
				productErrList = append(productErrList, productErr)
				out.FailNum = out.FailNum + 1
				continue
			}
			if snapShot.Id == 0 {
				productErr = append(productErr, "不存在快照")
				productErrList = append(productErrList, productErr)
				continue
			}
			err = json.Unmarshal([]byte(snapShot.JsonData), &newSnap)
			if err != nil {
				glog.Error("BatchOnTheShelf方法查询快照信息失败,", err)
				productErr = append(productErr, "没有快照")
				productErrList = append(productErrList, productErr)
				out.FailNum = out.FailNum + 1
				continue
			}
			isWeightOk, err := JudgeChannelStoreProductWeight(newSnap)
			glog.Info("JudgeChannelStoreProductWeight返回结果：", isWeightOk, err)
			if err != nil {
				glog.Error("JudgeChannelStoreProductWeight方法判断商品重量失败,", err)
				productErr = append(productErr, "判断商品重量失败")
				productErrList = append(productErrList, productErr)
				out.FailNum = out.FailNum + 1
				continue
			}
			if !isWeightOk {
				productErr = append(productErr, err.Error())
				productErrList = append(productErrList, productErr)
				out.FailNum = out.FailNum + 1
				continue
			}
			channelStoreProductIds = append(channelStoreProductIds, v.Id)
			out.SuccessNum = out.SuccessNum + 1
		}
	}
	//更新数据库
	//每次更新100条
	smallTotol := 100
	for {
		//处理消息队列信息
		if len(channelStoreProductIds) < smallTotol {
			smallTotol = len(channelStoreProductIds)
		}
		newChannelStorePoductIds := channelStoreProductIds[:smallTotol]
		session.In("id", newChannelStorePoductIds).Cols("up_down_state").Update(&models.ChannelStoreProduct{UpDownState: 1})
		channelStoreProductIds = channelStoreProductIds[smallTotol:]
		if len(channelStoreProductIds) == 0 {
			break
		}
	}

	if out.FailNum > 0 {
		var url string
		url, err = ExportProductErr(productErrList)
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, nil
		}
		out.QiniuUrl = url
	}

	out.Code = 200
	out.Message = ""
	return out, nil
}

// 获取分类商品在指定门店上架快照信息
func (c *Product) GetChannelProductSnapshot(ctx context.Context, in *pc.GetChannelProductSnapshotRequest) (*pc.ChannelProductDetailResponse, error) {
	out := &pc.ChannelProductDetailResponse{
		Code: 400,
	}
	var snapshots []*models.ChannelProductSnapshotExtend
	ss := NewDbConn().Table("channel_store_product").Alias("a").
		Join("inner", "channel_store_product_has_stock c", "a.id = c.channel_store_product_id").
		Join("inner", "channel_product_snapshot b", "a.snapshot_id=b.id").
		Join("left", "channel_category cc", "b.channel_category_id=cc.id").
		Where("a.finance_code=? and a.snapshot_id>0", in.FinanceCode).
		And("a.channel_id=?", in.ChannelId)

	if in.WarehouseId > 0 {
		ss.And("c.warehouse_id=?", in.WarehouseId)
		if in.UpDownState != -1 {
			ss.And("c.has_stock_up=?", in.UpDownState)
		}
	} else {
		if in.UpDownState != -1 {
			ss.And("a.up_down_state=?", in.UpDownState)
		}
	}
	if in.HasStock != 0 {
		ss.And("c.has_stock=1")
	}

	if in.ChannelCategoryId != -1 {
		ss.And("a.channel_category_id=?", in.ChannelCategoryId)
	}
	if len(in.ProductId) > 0 {
		if in.ProductType == 1 {
			ss.NotIn("a.product_id", in.ProductId)
		} else {
			ss.In("a.product_id", in.ProductId)
		}
	}
	if len(in.SkuId) > 0 {
		if in.ProductType == 1 {
			ss.NotIn("a.sku_id", in.SkuId)
		} else {
			ss.In("a.sku_id", in.SkuId)
		}
	}

	//有需要置顶的商品
	if in.TopSkuId > 0 {
		ss.Select("cc.name as channel_last_category_name,b.channel_category_id,b.json_data, case a.sku_id when " + cast.ToString(in.TopSkuId) + " then 1 else 0 end sort").OrderBy("sort desc")
	} else {
		ss.Select("cc.name as channel_last_category_name,b.channel_category_id,b.json_data")
	}

	var err error
	if out.TotalCount, err = ss.Limit(int(in.PageSize), int((in.PageIndex-1)*in.PageSize)).FindAndCount(&snapshots); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}

	for _, v := range snapshots {
		detail := new(pc.ChannelProductDetail)
		if err := json.Unmarshal([]byte(v.JsonData), detail); err != nil {
			glog.Error(err)
			continue
		}
		detail.Product.ChannelLastCategoryName = v.ChannelLastCategoryName
		out.Details = append(out.Details, detail)
	}

	out.Code = 200
	return out, nil
}

// 根据spu或者sku获取商品信息
func (c *Product) GetChannelProductSnapshotBySpuOrSku(ctx context.Context, in *pc.GetChannelProductSnapshotBySpuOrSkuRequest) (*pc.ChannelProductDetailResponse, error) {
	out := &pc.ChannelProductDetailResponse{
		Code: 400,
	}
	var snapshots []models.ChannelProductSnapshot
	ss := NewDbConn().Table("dc_product.channel_product_snapshot").Alias("a").
		Join("inner", "dc_product.channel_store_product b", "a.id = b.snapshot_id").
		Where(" b.channel_id = ? AND b.finance_code = ? ", in.ChannelId, in.FinanceCode)

	if len(in.SkuId) > 0 {
		ss.In(" b.sku_id", in.SkuId)
	} else if len(in.ProductId) > 0 {
		ss.In(" b.product_id", in.ProductId)
	} else {
		ss.Limit(10, 0)
	}
	var err error
	if err = ss.Distinct("a.json_data").Find(&snapshots); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}

	for _, v := range snapshots {
		detail := new(pc.ChannelProductDetail)
		if err := json.Unmarshal([]byte(v.JsonData), detail); err != nil {
			glog.Error(err)
			continue
		}

		out.Details = append(out.Details, detail)
	}

	out.Code = 200
	return out, nil
}

// 根据skuId或productId获取eshop.pro_product商品信息
func (c *Product) GetEshopProductSnapshotBySpuOrSku(ctx context.Context, in *pc.GetChannelProductSnapshotBySpuOrSkuRequest) (out *pc.ChannelProductDetailResponse, err error) {
	logPrefix := fmt.Sprintf("根据skuId或productId获取eshop.pro_product商品信息,入参为%s", kit.JsonEncode(in))
	glog.Info(logPrefix)
	out = &pc.ChannelProductDetailResponse{
		Code: 400,
	}
	out.Details = make([]*pc.ChannelProductDetail, 0)
	if (len(in.SkuId) == 0 && len(in.ProductId) == 0) || in.ChannelId <= 0 {
		errMsg := "获取eshop商品信息失败：入参无效"
		glog.Error(logPrefix, errMsg)

		err = errors.New(errMsg)
		out.Message = errMsg
		return out, err
	}
	db := NewDbConn()

	proSkuMap, skuMap, err := models.GetSkuMapInfo(db, map[string]interface{}{"skuIds": in.SkuId, "productIds": in.ProductId})
	if err != nil {
		errMsg := "获取eshop的商品sku信息失败" + err.Error()
		glog.Error(logPrefix, errMsg)
		err = errors.New(errMsg)
		out.Message = errMsg
		return out, err
	}
	productIds := make([]int32, 0)
	proExistMap := make(map[int32]int32)
	skuIds := make([]int32, 0)
	for _, v := range skuMap {
		if _, ok := proExistMap[v.ProductId]; !ok {
			productIds = append(productIds, v.ProductId)
			proExistMap[v.ProductId] = 1
		}
		skuIds = append(skuIds, v.Id)
	}

	if len(productIds) == 0 || len(skuIds) == 0 {
		errMsg := "获取eshop商品信息失败：商品id为空"
		glog.Error(logPrefix, errMsg)
		err = errors.New(errMsg)
		out.Message = errMsg
		return out, err
	}
	productMap, err := models.GetProductMapInfo(db, map[string]interface{}{"productIds": productIds})
	if err != nil {
		errMsg := "获取eshop商品数据失败" + err.Error()
		glog.Error(logPrefix, errMsg)
		err = errors.New(errMsg)
		out.Message = errMsg
		return out, err
	}

	productChannelAttrInfo, _, err := models.GetProductChannelAttrInfo(db, map[string]interface{}{"productIds": productIds, "channelId": in.ChannelId})
	if err != nil {
		errMsg := "获取eshop的商品渠道属性信息失败" + err.Error()
		glog.Error(logPrefix, errMsg)
		err = errors.New(errMsg)
		out.Message = errMsg
		return out, err
	}
	_, GetProductChannelInfo, err := models.GetProductChannelInfo(db, map[string]interface{}{"productIds": productIds, "channelId": in.ChannelId, "outType": 1})
	if err != nil {
		errMsg := "获取eshop的商品渠道信息失败" + err.Error()
		glog.Error(logPrefix, errMsg)
		err = errors.New(errMsg)
		out.Message = errMsg
		return out, err
	}
	categoryIds := make([]int32, 0)
	for _, v := range productMap {
		categoryIds = append(categoryIds, v.CategoryId)
	}
	GetProductCategory, err := models.GetProductCategory(db, map[string]interface{}{"ids": categoryIds})
	if err != nil {
		errMsg := "获取eshop的商品分类信息失败" + err.Error()
		glog.Error(logPrefix, errMsg)
		err = errors.New(errMsg)
		out.Message = errMsg
		return out, err
	}
	ProProductStoreInfoMap, err := models.GetProductStoreInfoExist(db, map[string]interface{}{"skuIds": skuIds, "channelId": in.ChannelId, "storeId": in.FinanceCode})
	if err != nil {
		errMsg := "获取eshop的门店商品信息失败" + err.Error()
		glog.Error(logPrefix, errMsg)
		err = errors.New(errMsg)
		out.Message = errMsg
		return out, err
	}

	// 获取商品库位信息
	locationMap, err := new(models.Location).GetLocationList(db, models.LocationQuery{
		SkuIds:  skuIds,
		StoreId: in.FinanceCode,
	})
	if err != nil {
		errMsg := "获取商品库位失败" + err.Error()
		glog.Error(logPrefix, errMsg)
		err = errors.New(errMsg)
		out.Message = errMsg
		return out, err
	}
	channelName := enum.ChannelMap[int(in.ChannelId)]
	if in.ChannelId == int32(enum.ChannelAwenId) {
		channelName = "小程序"
	}
	//组织数据
	for productId, product := range productMap {
		sku := make([]*pc.Sku, 0)
		SkuInfo := make([]*pc.SkuInfo, 0)
		ProductAttr := make([]*pc.ChannelProductAttr, 0)

		for _, s := range proSkuMap[productId] {
			k := fmt.Sprintf("%d_%s_%d", in.ChannelId, in.FinanceCode, s.Id)
			sku = append(sku, &pc.Sku{
				Id:            s.Id,
				ProductId:     product.Id,
				RetailPrice:   ProProductStoreInfoMap[k].RetailPrice, //建议价格
				MarketPrice:   ProProductStoreInfoMap[k].RetailPrice,
				BarCode:       s.BarCode,
				WeightForUnit: s.WeightForUnit,
				WeightUnit:    "kg",
				PreposePrice:  s.PreposePrice,
				StorePrice:    s.StorePrice,
				SkuThird:      []*pc.SkuThird{&pc.SkuThird{ThirdSkuId: s.SkuCode, ThirdSpuId: s.SkuCode, SkuId: s.Id, ErpId: 2}},
				SkuValue:      []*pc.SkuValue{&pc.SkuValue{SpecName: "规格", SpecValueValue: s.ProductSpecs}},
				LocationCode:  locationMap[fmt.Sprintf("%s_%d", in.FinanceCode, s.Id)].Code,
			})
			SkuInfo = append(SkuInfo, &pc.SkuInfo{
				RetailPrice:   ProProductStoreInfoMap[k].RetailPrice, //建议价格
				SkuId:         s.Id,
				ProductId:     s.ProductId,
				MarketPrice:   ProProductStoreInfoMap[k].RetailPrice,
				BarCode:       s.BarCode,
				WeightForUnit: s.WeightForUnit,
				WeightUnit:    "kg",
				PreposePrice:  s.PreposePrice,
				StorePrice:    s.StorePrice,
				SkuThird:      []*pc.SkuThird{&pc.SkuThird{ThirdSkuId: s.SkuCode, ThirdSpuId: s.SkuCode, SkuId: s.Id, ErpId: 2}},
				Skuv:          []*pc.SkuValue{&pc.SkuValue{SpecName: "规格", SpecValueValue: s.ProductSpecs}},
				LocationCode:  locationMap[fmt.Sprintf("%s_%d", in.FinanceCode, s.Id)].Code,
			})
		}

		for _, attr := range productChannelAttrInfo[product.Id] {
			ProductAttr = append(ProductAttr, &pc.ChannelProductAttr{
				Id:          attr.Id,
				ProductId:   attr.ProductId, //商品ID
				AttrId:      attr.AttrId,    //属性ID
				AttrName:    attr.AttrName,
				AttrValueId: attr.AttrValueId,
				AttrValue:   attr.AttrValue,
				ChannelId:   attr.ChannelId,
			})
		}

		ChannelProductDetail := pc.ChannelProductDetail{
			Product: &pc.ChannelProduct{
				Id:                product.Id,
				CategoryId:        cast.ToInt32(product.CategoryId),
				BrandId:           cast.ToInt32(product.BrandId), //品牌id
				BrandName:         product.BrandName,
				Name:              product.Name,                        //商品名称
				Code:              product.Code,                        //商品编号
				CreateDate:        product.CreateDate,                  //商品添加日期
				UpdateDate:        product.UpdateDate,                  //商品最后更新日期
				IsDel:             product.IsDel,                       //是否删除
				Pic:               product.Pic,                         //商品图片（多图）
				SellingPoint:      product.SellingPoint,                //商品卖点
				Video:             product.Video,                       //商品视频地址
				ContentPc:         product.ContentPc,                   //电脑端详情内容
				ContentMobile:     product.ContentMobile,               //手机端详情内容
				ProductType:       int32(product.ProductType),          //商品类别（1-实物商品，2-虚拟商品, 3-组合商品）
				CategoryName:      GetProductCategory[int(product.Id)], //分类名称
				ChannelId:         cast.ToString(in.ChannelId),         //渠道id
				ChannelName:       channelName,
				ChannelCategoryId: cast.ToInt32(product.CategoryId),                                //渠道的分类id
				ChannelTagId:      GetProductChannelInfo[product.Id][in.ChannelId].CategoryThirdId, //渠道商品类目id
				//SkuId:                   s.Id,
				ChannelLastCategoryName: GetProductCategory[int(product.Id)], //渠道的第三级分类名称

				Sku:  sku,
				Attr: ProductAttr,
			},
			SkuInfo:     SkuInfo,
			ProductAttr: ProductAttr,
		}

		out.Details = append(out.Details, &ChannelProductDetail)

	}
	glog.Info(logPrefix, "获取宠物saas商品快照信息", kit.JsonEncode(out))
	out.Code = 200
	return out, nil
}

// 根据渠道门店商品表的id查快照数据，提取快照的部分字段，更新到渠道门店商品表
func updateChannelStoreProductFromSnapshot(m models.ChannelStoreProduct, session *xorm.Session) string {
	if m.Id > 0 {
		//传了id，用id查
		if _, err := session.ID(m.Id).Get(&m); err != nil {
			glog.Error(err)
			return err.Error()
		}
	} else if m.FinanceCode != "" && m.ChannelId != 0 && m.ProductId != 0 {
		//传了财务编码、渠道ID、商品ID，用这3个查，这3个组合是唯一的
		if _, err := session.Where("channel_id = ? AND finance_code = ? AND product_id = ?", m.ChannelId, m.FinanceCode, m.ProductId).Get(&m); err != nil {
			glog.Error(err)
			return err.Error()
		}
	} else {
		return "更新查询条件为空"
	}

	if m.SnapshotId == 0 {
		return "门店商品数据不存在"
	}

	//根据快照id获取快照
	var snapshot models.ChannelProductSnapshot
	if _, err := session.ID(m.SnapshotId).Get(&snapshot); err != nil {
		glog.Error(err)
		return err.Error()
	} else if snapshot.Id == 0 {
		return "没有查到快照数据"
	}

	//解析快照数据
	var cpr pc.ChannelProductRequest
	if err := json.Unmarshal([]byte(snapshot.JsonData), &cpr); err != nil {
		glog.Error(err)
		return err.Error()
	}

	//处理类型id存在分类名称为空
	if cpr.Product.ChannelCategoryId > 0 && cpr.Product.ChannelCategoryName == "" {
		var model models.ChannelCategory
		session.Where("id=?", cpr.Product.ChannelCategoryId).Get(&model)
		if model.Id > 0 {
			cpr.Product.ChannelCategoryName = model.Name
		}
	}

	// TODO 问题代码，暂时不删除，做示例讲解
	// if _, err := session.ID(m.Id).Cols("channel_category_id,is_recommend,channel_category_name,name,sku_id").Update(&models.ChannelStoreProduct{ChannelCategoryId: int(cpr.Product.ChannelCategoryId), IsRecommend: int(cpr.Product.IsRecommend), ChannelCategoryName: cpr.Product.ChannelCategoryName, Name: cpr.Product.Name, SkuId: int(cpr.SkuInfo[0].SkuId)}); err != nil {
	// 	glog.Error(err)
	// 	return err.Error()
	// }
	if _, err := session.Exec("UPDATE channel_store_product SET channel_category_id=?,is_recommend=?,channel_category_name=?,name=?,sku_id=? WHERE id=?",
		cpr.Product.ChannelCategoryId, cpr.Product.IsRecommend, cpr.Product.ChannelCategoryName, cpr.Product.Name, cpr.SkuInfo[0].SkuId, m.Id); err != nil {
		glog.Error(err)
		return err.Error()
	}
	return ""
}

func CopyGjProductToChannelProductTask(channelIdSlice, productIDSlice []int32, id int32) {
	str, err := copyGjProductToChannelProduct(context.Background(), productIDSlice, channelIdSlice, "claim", true)
	if err != nil {
		glog.Error(err)
	}
	totalNum := cast.ToInt32(len(productIDSlice))
	failNum := int32(0)
	var excelUrl string
	if str[0][0] == "认领成功" {
		excelUrl = ""
	} else {
		productMap := map[string][]string{}
		for _, k := range str {
			if _, ok := productMap[k[0]]; ok {
				productMap[k[0]][3] = productMap[k[0]][3] + "，" + k[3]

			} else {
				productMap[k[0]] = append(productMap[k[0]], k...)
			}

		}
		failNum = cast.ToInt32(len(productMap))
		//错误信息以excel形式上传至七牛云
		headRow := append([]string{}, "平台商品ID", "SKUID", "商品名称", "失败原因")
		errList := append([][]string{}, headRow)

		for index := range productMap {
			errList = append(errList, productMap[index])
		}

		//errList = append(errList, str...)
		excelUrl, err = ExportProductErr(errList)
		if err != nil {
			glog.Error("错误信息上传失败; err: " + err.Error())
		}
	}

	SuccessNum := totalNum - failNum

	//更新任务状态为已完成
	updateModel := models.TaskList{
		TaskStatus:     3,
		TaskDetail:     "",
		ResulteFileUrl: excelUrl,
		ModifyTime:     time.Now(),
		FailNum:        failNum,
		SuccessNum:     SuccessNum,
	}
	if str[0][0] == "认领成功" {
		updateModel.TaskDetail = "成功"
	} else {
		updateModel.TaskDetail = "部分失败"
	}
	_, err = engine.Id(id).Update(updateModel)
	if err != nil {
		glog.Info("更新任务状态错误", err.Error())
		return
	}
	return
}

// 编辑渠道商品（渠道）价格同步
// 价格同步的UserNo为"priceSync"，只作为记录，
func (c *Product) EditChannelProductPriceSync(ctx context.Context, in *pc.ChannelProductRequest) (*pc.BaseResponse, error) {
	defer func() {
		// 是否有未捕获的异常
		if err := recover(); err != nil {
			glog.Error("周翔错误捕获4", err)
		}
	}()

	out := &pc.BaseResponse{Code: 400}
	//用户校验
	var userInfo models.LoginUserInfo
	userInfo.UserNo = "priceSync"
	in.Product.LastEditUser = userInfo.UserNo
	channelId := cast.ToInt32(in.Product.ChannelId)
	snap_JsonData := ""
	if cast.ToInt32(in.Product.ChannelId) == 3 {
		glog.Info("EditChannelProductPriceSync=", kit.JsonEncode(in))
	}
	if bt, err := json.Marshal(in); err != nil {
		glog.Error("EditChannelProductPriceSync=", err)
	} else {
		snap_JsonData = string(bt)
	}
	if cast.ToInt32(in.Product.ChannelId) == 3 {
		glog.Info("测试ele：EditChannelProductPriceSync", kit.JsonEncode(in))
	}
	glog.Info("priceSync排查价格为0的数据,参数为：", snap_JsonData, "ProductId:", in.Product.Id, "ChannelId：", in.Product.ChannelId)
	var model models.ChannelProductSnapshot
	//如果已存在，则编辑
	_, err := NewDbConn().SQL("SELECT id,channel_id,user_no,finance_code,product_id,json_data,create_date "+
		"FROM dc_product.channel_product_snapshot WHERE finance_code = ? AND product_id = ? AND channel_id=?;",
		in.FinanceCode, in.Product.Id, channelId).Get(&model)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	if model.Id > 0 {
		if _, err := NewDbConn().Id(model.Id).Update(&models.ChannelProductSnapshot{
			ChannelId:   int(channelId),
			UserNo:      userInfo.UserNo,
			FinanceCode: in.FinanceCode,
			ProductId:   in.Product.Id,
			JsonData:    snap_JsonData,
		}); err != nil {
			glog.Error("修改快照信息失败", err)

			out.Code = 400
			out.Message = "修改快照信息失败,err:" + err.Error()
			return nil, err
		}
		out.Details = append(out.Details, cast.ToString(model.Id))
	}
	out.Code = 200
	return out, nil
}

// 编辑渠道商品（渠道）---多账号门店批量编辑
func (c *Product) BatchEditChannelProduct(ctx context.Context, in *pc.ChannelProductRequest) (*pc.BatchBaseResponse, error) {
	out := &pc.BatchBaseResponse{Code: 400}

	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		out.Error = "用户不存在"
		return out, nil
	}

	in.Product.LastEditUser = userInfo.UserNo
	channelId := cast.ToInt32(in.Product.ChannelId)

	clientData := GetDataCenterClient()
	defer clientData.Close()

	platformChannel, err := clientData.RPC.QueryPlatformChannelById(clientData.Ctx, &dac.PlatformChannel{
		Id: channelId,
	})
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		out.Error = err.Error()
		return out, nil
	}
	if len(platformChannel.Data) == 0 {
		out.Message = "渠道未查到"
		out.Error = "渠道未查到"
		return out, nil
	}

	session := NewDbConn().NewSession()
	defer session.Close()
	session.Begin()

	in.Product.ChannelName = platformChannel.Data[0].Name
	in.Product.UpdateDate = time.Now().Format("2006-01-02 15:04:05")

	var sku []models.ChannelSku           //商品SKU
	var skuThird []models.ChannelSkuThird //第三方SKU货号
	var skuGroup []models.ChannelSkuGroup // 组合商品信息
	var skuValue []models.ChannelSkuValue //商品SKU规格组合
	var attr []models.ChannelProductAttr  //商品自定义属性

	//递归查询渠道分类做冗余
	if res, err := c.QueryChannelCategoryRecursion(ctx, &pc.IdRequest{Id: strconv.Itoa(int(in.Product.ChannelCategoryId))}); err != nil {
		glog.Error(err)
	} else {
		in.Product.ChannelCategoryName = c.getCategoryName(0, res.Details)
		c.categoryNames = c.categoryNames[0:0]
	}

	//更新商品主表（此处用map更新是因为用结构体的时候如果遇到可以为0或空的字段时，会更新不成功）
	_, err = session.Table("channel_product").Where("id=? and channel_id=?", in.Product.Id, in.Product.ChannelId).Update(&map[string]interface{}{
		"brand_id":               in.Product.BrandId,
		"name":                   in.Product.Name,
		"pic":                    in.Product.Pic,
		"selling_point":          in.Product.SellingPoint,
		"video":                  in.Product.Video,
		"content_pc":             in.Product.ContentPc,
		"content_mobile":         in.Product.ContentMobile,
		"is_discount":            in.Product.IsDiscount,
		"last_edit_user":         in.Product.LastEditUser,
		"channel_category_id":    in.Product.ChannelCategoryId,
		"channel_category_name":  in.Product.ChannelCategoryName,
		"channel_tag_id":         in.Product.ChannelTagId,
		"channel_name":           platformChannel.Data[0].Name,
		"is_recommend":           in.Product.IsRecommend,
		"update_date":            time.Now().Format("2006-01-02 15:04:05"),
		"brand_name":             in.Product.BrandName,
		"group_type":             in.Product.GroupType,
		"term_type":              in.Product.TermType,
		"term_value":             in.Product.TermValue,
		"use_range":              in.Product.UseRange,
		"virtual_invalid_refund": in.Product.VirtualInvalidRefund,
	})
	if err != nil {
		glog.Error(err)
		session.Rollback()
		out.Message = "事务更新失败，err:" + err.Error()
		out.Error = "事务更新失败，err:" + err.Error()
		return out, err
	}

	//删除ChannelSku相关记录
	if _, err := session.Where("product_id=?", in.Product.Id).And("channel_id=?", in.Product.ChannelId).Delete(&models.ChannelSku{}); err != nil {
		glog.Error(err)
		session.Rollback()
		out.Message = "事务删除失败，err:" + err.Error()
		out.Error = "事务删除失败，err:" + err.Error()
		return out, err
	}

	//删除ChannelSkuThird相关记录
	if _, err := session.Where("product_id=?", in.Product.Id).And("channel_id=?", in.Product.ChannelId).Delete(&models.ChannelSkuThird{}); err != nil {
		glog.Error(err)
		session.Rollback()
		out.Message = "事务删除失败，err:" + err.Error()
		out.Error = "事务删除失败，err:" + err.Error()
		return out, err
	}

	//删除ChannelSkuGroup相关记录
	if _, err := session.Where("product_id=?", in.Product.Id).And("channel_id=?", in.Product.ChannelId).Delete(&models.ChannelSkuGroup{}); err != nil {
		glog.Error(err)
		session.Rollback()
		out.Message = "事务删除失败，err:" + err.Error()
		out.Error = "事务删除失败，err:" + err.Error()
		return out, err
	}

	//删除ChannelSkuValue相关记录
	if _, err := session.Where("product_id=?", in.Product.Id).And("channel_id=?", in.Product.ChannelId).Delete(&models.ChannelSkuValue{}); err != nil {
		glog.Error(err)
		session.Rollback()
		out.Message = "事务删除失败，err:" + err.Error()
		out.Error = "事务删除失败，err:" + err.Error()
		return out, err
	}

	//商品自定义属性
	if len(in.ProductAttr) > 0 {
		for _, v := range in.ProductAttr {
			attr = append(attr, models.ChannelProductAttr{
				ProductId:   in.Product.Id,
				AttrId:      v.AttrId,
				AttrName:    v.AttrName,
				AttrValueId: v.AttrValueId,
				AttrValue:   v.AttrValue,
				ChannelId:   channelId,
			})
		}

		//删除之前属性记录
		if _, err := session.Where("product_id=?", in.Product.Id).And("channel_id=?", in.Product.ChannelId).Delete(&models.ChannelProductAttr{}); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务删除失败，err:" + err.Error()
			out.Error = "事务删除失败，err:" + err.Error()
			return out, err
		}

		//批量添加新的属性
		if _, err := session.Insert(&attr); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务新增失败，err:" + err.Error()
			out.Error = "事务新增失败，err:" + err.Error()
			return out, err
		}
	}

	//规格排序
	skuValuesort := int32(1)

	//SKU相关信息整理
	for _, v := range in.SkuInfo {
		var skuId int32
		skuId = v.SkuId
		skuModel := models.ChannelSku{
			Id:            skuId,
			ProductId:     in.Product.Id,
			MarketPrice:   v.MarketPrice,
			PriceUnit:     v.PriceUnit,
			RetailPrice:   v.RetailPrice,
			ChannelId:     channelId,
			IsUse:         v.IsUse,
			WeightForUnit: v.WeightForUnit,
			WeightUnit:    v.WeightUnit,
			MinOrderCount: v.MinOrderCount,
			BarCode:       v.BarCode,
			PreposePrice:  v.PreposePrice,
			StorePrice:    v.StorePrice,
		}
		if channelId == ChannelDigitalHealth {
			var digitalHealthPrice models.HospitalProductPrice
			_, err := engine.Where("sku_id=?", skuId).Get(&digitalHealthPrice)
			if err != nil {
				out.Message = "获取互联网价格失败，err:" + err.Error()
				out.Error = "事务新增失败，err:" + err.Error()
				return out, err
			}
			if digitalHealthPrice.Id > 0 {
				skuModel.StorePrice = cast.ToInt32(digitalHealthPrice.Price)
				skuModel.PreposePrice = cast.ToInt32(digitalHealthPrice.Price)
			}
		}

		sku = append(sku, skuModel)

		//第三方货号
		for _, t := range v.SkuThird {
			third := models.ChannelSkuThird{
				SkuId:      skuId,
				ThirdSkuId: t.ThirdSkuId,
				ThirdSpuId: t.ThirdSpuId,
				ErpId:      t.ErpId,
				ProductId:  in.Product.Id,
				ChannelId:  channelId,
				IsUse:      t.IsUse,
			}

			if t.ThirdSkuId != "" || t.ThirdSpuId != "" {
				third.ThirdSpuSkuId = fmt.Sprintf("%s,%s", t.ThirdSpuId, t.ThirdSkuId)
			} else {
				continue
			}

			skuThird = append(skuThird, third)
		}

		//组合商品信息
		for _, g := range v.SkuGroup {
			group := models.ChannelSkuGroup{
				ProductId:      int(g.ProductId),
				SkuId:          int(skuId),
				GroupProductId: int(g.GroupProductId),
				GroupSkuId:     int(g.GroupSkuId),
				Count:          int(g.Count),
				DiscountType:   int(g.DiscountType),
				DiscountValue:  int(g.DiscountValue),
				MarketPrice:    int(g.MarketPrice),
				ChannelId:      int(g.ChannelId),
				ProductType:    int(g.ProductType),
			}

			skuGroup = append(skuGroup, group)
		}

		//SKU规格组合
		for _, s := range v.Skuv {
			skuValue = append(skuValue, models.ChannelSkuValue{
				SpecId:      s.SpecId,
				SpecValueId: s.SpecValueId,
				SkuId:       skuId,
				ProductId:   in.Product.Id,
				Pic:         s.Pic,
				Sort:        skuValuesort,
				ChannelId:   channelId,
			})
			skuValuesort++
		}
	}

	if len(sku) > 0 {
		if _, err := session.Insert(&sku); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务新增失败，err:" + err.Error()
			out.Error = "事务新增失败，err:" + err.Error()
			return out, err
		}
	}

	//第三方SKU货号
	if len(skuThird) > 0 {
		if _, err := session.Insert(&skuThird); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务新增失败，err:" + err.Error()
			out.Error = "事务新增失败，err:" + err.Error()
			return out, err
		}
	}

	//组合商品
	if len(skuGroup) > 0 {
		if _, err := session.Insert(&skuGroup); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务新增失败，err:" + err.Error()
			out.Error = "事务新增失败，err:" + err.Error()
			return out, err
		}
	}

	//商品SKU规格组合
	if len(skuValue) > 0 {
		if _, err := session.Insert(&skuValue); err != nil {
			glog.Error(err)
			session.Rollback()
			out.Message = "事务新增失败，err:" + err.Error()
			out.Error = "事务新增失败，err:" + err.Error()
			return out, err
		}
	}

	//只有总账号和多门店账号更新渠道商品信息
	var shopList []string
	if len(userInfo.FinancialCode) == 0 {
		if userInfo.IsGeneralAccount {
			if err := session.Commit(); err != nil {
				session.Rollback()
				glog.Error(err)
				out.Message = "事务提交失败，err:" + err.Error()
				out.Error = "事务提交失败，err:" + err.Error()
				return out, err
			}
		} else {
			session.Rollback()
		}
		var isLogo int32
		if userInfo.IsGeneralAccount {
			isLogo = 1
		}
		out_result, err := clientData.RPC.GetHospitalListByUserNo(clientData.Ctx, &dac.GetHospitalListByUserNoRequest{
			UserNo:    userInfo.UserNo,
			ChannelId: channelId,
			IsLogo:    isLogo,
		})
		if err != nil {
			out.Message = "请求GetHospitalListByUserNo失败"
			out.Error = err.Error()
			out.Code = 400
			return out, nil
		}
		for _, v := range out_result.Data {
			shopList = append(shopList, v.StructOuterCode)
		}
	}

	//写入商品快照
	//bt, err := json.Marshal(in)
	//if err != nil {
	//	glog.Error(err)
	//}
	//

	snap := pc.ChannelProductSnapshot{
		ChannelId: cast.ToInt32(in.Product.ChannelId),
		ProductId: in.Product.Id,
		UserNo:    userInfo.UserNo,
	}

	var params_excel [][]string
	request := 0 //记录执行总数
	err_int := 0 //记录执行失败总数

	//京东渠道总账更改快照时不需要更改前置仓价格和门店仓价格，而多门店账号需要
	if userInfo.FinancialCode == "" {
		if len(shopList) > 0 {
			//优化sql
			var channel_product_snapshot_list []models.ChannelProductSnapshot
			engine.Where("channel_id=?", channelId).And("product_id=?", in.Product.Id).In("finance_code", shopList).Cols("finance_code,json_data").Find(&channel_product_snapshot_list)
			channel_product_snapshot_map := make(map[string]string, len(channel_product_snapshot_list))
			for _, v := range channel_product_snapshot_list {
				channel_product_snapshot_map[v.FinanceCode] = v.JsonData
			}

			warehouseCategoryMap := make(map[string]int32)
			//根据财务编码获取门店对应的仓库
			//dcClinet := GetDispatchClient() //一个商品一个连接(控制连接数量，如果商品数量过多，还得进一步优化，目前重新认领是单个商品)
			//defer dcClinet.Close()
			//warehouseList, err := dcClinet.RPC.GetWarehouseInfoByFanceCodes(context.Background(), &dc.GetWarehouseInfoByFanceCodesRequest{
			//	FinanceCode: shopList})
			//glog.Info("GetWarehouseInfoByFanceCodes根据财务编码批量查询仓库类型：", shopList)
			//if err != nil {
			//	err = errors.New(utils.RunFuncName() + "GetWarehouseInfoByFanceCodes根据财务编码批量查询仓库类型失败：" + err.Error())
			//	out.Message = "GetWarehouseInfoByFanceCodes根据财务编码批量查询仓库类型失败"
			//	out.Error = err.Error()
			//	out.Code = 400
			//	return out, nil
			//}
			product := new(Product)
			resp, err := product.GetChannelWarehouses(shopList, channelId)
			if err != nil {
				err = errors.New(utils.RunFuncName() + "GetWarehouseInfoByFanceCodes根据财务编码批量查询仓库类型失败：" + err.Error())
				out.Message = "GetWarehouseInfoByFanceCodes根据财务编码批量查询仓库类型失败"
				out.Error = err.Error()
				out.Code = 400
				return out, nil
			}
			if channelId == ChannelAwenId { // 如果是阿闻渠道并且查询阿闻外卖没有查询到关联关系则查询竖屏自提
				data, err := product.GetChannelWarehouses(shopList, ChannelAwenPickUpId)
				if err != nil {
					err = errors.New(utils.RunFuncName() + "GetWarehouseInfoByFanceCodes根据财务编码批量查询仓库类型失败：" + err.Error())
					out.Message = "GetWarehouseInfoByFanceCodes根据财务编码批量查询仓库类型失败"
					out.Error = err.Error()
					out.Code = 400
				}

				resp = append(resp, data...)
			}
			for _, ware := range resp {
				warehouseCategoryMap[ware.ShopId] = int32(ware.Category)
			}

			var syncPriceShopList []string
			for _, v := range shopList {
				request++
				var exl_str []string
				exl_str = append(exl_str, v)
				exl_str = append(exl_str, cast.ToString(in.Product.Id))
				var jsonData string

				// 由于in是指针类型，需每次序列化后赋值，方便独立保留价格
				var channelProductUpdate pc.ChannelProductRequest
				if bt, err := json.Marshal(in); err != nil {
					glog.Error(err)
					err_int++
					exl_str = append(exl_str, "序列化失败失败"+err.Error())
					params_excel = append(params_excel, exl_str)
					continue
				} else {
					err = json.Unmarshal(bt, &channelProductUpdate)
					if err != nil {
						glog.Error(err)
						err_int++
						exl_str = append(exl_str, "序列化失败失败"+err.Error())
						params_excel = append(params_excel, exl_str)
						continue
					}
				}

				if channelId == ChannelElmId && len(channelProductUpdate.SkuInfo[0].BarCode) == 0 {
					channelProductUpdate.SkuInfo[0].BarCode = cast.ToString(channelProductUpdate.SkuInfo[0].SkuId)
				}
				var channelProductReq pc.ChannelProductRequest
				if bt, err := json.Marshal(&channelProductUpdate); err != nil {
					glog.Error(err)
					err_int++
					exl_str = append(exl_str, "序列化失败失败"+err.Error())
					params_excel = append(params_excel, exl_str)
					continue
				} else {
					snap.JsonData = string(bt)
				}
				if res, ok := channel_product_snapshot_map[v]; ok {
					jsonData = res
				} else {
					snap.FinanceCode = v
					_, err := c.NewChannelProductSnapshot(ctx, &snap)
					if err != nil {
						err_int++
						exl_str = append(exl_str, "创建商品快照失败"+err.Error())
						params_excel = append(params_excel, exl_str)
					} else if warehouseCategoryMap[v] == 4 {
						syncPriceShopList = append(syncPriceShopList, v)
					}
					continue
				}
				err = json.Unmarshal([]byte(jsonData), &channelProductReq)
				if err != nil {
					glog.Error(err)
					err_int++
					exl_str = append(exl_str, "反序列化失败失败"+err.Error())
					params_excel = append(params_excel, exl_str)
				}

				// 门店仓多门店编辑时保留原价
				channelProductUpdate.SkuInfo[0].StorePrice = channelProductReq.SkuInfo[0].StorePrice
				//京东和饿了么渠道总账号保留每家门店的价格
				if userInfo.IsGeneralAccount && (channelId == ChannelJddjId || channelId == ChannelElmId) {
					channelProductUpdate.SkuInfo[0].PreposePrice = channelProductReq.SkuInfo[0].PreposePrice
				}
				//如果是总账户的话，编辑的时候不更新力荐和起购数
				if userInfo.IsGeneralAccount {
					channelProductUpdate.SkuInfo[0].MinOrderCount = channelProductReq.SkuInfo[0].MinOrderCount
					channelProductUpdate.Product.IsRecommend = channelProductReq.Product.IsRecommend
				}
				if bt, err := json.Marshal(&channelProductUpdate); err != nil {
					glog.Error(err)
					err_int++
					exl_str = append(exl_str, "序列化失败失败"+err.Error())
					params_excel = append(params_excel, exl_str)
				} else {
					snap.JsonData = string(bt)
				}
				snap.FinanceCode = v
				//_, err = c.NewChannelProductSnapshot(ctx, &snap)
				_, err = newChannelProductSnapshot(ctx, &snap, warehouseCategoryMap[v])
				if err != nil {
					err_int++
					exl_str = append(exl_str, "更新商品快照失败"+err.Error())
					params_excel = append(params_excel, exl_str)
				} else if warehouseCategoryMap[v] == 4 {
					syncPriceShopList = append(syncPriceShopList, v)
				}

				//京东渠道多账号更新价格
				if !userInfo.IsGeneralAccount && channelId == ChannelJddjId {
					//financeCodeStr := strings.Join(shopList, ",")
					priceIn := &pc.SyncJddjPriceRequest{
						OutSkuId:     in.SkuInfo[0].SkuId,
						StorePrice:   in.SkuInfo[0].StorePrice,
						PreposePrice: in.SkuInfo[0].PreposePrice,
						FinanceCode:  v,
					}
					res, err := c.SyncJddjPrice(ctx, priceIn)
					if err != nil {
						glog.Error(err)
						err_int++
						exl_str = append(exl_str, "批量同步京东到家门店商品价格失败，err:"+err.Error())
						params_excel = append(params_excel, exl_str)
					} else if res.Code != 200 {
						glog.Info(res.Message)
						err_int++
						exl_str = append(exl_str, "批量同步京东到家门店商品价格失败err:"+res.Message)
						params_excel = append(params_excel, exl_str)
					} else {
						glog.Info("批量同步完成")
					}
				}
			}
			go func(syncPriceShopList []string, in *pc.ChannelProductRequest) {
				c.UpdatePriceCommonBatch(context.Background(), syncPriceShopList, in)
			}(shopList, in)
		}
	}

	if err_int > 0 {
		var url string
		url, err = ExportProductErr(params_excel)
		if err != nil {
			glog.Error("错误信息上传失败; err: " + err.Error())
			out.Message = "错误信息上传失败; err: " + err.Error()
			out.Error = "错误信息上传失败; err: " + err.Error()
		}
		out.QiniuUrl = url
	}
	out.SuccessNum = int32(request) - int32(err_int)
	out.FailNum = int32(err_int)
	out.Code = 200
	return out, nil
}

// 根据渠道分类id获取渠道名称
func (c *ChannelProduct) GetChannelCategoryById(channelCategoryId int32) (models.ChannelCategory, error) {
	model := models.ChannelCategory{}
	NewDbConn().Select("id,channel_id,name,parent_id,sort,original_data,create_date,update_date").In("id", channelCategoryId).Get(&model)
	return model, nil
}

func (c *ChannelProduct) GetChannelCategoryCode(channelCategoryId, channel_id int32, FinanceCode string) (string, error) {
	var category_id string
	//先获取门店的master_id
	infos, err := GetThirdStoreMaster(channel_id, FinanceCode)
	if err != nil {
		return category_id, err
	}

	channel_store_id := ""
	if len(infos) > 0 {
		channel_store_id = infos[0].ChannelStoreId
	}

	// 获取第三方的分类id
	if len(channel_store_id) > 0 {
		NewDbConn().SQL("select category_id from dc_product.channel_category_thirdid cct "+
			"where channel_id = ? and id = ?  and channel_store_id = ? ", channel_id, channelCategoryId, channel_store_id).Get(&category_id)

	}
	return category_id, nil

}

// 查询同步到ES的商品信息
func (c *Product) GetChannelProductEsBaseData(ctx context.Context, in *pc.ChannelProductEsBaseDataRequest) (*pc.ChannelProductEsBaseDataResponse, error) {
	out := new(pc.ChannelProductEsBaseDataResponse)
	out.Code = 400

	sql := "SELECT channel_store_product.id, channel_store_product.update_date, channel_store_product.sales_volume, channel_store_product.channel_id, channel_store_product.finance_code, channel_store_product.sku_id, channel_store_product.up_down_state, REPLACE ( REPLACE ( CONCAT( species, ',', varieties, ',', sex, ',', shape, ',', age, ',', special_stage, ',', is_sterilization, ',', content_type, ',', `status` ), '不限,', '' ), ',不限', '' ) AS tags, channel_product_snapshot.json_data, channel_store_product_has_stock.warehouse_id, channel_store_product_has_stock.has_stock FROM channel_store_product INNER JOIN channel_product_snapshot ON channel_product_snapshot.id = channel_store_product.snapshot_id LEFT JOIN product_tag ON product_tag.sku_id = channel_store_product.sku_id AND product_tag.product_type = 3 INNER JOIN channel_store_product_has_stock ON channel_store_product_has_stock.channel_store_product_id = channel_store_product.id WHERE channel_store_product.channel_id = 1 "

	if in.FinanceCode != "" {
		sql += fmt.Sprintf(" AND channel_store_product.finance_code = '%s'", in.FinanceCode)
	}

	if in.UpdateTime != "" {
		sql += fmt.Sprintf(" AND channel_store_product.update_date > '%s'", in.UpdateTime)
	}

	if in.PageIndex > 0 && in.PageSize > 0 {
		sql += fmt.Sprintf(" limit %d,%d", in.PageIndex*in.PageSize-in.PageSize, in.PageSize)
	}

	if err := engine.SQL(sql).Find(&out.Details); err != nil {
		glog.Error(err)
		return nil, err
	}

	out.Code = 200

	// glog.Info("GetChannelProductEsBaseData log: ", kit.JsonEncode(out.Details))
	return out, nil
}

// 更新门店仓价格
func UpdateZlPrice(financeCode string, skuId, channelId int32) (price int32) {
	var channelProduct ChannelProductPriceSync
	channelProduct.ChannelId = int(channelId)
	channelProduct.FinanceCode = financeCode
	channelProduct.ProductSkuId = cast.ToString(skuId)
	channelProduct.SyncPrice()
	price = int32(channelProduct.ProductSyncPrice)
	return
}

// 更新前置仓价格
func UpdateQzPrice(financeCode string, id, skuId, channelId int32) (price int32) {
	var channelProduct ChannelProductPriceSync
	channelProduct.ChannelId = int(channelId)
	channelProduct.FinanceCode = financeCode
	channelProduct.ProductSkuId = cast.ToString(skuId)
	channelProduct.SyncPrice()
	price = int32(channelProduct.ProductSyncPrice)
	return
}

// 组合商品库存通知
func (c *Product) GroupProductStockNotice(ctx context.Context, in *pc.StockNoticeRequest) (*pc.BaseResponse, error) {
	out := &pc.BaseResponse{Code: 400}
	// 这里不用协程，会有死锁
	for _, v := range in.StockInfo {
		for _, v2 := range v.Skus {
			_, _, err := updateHasStock(v2.SkuId, v2.Stock, v.FinanceCode)
			if err != nil {
				continue
				glog.Error(err)
				//return out, err
			}
		}
	}

	out.Code = 200
	return out, nil

	// var wg sync.WaitGroup
	// ch := make(chan int, 10)

	// for _, v := range in.StockInfo {
	// 	for _, v2 := range v.Skus {
	// 		wg.Add(1)
	// 		ch <- 1
	// 		go func(skuid, stock int32, finaceCode string) {
	// 			defer func() {
	// 				<-ch
	// 				wg.Done()
	// 			}()
	// 			_, err := updateHasStock(skuid, stock, finaceCode)
	// 			if err != nil {
	// 				glog.Error(err)
	// 			}
	// 		}(v2.SkuId, v2.Stock, v.FinanceCode)
	// 	}
	// }
	// wg.Wait()
	// return &pc.BaseResponse{Code: 200}, nil
}

// ImportA8Price 批量导入A8价格
// 暂不考虑批量查询、插入，不便于处理错误，导出场景并不常用且数据不大
func ImportA8Price(task *pc.TaskList) (rsUrl string, success int32, fail int32, err error) {
	resp, err := http.Get(task.OperationFileUrl)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	file, err := excelize.OpenReader(resp.Body)
	if err != nil {
		return
	}
	sheetName := file.GetSheetName(0)
	rows, err := file.Rows(sheetName)
	if err != nil {
		return
	}

	writer, err := file.NewStreamWriter(sheetName)
	if err != nil {
		return
	}
	_ = writer.SetRow("A1", []interface{}{
		"前置仓仓库id", "A8编码", "价格(元)", "是否导入成功", "导入失败原因",
	})

	db := NewDbConn()

	for i := 0; rows.Next(); i++ {
		// 注意这里一定要读取行，不然内容会附加到下一行
		row, err := rows.Columns()

		switch len(row) {
		case 0:
			row = []string{"", "", ""}
		case 1:
			row = append(row, []string{"", ""}...)
		case 2:
			row = append(row, "")
		}

		if err != nil && i > 0 {
			_ = writer.SetRow(fmt.Sprintf("A%d", i+1), []interface{}{row[0], row[1], row[2], "否", err.Error()})
			fail++
			continue
		}
		// 表头不处理
		if i == 0 {
			continue
		}

		if err = importA8PriceHandleRow(db, task, row); err != nil {
			_ = writer.SetRow(fmt.Sprintf("A%d", i+1), []interface{}{row[0], row[1], row[2], "否", err.Error()})
			fail++
		} else {
			_ = writer.SetRow(fmt.Sprintf("A%d", i+1), []interface{}{row[0], row[1], row[2], "是"})
			success++
		}
	}

	_ = rows.Close()
	_ = writer.Flush()

	rsUrl, err = utils.UploadExcelToQiNiu(file, "")
	if err != nil {
		return "", success, fail, errors.New("上传excel失败 " + err.Error())
	}

	return
}

// importA8PriceHandleRow 处理a8价格导入行
func importA8PriceHandleRow(db *xorm.Engine, task *pc.TaskList, row []string) (err error) {
	// 验证、清理参数
	if len(row) < 3 {
		return errors.New("参数错误")
	}
	wId := cast.ToInt(strings.TrimSpace(row[0]))
	sId := strings.TrimSpace(row[1])
	price := cast.ToInt(math.Round(cast.ToFloat64(strings.TrimSpace(row[2])) * 100))

	if wId < 1 || len(sId) == 0 || price <= 0 {
		return errors.New("参数错误")
	}

	session := db.NewSession()
	defer session.Close()
	_ = session.Begin()

	// 处理行
	existPrice := new(models.QzcPriceSync)
	if _, err = session.Table("dc_product.qzc_price_sync").Where("warehouse_id = ? and third_sku_id = ?", wId, sId).
		Select("id,sku_id,price").Get(existPrice); err != nil {
		return
	} else if existPrice.Id > 0 { // 存在的更新处理
		if existPrice.Price == price { // 价格没变化不处理
			return
		}

		if _, err = session.ID(existPrice.Id).Update(&models.QzcPriceSync{
			Price:          price,
			UpdateUserName: task.CreateName,
		}); err != nil {
			_ = session.Rollback()
			return
		}

		if _, err = session.Insert(&models.QzcPriceSyncRecord{
			Type:        1,
			WarehouseId: wId,
			ThirdSkuId:  sId,
			Operation: fmt.Sprintf("批量修改商品skuid:%d的价格，从%s元改为%s元",
				existPrice.SkuId,
				decimal.NewFromFloat(float64(existPrice.Price)/100).Round(2).String(),
				decimal.NewFromFloat(float64(price)/100).Round(2).String(),
			),
			UserNo:   task.CreateId,
			UserName: task.CreateName,
			UserIp:   task.CreateIp,
		}); err != nil {
			_ = session.Rollback()
			return
		}

		err = session.Commit()
		return
	}

	// 不存在的插入处理
	qzcPrice := new(models.QzcPriceSync)
	if _, err = session.Table("dc_dispatch.warehouse").Alias("w").
		Join("left", "dc_product.sku_third t", fmt.Sprintf("t.third_sku_id = '%s' and erp_id =2", sId)).
		Select(`w.id as warehouse_id,w.name as warehouse_name,t.third_sku_id,t.sku_id,t.product_id`).
		Where("w.id = ?", wId).Get(qzcPrice); err != nil {
		return
	}

	if qzcPrice.WarehouseId == 0 {
		return errors.New("仓库Id错误")
	} else if qzcPrice.SkuId == 0 {
		return errors.New("货号不存在")
	}

	qzcPrice.Price = price
	qzcPrice.CreateUserName = task.CreateName

	if _, err = session.Insert(qzcPrice); err != nil {
		_ = session.Rollback()
		return
	}

	err = session.Commit()
	return
}

// ExportA8Price 导出前置仓价格
func ExportA8Price(in *pc.A8PriceInfoRequest) (url string, count int32, err error) {
	db := NewDbConn()
	var warehouses []models.Warehouse

	subQuery := "SELECT 1 FROM dc_product.qzc_price_sync p WHERE p.warehouse_id = w.id "
	var subArgs []interface{}
	if len(in.ThirdSkuId) > 0 {
		subQuery += " and p.third_sku_id LIKE ?"
		subArgs = append(subArgs, in.ThirdSkuId+"%")
	}
	wq := db.Table("dc_dispatch.warehouse").Alias("w").
		Where(fmt.Sprintf("exists (%s)", subQuery), subArgs...)
	if len(in.WarehouseName) > 0 {
		wq.Where("name like ?", "%"+in.WarehouseName+"%")
	}
	if in.WarehouseId > 0 {
		wq.Where("id = ?", in.WarehouseId)
	}
	if err = wq.Select("id,name").Find(&warehouses); err != nil {
		return
	}

	file := excelize.NewFile()
	writer, _ := file.NewStreamWriter("Sheet1")

	_ = writer.SetRow("A1", []interface{}{
		"仓库Id", "仓库名称", "A8货号", "SkuId", "商品价格（元）",
	})

	pageSize := 50000
	sheetNum := int32(1)
	line := 1 // sheet写入行号

	for _, warehouse := range warehouses {
		// 单个sheet最多50万数据，按仓库断开
		if (float64(count) / float64(500000)) > float64(sheetNum) {
			_ = writer.Flush()
			sheetNum++
			sheetName := fmt.Sprintf("Sheet%d", sheetNum)
			file.NewSheet(sheetName)
			writer, _ = file.NewStreamWriter(sheetName)
			_ = writer.SetRow("A1", []interface{}{
				"仓库Id", "仓库名称", "A8货号", "SkuId", "商品价格（元）",
			})
			line = 1
		}

		pageIndex := 1
		query := db.Table("qzc_price_sync").Where("warehouse_id = ?", warehouse.Id)
		if len(in.ThirdSkuId) > 0 {
			query.Where("third_sku_id like ?", in.ThirdSkuId+"%")
		}

		for {
			var prices []*models.QzcPriceSync
			if err = query.Clone().Select("third_sku_id,sku_id,price").
				Desc("id").Limit(pageSize, pageSize*(pageIndex-1)).Find(&prices); err != nil {
				return
			}
			for _, price := range prices {
				count++
				line++
				_ = writer.SetRow(fmt.Sprintf("A%d", line), []interface{}{
					warehouse.Id, warehouse.Name, price.ThirdSkuId, price.SkuId,
					cast.ToFloat64(price.Price) / 100,
				})
			}

			// 不足一页，说明没有下一页
			if len(prices) < pageSize {
				break
			}

			pageIndex++
		}
	}

	_ = writer.Flush()

	url, err = utils.UploadExcelToQiNiu(file, "")
	return
}

// ImportA8Price 导入上传的前置仓价格信息
func (c *Product) ImportA8Price(ctx context.Context, in *pc.ImportA8PriceRequest) (out *pc.ImportA8PriceResponse, e error) {
	out = &pc.ImportA8PriceResponse{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("ChannelProduct ImportA8Price 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	// 用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return
	}

	// 下载excel
	req, err := http.NewRequest("POST", in.QiniuUrl, nil)
	if err != nil {
		out.Message = err.Error()
		return
	}
	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		out.Message = err.Error()
		return
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = err.Error()
		return
	}

	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, _ := f.GetRows(sheetName)

	// excel为空
	if len(rows) <= 1 {
		out.Message = "请导入前置仓价格信息"
		return
	}

	var repeatPriceMap = make(map[string]int32)
	for i, row := range rows {
		if i == 0 {
			continue
		}
		price, err := cast.ToInt32E(cast.ToFloat64(row[2]) * 100)
		if err != nil || price >= 100000000 {
			out.Message = "导入失败，文件中有商品价格超出最大限制，请核查后再次导入"
			return
		}
		if price <= 0 {
			out.Message = "导入失败，文件中有商品价格为空或者为0或者格式不正确，请核查后再次导入"
			return
		}
		_, err = cast.ToInt32E(row[0])
		if err != nil {
			out.Message = "导入失败，文件中有格式不正确的仓库id，请核查后再次导入"
			return
		}
		if len(strings.TrimSpace(row[1])) == 0 {
			out.Message = "导入失败，文件中有格式不正确的A8货号，请核查后再次导入"
			return
		}
		if _, ok := repeatPriceMap[row[0]+":"+row[1]]; !ok {
			repeatPriceMap[row[0]+":"+row[1]] = price
		} else {
			out.Message = "导入失败，文件中有重复数据，请核查后再次导入"
			return
		}
	}

	userInfoJson, _ := json.Marshal(userInfo)

	// 保存任务信息
	if _, err = NewDbConn().Insert(&models.TaskList{
		ChannelId:        in.HandleType,
		TaskContent:      19,
		TaskStatus:       1,
		OperationFileUrl: in.QiniuUrl,
		RequestHeader:    string(userInfoJson),
		Status:           1,
		ModifyId:         userInfo.UserNo,
		ModifyTime:       time.Now(),
		CreateId:         userInfo.UserNo,
		CreateTime:       time.Now(),
		CreateName:       userInfo.UserName,
		CreateMobile:     userInfo.Mobile,
		CreateIp:         in.Ip,
	}); err != nil {
		out.Message = "保存批量导入前置仓价格任务信息失败 " + err.Error()
		return
	}

	out.Message = "批量导入前置仓价格任务进行中..."
	out.Code = 200
	return
}

// QueryQzcOperationRecord 查询操作记录表
func (c *Product) QueryQzcOperationRecord(ctx context.Context, in *pc.QueryA8PriceRecordRequest) (out *pc.QueryA8PriceRecordResponse, e error) {
	out = &pc.QueryA8PriceRecordResponse{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("ChannelProduct QueryQzcOperationRecord 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.StartTime) > 0 && len(in.StartTime) <= 10 {
		in.StartTime = in.StartTime + " 00:00:00"
	}
	if len(in.EndTime) > 0 && len(in.EndTime) <= 10 {
		in.EndTime = in.EndTime + " 23:59:59"
	}

	conn := NewDbConn()
	session := conn.Table("qzc_price_sync_record").Alias("r").
		Join("left", "dc_dispatch.warehouse w", "w.id = r.warehouse_id")

	if len(in.StartTime) > 0 && len(in.EndTime) > 0 {
		session.Where("create_time between ? and ?", in.StartTime, in.EndTime)
	}

	if in.Promoter == 0 {
		// 用户校验
		userInfo := loadLoginUserInfo(ctx)
		if userInfo == nil {
			out.Message = "用户不存在"
			return out, nil
		}
		session.Where("user_no = ?", userInfo.UserNo)
	}
	// 仓库筛选
	in.Warehouse = strings.TrimSpace(in.Warehouse)
	if len(in.Warehouse) > 0 {
		session.Where("w.code like ? or w.name like ?", "%"+in.Warehouse+"%", "%"+in.Warehouse+"%")
	}

	// 货号筛选
	in.Sku = strings.TrimSpace(in.Sku)
	if len(in.Sku) > 0 {
		session.Where("third_sku_id like ?", "%"+in.Sku+"%")
	}

	pr := utils.PaginateReq{
		Count: session.Clone(),
		List: session.Desc("r.id").Select(`create_time,case type when 2 then '移除商品' else '编辑商品' end type, 
w.name as warehouse_name,w.code as warehouse_code,r.operation,r.user_name,r.user_ip`),
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}

	if err := pr.Paginate(&out.Total, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// QueryQzcPriceList 查询前置仓价格表信息
func (c *Product) QueryQzcPriceList(ctx context.Context, in *pc.A8PriceInfoRequest) (out *pc.A8PriceInfoResponse, e error) {
	out = &pc.A8PriceInfoResponse{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("ChannelProduct QueryQzcPriceList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	conn := NewDbConn()
	session := conn.Table("qzc_price_sync").Alias("p").
		Join("left", "dc_dispatch.warehouse w", "w.id = p.warehouse_id")
	if len(in.WarehouseName) > 0 {
		session.Where("w.name like ?", "%"+in.WarehouseName+"%")
	}
	if in.WarehouseId > 0 {
		session.Where("p.warehouse_id = ?", in.WarehouseId)
	}

	if len(in.ThirdSkuId) > 0 {
		session.Where("p.third_sku_id like ?", in.ThirdSkuId+"%")
	}

	pr := utils.PaginateReq{
		Count: session.Clone(),
		List: session.Desc("p.id").Select(`p.id,p.warehouse_id,w.name as warehouse_name,p.third_sku_id,p.sku_id,
p.product_id,p.price,p.create_time,if(p.last_time = p.create_time,'',p.last_time) as last_time,p.create_user_name,p.update_user_name`),
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// UpdateQzcPrice 编辑前置仓价格（需写入操作记录表）
func (c *Product) UpdateQzcPrice(ctx context.Context, in *pc.UpdateA8PriceRequest) (out *pc.UpdateA8PriceResponse, e error) {
	out = &pc.UpdateA8PriceResponse{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("ChannelProduct UpdateQzcPrice 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	// 用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return out, nil
	}

	if in.Id <= 0 {
		out.Message = "id不能为0"
		return out, nil
	}
	if in.Price <= 0 {
		out.Message = "价格不能小于等于0"
		return out, nil
	}
	conn := NewDbConn()
	session := conn.NewSession()
	defer session.Close()

	var qzcPrice = models.QzcPriceSync{}
	if _, err := conn.Where("id = ?", in.Id).Get(&qzcPrice); err != nil {
		out.Message = "查询前置仓价格信息失败 " + err.Error()
		return
	}
	if qzcPrice.Id == 0 {
		out.Message = "该前置仓价格信息不存在"
		return
	}

	session.Begin()

	if _, err := session.ID(in.Id).Update(&models.QzcPriceSync{
		Price:          int(in.Price),
		UpdateUserName: userInfo.UserName,
	}); err != nil {
		session.Rollback()
		out.Message = "编辑前置仓价格失败 " + err.Error()
		return
	}

	if _, err := session.Insert(&models.QzcPriceSyncRecord{
		Type:        1,
		WarehouseId: qzcPrice.WarehouseId,
		ThirdSkuId:  qzcPrice.ThirdSkuId,
		Operation: fmt.Sprintf("修改商品skuid:%d的价格，从%s元改为%s元",
			qzcPrice.SkuId,
			decimal.NewFromFloat(float64(qzcPrice.Price)/100).Round(2).String(),
			decimal.NewFromFloat(float64(in.Price)/100).Round(2).String(),
		),
		UserNo:   userInfo.UserNo,
		UserName: userInfo.UserName,
		UserIp:   in.Ip,
	}); err != nil {
		session.Rollback()
		out.Message = "插入前置仓价格操作信息失败" + err.Error()
		return
	}
	if err := session.Commit(); err != nil {
		session.Rollback()
		out.Error = "事务提交失败，err:" + err.Error()
		return
	}

	out.Code = 200
	return
}

// DelQzcPrice 删除前置仓价格信息
func (c *Product) DelQzcPrice(ctx context.Context, in *pc.UpdateA8PriceRequest) (out *pc.UpdateA8PriceResponse, e error) {
	out = &pc.UpdateA8PriceResponse{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("ChannelProduct DelQzcPrice 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	// 用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		return out, nil
	}

	if in.Id <= 0 {
		out.Message = "id不能为0"
		return out, nil
	}
	conn := NewDbConn()
	session := conn.NewSession()
	defer session.Close()

	var qzcPrice = models.QzcPriceSync{}
	if _, err := conn.Where("id = ?", in.Id).Get(&qzcPrice); err != nil {
		out.Message = "查询前置仓价格信息失败" + err.Error()
		return
	}
	if qzcPrice.Id == 0 {
		out.Message = "该前置仓价格信息不存在"
		return
	}

	session.Begin()
	if _, err := session.ID(in.Id).Delete(&models.QzcPriceSync{}); err != nil {
		session.Rollback()
		out.Message = "删除前置仓价格失败" + err.Error()
		return
	}

	if _, err := session.Insert(&models.QzcPriceSyncRecord{
		Type:        2,
		WarehouseId: qzcPrice.WarehouseId,
		ThirdSkuId:  qzcPrice.ThirdSkuId,
		Operation:   fmt.Sprintf("移除商品skuid:%d", qzcPrice.SkuId),
		UserNo:      userInfo.UserNo,
		UserName:    userInfo.UserName,
		UserIp:      in.Ip,
	}); err != nil {
		session.Rollback()
		out.Message = "插入前置仓价格操作信息失败" + err.Error()
		return
	}
	if err := session.Commit(); err != nil {
		session.Rollback()
		out.Message = "事务提交失败，err:" + err.Error()
		return
	}
	out.Code = 200
	return
}

// SearchWarehouse 根据来源和配送方式获取仓库ID
func (c *Product) SearchWarehouse(ctx context.Context, p *pc.SearchWarehouseRequest) (*pc.SearchWarehouseResponse, error) {
	out := &pc.SearchWarehouseResponse{Code: 400}

	redisConn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}

	// 如果是自提则替换成阿闻自提渠道ID
	if int(p.ChannelId) == enum.ChannelAwenId && p.AdCode == 1 {
		p.ChannelId = int32(enum.ChannelAwenPickUpId)
	}
	warehouse := utils.LoadChannelWarehouseCache(redisConn, p.FinanceCode, int(p.ChannelId))
	if warehouse == nil {
		out.Message = "根据财务编码查询不到对应的仓库"
		return out, nil
	}
	out.WarehouseIds = warehouse.WarehouseId

	out.Code = 200
	out.Message = ""
	return out, nil
}

// QueryChildProducts
// 查询组合商品的子商品信息
// 第三方下单时 如果时组合商品 第三方只会给到我们父商品的商品信息 不会给到我们组合商品的商品明细
// 此时需要我们通过父商品查询组合商品的商品明细信息
// @version v6.0
func (c Product) QueryChildProducts(ctx context.Context, in *pc.QueryChildProductsRequest) (*pc.ChildProductsResponse, error) {
	out := new(pc.ChildProductsResponse)
	out.Code = 400
	glog.Info("查询子商品入参", kit.JsonEncode(in))
	if len(in.ParentSkuId) == 0 {
		out.Error = "父skuId参数不能为空"
		return out, nil
	}
	//查询
	var products []*pc.ChildProduct
	db := NewDbConn()
	sql := `SELECT 
			channel_sku_group.id,
			channel_sku_group.sku_id parent_sku_id,
			channel_sku_group.group_product_id product_id,
			channel_sku_group.group_sku_id sku_id,
			channel_sku_group.count AS number,
			channel_sku_group.market_price,
			channel_sku_group.product_type, 
			channel_sku_group.discount_value, 
			channel_sku_group.discount_type, 
			channel_product.name as product_name,
			channel_product.bar_code, 
			channel_product.term_type, 
			channel_product.term_value, 
			channel_product.pic AS image, 
			channel_product.virtual_invalid_refund,
			channel_sku_third.third_sku_id
			FROM channel_sku_group 
			JOIN channel_product ON channel_sku_group.group_product_id = channel_product.id AND channel_product.channel_id = ?
			JOIN channel_sku_third ON channel_sku_third.sku_id = channel_sku_group.group_sku_id AND channel_sku_third.channel_id = ? AND  channel_sku_third.erp_id = ? 
			WHERE channel_sku_group.sku_id = ? AND channel_sku_group.channel_id = ?`
	err := db.SQL(sql, in.ChannelId, in.ChannelId, in.Erp, in.ParentSkuId, in.ChannelId).Find(&products)
	if err != nil {
		glog.Error("查询组合商品子商品出错", err)
		return out, err
	}
	//discount_value discount_type market_price
	//商品的规格
	skuIds := make([]string, len(products))
	//v6.0 productId的查询需要改成skuId 快照表修改后此处的代码需要修改
	for i, v := range products {
		skuIds[i] = v.SkuId
	}

	//从父商品的快照中查询价格信息与优惠设置
	var jsonData string
	has, err := db.Table("channel_product_snapshot").Select("json_data").
		Where("finance_code=? AND channel_id = ? AND product_id=?", in.FinanceCode, in.ChannelId, in.ParentProductId).Get(&jsonData)
	if err != nil {
		glog.Error("查询组合商品子商品快照信息出错", err)
		return out, err
	}

	priceSkuMap := make(map[string]*pc.SkuGroup)
	if has {
		var newSnap pc.ChannelProductRequest
		if err := json.Unmarshal([]byte(jsonData), &newSnap); err != nil {
			glog.Error("QueryChildProducts反序列化报错：", in.FinanceCode, jsonData)
			out.Message = "快照数据读取错误"
			return out, nil
		}
		for _, skuItem := range newSnap.SkuInfo {
			for _, child := range skuItem.SkuGroup {
				strSku := cast.ToString(child.GroupSkuId)
				priceSkuMap[strSku] = child
			}
		}
	}

	//查询规格信息
	var skuValue []*pc.SkuValue
	err = db.Table("channel_sku_value").
		Select("`id`, `spec_id`, `spec_value_id`, `sku_id`").
		Where("channel_id = ?", in.ChannelId).
		In("sku_id", skuIds).
		OrderBy("sort").
		Find(&skuValue)
	if err != nil {
		glog.Error("查询sku规格信息出错", err)
		return out, err
	}
	//如果没有规格信息 则直接返回
	if len(skuValue) == 0 {
		out.Code = 200
		out.Products = products
		return out, nil
	}
	type skuSpec struct {
		SpecId      int32
		SpecValueId int32
	}
	//sku 与 spec的关系
	skuSpecIdMap := make(map[string]skuSpec)
	for _, v := range skuValue {
		skuSpecIdMap[cast.ToString(v.SkuId)] = skuSpec{
			SpecId:      v.SpecId,
			SpecValueId: v.SpecValueId,
		}
	}

	var specID strings.Builder
	var specValueID strings.Builder
	for i, v := range skuValue {
		specID.WriteString(strconv.Itoa(int(v.SpecId)))
		specValueID.WriteString(strconv.Itoa(int(v.SpecValueId)))
		if i != len(skuValue)-1 {
			specID.WriteString(",")
			specValueID.WriteString(",")
		}
	}

	var spec map[int32]*pc.Spec
	var specValue map[int32]*pc.SpecValue
	if res, err := c.QuerySpecMap(ctx, &pc.IdRequest{Id: specID.String()}); err != nil {
		glog.Error("查询sku规格信息出错1", err)
		return out, err
	} else {
		spec = res.Spec
	}
	if res, err := c.QuerySpecValueMap(ctx, &pc.IdRequest{Id: specValueID.String()}); err != nil {
		glog.Error("查询sku规格信息出错2", err)
		return out, err
	} else {
		specValue = res.SpecValue
	}

	for _, item := range products {
		if specIds, hasSpec := skuSpecIdMap[item.SkuId]; hasSpec {
			//规格名称
			if _, ok := spec[specIds.SpecId]; ok {
				item.Specs = spec[specIds.SpecId].Name
			}
			//规格值
			if _, ok := specValue[specIds.SpecValueId]; ok {
				item.Specs += "：" + specValue[specIds.SpecValueId].Value
			}
		}
		if groupSkuInfo, hasPrice := priceSkuMap[item.SkuId]; hasPrice {
			//规格名称
			item.MarketPrice = groupSkuInfo.MarketPrice
			item.DiscountValue = groupSkuInfo.DiscountValue
			item.DiscountType = groupSkuInfo.DiscountType
		}
	}

	out.Code = 200
	out.Products = products
	return out, nil
}

// List 渠道商品库列表
func (c *ChannelProduct) List(ctx context.Context, in *pc.ChannelProductListReq) (out *pc.ChannelProductListRes, e error) {
	out = &pc.ChannelProductListRes{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("ChannelProduct List 出错，入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := NewDbConn()
	query, err := models.QueryChannelProductListByReq(db, in)
	if err != nil {
		out.Message = err.Error()
		return
	}

	var cps []*models.ChannelProduct
	pr := &utils.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query.Select("a.id,a.name,a.pic,a.channel_id,a.is_use,a.update_date,a.product_type,a.group_type,a.channel_category_id,a.channel_category_name"),
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}
	if err = pr.Paginate(&out.TotalCount, &cps); err != nil {
		out.Message = err.Error()
		return
	}
	if len(cps) == 0 {
		out.Code = 200
		return
	}

	var pIds []int32
	cpMap := make(map[int32]*models.ChannelProduct)
	SpecialMap := make(map[int32]*pc.ChannelProductList)
	for _, cp := range cps {
		pIds = append(pIds, cp.Id)
		cpMap[cp.Id] = cp
		SpecialMap[cp.Id] = new(pc.ChannelProductList)
	}
	es, err := models.QueryChannelSkuExtendByProductIds(db, in.ChannelId, pIds)
	if err != nil {
		out.Message = err.Error()
		return
	}

	// 查询商品
	snapshot := make(map[int32]models.ChannelProductSnapshot)
	if len(in.FinanceCode) > 0 {
		shots, err := models.ListChannelProductSnapshotByProductIds(db, in.ChannelId, pIds, in.FinanceCode)
		if err != nil {
			out.Message = err.Error()
			return
		}

		for _, shot := range shots {
			snapshot[shot.ProductId] = shot
		}
	}

	// 初始化列表sku信息 [spuId][skuId]
	listSkus := make(map[int32]map[int32]*pc.ChannelProductList_Sku)
	skuSorts := make(map[int32][]int32) // spu下sku顺序记录

	for _, extend := range es {
		sku := extend.ChannelSku
		if _, ok := listSkus[sku.ProductId]; !ok {
			listSkus[sku.ProductId] = make(map[int32]*pc.ChannelProductList_Sku)
		}
		listSkus[sku.ProductId][sku.Id] = &pc.ChannelProductList_Sku{
			SkuId:         sku.Id,
			BarCode:       sku.BarCode,
			WeightForUnit: sku.WeightForUnit,
			Price:         sku.StorePrice,
		}
		if sku.VipDiscount > 0 {
			listSkus[sku.ProductId][sku.Id].VipPrice = int32(decimal.NewFromInt32(sku.StorePrice).Mul(decimal.NewFromFloat(sku.VipDiscount / 10)).Ceil().IntPart())
		}
		skuSorts[extend.ChannelProduct.Id] = append(skuSorts[extend.ChannelProduct.Id], sku.Id)
	}

	// 填充a8、子龙货号
	var skuThirds []*models.ChannelSkuThird
	if err = db.Where("channel_id = ?", in.ChannelId).In("product_id", pIds).Find(&skuThirds); err != nil {
		out.Message = err.Error()
		return
	}
	for _, third := range skuThirds {
		if _, ok := listSkus[third.ProductId][third.SkuId]; ok {
			if third.ErpId == 2 {
				listSkus[third.ProductId][third.SkuId].A8Id = third.ThirdSkuId
			} else if third.ErpId == 4 {
				listSkus[third.ProductId][third.SkuId].ZilongId = third.ThirdSkuId
			}
		}
	}

	// 存在财务编码，还要计算库存及价格
	if len(in.FinanceCode) > 0 {
		// 填充规格值
		type SkuSpec struct {
			ProductId int32  `json:"product_id"`
			SkuId     int32  `json:"sku_id"`
			SpecValue string `json:"spec_value"`
		}
		var skuSpecs []*SkuSpec
		// 这里不能用group，因为channel_sku_value有重复值
		if err = db.Table("channel_sku_value").Alias("v").Join("inner", "spec_value s", "s.id = v.spec_value_id").
			Where("channel_id = ?", in.ChannelId).In("product_id", pIds).
			Select("distinct v.product_id,v.sku_id,value as spec_value").Find(&skuSpecs); err != nil {
			out.Message = "查询规格值出错 " + err.Error()
			return
		}
		for _, skuSpec := range skuSpecs {
			if ls, ok := listSkus[skuSpec.ProductId][skuSpec.SkuId]; ok {
				if len(ls.SpecValue) == 0 {
					ls.SpecValue = skuSpec.SpecValue
				} else {
					ls.SpecValue += "," + skuSpec.SpecValue
				}
			}
		}

		// 合并快照信息
		if err = es.MergeSnapshot(db, in.FinanceCode); err != nil {
			out.Message = err.Error()
			return
		} else {
			for _, extend := range es {
				listSkus[extend.ChannelSku.ProductId][extend.ChannelSku.Id].Price = extend.ChannelSku.MarketPrice
				if extend.ChannelSku.VipDiscount > 0 {
					listSkus[extend.ChannelSku.ProductId][extend.ChannelSku.Id].VipPrice = int32(decimal.NewFromInt32(extend.ChannelSku.MarketPrice).
						Mul(decimal.NewFromFloat(extend.ChannelSku.VipDiscount / 10)).Ceil().IntPart())
				}
				if extend.ChannelCategoryId > 0 {
					cpMap[extend.ChannelSku.ProductId].ChannelCategoryId = extend.ChannelCategoryId
				}
				cpMap[extend.ChannelSku.ProductId].Pic = extend.ChannelProduct.Pic
				cpMap[extend.ChannelSku.ProductId].Name = extend.ChannelProduct.Name
				SpecialMap[extend.ChannelSku.ProductId].IsDrugs = extend.IsDrugs
				SpecialMap[extend.ChannelSku.ProductId].MinOrderCount = extend.ChannelSku.MinOrderCount
				SpecialMap[extend.ChannelSku.ProductId].IsRecommend = extend.ChannelProduct.IsRecommend
			}
		}

		// 已经查询的仓库，单个仓库只查一次
		queried := make(map[int]bool)
		// 附加指定渠道的库存
		channelStock := func(channelId int32) (err error) {
			warehouse, err := models.QueryWarehouseByRelation(db, channelId, in.FinanceCode)
			if err != nil {
				return
			} else if warehouse.Id < 1 { // 没有绑定仓库不处理
				return
			} else if queried[warehouse.Id] { // 单个仓库只查一次
				return
			}
			out.WarehouseType = append(out.WarehouseType, warehouse.GetCategoryText()+"库存")

			// 查询库存
			stockMap, err := es.QueryStock(db, warehouse.Id)
			if err != nil {
				out.Message = "查询库存出错 " + err.Error()
				return
			}

			for _, extend := range es {
				listSku := listSkus[extend.ChannelSku.ProductId][extend.ChannelSku.Id]
				// 赋值库存，只有虚拟商品不会有stock
				if stock, ok := stockMap[extend.ChannelSku.Id]; ok {
					listSku.Stocks = append(listSku.Stocks, stock)
				}
			}
			queried[warehouse.Id] = true
			return
		}

		if err = channelStock(in.ChannelId); err != nil {
			out.Message = err.Error()
			return
		}
		// 阿闻渠道还要附加自提的库存
		if in.ChannelId == 1 {
			if err = channelStock(10); err != nil {
				out.Message = err.Error()
				return
			}
		}
	} else {
		for _, extend := range es {
			SpecialMap[extend.ChannelSku.ProductId].IsDrugs = extend.IsDrugs
		}
	}

	for _, cp := range cps {
		data := &pc.ChannelProductList{
			Id:                  cp.Id,
			Name:                cp.Name,
			TypeText:            cp.GetProductTypeText(),
			Pic:                 cp.Pic,
			ChannelId:           cp.ChannelId,
			IsUse:               cp.IsUse,
			UpdateDate:          cp.UpdateDate.Format(kit.DATETIME_LAYOUT),
			ProductType:         cp.ProductType,
			ChannelCategoryId:   cp.ChannelCategoryId,
			ChannelCategoryName: cp.ChannelCategoryName,
			IsDrugs:             SpecialMap[cp.Id].IsDrugs,
			IsRecommend:         SpecialMap[cp.Id].IsRecommend,
			MinOrderCount:       SpecialMap[cp.Id].MinOrderCount,
			ProductThirdId:      snapshot[cp.Id].ProductThirdId,
			SyncError:           snapshot[cp.Id].SyncError,
		}

		for _, skuId := range skuSorts[cp.Id] {
			data.Skus = append(data.Skus, listSkus[cp.Id][skuId])
		}

		out.Data = append(out.Data, data)
	}

	out.Code = 200
	return
}

// Count 渠道商品库列表商品数量统计
func (c *ChannelProduct) Count(ctx context.Context, in *pc.ChannelProductCountReq) (out *pc.ChannelProductCountRes, e error) {
	out = &pc.ChannelProductCountRes{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("ChannelProduct Count 出错，入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := NewDbConn()
	var tmps []*pc.ChannelProductCountRes

	// 阿闻渠道 自提有库存也算有库存
	stockChannelId := cast.ToString(in.ChannelId)
	if in.ChannelId == 1 {
		stockChannelId += ",10"
	}

	query := db.Table("channel_product").Alias("a").
		Join("left", "dc_product.channel_store_product s", fmt.Sprintf("s.product_id = a.id and a.channel_id = s.channel_id and s.finance_code = '%s'", in.FinanceCode)).
		Join("left", fmt.Sprintf(`(select distinct s.product_id from dc_dispatch.warehouse_relation_shop r
inner join dc_order.warehouse_goods wg on wg.warehouse_id = r.warehouse_id
inner join dc_product.sku s on s.id = wg.goodsid
where r.channel_id in (%s) and r.shop_id = '%s' and (wg.stock - ifnull(
(select sum(stock) as freeze_stock from dc_order.order_freeze_stock f where f.sku_id = wg.goodsid and f.warehouse_id = wg.warehouse_id),0))>0
) st`, stockChannelId, in.FinanceCode), "st.product_id = a.id").
		Select(`count(*) as total,sum(if(s.up_down_state = 1,1,0)) as up,sum(if(s.up_down_state = 0 or s.up_down_state is null,1,0)) as down,
count(st.product_id) as has_stock,sum(if((s.up_down_state = 0 or s.up_down_state is null) and st.product_id > 0,1,0)) as has_stock_and_down`).
		Where("a.channel_id=?", in.ChannelId)

	// 侧边栏分类过滤
	if in.CategoryId > 0 {
		var categoryIds []string
		if err := db.Table("channel_category").Select("id").Where("channel_id = 1 and (id = ? or parent_id = ?)",
			in.CategoryId, in.CategoryId).Find(&categoryIds); err != nil {
			out.Message = "查询分类出错 " + err.Error()
			return
		}
		if len(categoryIds) == 0 {
			out.Code = 200
			return
		}
		query.And(fmt.Sprintf(`ifnull(
		(select ps.channel_category_id from channel_product_snapshot ps where ps.product_id = a.id and ps.channel_id = a.channel_id
		and ps.finance_code = '%s' and ps.channel_category_id > 0 limit 1),a.category_id) in (%s)`, in.FinanceCode, strings.Join(categoryIds, ",")))
	}

	if err := query.Find(&tmps); err != nil {
		out.Message = "查询出错 " + err.Error()
		return
	} else if len(tmps) > 0 {
		out = tmps[0]
	}

	out.Code = 200
	return
}

// ExceptionList 渠道商品库异常列表
func (c *ChannelProduct) ExceptionList(ctx context.Context, in *pc.ChannelProductListReq) (out *pc.ChannelProductExceptionListRes, e error) {
	out = &pc.ChannelProductExceptionListRes{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("ChannelProduct ExceptionList 出错，入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := NewDbConn()

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	par := make([]interface{}, 0)
	sql := "select b.erp_id,b.third_sku_id from channel_product a inner join  `channel_sku_third` b ON a.id=b.product_id AND b.channel_id=a.channel_id where a.channel_id=? "
	par = append(par, in.ChannelId)
	if in.ProductType != 0 {
		switch in.ProductType {
		case 31: // 实实组合
			sql += " and a.product_type = 3 and a.group_type = 1"
		case 33: // 实虚组合
			sql += " and a.product_type = 3 and a.group_type = 3"
		default:
			sql += " and a.product_type=?"
			par = append(par, in.ProductType)
		}
	}

	if len(in.Where) > 0 {
		switch in.WhereType {
		case "third_spu_sku_id":
			sql += " and b.third_sku_id LIKE ? "
			par = append(par, "%"+in.Where+"%")
		default:
			sql += " and b.third_sku_id LIKE ? "
			par = append(par, "%"+in.Where+"%")
		}
	}
	sql += " and b.erp_id in (2,4) and b.third_sku_id!='' "
	sql += " GROUP BY b.erp_id,b.third_sku_id  HAVING COUNT(1)>1"
	third_list := make([]models.ExceptionThird, 0)
	db.ShowSQL()
	count := 0
	sql1 := "select count(1) from (" + sql + ") ss"
	_, err := db.SQL(sql1, par...).Get(&count)
	if err != nil {
		out.Message = err.Error()
	}
	if count > 0 {
		out.TotalCount = int32(count)
		sql += " LIMIT " + cast.ToString((in.PageIndex-1)*in.PageSize) + "," + cast.ToString(in.PageSize)
		db.SQL(sql, par...).Find(&third_list)
		if len(third_list) > 0 {
			//货号集合
			third_list_str := make([]string, 0)
			//用来标识这个货号这个ERPID是否存在重复
			third_list_map := make(map[string]int)
			for _, x := range third_list {
				third_list_str = append(third_list_str, x.ThirdSkuId)
				third_list_map[x.ThirdSkuId+"|"+cast.ToString(x.ErpId)] = 1
			}
			//查询出所有有重复货号的数据，但是这个时候还没有判断是子龙货号还是A8货号，后面再赛选
			var cps []*models.ChannelProductException
			//db.Select("a.id,a.name,a.pic,a.channel_id,a.is_use,a.update_date,a.product_type,a.group_type,b.third_sku_id,b.erp_id").Where("b.channel_id=?", in.ChannelId).In("third_sku_id", third_list_str).Find(&cps)
			if err = db.SQL("select a.id,a.name,a.pic,a.channel_id,a.is_use,a.update_date,a.product_type,a.group_type,b.third_sku_id,b.erp_id,b.sku_id from channel_product a inner join channel_sku_third b ON a.id=b.product_id AND b.channel_id=a.channel_id "+
				" where b.channel_id=? and third_sku_id in ("+"'"+strings.Join(third_list_str, "','")+"'"+")", in.ChannelId).Find(&cps); err != nil {
				out.Message = err.Error()
				return
			}

			var pIds []string
			for _, cp := range cps {
				//如果是重复货号就加入
				if _, ok := third_list_map[cp.ThirdSkuId+"|"+cast.ToString(cp.ErpId)]; ok {
					pIds = append(pIds, cast.ToString(cp.Id))
				}

			}
			//查询所有重复商品的sku信息
			// 填充a8、子龙货号
			// 初始化列表sku信息 [spuId][skuId]
			listSkus := make(map[int32]*pc.ChannelProductExceptionDetail)
			var skuThirds []*models.ChannelSkuThirdAndPt
			if err = db.SQL("SELECT a.*,b.third_sku_id third_sku_id_pt FROM channel_sku_third a LEFT JOIN sku_third b ON a.sku_id=b.sku_id AND a.erp_id=b.erp_id where a.channel_id=? AND a.product_id IN ("+strings.Join(pIds, ",")+")", in.ChannelId).Find(&skuThirds); err != nil {
				out.Message = err.Error()
				return
			}

			for _, sku := range cps {
				if _, ok := listSkus[sku.Id]; !ok {
					listSkus[sku.Id] = new(pc.ChannelProductExceptionDetail)
				}
				listSkus[sku.Id].Id = sku.Id
				listSkus[sku.Id].SkuId = sku.SkuId
				listSkus[sku.Id].ProductType = sku.ProductType
				listSkus[sku.Id].Pic = sku.Pic
				listSkus[sku.Id].UpdateDate = sku.UpdateDate.Format(kit.DATETIME_LAYOUT)
				listSkus[sku.Id].TypeText = sku.GetProductTypeText()
				listSkus[sku.Id].Name = sku.Name
			}

			for _, third := range skuThirds {
				if _, ok := listSkus[third.ProductId]; ok {
					if third.ErpId == 2 {
						listSkus[third.ProductId].A8Id = third.ThirdSkuId
						listSkus[third.ProductId].A8IdPt = third.ThirdSkuIdPt
					} else if third.ErpId == 4 {
						listSkus[third.ProductId].ZilongId = third.ThirdSkuId
						listSkus[third.ProductId].ZilongIdPt = third.ThirdSkuIdPt
					}
				}
			}
			//构建返回值
			for _, itemthird := range third_list {
				pcitem := pc.ChannelProductExceptionList{}
				ZilongOrA8 := "A8货号:"
				if itemthird.ErpId == 4 {
					ZilongOrA8 = "子龙货号:"
				}
				ZilongOrA8 = ZilongOrA8 + itemthird.ThirdSkuId
				pcitem.ThirdSkuId = ZilongOrA8
				pcitem.ErpId = itemthird.ErpId
				pcitem.ThirdSkuItem = itemthird.ThirdSkuId
				for _, k1 := range listSkus {
					if k1.ZilongId == itemthird.ThirdSkuId && itemthird.ErpId == 4 {
						pcitem.Data = append(pcitem.Data, k1)
					}
					if k1.A8Id == itemthird.ThirdSkuId && itemthird.ErpId == 2 {
						pcitem.Data = append(pcitem.Data, k1)
					}
				}
				out.Data = append(out.Data, &pcitem)
			}
		}
	}

	out.Code = 200
	return out, nil
}

// StoreProductUpDistinct 去重的上架商品列表
func (c *ChannelProduct) StoreProductUpDistinct(ctx context.Context, in *pc.StoreProductUpDistinctReq) (out *pc.StoreProductUpDistinctRes, e error) {
	out = &pc.StoreProductUpDistinctRes{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("ChannelProduct StoreProductUpDistinct 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.ChannelId == 0 {
		out.Message = "渠道id不能为空"
		return
	}

	if len(in.FinanceCode) == 0 {
		out.Message = "门店不能为空"
		return
	}

	db := NewDbConn()

	session := db.Table("channel_store_product").Alias("csp").
		Join("inner", "channel_product cp", "cp.id = csp.product_id AND cp.channel_id = csp.channel_id").
		Where("csp.channel_id = ?", in.ChannelId).In("csp.finance_code", in.FinanceCode).Where("csp.up_down_state =1")

	if len(in.ProductId) > 0 {
		session.In("csp.product_id", in.ProductId)
	}

	if len(in.SkuId) > 0 {
		session.In("csp.sku_id", in.SkuId)
	}

	if in.Name != "" {
		session.And("cp.name like ?", "%"+in.Name+"%")
	}

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	if err := session.Limit(int(in.PageSize+1), int(in.PageSize*(in.PageIndex-1))).
		Select("distinct csp.product_id,csp.sku_id,cp.name").
		Find(&out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	if len(out.Data) > int(in.PageSize) {
		out.HasMore = true
		out.Data = out.Data[:in.PageSize]
	}

	out.Code = 200
	return
}

// CountByPrice 商品按价格统计
func (c *ChannelProduct) CountByPrice(ctx context.Context, in *pc.ProductCountByPriceReq) (out *pc.ProductCountByPriceRes, e error) {
	out = &pc.ProductCountByPriceRes{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("ChannelProduct CountByPrice 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.ChannelId == 0 {
		out.Message = "渠道id不能为空"
		return
	}
	if len(in.SkuId) == 0 {
		out.Message = "商品SkuId不能为空"
		return
	}

	if len(in.FinanceCode) == 0 {
		out.Message = "财务编码不能为空"
		return
	}

	// 当单次统计数量过大时，查询性能衰减非常严重
	// 如约6.5万数据耗时400ms，13万 1.5秒，26万 8.8秒
	var eg errgroup.Group
	pageSize := 65000 / len(in.SkuId)

	idsArr := make([]string, 0, len(in.SkuId))
	for _, id := range in.SkuId {
		idsArr = append(idsArr, strconv.FormatInt(int64(id), 10))
	}
	ids := strings.Join(idsArr, ",")

	db := NewDbConn()
	ch := make(chan struct{}, 8)

	// 分批处理
	for i := 0; i < len(in.FinanceCode); i = i + pageSize {
		ch <- struct{}{}
		end := i + pageSize
		if end > len(in.FinanceCode) {
			end = len(in.FinanceCode)
		}

		shops := in.FinanceCode[i:end]
		eg.Go(func() error {
			defer func() {
				<-ch
			}()
			var data []*pc.ProductCountByPriceRes_Data
			if err := db.SQL(fmt.Sprintf(`SELECT t.sku_id,t.product_id,t.market_price,t.count,s.name as shop_name,cp.name FROM 
(SELECT product_id,sku_id,market_price,MIN(finance_code) AS finance_code,count(1) as count
FROM channel_store_product csp
WHERE channel_id = ? AND up_down_state = 1 AND sku_id in (%s) AND finance_code IN ('%s')
GROUP BY product_id,market_price) t
JOIN datacenter.store s ON s.finance_code = t.finance_code
JOIN dc_product.channel_product cp ON cp.id = t.product_id AND channel_id = 1`, ids, strings.Join(shops, "','")), in.ChannelId).
				Find(&data); err != nil {
				return err
			}

			out.Data = append(out.Data, data...)
			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// PriceStore 商品同一价格关联的门店
func (c *ChannelProduct) PriceStore(ctx context.Context, in *pc.ProductPriceStoreReq) (out *pc.ProductPriceStoreRes, e error) {
	out = &pc.ProductPriceStoreRes{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("ChannelProduct PriceStore 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.ChannelId == 0 {
		out.Message = "渠道id不能为空"
		return
	}
	if in.SkuId == 0 {
		out.Message = "商品SkuId不能为空"
		return
	}
	if len(in.FinanceCode) == 0 {
		out.Message = "财务编码不能为空"
		return
	}

	session := NewDbConn().Table("channel_store_product").Alias("csp").
		Join("inner", "datacenter.store s", "s.finance_code = csp.finance_code").
		Where("csp.channel_id = ? and csp.up_down_state = 1 and csp.sku_id = ?", in.ChannelId, in.SkuId).
		Where("csp.market_price = ?", in.Price).
		In("csp.finance_code", in.FinanceCode)

	in.Search = strings.TrimSpace(in.Search)

	if len(in.Search) > 0 {
		switch in.SearchType {
		case 1:
			session.Where("s.name like ?", "%"+in.Search+"%")
		case 2:
			session.Where("csp.finance_code = ?", in.Search)
		}
	}

	pr := utils.PaginateReq{
		Count:    session.Clone(),
		List:     session.Asc("s.id").Select("s.finance_code,s.name"),
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.TotalCount, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// MtDelProduct 渠道（美团、饿了么）商品删除回调处理
func (c *ChannelProduct) MtDelProduct(ctx context.Context, in *pc.MtDelProductReq) (out *pc.ChannelProductDelResp, e error) {
	out = &pc.ChannelProductDelResp{
		Code: 400,
	}
	defer func() {
		if out.Code != 200 {
			glog.Info("EditShopBindWarehouse 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	poiCode := in.AppPoiCode
	productIds := strings.Join(in.AppSpuCode, ",")

	// 门店id转换为财务编码，store_relation表查询
	var storeRelation models.StoreRelation
	NewDatacenterDbConn().SQL("SELECT * from datacenter.store_relation sr where channel_store_id =? and channel_id = ?", poiCode, ChannelMtId).Get(&storeRelation)
	financeCode := storeRelation.FinanceCode
	if len(financeCode) <= 0 {
		return out, errors.New("美团回调删除商品，没有查询到第三方门店对应的财务编码channelStoreId:" + poiCode)
	}

	// 查询到对应的商品信息，删除
	pcSession := NewDbConn().NewSession()
	defer pcSession.Close()
	pcSession.Begin()

	_, err := pcSession.Exec("DELETE FROM dc_product.channel_store_product WHERE channel_id=? AND finance_code=? AND product_id IN (?)", ChannelMtId, financeCode, productIds)
	if err != nil {
		pcSession.Rollback()
		glog.Error("美团回调删除商品, 删除渠道商品表异常")
		return out, errors.New("美团回调删除商品, 删除渠道商品表异常")
	}

	_, err = pcSession.Exec("DELETE FROM dc_product.channel_product_snapshot WHERE channel_id=? AND finance_code=? AND product_id IN (?)", ChannelMtId, financeCode, productIds)
	if err != nil {
		pcSession.Rollback()
		glog.Error("美团回调删除商品, 删除渠道商品表异常")
		return out, errors.New("美团回调删除商品, 删除渠道商品表异常")
	}

	pcSession.Commit()
	out.Code = 200
	return out, nil
}

// ElmDelProduct 饿了么商品删除回调处理
func (c *ChannelProduct) ElmDelProduct(ctx context.Context, in *pc.ElmDelProductReq) (out *pc.ChannelProductDelResp, e error) {
	out = &pc.ChannelProductDelResp{
		Code: 400,
	}
	defer func() {
		if out.Code != 200 {
			glog.Info("ElmDelProduct 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	shopId := in.ShopId
	skuId := in.SkuId

	// 门店id转换为财务编码，store_relation表查询
	var storeRelation models.StoreRelation
	NewDatacenterDbConn().SQL("SELECT * from datacenter.store_relation sr where channel_store_id =? and channel_id = ?", shopId, ChannelElmId).Get(&storeRelation)
	financeCode := storeRelation.FinanceCode
	if len(financeCode) <= 0 {
		return out, errors.New("饿了么回调删除商品，没有查询到第三方门店对应的财务编码shopId=" + shopId)
	}

	var productId int32
	_, err := NewDbConn().Table("dc_product.channel_sku").Select("product_id").Where("id=? AND channel_id=?", skuId, ChannelElmId).Get(&productId)
	if err != nil || productId == 0 {
		glog.Error("饿了么回调删除商品, 查询渠道商品的productId异常，没有找到对应记录")
		return out, errors.New("没有找到对应的渠道商品记录")
	}

	// 查询到对应的商品信息，删除
	pcSession := NewDbConn().NewSession()
	defer pcSession.Close()
	pcSession.Begin()

	_, err = pcSession.Exec("DELETE FROM dc_product.channel_store_product WHERE channel_id=? AND finance_code=? AND sku_id=?", ChannelElmId, financeCode, skuId)
	if err != nil {
		pcSession.Rollback()
		glog.Error("饿了么回调删除商品, 删除渠道商品表异常")
		return out, errors.New("饿了么回调删除商品, 删除渠道商品表异常")
	}

	_, err = pcSession.Exec("DELETE FROM dc_product.channel_product_snapshot WHERE channel_id=? AND finance_code=? AND product_id=?", ChannelElmId, financeCode, productId)
	if err != nil {
		pcSession.Rollback()
		glog.Error("饿了么回调删除商品, 删除渠道商品快照表异常")
		return out, errors.New("饿了么回调删除商品, 删除渠道商品表异常")
	}

	pcSession.Commit()
	out.Code = 200
	return out, nil
}

// 饿了么回调时， 执行的操作
func (c *ChannelProduct) ElmDownProduct(ctx context.Context, in *pc.ElmDownProductReq) (out *pc.ElmDownProductResp, e error) {
	out = &pc.ElmDownProductResp{
		Code: 400,
	}
	defer func() {
		if out.Code != 200 {
			glog.Info("ElmDownProduct 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	shopId := in.ShopId
	skuId := in.SkuId

	// 获取到财务编码
	// 门店id转换为财务编码，store_relation表查询
	var storeRelation models.StoreRelation
	NewDatacenterDbConn().SQL("SELECT * from datacenter.store_relation sr where channel_store_id =? and channel_id = ?", shopId, ChannelElmId).Get(&storeRelation)
	financeCode := storeRelation.FinanceCode
	if len(financeCode) <= 0 {
		return out, errors.New("饿了么更新上下架操作异常，没有找到对应的财务编码：shopId=" + shopId)
	}

	var ids = make([]int32, 0)
	err := engine.Table("channel_store_product").Where("sku_id=? AND finance_code=? and channel_id=? and up_down_state=1 ", skuId, financeCode, ChannelElmId).Select("id").Find(&ids)
	if err != nil {
		//只记录日志，不重试了
		glog.Error("饿了么更新上下架操作，查询渠道商品异常：", " 财务编码:", financeCode, " sku_id:", skuId, err)
	}

	// 商品状态 1上架，0下架
	operateType := in.OperateType

	// 回调操作：
	if operateType == 1 && len(ids) == 0 {
		// 1、上架操作，数据库没有找到对应的上架商品：调第三方下架接口
		etClient := et.GetExternalClient()
		defer etClient.Close()

		var skuCreateReq = et.UpdateElmShopSkuPriceRequest{
			ShopId:      shopId,
			CustomSkuId: skuId,
			AppChannel:  in.AppChannel,
		}
		res, err := etClient.ELMPRODUCT.OfflineElmShopSkuOne(etClient.Ctx, &skuCreateReq)
		if err != nil || res.Code != 200 {
			//只记录日志，不重试了
			glog.Error("饿了么更新上下架操作，更新数据库状态为下架状态失败：", " Ids:", ids, err)
		}
	} else if operateType == 0 && len(ids) > 0 {
		// 2、下架操作，我方有对应商品并且是上架状态：我方下架
		product := new(models.ChannelStoreProduct)
		product.UpDownState = 0
		product.DownType = -1
		_, err := engine.In("id ", ids).Cols("up_down_state", "down_type").Update(product)
		if err != nil {
			//只记录日志，不重试了
			glog.Error("饿了么更新上下架操作，更新数据库状态为下架状态失败：", " Ids:", ids, err)
		}
	}

	out.Code = 200
	return out, nil
}
