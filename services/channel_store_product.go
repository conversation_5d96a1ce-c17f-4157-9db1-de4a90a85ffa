package services

import (
	"_/enum"
	"_/models"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/limitedlee/microservice/common/config"
	"github.com/tricobbler/mqgo"
	kit "github.com/tricobbler/rp-kit"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

func init() {
	go ConsumeStock()
}

// MQ消息发送人定义
const (
	UpdateStockSender = "updateStock"
)

//订阅单商品库存
func ConsumeStock() {
	queue := "dc_sz_stock_mq_has_stock"
	mqgo.NewMq(config.GetString("mq.oneself"), engine).Consume(queue, queue, "datacenter", func(request string) (response string, err error) {
		// 测试用
		//request := `{"app_poi_code":"CX0013","food_data":[{"app_food_code":"1020749","skus":[{"sku_id":"1020749001","stock":"0"}]},{"app_food_code":"1020750","skus":[{"sku_id":"1020750001","stock":"886"}]},{"app_food_code":"1020751","skus":[{"sku_id":"1020751001","stock":"777"}]},{"app_food_code":"1020752","skus":[{"sku_id":"1020752001","stock":"665"}]},{"app_food_code":"1020753","skus":[{"sku_id":"1020753001","stock":"555"}]},{"app_food_code":"1020754","skus":[{"sku_id":"1020754001","stock":"444"}]},{"app_food_code":"1023418","skus":[{"sku_id":"1023418001","stock":"100"}]}]}`
		//redisHandle = GetRedisConn()
		//engine = NewDbConn()
		defer func() {
			if p := recover(); p != nil {
				glog.Errorf("ConsumeStock 处理库存panic:%v,入参:%s,堆栈信息:%s", p, request, utils.PanicTrace())
				err = fmt.Errorf("处理库存panic:%v", p)
			}
		}()

		//glog.Info("接收到库存消息：" + request)
		var req models.StoreStock
		if err := json.Unmarshal([]byte(request), &req); err != nil {
			glog.Error("接收到库存消息推送错误的：" + request + err.Error())
			return "", err
		}

		// MQ消息发送人等于自己则不处理，直接return
		if req.Sender == UpdateStockSender {
			return "", nil
		}

		// 处理阿闻渠道库存更新
		err1 := updateStockOnAwen(req.Food_data, req.App_poi_code)
		if err1 != nil {
			glog.Errorf("更新阿闻渠道库存异常,req:%s,err:%+v", kit.JsonEncode(req), err1)
			err = err1
		}
		// 处理美团、饿了么、京东到家等第三渠道组合商品库存更新
		err2 := updateGroupStockOnThirdChannel(req.Food_data, req.App_poi_code)
		if err2 != nil {
			glog.Errorf("更新美团、饿了么、京东到家等第三渠道库存异常,req:%s,err:%+v", kit.JsonEncode(req), err2)
			err = err2
		}

		return "", err
	})
}

// 更新美团、饿了么、京东到家等第三方渠道组合商品库存
func updateGroupStockOnThirdChannel(list []models.FoodDataStore, appPoiCode string) (err error) {
	defer func() {
		if err != nil {
			glog.Info("ConsumeStock updateGroupStockOnThirdChannel，财务编码", appPoiCode, "，"+err.Error())
		}
	}()
	// 如果len(list)==1而且skuId以"099"结尾则是组合商品skuId
	// 直接return不处理后面逻辑，因为前面mq消息已处理过
	/*if len(list) == 1 && len(list[0].Skus) > 0 && strings.HasSuffix(list[0].Skus[0].Sku_id, "099") {
		return nil
	}*/

	type Relation struct {
		ShopId     string // 财务编码
		ChannelIds string // 渠道id
	}

	var relations []*Relation
	query := engine.Table("dc_dispatch.warehouse_relation_shop").
		Select("shop_id,group_concat(channel_id) as channel_ids").
		In("channel_id", []int{2, 3, 4}) // 2美团,3饿了么,4京东到家

	// 包含#传的是仓库ID
	if strings.HasPrefix(appPoiCode, "#") {
		query.Where("warehouse_id = ?", strings.TrimLeft(appPoiCode, "#"))
	} else {
		query.Where("shop_id = ?", appPoiCode)
	}

	// 必须以shop_id,warehouse_id，以仓库为处理单位
	if err = query.GroupBy("shop_id,warehouse_id").Find(&relations); err != nil {
		return errors.New("查询绑定关系 " + err.Error())
	} else if len(relations) < 1 {
		return
	}

	var notices []string

	for _, r := range relations {
		// 过滤出组合商品已上线sku信息
		groupSkuMap, err := filterUpGroupSkuMap(list, r.ShopId)
		if err != nil {
			notices = append(notices, fmt.Sprintf("过滤组合商品已上线skuId异常，财务编码：%s,err:%+v", r.ShopId, err))
			continue
		}
		if len(groupSkuMap) == 0 {
			continue
		}
		if err = doPushGroupStock(r.ShopId, groupSkuMap, r.ChannelIds); err != nil {
			notices = append(notices, err.Error())
		}
	}

	if len(notices) > 0 {
		return errors.New(strings.Join(notices, ","))
	}

	return
}

func doPushGroupStock(financeCode string, groupSkuMap map[int32][]int32, channelIds string) error {
	//批量查询库存
	skuCodeList := make([]*ic.SkuCodeInfo, 0)
	for _, groupSku := range groupSkuMap {
		for _, skuId := range groupSku {
			skuCode := &ic.SkuCodeInfo{}
			skuCode.FinanceCode = financeCode
			skuCode.Sku = cast.ToString(skuId)
			skuCodeList = append(skuCodeList, skuCode)
		}
	}

	// 上面的查询 channelIds是按仓库分组的，这里的所有渠道都是对应一个仓库
	// 因此可以随便传一个渠道id
	skuStockMap, err := GetStockInfoBySkuCode(0, skuCodeList, cast.ToInt32(strings.Split(channelIds, ",")[0]), 1)
	if err != nil {
		glog.Errorf("doPushGroupStock 批量查询组合商品sku库存异常，财务编码：%s,err:%+v", financeCode, err)
		return err
	}

	// 发送MQ消息，推送组合商品库存给第三方渠道
	err = sendMqUpdateGroupStockOnThirdChannel(financeCode, groupSkuMap, skuStockMap, channelIds)
	if err != nil {
		glog.Errorf("doPushGroupStock 推送第三方渠道组合商品库存异常，财务编码：%s,err:%+v", financeCode, err)
		return err
	}
	return nil
}

// 推送MQ消息，更新美团、饿了么、京东到家等第三方渠道组合商品库存
func sendMqUpdateGroupStockOnThirdChannel(financeCode string, groupSkuMap map[int32][]int32, skuStockMap map[string]int32, channelIds string) error {
	var skuId string
	list := make([]models.FoodDataStore, 0, len(groupSkuMap))

	for productId, skuIds := range groupSkuMap {
		productSku := models.FoodDataStore{
			App_food_code: cast.ToString(productId),
		}
		for _, item := range skuIds {
			skuId = cast.ToString(item)
			productSku.Skus = append(productSku.Skus, models.SkusStore{
				Sku_id: skuId,
				Stock:  cast.ToString(skuStockMap[fmt.Sprintf("%s:%s", financeCode, skuId)]),
			})
		}
		list = append(list, productSku)
	}

	//由于美团、饿了么业务场景问题，则最大处理数量为100条，需要分页处理
	pageSize := 100
	pageCount := (len(list) + pageSize - 1) / pageSize
	var skuSharding []models.FoodDataStore
	var mqList []models.MqInfo
	for i := 0; i < pageCount; i++ {
		if i == pageCount-1 {
			skuSharding = list[i*pageSize:]
		} else {
			skuSharding = list[i*pageSize : (i+1)*pageSize]
		}

		mqBody, _ := json.Marshal(models.StoreStock{
			App_poi_code: financeCode,
			Food_data:    skuSharding,
			Sender:       UpdateStockSender,
		})
		mqInfo := models.MqInfo{
			Exchange: "datacenter",
			Content:  string(mqBody),
		}

		for _, channelId := range strings.Split(channelIds, ",") {
			switch channelId {
			case "2": // 美团
				mqInfo.Quene = "dc_sz_stock_mq"
			case "3": // 饿了么
				mqInfo.Quene = "dc_sz_stock_mq_elm"
			case "4": // 京东到家
				mqInfo.Quene = "dc_sz_stock_mq_jddj"
			default:
				continue
			}
			mqList = append(mqList, mqInfo)
		}
	}

	_, err := orderEngine.Insert(mqList)
	if err != nil {
		glog.Errorf("sendMqUpdateGroupStockOnThirdChannel 保存第三方渠道组合商品库存MQ消息异常，财务编码：%s,err:%+v", financeCode, err)
		return err
	}
	return nil
}

// 过滤组合商品三大渠道已经上架sku信息
func filterUpGroupSkuMap(list []models.FoodDataStore, financeCode string) (map[int32][]int32, error) {
	skuIdList := make([]string, 0, len(list))
	for _, m := range list {
		for _, n := range m.Skus {
			skuIdList = append(skuIdList, n.Sku_id)
		}
	}

	// 找出组合商品skuId
	var skuIds []int
	err := engine.Table("channel_sku_group").In("group_sku_id", skuIdList).GroupBy("sku_id").Select("sku_id").Find(&skuIds)
	if err != nil {
		glog.Errorf("filterGroupSkuId 查询组合商品skuId异常,财务编码:%s,err:%+v", financeCode, err)
		return nil, err
	}
	if len(skuIds) == 0 {
		return nil, nil
	}

	var storeProductList []pc.ChannelStoreProduct
	err = engine.Table("channel_store_product").Alias("csp").Join("left", "channel_product cp", "csp.product_id=cp.id").Where("cp.product_type=3 and csp.channel_id in (2,3,4) and csp.up_down_state=1 and csp.finance_code=?", financeCode).In("csp.sku_id", skuIds).GroupBy("csp.sku_id").Select("csp.id,csp.sku_id,csp.product_id").Find(&storeProductList)
	if err != nil {
		glog.Errorf("filterGroupSkuId 查询门店组合商品三大渠道上架skuId异常,财务编码:%s,err:%+v", financeCode, err)
		return nil, err
	}
	result := make(map[int32][]int32)
	for _, item := range storeProductList {
		result[item.ProductId] = append(result[item.ProductId], item.SkuId)
	}
	return result, nil
}

// 更新阿闻渠道库存
func updateStockOnAwen(list []models.FoodDataStore, appPoiCode string) error {
	type Relation struct {
		ShopId     string // 财务编码
		ChannelIds string // 渠道id
	}
	var relations []*Relation

	if strings.HasPrefix(appPoiCode, "#") {
		if err := engine.Table("dc_dispatch.warehouse_relation_shop").
			Select("shop_id").In("channel_id", []int{1, 10}). // 1阿闻、10阿闻自提
			Where("warehouse_id = ?", strings.TrimLeft(appPoiCode, "#")).
			Select("shop_id,group_concat(channel_id) as channel_ids").GroupBy("shop_id").Find(&relations); err != nil {
			return errors.New("查询绑定关系 " + err.Error())
		} else if len(relations) < 1 {
			return nil
		}
	} else {
		relations = append(relations, &Relation{
			ShopId:     appPoiCode,
			ChannelIds: "1",
		})
	}

	for _, food_data := range list {
		v := food_data.Skus[0]
		id, _ := strconv.Atoi(v.Sku_id)
		stock, _ := strconv.Atoi(v.Stock)
		isok, warehouseId, err := updateHasStock(int32(id), int32(stock), appPoiCode)
		if err != nil {
			glog.Error("updateHasStock报错：" + err.Error())
			continue
		}
		//只有修改成功了说明数据有变动，才去查询组合商品的库存是否需要更新
		if isok <= 0 {
			continue
		}

		go func() {
			for _, r := range relations {
				channelId := cast.ToInt32(strings.Split(r.ChannelIds, ",")[0])
				err := updateGroupHasStock(id, r.ShopId, channelId, warehouseId)
				if err != nil {
					glog.Error("updateGroupHasStock报错：" + err.Error())
				}
			}
		}()
	}
	return nil
}

//处理组合商品从无到有的修改
func updateGroupHasStock(skuid int, financeCode string, channelId int32, warehouseId int) error {

	groupSkuids := make([]int, 0)
	//NewDbConn()
	//根据skuid查询所有这个门店下包含这个SKUID的组合商品,只需要查询已经上架的，没上架的上架的时候会查询更新，只需要查询has_stock=0的，有库存的下单的时候会查询更新
	if err := engine.SQL(`select DISTINCT b.sku_id from channel_sku_group b 
      inner join channel_store_product_has_stock c on b.sku_id=c.sku_id 
     where b.group_sku_id=? and c.has_stock=0  and c.channel_id=1  and c.warehouse_id=?`, skuid, warehouseId).Find(&groupSkuids); err != nil {
		glog.Error(err)
		return err
	}

	if len(groupSkuids) > 0 {

		for _, x := range groupSkuids {

			//查询库存
			skuCodes := []*ic.SkuCodeInfo{}
			skuCode := &ic.SkuCodeInfo{}
			skuCode.FinanceCode = financeCode
			skuCode.Sku = cast.ToString(x)
			skuCode.StockWarehouse = append(skuCode.StockWarehouse, int32(warehouseId))
			skuCodes = append(skuCodes, skuCode)
			//只要去查询了库存，库存那边就会调用组合商品的库存推送更新商品这边的has_stock
			_, err := GetStockInfoBySkuCode(0, skuCodes, channelId, 1) //查询本地的库存
			if err != nil {
				glog.Error("GetStockInfoBySkuCode查询库存失败：", err)
			}
		}
	}
	return nil
}

//组合商品的库存被查询会直接调用这个方法,返回是否更新成功，更新成功了才去做组合商品的操作
func updateHasStock(id, stock int32, financeCode string) (int, int, error) {
	//用于更新的财务编码
	var codes []string
	//处理financeCode是仓库ID的数据
	warehouseId := 0
	//如果包含#传的是仓库ID就直接用，否则转成仓库ID
	if strings.HasPrefix(financeCode, "#") {
		warehouseId = cast.ToInt(strings.TrimLeft(financeCode, "#"))
		if err := engine.Table("dc_dispatch.warehouse_relation_shop").
			Select("shop_id").In("channel_id", []int{1, 10}). // 1阿闻、10阿闻自提
			Where("warehouse_id = ?", warehouseId).
			Select("DISTINCT shop_id").Find(&codes); err != nil {
			return 0, 0, errors.New("查询绑定关系 " + err.Error())
		} else if len(codes) < 1 {
			return 0, 0, nil
		}
	} else {
		codes = append(codes, financeCode)
		redisHandle := GetRedisConn()
		if kit.EnvCanCron() {
			defer redisHandle.Close()
		}

		// 获取阿闻渠道绑定的仓库
		warehouse := utils.LoadChannelWarehouseCache(redisHandle, financeCode, enum.ChannelAwenId)
		if warehouse != nil {
			warehouseId = int(warehouse.WarehouseId)
		} else {
			glog.Infof("updateHasStock 更新有无库存 找不到阿闻渠道仓库:%s", financeCode)
		}
	}

	var retCount int64
	//查询到了仓库才去处理
	if warehouseId != 0 {
		hasStock := 1
		if stock > 0 {
			stock = 1
			hasStock = 0
		} else {
			stock = 0
		}

		//先插入channel_store_product_has_stock
		err := Add_Product_Has_Stock(id, codes, warehouseId)
		if err != nil {
			return 0, 0, err
		}

		// 暂时不用 finance_code in更新，会有死锁
		var stringsData []string
		err = engine.Table("channel_store_product_has_stock").Cols("channel_store_product_id").
			Where("channel_id=1 AND has_stock=? AND warehouse_id =? AND sku_id=?", hasStock, warehouseId, id).Find(&stringsData)
		if err != nil {
			glog.Error("is failed!", err.Error())
			return 0, 0, err
		}
		if len(stringsData) <= 0 {
			return 0, 0, nil
		}
		glog.Info("ints: ", stringsData)
		//ok, err := session.Exec("UPDATE channel_store_product_has_stock a JOIN channel_store_product b " +
		//	"ON a.channel_store_product_id = b.id SET a.has_stock=?,a.has_stock_up=b.up_down_state*? " +
		//	"WHERE a.channel_id=1 AND a.has_stock=? AND a.warehouse_id =? AND a.sku_id=?", stock, stock, hasStock, warehouseId, id)

		// 查询出要处理的数据集合和上架状态
		var list []models.ChannelStoreProductHasStock
		sql := `SELECT a.id,a.channel_store_product_id,b.up_down_state*? has_stock_up, ? has_stock FROM channel_store_product_has_stock a 
		INNER JOIN channel_store_product b ON a.channel_store_product_id = b.id 
		WHERE a.channel_id=1 AND a.has_stock=? AND a.warehouse_id =? AND a.sku_id=?;`
		if err = engine.SQL(sql, stock, stock, hasStock, warehouseId, id).Find(&list); err != nil {
			return 0, 0, err
		} else if len(list) == 0 {
			return 0, 0, nil
		}
		for _, v := range list {
			sql = `UPDATE channel_store_product_has_stock set has_stock=?,has_stock_up=? where Id=? ;`
			_, err := engine.Exec(sql, v.HasStock, v.HasStockUp, v.Id)
			if err != nil {
				glog.Error("update channel_store_product_has_stock", err.Error())
				return 0, 0, err
			}
			retCount++
		}
		// 先更新has_stock表， 再上架表的时间
		if len(stringsData) > 0 {
			_, err = engine.Exec("update channel_store_product set update_date=now() where id in (" + strings.Join(stringsData, ",") + ")")
			if err != nil {
				glog.Error("update channel_store_update", err.Error())
			}
		}
	}
	return int(retCount), warehouseId, nil
}

//先处理channel_store_product_has_stock不存在的就先插入数据
func Add_Product_Has_Stock(skuId int32, codes []string, warehouseId int) error {
	_, err := engine.Exec("INSERT INTO channel_store_product_has_stock (channel_store_product_id, warehouse_id,channel_id,sku_id,finance_code,product_id,create_time,update_time,has_stock,has_stock_up) " +
		" SELECT f.id," + cast.ToString(warehouseId) + ",1," + cast.ToString(skuId) + ",f.finance_code,f.product_id,NOW(),NOW(),0,0 FROM channel_store_product f LEFT JOIN channel_store_product_has_stock r " +
		" ON f.id = r.channel_store_product_id AND r.warehouse_id= " + cast.ToString(warehouseId) +
		" WHERE  f.finance_code IN (" + "'" + strings.Join(codes, "','") + "'" + ") AND f.sku_id=" + cast.ToString(skuId) + " AND f.channel_id=1 AND r.channel_store_product_id IS NULL ")

	if err != nil {
		glog.Error("插入channel_store_product_has_stock出错：" + err.Error())
	}

	return nil
}

//根据传入的SKUID，过滤掉没有库存的返回
func (c *Product) GetHasStockSkuIDs(_ context.Context, in *pc.HasStockSkuIDsRequest) (*pc.HasStockSkuIDsResponse, error) {
	out := new(pc.HasStockSkuIDsResponse)
	out.Code = 400
	engine := NewDbConn()
	engine.ShowSQL(true)
	if err := engine.Table("channel_store_product").Alias("a").Join("left", "channel_store_product_has_stock b", "a.id = b.channel_store_product_id").
		In("a.sku_id", in.SkuId).Where("a.finance_code=? AND a.channel_id=1 and b.has_stock = 1", in.FinanceCode).Select("a.sku_id").GroupBy("a.sku_id").Find(&out.SkuId); err != nil {
		out.Error = err.Error()
		return out, err
	}
	out.Code = 200
	return out, nil
}
