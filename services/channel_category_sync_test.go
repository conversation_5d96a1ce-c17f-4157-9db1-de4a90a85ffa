package services

import (
	"_/proto/pc"
	"context"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/ptypes/empty"
	"reflect"
	"testing"
)

func TestProduct_ImportSyncCategoryShop(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.ImportSyncCategoryShopRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &pc.ImportSyncCategoryShopRequest{
					ChannelId: "2",
					QiniuUrl:  "baidu.cin",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := this.ImportSyncCategoryShop(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ImportSyncCategoryShop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ImportSyncCategoryShop() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetAndDeleteCategory(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.SyncCategoryRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.GetAndDeleteCategoryResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryRequest{
					ChannelId:     2,
					AppPoiCode:    "4889_2705535",
					FinanceCode:   "CX0004",
					StoreMasterId: 1,
				},
			},
		},
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryRequest{
					ChannelId:     2,
					AppPoiCode:    "6411_2700983",
					FinanceCode:   "CX0013",
					StoreMasterId: 2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := this.GetAndDeleteCategory(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}

//SyncCategorySchedulerCallBack
func TestProduct_SyncCategorySchedulerCallBack(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.SyncCategoryScheduleCallBackRequest
	}
	var tests = []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
		data    string
	}{
		// TODO: Add test cases.
		{
			name: "",
			data: `[{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"AA0193","ShopName":"安安广州中山小榄","IsSuccess":false,"Message":"","ChannelId":3,"SyncType":1,"ChannelStoreId":"MINHIN50","AppChannel":1,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"CX0010","ShopName":"宠颐生北京爱福","IsSuccess":false,"Message":"","ChannelId":3,"SyncType":1,"ChannelStoreId":"32267735205","AppChannel":1,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"CX0004","ShopName":"宠颐生北京爱之都","IsSuccess":false,"Message":"","ChannelId":3,"SyncType":1,"ChannelStoreId":"100000288783","AppChannel":2,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"CX0011","ShopName":"宠颐生北京爱之源1234","IsSuccess":false,"Message":"","ChannelId":3,"SyncType":1,"ChannelStoreId":"6824006998089739852","AppChannel":2,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"DSS0193","ShopName":"","IsSuccess":false,"Message":"","ChannelId":3,"SyncType":1,"ChannelStoreId":"201522411","AppChannel":2,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"RP0179","ShopName":"瑞鹏武汉奥山","IsSuccess":false,"Message":"不存在此门店","ChannelId":2,"SyncType":1,"ChannelStoreId":"234","AppChannel":1,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"RP0158","ShopName":"瑞鹏珠海九洲大道","IsSuccess":false,"Message":"不存在此门店","ChannelId":2,"SyncType":1,"ChannelStoreId":"6411_2701014","AppChannel":2,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"CX0004","ShopName":"宠颐生北京爱之都","IsSuccess":false,"Message":"不存在此门店","ChannelId":2,"SyncType":1,"ChannelStoreId":"6411_2701032","AppChannel":2,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"CX0159","ShopName":"宠颐生锦州中心","IsSuccess":false,"Message":"不存在此门店","ChannelId":2,"SyncType":1,"ChannelStoreId":"6411_2701024","AppChannel":2,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"CX0013","ShopName":"宠颐生北京爱佳","IsSuccess":false,"Message":"商品分类已存在:category_name=PWT001","ChannelId":2,"SyncType":1,"ChannelStoreId":"6411_2700983","AppChannel":2,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"CX0011","ShopName":"宠颐生北京爱之源1234","IsSuccess":false,"Message":"相同sig不能并发请求，请等待上一个请求返回结果后，再进行下一步操作。相关请求sig：df78b7ccde39643a06ab38c7bb9e778f","ChannelId":2,"SyncType":1,"ChannelStoreId":"6411_2700983","AppChannel":2,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"","ShopName":"","IsSuccess":false,"Message":"","ChannelId":4,"SyncType":1,"ChannelStoreId":"","AppChannel":1,"ParentId":0,"Sort":0},{"CategoryId":201540324,"CategoryName":"PWT001","FinanceCode":"","ShopName":"","IsSuccess":false,"Message":"","ChannelId":4,"SyncType":1,"ChannelStoreId":"","AppChannel":2,"ParentId":0,"Sort":0}]`,
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryScheduleCallBackRequest{
					TaskId: 183808,
					Vo:     []*pc.SyncCategoryScheduleRequest{},
				},
			},
		},
	}

	SetupDB()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &Product{}
			var data []*pc.SyncCategoryScheduleRequest
			json.Unmarshal([]byte(tt.data), &data)

			tt.args.in.Vo = data // 传入数据

			got, err := this.SyncCategorySchedulerCallBack(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ImportSyncCategoryShop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ImportSyncCategoryShop() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_RenewCategory(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.SyncCategoryRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.RenewCategoryResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryRequest{
					ChannelId:     2,
					AppPoiCode:    "4889_2705535",
					FinanceCode:   "CX0004",
					StoreMasterId: 1,
				},
			},
		},
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryRequest{
					ChannelId:     2,
					AppPoiCode:    "6411_2700983",
					FinanceCode:   "CX0013",
					StoreMasterId: 2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := this.RenewCategory(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}

func TestProduct_RelateCategoryAndProduct(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.SyncCategoryRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.RelateCategoryAndProductResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		//{"channel_id":2,"finance_code":"CX0004","app_poi_code":"4889_2705535","store_master_id":1})
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryRequest{
					ChannelId:     2,
					AppPoiCode:    "4889_2705535",
					FinanceCode:   "CX0004",
					StoreMasterId: 1,
				},
			},
		},
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &pc.SyncCategoryRequest{
					ChannelId:     2,
					AppPoiCode:    "6411_2700983",
					FinanceCode:   "CX0013",
					StoreMasterId: 2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			_, _ = this.RelateCategoryAndProduct(tt.args.ctx, tt.args.in)
			//fmt.Println(got, err)
		})
	}
}

func TestProduct_SyncChannelCategoryScheduleCallback(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		req *pc.SyncChannelCategoryScheduleCallbackRequest
	}
	str := `{"task_id":182770,"data":[{"finance_code":"CX0013","shop_name":"爱佳","is_success":true,"message":"","category_id":201540413,"category_name":"CST2单实物","category_post_err":"secondary_category_code与secondary_category_name对应的不是同一个分类","product_id":0,"sku_id":0,"sku_post_err":"","system_error":""},{"finance_code":"CX0013","shop_name":"爱佳","is_success":true,"message":"","category_id":201540410,"category_name":"sit2分类","category_post_err":"商品分类已存在:category_name=sit2分类","product_id":0,"sku_id":0,"sku_post_err":"","system_error":""},{"finance_code":"CX0013","shop_name":"爱佳","is_success":true,"message":"","category_id":201540411,"category_name":"sit2实物","category_post_err":"父分类同步失败:商品分类已存在:category_name=sit2分类","product_id":0,"sku_id":0,"sku_post_err":"","system_error":""},{"finance_code":"CX0013","shop_name":"爱佳","is_success":true,"message":"","category_id":201540406,"category_name":"分类21","category_post_err":"商品分类已存在:category_name=分类21","product_id":0,"sku_id":0,"sku_post_err":"","system_error":""},{"finance_code":"CX0013","shop_name":"爱佳","is_success":true,"message":"","category_id":201540407,"category_name":"分类1020","category_post_err":"父分类同步失败:商品分类已存在:category_name=分类21","product_id":0,"sku_id":0,"sku_post_err":"","system_error":""},{"finance_code":"CX0013","shop_name":"爱佳","is_success":true,"message":"","category_id":201540232,"category_name":"","category_post_err":"该商品快照里的分类在第三方分类数据里未找到","product_id":1020753,"sku_id":1020753001,"sku_post_err":"","system_error":""}]}`
	paren := new(pc.SyncChannelCategoryScheduleCallbackRequest)
	_ = json.Unmarshal([]byte(str), &paren)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *empty.Empty
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				req: &pc.SyncChannelCategoryScheduleCallbackRequest{
					TaskId: paren.TaskId,
					Data:   paren.Data,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := c.SyncChannelCategoryScheduleCallback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SyncChannelCategoryScheduleCallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SyncChannelCategoryScheduleCallback() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_CancelSyncChannelCategoryTask(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		req *pc.CancelTaskRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.CancelTaskResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				req: &pc.CancelTaskRequest{
					Id: 183884,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := c.CancelSyncChannelCategoryTask(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CancelSyncChannelCategoryTask() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CancelSyncChannelCategoryTask() got = %v, want %v", got, tt.want)
			}
		})
	}
}
