package services

import (
	"testing"
)

func TestDealAsyncTaskSwitchWarehouse(t *testing.T) {
	type args struct {
		taskContent int32
	}
	tests := []struct {
		name string
		args args
		want func() bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				taskContent: 67,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := DealAsyncTaskSwitchWarehouse(tt.args.taskContent)
			got()

		})
	}
}
