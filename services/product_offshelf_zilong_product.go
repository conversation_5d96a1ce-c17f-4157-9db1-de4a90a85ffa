package services

import (
	"_/models"
	"_/proto/dac"
	"_/proto/pc"
	"_/utils"
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/maybgit/glog"
	"github.com/ppkg/distributed-worker/dto"
	kit "github.com/tricobbler/rp-kit"
)

// 下架子龙无效(已停用或不存在)商品
func (c *Product) OffshelfZilongInvalidProduct(ctx context.Context, req *pc.OffshelfZilongInvalidProductRequest) (*pc.OffshelfZilongInvalidProductResponse, error) {
	resp := &pc.OffshelfZilongInvalidProductResponse{
		Code: http.StatusBadRequest,
	}
	// 验证Excel地址是否为空
	if req.ExcelUrl == "" {
		resp.Message = "Excel文档地址不能为空"
		return resp, nil
	}

	// 解析Excel文档并过滤符合规则子龙货号
	skuIdList, err := c.parseZilongProductExcel(ctx, req.ExcelUrl)
	if err != nil {
		glog.Errorf("offshelfZilongInvalidProduct 解析子龙商品Excel文档异常:%+v,文档地址:%s", err, req.ExcelUrl)
		resp.Message = fmt.Sprintf("解析子龙商品Excel文档异常:%+v", err)
		return resp, nil
	}
	if len(skuIdList) == 0 {
		glog.Warningf("offshelfZilongInvalidProduct 没有任何子龙商品需要进行下架处理,请求入参:%s", kit.JsonEncode(req))
		resp.Message = "没有任何子龙商品需要进行下架处理"
		return resp, nil
	}

	// 子龙货号换算成商品ID
	productIdList, err := c.zilongSkuIdToAwenProductId(skuIdList)
	if err != nil {
		resp.Message = err.Error()
		return resp, nil
	}
	if len(productIdList) == 0 {
		glog.Warningf("offshelfZilongInvalidProduct 子龙货号换算成阿闻商品ID后为空,请求入参:%s", kit.JsonEncode(req))
		resp.Message = "没有任何子龙商品需要进行下架处理"
		return resp, nil
	}

	// 获取所有渠道店铺
	storeList, err := c.listChannelAllStore()
	if err != nil {
		resp.Message = err.Error()
		return resp, nil
	}

	// 过滤绑定子龙仓库的渠道门店
	storeList = c.filterZilongChannelStore(storeList)

	// 提交分布式任务
	name := fmt.Sprintf("%d个子龙商品下架处理-%s", len(productIdList), time.Now().Format("************"))
	jobReq := dto.AsyncJobRequest{
		Name:     name,
		Label:    "子龙商品下架",
		Type:     "offshelfZilongInvalidProduct",
		IsNotify: true,
		PluginSet: []string{
			"offshelfZilongInvalidProduct",
		},
		TaskInputList:          []string{},
		TaskExceptionOperation: 1,
	}

	productList := make([]zilongProductInfo, 0, len(productIdList))
	for _, v := range productIdList {
		productList = append(productList, zilongProductInfo{
			Id: v,
		})
	}

	for _, v := range storeList {
		jobReq.TaskInputList = append(jobReq.TaskInputList, kit.JsonEncode(offshelfZilongProductInputItem{
			FinanceCode: v.FinanceCode,
			ChannelId:   v.ChannelId,
			List:        productList,
			UserInfo:    loadLoginUserInfo(ctx),
		}))
	}

	jobId, err := workerCtx.AsyncSubmitJob(jobReq)
	if err != nil {
		glog.Errorf("offshelfZilongInvalidProduct 请求分布式调度异常:%+v,请求参数:%s", err, kit.JsonEncode(req))
		resp.Message = fmt.Sprintf("请求分布式调度异常:%+v", err)
		return resp, nil
	}
	resp.Code = http.StatusOK
	resp.JobId = jobId
	return resp, nil
}

// 过滤绑定子龙仓库的渠道门店
func (c *Product) filterZilongChannelStore(list []*dac.StoreInfo) []*dac.StoreInfo {
	financeCodeMap := make(map[string][]*dac.StoreInfo)
	for _, v := range list {
		financeCodeMap[v.FinanceCode] = append(financeCodeMap[v.FinanceCode], v)
	}

	var result []*dac.StoreInfo

	redisConn := GetRedisConn()
	if kit.EnvCanCron() {
		defer redisConn.Close()
	}

	for financeCode, storeList := range financeCodeMap {
		warehouse := LoadWarehouseRelationCache(financeCode)
		if len(warehouse) == 0 {
			glog.Infof("offshelfZilongInvalidProduct/filterZilongChannelStore %s 没有查询到仓库关联关系(warehouse:store:relation)", financeCode)
			continue
		}

		isZilongStore := false
		for _, item := range warehouse {
			if item.Category == 3 && item.Status == 1 {
				isZilongStore = true
				break
			}
		}
		if isZilongStore {
			result = append(result, storeList...)
		} else {
			glog.Infof("offshelfZilongInvalidProduct/filterZilongChannelStore %s 没有绑定子龙仓库", financeCode)
		}
	}
	return result
}

// 获取所有渠道门店列表
func (c *Product) listChannelAllStore() ([]*dac.StoreInfo, error) {
	client := dac.GetDataCenterClient()
	defer client.Close()

	var list storeListSort

	channelIds := []int32{
		ChannelAwenId,
		ChannelMtId,
		ChannelElmId,
		ChannelJddjId,
	}

	for _, channelId := range channelIds {
		req := &dac.StoreInfoRequest{
			ChannelId: channelId,
		}
		resp, err := client.RPC.QueryStoreInfo(client.Ctx, req)
		if err != nil {
			glog.Errorf("listChannelAllStore 获取渠道(%d)店铺信息异常:%+v", req.ChannelId, err)
			return nil, fmt.Errorf("获取渠道(%d)店铺信息异常:%+v", req.ChannelId, err)
		}
		if resp.Code != http.StatusOK {
			glog.Errorf("listChannelAllStore 获取渠道(%d)店铺信息失败:%s", req.ChannelId, kit.JsonEncode(resp))
			return nil, fmt.Errorf("获取渠道(%d)店铺信息失败:%s,%s", req.ChannelId, resp.Message, resp.Error)
		}
		list = append(list, resp.Details...)
	}

	return list, nil
}

type storeListSort []*dac.StoreInfo

func (s storeListSort) Len() int {
	return len(s)
}
func (s storeListSort) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}
func (s storeListSort) Less(i, j int) bool {
	if s[i].FinanceCode < s[j].FinanceCode {
		return true
	}
	if s[i].FinanceCode > s[j].FinanceCode {
		return false
	}
	return s[i].ChannelId < s[j].ChannelId
}

// 子龙货号换算成商品ID
func (c *Product) zilongSkuIdToAwenProductId(list []string) ([]int32, error) {
	session := NewDbConn().NewSession()
	defer session.Close()
	var result []int32

	var pageSize int32 = 100
	pages := (int32(len(list)) + pageSize - 1) / pageSize
	for i := int32(0); i < pages; i++ {
		var skuIds []string
		if i == pages-1 {
			skuIds = list[i*pageSize:]
		} else {
			skuIds = list[i*pageSize : (i+1)*pageSize]
		}

		var list []models.GjSkuThird
		err := session.Table("gj_sku_third").In("third_sku_id", skuIds).Select("sku_id,product_id,third_sku_id,erp_id").Find(&list)

		if err != nil {
			glog.Errorf("offshelfZilongInvalidProduct/zilongSkuIdToAwenProductId 第%d页 子龙货号换算成商品ID异常:%+v,请求参数:%s", i+1, err, kit.JsonEncode(skuIds))
			return nil, fmt.Errorf("第%d页 子龙货号换算成商品ID异常:%+v,请求参数:%s", i+1, err, kit.JsonEncode(skuIds))
		}
		glog.Infof("offshelfZilongInvalidProduct/zilongSkuIdToAwenProductId 第%d页 子龙货号换算成商品ID结果:%s,请求参数:%s", i+1, kit.JsonEncode(list), kit.JsonEncode(skuIds))

		productIdMap := make(map[int32]struct{}, len(list))
		var noneZilongProductIds []int32
		for _, v := range list {
			// 如果存在不是子龙货号则需要剔除
			if v.ErpId != 4 {
				noneZilongProductIds = append(noneZilongProductIds, int32(v.ProductId))
				glog.Infof("offshelfZilongInvalidProduct/zilongSkuIdToAwenProductId 第%d页 存在非子龙货号:%s", i+1, kit.JsonEncode(v))
				continue
			}
			productIdMap[int32(v.ProductId)] = struct{}{}
		}
		for _, v := range noneZilongProductIds {
			delete(productIdMap, v)
		}
		for k := range productIdMap {
			result = append(result, k)
		}
	}
	return result, nil
}

// 解析子龙商品Excel文档
func (c *Product) parseZilongProductExcel(ctx context.Context, url string) ([]string, error) {
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return nil, errors.New("用户不存在")
	}

	// 下载excel
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("构建下载Excel文档(%s)请求异常,%+v", url, err)
	}

	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		return nil, fmt.Errorf("执行下载Excel文档(%s)请求异常,%+v", url, err)
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文档(%s)异常,%+v", url, err)
	}

	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows := f.GetRows(sheetName)

	list := make([]string, 0, len(rows))
	for i, item := range rows {
		if i == 0 {
			continue
		}
		// 子龙货号
		skuId := strings.TrimSpace(item[0])
		// 如果是组合商品直接跳过
		if strings.HasSuffix(skuId, "099") {
			continue
		}
		// 在子龙系统是否存在
		isExist := strings.TrimSpace(item[1])
		// 如果不存在则加入到list中
		if isExist == "0" {
			list = append(list, skuId)
			continue
		}
		// 在子龙系统是否停用
		isOffshelf := strings.TrimSpace(item[2])
		// 如果停用则加入到list中
		if isOffshelf == "1" {
			list = append(list, skuId)
			continue
		}
	}
	return list, nil
}

type offshelfZilongProductInputItem struct {
	// 门店财务编码
	FinanceCode string `json:"finance_code"`
	// 渠道，1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;
	ChannelId int32 `json:"channel_id"`
	// 需要下架的商品ID
	List []zilongProductInfo `json:"list"`
	// 用户信息
	UserInfo *models.LoginUserInfo `json:"user_info"`
}

type zilongProductInfo struct {
	Id int32 `json:"id"`
}
