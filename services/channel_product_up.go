package services

import (
	"_/enum"
	"_/models"
	"_/proto/dac"
	"_/proto/et"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/empty"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

////-------------------   商品上架逻辑处理封装

// 上架执行结果
type ChannelProductUp_Result struct {
	// 商品信息
	StoreProduct *ChannelProductUp_StoreProduct
	// 是否成功
	IsSuccess bool
	// 信息
	Message string
}

// 商品上架包装器
// A.实例化ChannelProductUpDown
// B.调用UpProduct方法执行上架
// C.(可选)调用Export将错误结果导出到七牛云
type ChannelProductUpDown struct {
	// 商品Id列表-必须
	ProductIds []string
	// 渠道-必须
	ChannelId int
	// 需要上架的门店财务代码-必须
	FinanceCodes []string
	// 操作的用户代码
	UserNo   string
	UserName string
	// 是否需要同步价格
	IsSyncPrice bool
	// 商品与门店财务代码的关联关系
	StoreProducts []*ChannelProductUp_StoreProduct
	// 上架处理结果
	UpResult []*ChannelProductUp_Result
	// 是否出现了未捕获的异常
	UnknownError error
	Ctx          context.Context `json:"-"`

	TaskId int

	// 获取门店对应的appChannel数据
	FinanceCodesAppChannel map[string]int

	// 下架类型0：单个的手动下架 1: 7天无库存自动下架 2：批量下架  3: 过期商品自动下架 4：oms停用商品自动下架
	DownType int

	// 上架类型(2上架3批量上架4下架 5批量下架6自动上架7自动下架)
	UpType int
}

// 商品与店铺关联关系
type ChannelProductUp_StoreProduct struct {
	// 门店财务编码列表 -- 必填
	StoreFinanceCode string
	// 门店在渠道里的Id
	StoreChannelFinanceCodes string
	// 仓库类型 3 门店仓 4 前置仓
	StoreWarehouseCategory int
	// 门店对应仓库Id
	StoreWarehouseId int
	// 前置仓售药资质
	//WarehouseSellDrugs int // v6.27.2 删除
	// 商品Id -- 必填
	ProductId string
	// 商品Sku -- 必填
	ProductSkuId string
	// 商品在渠道的分类Id
	ProductChannelCategoryId int
	// 第三方平台分类id
	ProductChannelTagId int
	// 商品库存数量
	ProductStock int
	// 门店仓价格
	ProductSkuStorePrice int32
	// 前置仓价格
	ProductSkuPreposePrice int32
	// 商品重量为空 -- 必填
	ProductWeight float64
	// 商品A8货号
	ProductA8SkuId string
	// 商品的子龙货号
	ProductZilongId string
	// 1-实物 2-虚拟 3-组合商品
	ProductType int
	// 组合商品应用范围 1 电商 2 前置仓 3 门店仓
	ProductGroupUseRang map[int]bool
	// 组合商品有效期截止日期
	ProductGroupTermEndDate time.Time
	// 商品快照Id
	ProductSnapshotId int
	// 第三方的商品ID
	ProductThirdId string
	// 操作第三方时候的错信息
	SyncError string
}

// 渠道商品上架，支持单门店多门店
func (c *Product) UpChannelProduct(ctx context.Context, request *pc.UpDownChannelProductRequest) (*pc.BaseResponse, error) {
	logPrefix := fmt.Sprintf("商品上架====,渠道：%d,门店：%s,商品：%s", request.ChannelId, kit.JsonEncode(request.FinanceCode), kit.JsonEncode(request.ProductId))
	glog.Info(logPrefix, "入参：", kit.JsonEncode(request))
	var response = &pc.BaseResponse{Code: 400}
	// 上下架类封装
	var channelProductUpDown ChannelProductUpDown
	channelProductUpDown.ProductIds = request.ProductId
	channelProductUpDown.ChannelId = int(request.ChannelId)
	channelProductUpDown.FinanceCodes = request.FinanceCode
	channelProductUpDown.UserNo = request.UserNo
	channelProductUpDown.UserName = request.UserName
	channelProductUpDown.Ctx = ctx
	// 全部渠道需要同步价格 商品上架需要同步价格
	channelProductUpDown.IsSyncPrice = true
	//if request.ChannelId == ChannelAwenId || request.ChannelId == ChannelElmId || request.ChannelId == ChannelJddjId {
	//	channelProductUpDown.IsSyncPrice = true
	//}
	//单个门店  单个商品
	if len(request.FinanceCode) == 1 && len(request.ProductId) == 1 {
		channelProductUpDown.UpType = enum.RecordTypeUpOne // 日志记录
		channelProductUpDown.UpPorudct()
		if channelProductUpDown.UnknownError != nil {
			response.Message = channelProductUpDown.UnknownError.Error()
			return response, nil
		}
		if len(channelProductUpDown.UpResult) > 0 {
			if channelProductUpDown.UpResult[0].IsSuccess == false {
				response.Message = channelProductUpDown.UpResult[0].Message
				//v6.3.6 将错误传递出去给分布式计算那边
				for i := range channelProductUpDown.UpResult {
					result := channelProductUpDown.UpResult[i]
					downERROR := &pc.UpDownERROR{
						FinanceCode: result.StoreProduct.StoreFinanceCode,
						ProductId:   result.StoreProduct.ProductId,
						SkuId:       result.StoreProduct.ProductSkuId,
						IsSuccess:   result.IsSuccess,
						Message:     result.Message,
					}
					response.UpDownDetail = append(response.UpDownDetail, downERROR)
				}

				return response, nil
			}
		}
	} else {
		userTask, err := c.GetUserUnFinishedTask(ctx, &pc.GetUserUnFinishedTaskRequest{
			UserNo:        request.UserNo,
			ChannelId:     request.ChannelId,
			TaskContent:   4,
			OperationData: utils.ObjectToJsonString(channelProductUpDown),
		})
		//userTask,err := GetUserUnFinishedTask(request.UserNo,4,request.ChannelId,utils.ObjectToJsonString(channelProductUpDown))
		if err != nil {
			response.Message = "生成异步任务失败,请稍后重试"
			return response, nil
		}
		if userTask.Id > 0 {
			response.Message = "已经存在相同的任务"
			return response, nil
		}
		//保存到异步任务
		task := models.TaskList{
			ChannelId:        request.ChannelId,
			TaskContent:      4,
			OperationFileUrl: utils.ObjectToJsonString(channelProductUpDown),
			ModifyId:         request.UserNo,
			CreateId:         request.UserNo,
			CreateName:       request.UserName,
			CreateMobile:     request.UserMobile,
			CreateIp:         request.UserIp,
			IpLocation:       request.IpLocation,
			ExtendedData:     enum.ChannelMap[int(request.ChannelId)] + enum.TaskContentMapText[4],
		}
		_, err = InsertTaskList(task)
		if err != nil {
			glog.Error(err)
			response.Message = "生成异步任务失败,请稍后重试"
			return response, nil
		}
	}

	response.Code = 200
	return response, nil
}

// 根据第三方回调，判断是否调用下架
func (c *Product) ThirdDownChannelProduct(ctx context.Context, request *pc.ThirdDownChannelProductRequest) (*pc.BaseResponse, error) {
	var response = &pc.BaseResponse{Code: 400}

	//先把第三方对应的财务编码转出来  store:relation:mttodc
	redisConn := GetRedisConn()

	//我们本地数据库需要下架的数据
	if len(request.Detail) > 0 {
		for _, x := range request.Detail {

			//此为不存在
			FinanceCode := redisConn.HGet("store:relation:mttodc", x.AppPoiCode).Val()
			if FinanceCode != "" {
				var stringlist = make([]int, 0)
				for _, item := range x.ProductId {
					stringlist = append(stringlist, cast.ToInt(item))
				}
				user := new(models.ChannelStoreProduct)
				user.UpDownState = 0
				user.DownType = -1
				_, err := engine.Where("finance_code=? and channel_id=? and up_down_state=1", FinanceCode, request.ChannelId).In("product_id", stringlist).Cols("up_down_state", "down_type").Update(user)
				if err != nil {
					//只记录日志，不重试了
					glog.Error("第三方商品下架回调处理失败", " 财务编码:"+FinanceCode, " skuid:", stringlist, err)
				}
			}

		}

	}

	//需要在第三方下架的数据
	if len(request.DetailThird) > 0 {
		etClient := et.GetExternalClient()
		defer etClient.Close()
		for _, x := range request.DetailThird {
			FinanceCode := redisConn.HGet("store:relation:mttodc", x.AppPoiCode).Val()
			//需要调用下架的第三方ID是商品ID
			var stringlist = make([]int32, 0)
			var productSkus = make([]int32, 0)

			for _, productitem := range x.ProductId {
				stringlist = append(stringlist, cast.ToInt32(productitem))
			}

			setAll := utils.NewSet(stringlist...)
			engine = NewDbConn()

			if FinanceCode != "" {
				//查询在我们这边是否上架并且存在，否则下架
				err := engine.Table("channel_store_product").Where("finance_code=? and channel_id=? and up_down_state=1 ", FinanceCode, request.ChannelId).In("product_id", stringlist).Select("product_id").Find(&productSkus)
				if err != nil {
					//只记录日志，不重试了
					glog.Error("第三方商品下架回调处理失败", " 财务编码:"+FinanceCode, " product_id:", stringlist, err)
				}

				productSet := utils.NewSet(productSkus...)
				setAll = setAll.Minus(productSet)
				//需要调用第三方下架的skuid
				downproducts := setAll.ListString()

				if len(downproducts) > 0 && request.ChannelId == 2 {

					//直接执行下架操作
					//store:relation:mttodc 通过美团ID取门店信息
					var params = et.RetailSellStatusRequest{}
					params.AppPoiCode = x.AppPoiCode
					params.SellStatus = 1
					for _, foodCodeItem := range downproducts {
						params.FoodData = append(params.FoodData, &et.AppFoodCode{AppFoodCode: foodCodeItem})
					}

					storeMasterId := GetAppChannelByStoreId(x.AppPoiCode)
					params.StoreMasterId = storeMasterId

					res, err := etClient.RPC.RetailSellStatus(etClient.Ctx, &params)
					glog.Info("下架-3333上下架到美团返回数据", kit.JsonEncode(res), ",入参：", kit.JsonEncode(params), "错误err：", kit.JsonEncode(err))

					if err != nil {
						glog.Error("第三方商品下架回调处理失败", " 财务编码:"+FinanceCode, " product_id:", stringlist, err)
					}
					if res.Code != 200 {
						glog.Error("第三方商品下架回调处理失败", " 财务编码:"+FinanceCode, " product_id:", stringlist, res.Message)
					}
				}
			}
		}
	}

	response.Code = 200
	return response, nil
}

// 渠道商品下架，支持单门店多门店
func (c *Product) DownChannelProduct(ctx context.Context, request *pc.UpDownChannelProductRequest) (*pc.BaseResponse, error) {
	var response = &pc.BaseResponse{Code: 400}
	// 上下架类封装
	var channelProductUpDown ChannelProductUpDown
	channelProductUpDown.ProductIds = request.ProductId
	channelProductUpDown.ChannelId = int(request.ChannelId)
	channelProductUpDown.FinanceCodes = request.FinanceCode
	channelProductUpDown.UserNo = request.UserNo
	channelProductUpDown.UserName = request.UserName
	// 多门店或多商品保存到异步任务
	if len(request.FinanceCode) > 1 || len(request.ProductId) > 1 {
		userTask, err := c.GetUserUnFinishedTask(ctx, &pc.GetUserUnFinishedTaskRequest{
			UserNo:        request.UserNo,
			ChannelId:     request.ChannelId,
			TaskContent:   18,
			OperationData: utils.ObjectToJsonString(channelProductUpDown),
		})
		//userTask,err := GetUserUnFinishedTask(request.UserNo,18,request.ChannelId,utils.ObjectToJsonString(channelProductUpDown))
		if err != nil {
			response.Message = "生成异步任务失败,请稍后重试"
			return response, nil
		}
		if userTask.Id > 0 {
			response.Message = "已经存在相同的任务"
			return response, nil
		}
		//保存到异步任务,新的类型
		task := models.TaskList{
			ChannelId:        request.ChannelId,
			TaskContent:      18,
			OperationFileUrl: utils.ObjectToJsonString(channelProductUpDown),
			ModifyId:         request.UserNo,
			CreateId:         request.UserNo,
			CreateName:       request.UserName,
			CreateMobile:     request.UserMobile,
			CreateIp:         request.UserIp,
			IpLocation:       request.IpLocation,
			ExtendedData:     enum.ChannelMap[int(request.ChannelId)] + enum.TaskContentMapText[18],
		}
		_, err = InsertTaskList(task)
		if err != nil {
			glog.Error(err)
			response.Message = "生成异步任务失败,请稍后重试"
			return response, nil
		}
	} else {
		//执行下架
		channelProductUpDown.DownType = enum.DownRecordTypeOne
		channelProductUpDown.DownPorudct()
		if channelProductUpDown.UnknownError != nil {
			response.Message = channelProductUpDown.UnknownError.Error()
			return response, nil
		}
		// 下架结果
		if len(channelProductUpDown.UpResult) > 0 {
			response.Message = channelProductUpDown.UpResult[0].Message

			//v6.3.6 将错误传递出去给分布式计算那边
			for i := range channelProductUpDown.UpResult {
				result := channelProductUpDown.UpResult[i]
				downERROR := &pc.UpDownERROR{
					FinanceCode: result.StoreProduct.StoreFinanceCode,
					ProductId:   result.StoreProduct.ProductId,
					SkuId:       result.StoreProduct.ProductSkuId,
					IsSuccess:   result.IsSuccess,
					Message:     result.Message,
				}
				response.UpDownDetail = append(response.UpDownDetail, downERROR)
			}

		}
	}

	response.Code = 200
	return response, nil
}

// 通用校验逻辑
func (storeProduct *ChannelProductUp_StoreProduct) Valid(channelId int, args ...interface{}) error {

	// 非阿闻、互联网医院渠道
	if !(channelId == ChannelAwenId || channelId == ChannelDigitalHealth) {
		if len(storeProduct.StoreChannelFinanceCodes) == 0 {
			return errors.New("门店未找到渠道门店代码信息")
		}
		//if storeProduct.ProductSnapshotId == 0 {
		//	return errors.New("商品快照不存在，请先编辑商品")
		//}
		if storeProduct.ProductChannelCategoryId == 0 {
			return errors.New("店内分类Id为空，请选择店内分类")
		}
		if storeProduct.ProductChannelTagId == 0 {
			return errors.New("第三方分类Id为空，请选择第三方分类")
		}
	}

	// 添加删除商品的判断，商品删除提示平台已经删除
	isTrue, _ := engine.SQL("select * from  product where is_del = 1  and id = ?  ", storeProduct.ProductId).Exist()
	if isTrue {
		return errors.New(storeProduct.ProductId + "平台商品已经被删除或者停用")
	}

	// 基础验证
	if len(storeProduct.ProductSkuId) == 0 {
		return errors.New("商品没有找到SkuId")
	}
	if len(storeProduct.StoreFinanceCode) == 0 {
		return errors.New("未找到门店财务代码")
	}

	if "1049776001" == storeProduct.ProductSkuId {
		glog.Info("1049776001", "进入了这边，", storeProduct.StoreWarehouseCategory)
	}

	// 判断一下仓库类型
	switch storeProduct.StoreWarehouseCategory {
	case 1: // 电商仓
		if channelId != ChannelAwenId && storeProduct.ProductSkuStorePrice == 0 {
			return errors.New(storeProduct.ProductId + " 门店仓价格为必填项")
		}
		if len(storeProduct.ProductA8SkuId) == 0 {
			return errors.New(storeProduct.ProductId + " 查询不到A8货号信息，无法上架")
		}
	case 3: // 门店仓
		if channelId != ChannelAwenId && storeProduct.ProductSkuStorePrice == 0 {
			return errors.New(storeProduct.ProductId + " 门店仓价格为必填项")
		}
		if len(storeProduct.ProductZilongId) == 0 {
			return errors.New(storeProduct.ProductId + " 查询不到子龙货号信息，无法上架")
		}
	case 4, 5: //前置仓
		if channelId != ChannelAwenId && storeProduct.ProductSkuPreposePrice == 0 {
			return errors.New(storeProduct.ProductId + " 前置仓价格为必填项")
		}
		if len(storeProduct.ProductA8SkuId) == 0 {
			return errors.New(storeProduct.ProductId + " 查询不到A8货号信息，无法上架")
		}
	default:
		return errors.New(storeProduct.StoreFinanceCode + " 门店仓库类型不正确")
	}
	if storeProduct.ProductType == 1 { // 实物商品
		// 校验商品库存信息
		stock, err := GetStockInfoBySkuCodeAndShopId(int32(channelId), cast.ToInt32(storeProduct.ProductSkuId), storeProduct.StoreFinanceCode, args[0])
		if err != nil {
			return errors.New(fmt.Sprintf("查询库存失败 商品%s, err:%s", storeProduct.ProductId, err.Error()))
		}
		if stock == 0 {
			return errors.New(fmt.Sprintf("商品 %s 库存为0，无法上架", storeProduct.ProductId))
		} else {
			storeProduct.ProductStock = int(stock)
		}
		if storeProduct.ProductWeight == 0 {
			return errors.New("商品重量不能为空")
		}
	} else if storeProduct.ProductType == 2 { // 虚拟商品
		var isBefore = storeProduct.ProductGroupTermEndDate.Before(time.Now())
		if isBefore {
			return errors.New(fmt.Sprintf("商品 %s 已经过期，无法上架", storeProduct.ProductId))
		}
		// 虚拟商品的应用范围 bj接口
		useRange, err := CheckVirtualProductUseRange(storeProduct, channelId)
		if err != nil {
			return errors.New(fmt.Sprintf("虚拟商品的应用范围 bj接口查询异常：%s", storeProduct.ProductId))
		}
		if _, ok := useRange[storeProduct.StoreFinanceCode]; !ok {
			return errors.New(fmt.Sprintf("虚拟商品的应用范围不在门店的应用范围或者商品在门店已经停售 %s : %s", storeProduct.ProductId, storeProduct.StoreFinanceCode))
		}

	} else if storeProduct.ProductType == 3 { // 组合商品

		if storeProduct.StoreWarehouseCategory == 3 {
			if _, ok := storeProduct.ProductGroupUseRang[3]; !ok {
				return errors.New("组合商品不能上架到门店仓")
			}
		}
		if storeProduct.StoreWarehouseCategory == 4 {
			if _, ok := storeProduct.ProductGroupUseRang[2]; !ok {
				return errors.New("组合商品不能上架到前置仓")
			}
		}
		//if channelId != ChannelAwenId {  // 去除限制逻辑
		//	return errors.New("组合商品只允许上架到阿闻渠道")
		//}

		//var idDeleted = 0
		//engine.Table("product").Where("id=?", storeProduct.ProductId).Select("is_del").Get(&idDeleted)
		//if idDeleted == 1 {
		//	return errors.New("组合商品已经停用")
		//}

	}
	return nil
}

// 初始化StoreProducts参数
func (this *ChannelProductUpDown) init() {
	this.StoreProducts = this.buildStoreProducts(this.ChannelId, this.ProductIds, this.FinanceCodes)
}

// ////////////////////////////////////////////////////////////////  上架
// 上架前的通用操作
func (this *ChannelProductUpDown) beforeUp(storeProduct *ChannelProductUp_StoreProduct, args ...interface{}) error {

	// 校验商品和店铺的组合信息
	err := storeProduct.Valid(this.ChannelId, args[0])
	if err != nil {
		return err
	}

	// 组合商品需要检验子商品是否满足条件
	if storeProduct.ProductType == 3 {
		// 查询组合商品里的子商品
		var groupProductIds []string
		engine.Table(&models.ChannelSkuGroup{}).Where("product_id=?", storeProduct.ProductId).Select("distinct group_product_id").Find(&groupProductIds)
		// 子商品的StoreProducts
		var subChannelStoreProdcts = this.buildStoreProducts(this.ChannelId, groupProductIds, []string{storeProduct.StoreFinanceCode})

		//多于的sku去除
		group := make([]models.ChannelSkuGroup, 0)
		engine.SQL("select * from channel_sku_group where product_id = ? and channel_id = ?", storeProduct.ProductId, this.ChannelId).Find(&group)

		for _, subChannelStoreProdct := range subChannelStoreProdcts {
			// 校验子商品
			for _, v_group := range group {
				if v_group.GroupProductId == cast.ToInt(subChannelStoreProdct.ProductId) && cast.ToInt(subChannelStoreProdct.ProductSkuId) == v_group.GroupSkuId {
					err := subChannelStoreProdct.Valid(this.ChannelId, args[0])
					if err != nil {
						return errors.New(fmt.Sprintf("子商品校验异常%s : %s ", subChannelStoreProdct.ProductId, err.Error()))
					}
				}
			}
		}
	}

	return nil
}

// 上架前需要校验虚拟商品的应用范围bj提供接口
func CheckVirtualProductUseRange(storeProduct *ChannelProductUp_StoreProduct, channelId int) (map[string]struct{}, error) {

	url := config.GetString("virtual_product_use_range")

	//url = "http://zuul-app.rprprprp.com/scrm-organization-api/scrmcompany/ensurecards/category/queryCategoryHospitalByRuleValue"
	glog.Info("virtual_product_use_range url is :", url)

	type ChannelListData struct {
		CategoryCode string `json:"categoryCode"`
		ChannelId    string `json:"channelId"`
		ChannelName  string `json:"channelName"`
	}
	type Result struct {
		Id           int                `json:"id"`
		CategoryCode string             `json:"categoryCode"`
		EppCode      string             `json:"eppCode"`
		HospitalName string             `json:"hospitalName"`
		TypeData     int                `json:"type"`
		CityName     string             `json:"cityName"`
		RegionName   string             `json:"regionName"`
		ChannelList  []*ChannelListData `json:"channelList"`
	}

	type Response struct {
		StatusCode    int       `json:"statusCode"`
		Message       string    `json:"message"`
		SystemError   string    `json:"systemError"`
		BusinessError string    `json:"businessError"`
		Result        []*Result `json:"result"`
	}

	mp := make(map[string]interface{}, 0)
	mp["ruleValue"] = storeProduct.ProductSkuId
	//mp["ruleValue"] = 102703
	_, data := utils.HttpGet(url, "", "", mp)
	//glog.Info("virtual_product_use_range data", data)
	responseData := Response{}
	mapStore := make(map[string]struct{}, 0)
	err := json.Unmarshal([]byte(data), &responseData)
	if err != nil || responseData.StatusCode != 200 {
		msg := "北京接口查询虚拟商品的应用范围异常"
		glog.Error(msg, err.Error())
		return mapStore, errors.New(msg)
	}

	var channelIdToBeiJing string
	switch channelId {
	case ChannelAwenId:
		channelIdToBeiJing = "300012311"
	case ChannelMtId:
		channelIdToBeiJing = "100019588"
	case ChannelElmId:
		channelIdToBeiJing = "100019590"
	case ChannelJddjId:
		channelIdToBeiJing = "300012312"
	case ChannelMallId:
		channelIdToBeiJing = "100011424"
	}

	glog.Info("channelIdToBeiJingId : ", channelIdToBeiJing)
	for _, v := range responseData.Result {
		for _, v_channelData := range v.ChannelList {
			data := v_channelData
			if data.ChannelId == channelIdToBeiJing {
				mapStore[v.EppCode] = struct{}{}
			}
		}
	}
	// glog.Info("返回的对应渠道门店的数据：", kit.JsonEncode(mapStore))
	return mapStore, nil
}

func QueryProduct(productId int) (product *models.Product) {
	product = new(models.Product)
	engine.SQL("select * from product where id = ? ", productId).Get(product)
	return
}

func CheckIsVirtualProduct(productId int) bool {
	product := models.Product{}
	engine.SQL("select * from product where id = ? ", productId).Get(&product)
	if product.ProductType == 2 {
		return true
	}
	return false
}

// 判断你是否是组合商品
func CheckIsCombinationProduct(productId int) bool {
	var productType int
	engine.SQL("select product_type from product where id = ? ", productId).Get(&productType)
	if productType == 3 {
		return true
	}
	return false
}

// 判断是否可以调用美团和饿了么第三方接口
func CanCallThirdApi(productId, channelId int, financeCode string) (canCall bool) {
	if channelId != ChannelMtId && channelId != ChannelElmId {
		return true
	}
	if productId == 0 || channelId == 0 || len(financeCode) == 0 {
		glog.Infof("判断是否可以调用美团和饿了么第三方接口|入参异常productId=%d|channelId=%d|financeCode=%s", productId, channelId, financeCode)
		return true
	}

	//美团饿了么的商品数据和本地数据同步情况： 1的允许跑数据，跑完一个渠道+1，等于3就是跑完的
	thirdDoStatus := 0
	if _, err := engine.SQL("select do_status from datacenter.store where  finance_code = ?", financeCode).Get(&thirdDoStatus); err != nil {
		glog.Errorf("查询店铺数据失败:%d-%d-%s|err为%s", productId, channelId, financeCode, err.Error())
		return
	}
	// 还没有做初始同步数据的 ， 不阻断
	if thirdDoStatus < 3 {
		return true
	}
	productThirdId := ""
	if _, err := engine.SQL("select product_third_id from dc_product.channel_product_snapshot where product_id = ? and channel_id = ? and finance_code = ?", productId, channelId, financeCode).Get(&productThirdId); err != nil {
		glog.Errorf("查询商品第三方商品id失败为%d-%d-%s,err:%s", productId, channelId, financeCode, err.Error())
		return
	}
	if productThirdId != "" {
		return true
	}
	return

}

// 写回美团、饿了么的第三方商品id(因为这里写入了dc_product.product_third_id, 主要用于标识第三方铺品成功。 这个方法仅供饿了么和美团铺品时调用)
func UpdateProductThirdIdBySpu(from string, productId, channelId int, financeCode string, syncError string) (err error) {
	logPrefix := fmt.Sprintf("写回美团和饿了么的第三方商品id step1|入参为,from:%s,productId:%d,channelId:%d,financeCode:%s,syncError:%s", from, productId, channelId, financeCode, syncError)
	glog.Info(logPrefix)
	productThirdId := ""
	var has bool
	if has, err = engine.SQL("select product_third_id from dc_product.channel_product_snapshot where product_id = ? and channel_id = ? and finance_code = ?", productId, channelId, financeCode).Get(&productThirdId); err != nil {
		glog.Error(logPrefix, "查询数据库失败：", err.Error())
		return
	} else if !has {
		glog.Error(logPrefix, "未找到数据")
		return errors.New("未找到数据")
	}

	if syncError == "" {
		// 没有同步错误且第三方商品id为空， 则更新第三方商品id,且sync_error字段置为空
		if productThirdId == "" {
			var result sql.Result
			if result, err = engine.Exec("update dc_product.channel_product_snapshot set product_third_id=product_id,sync_error=?  where  product_id = ?  and channel_id = ? and finance_code = ?", syncError, productId, channelId, financeCode); err != nil {
				glog.Error(logPrefix, "执行数据库失败：", err.Error())
				return
			} else {
				rows, _ := result.RowsAffected()
				glog.Info(logPrefix, "更新第三方商品id成功.影响条数:", rows, ",更新时间为：", time.Now().Format("2006-01-02 15:04:05"))
			}
		} else {
			// 如果第三方商品id已经存在， 但是入参syncError为空， 则需要置空dc_product.channel_product_snapshot的sync_error字段
			if _, err = engine.Exec("update dc_product.channel_product_snapshot set sync_error=?  where  product_id = ?  and channel_id = ? and finance_code = ?", syncError, productId, channelId, financeCode); err != nil {
				glog.Error(logPrefix, "执行数据库失败：", err.Error())
				return
			}
		}

	} else if syncError != "" {
		// 如果入参syncError不为空， 则写入dc_product.channel_product_snapshot的sync_error字段
		//logPrefix = fmt.Sprintf("记录同步美团、饿了么的第三方商品信息错误信息,入参：%d,%d,%s", productId, channelId, financeCode)
		if _, err = engine.Exec("update dc_product.channel_product_snapshot set sync_error=?  where  product_id = ?  and channel_id = ? and finance_code = ?", syncError, productId, channelId, financeCode); err != nil {
			glog.Error(logPrefix, "执行数据库失败：", err.Error())
			return
		}
	}

	return
}

// 写回 同步美团、饿了么的第三方商品的错误信息
func UpdateProductThirdSyncErr(productId, channelId int, financeCode, syncError string) (err error) {
	logPrefix := fmt.Sprintf("写回美团和饿了么的第三方商品id step2|入参为,productId:%d,channelId:%d,financeCode:%s,syncError:%s", productId, channelId, financeCode, syncError)
	if productId == 0 || channelId == 0 || len(financeCode) == 0 {
		glog.Error(logPrefix, "参数错误")
		return errors.New("参数错误")
	}
	if _, err = engine.Exec("update dc_product.channel_product_snapshot set sync_error=?  where  product_id = ?  and channel_id = ? and finance_code = ?", syncError, productId, channelId, financeCode); err != nil {
		glog.Error(logPrefix, "执行数据库失败：", err.Error())
		return
	}

	return
}

// 查询饿了么是否有bar_code的重复数据
func CheckkChannelBarCode(skuId int32, channelId int, financeCode, barCode string) bool {
	var productThirdId string
	if _, err := engine.SQL(`SELECT s.id
FROM dc_product.channel_sku s
     LEFT JOIN dc_product.channel_product_snapshot ps ON s.product_id = ps.product_id AND s.channel_id = ps.channel_id
WHERE s.channel_id = ?
  AND ps.finance_code = ?
  AND s.id != ?
  AND s.bar_code = ?
  AND ps.product_third_id != "";`, channelId, financeCode, skuId, barCode).Get(&productThirdId); err != nil {
		glog.Error("查询bar_code重复数据库失败：", err.Error())
	}
	if productThirdId != "" {
		glog.Infof("已有相同商品条码（UPC）的商品,skuId:%d,channelId:%d,financeCode:%s,barCode:%s", skuId, channelId, financeCode, barCode)
		return true
	}
	return false
}

// 写回美团的第三方商品id
func MtProductThirdId(from string, mtRes *et.RetailSellStatusResult, foodData []*et.RetailBatchinitdata, financeCode string, operateType int32) {
	logPrefix := fmt.Sprintf("----------MtProductThirdId,写回美团的第三方商品id调用位置是%s|", from)
	glog.Info(logPrefix, "----------入参为:", kit.JsonEncode(mtRes), "----------foodData为:", kit.JsonEncode(foodData), "----------财务编码为:", financeCode)
	// v7.0.11 同步第三方商品ID回来
	productThirdIdMap := make(map[string]string)
	productThirdErr := make(map[string]string)
	if mtRes == nil || (mtRes.ResultCode != 1 && mtRes.ResultCode != 2 && mtRes.ResultCode != 3) {
		glog.Error(logPrefix, "跳过写回美团的第三方商品id|入参为", kit.JsonEncode(mtRes), "----------foodData为", kit.JsonEncode(foodData), "----------财务编码为", financeCode)
		return
	}
	//美团编辑
	if operateType == 2 {
		if mtRes.ErrorList != nil && len(mtRes.ErrorList) > 0 {
			for _, v := range mtRes.ErrorList {
				if len(v.AppSpuCode) > 0 && len(v.Msg) > 0 {
					productThirdErr[v.AppSpuCode] = v.Msg
				}
			}
		}
		for _, v := range foodData {
			productThirdIdMap[v.AppFoodCode] = ""
			if syncError, ok := productThirdErr[v.AppFoodCode]; ok {
				productThirdIdMap[v.AppFoodCode] = syncError
			}
		}
		for productId, syncErr := range productThirdIdMap {
			if _, err := engine.Exec("update dc_product.channel_product_snapshot set product_third_id=product_id,sync_error=?  where  product_id = ?  and channel_id = ? and finance_code = ?", syncErr, cast.ToInt(productId), ChannelMtId, financeCode); err != nil {
				glog.Error(logPrefix, "编辑美团商品时，更新第三方商品id失败，operateType=", operateType, ",product_id=", cast.ToInt(productId), ",finance_code=", financeCode)
			}
		}
		return

	}
	if mtRes.ResultCode == 3 && mtRes.ErrorList != nil {
		for _, v := range mtRes.ErrorList {
			if v.AppSpuCode == "" {
				continue
			}
			UpdateProductThirdSyncErr(cast.ToInt(v.AppSpuCode), ChannelMtId, financeCode, v.Msg)
		}
		return
	}
	// 美团是否存在指定店铺， 美团报：不存在此店铺
	mtShopExist := true
	// 	美团返回的：1-全部操作成功 2-部分成功，成功的数据存储在success_list或者success_map字段，失败的数据存在error_list字段中
	if (mtRes.ResultCode == 2) && mtRes.ErrorList != nil {
		for _, v := range mtRes.ErrorList {
			if v.AppSpuCode == "" {
				mtShopExist = false
			}
			if len(v.AppSpuCode) > 0 && len(v.Msg) > 0 {
				productThirdErr[v.AppSpuCode] = v.Msg
			}
		}
	}
	if !mtShopExist {
		glog.Error(logPrefix, "跳过写回美团的第三方商品id|因为美团报错w为不存在此门店|入参为", kit.JsonEncode(mtRes), "foodData为", kit.JsonEncode(foodData), "财务编码为", financeCode)
		return
	}

	for _, v := range foodData {
		productThirdIdMap[v.AppFoodCode] = ""
		if syncError, ok := productThirdErr[v.AppFoodCode]; ok {
			productThirdIdMap[v.AppFoodCode] = syncError
		}
	}

	for productId, syncErr := range productThirdIdMap {
		UpdateProductThirdIdBySpu(from, cast.ToInt(productId), ChannelMtId, financeCode, syncErr)
	}
}

// 写回饿了么的第三方商品id
func ElmProductThirdId(from string, elmRes *et.ElmBatchReturn, foodData []*et.UpdateElmShopSkuRequest, financeCode string, skuProductIds map[int32]int32) {
	logPrefix := fmt.Sprintf("----------ElmProductThirdId,写回饿了么的第三方商品id|调用位置是%s|", from)
	glog.Info(logPrefix, "----------入参为:", kit.JsonEncode(elmRes), ",----------foodData:", kit.JsonEncode(foodData), "----------财务编码:", financeCode, "----------skuProductIds:", kit.JsonEncode(skuProductIds))
	// v7.0.11 同步第三方商品ID回来
	productThirdIdMap := make(map[string]string)
	productThirdErr := make(map[string]string)
	if elmRes == nil || elmRes.Body == nil {
		return
	}
	if len(elmRes.Body.Error) > 0 && elmRes.Body.Error != "success" {
		for _, v := range elmRes.Body.Data.FailList {
			productThirdErr[cast.ToString(skuProductIds[cast.ToInt32(v.CustomSkuId)])] = v.Error
		}
	}

	for _, v := range foodData {
		productId := cast.ToString(skuProductIds[cast.ToInt32(v.CustomSkuId)])
		productThirdIdMap[productId] = ""
		if syncError, ok := productThirdErr[productId]; ok {
			productThirdIdMap[productId] = syncError
		}
	}

	for productId, syncErr := range productThirdIdMap {
		UpdateProductThirdIdBySpu(from, cast.ToInt(productId), ChannelElmId, financeCode, syncErr)
	}
}

// 判断你是否是组合商品
func (this *ChannelProductUpDown) GetFinanAppChannel(fins []string) {
	var data []models.StoreAppDto
	//engine.ShowSQL(true)
	sql := "select finance_code, app_channel from datacenter.store s where finance_code  in ('" + strings.Join(fins, "','") + "')"
	err := engine.SQL(sql).Find(&data)
	if err != nil {
		return
	}

	m := make(map[string]int)
	for i := range data {
		dtos := data[i]
		m[dtos.FinanceCode] = dtos.AppChannel
	}
	this.FinanceCodesAppChannel = m

}

// AwenProductUpInfo 批量先查出 商品类型、是否可销、是否处方药
type AwenProductUpInfo struct {
	ProductId int
	SkuId     int
	// 第三方货号
	ThirdSkuId string
	// 商品类别（1-实物商品，2-虚拟商品，3-组合商品）
	ProductType int
	// 组合类型(1:实实组合,2:虚虚组合,3.虚实组合)
	GroupType int
	// 是否药品
	IsDrugs int
	// 商品是否处方药,只药品有，1是、0否
	ProductIsPrescribedDrug int
	// 处方信息完整
	PrescribeComplete int
	// 子龙是否可sell，1是，0否
	CanSell string
	// 互联网医院是否可以开处方
	CanPrescribe int
	// 父商品id
	ParentSkuId int
	// 关联子商品
	ChildRen []*AwenProductUpInfo
}

// Check 阿闻渠道上架检查
func (i *AwenProductUpInfo) Check(warehouseCategory int, FinanceCode string, ChannelId int, Code string) (err error) {
	// 虚拟商品、虚虚组合商品不判断
	if i.ProductType == 2 || i.GroupType == 2 {
		return
	}

	if i.ProductType == 3 {
		for _, child := range i.ChildRen {
			if err = child.Check(warehouseCategory, FinanceCode, ChannelId, Code); err != nil {
				return errors.New(fmt.Sprintf("子商品 %d %s", child.ProductId, err.Error()))
			}
		}
		return
	}

	if warehouseCategory == 3 {
		// 子龙状态判断，默认值""，如果有子龙货号有效那么 "0"、"1" 对应是否可销
		if i.ThirdSkuId != "" && i.CanSell == "" {
			return errors.New("子龙货号无效")
		}
		if FinanceCode != "" {
			//门店对应的仓库编码的子龙id
			if Code == "" {
				var zilongCode models.ZlCode
				if _, err := engine.SQL(`select distinct w.code,s.zilong_id from dc_dispatch.warehouse w left join
			dc_dispatch.warehouse_relation_shop wrs on wrs.warehouse_id = w.id inner join datacenter.store s on w.code = s.finance_code
			where wrs.shop_id = ? and wrs.channel_id = ?;`, FinanceCode, ChannelId).Get(&zilongCode); err != nil {
					return errors.New(fmt.Sprintf("门店:%s,%v,对应的仓库编码的子龙id不存在", FinanceCode, ChannelId))
				}
				Code = zilongCode.Code
			}
			//备注： 这里用仓库码查是因为门店A可能卖的是门店B的货， 所以必须要用仓库码去判断在子龙是否可售
			var SyncZlProductRecord models.SyncZlProductRecordExtend
			Ishave, err := engine.SQL(`select r.* from dc_product.sync_zl_product_record r inner join dc_product.sku_third t 
    on r.item_code = t.third_sku_id and t.erp_id = 4 inner join datacenter.store s on r.zilong_id = s.zilong_id where t.product_id =? and s.finance_code = ?;`,
				i.ProductId, Code).Get(&SyncZlProductRecord)
			if err != nil {
				glog.Error("查询 sync_zl_product_record 出错", FinanceCode, i.ProductId, err.Error())
			}
			if Ishave {
				if SyncZlProductRecord.CanSell == 0 {
					return errors.New("商品在子龙不可销")
				}
			}
		}

	}

	//处方药相关的检测只有阿闻渠道才检查 v6.27.2
	if ChannelId == 1 {
		// 非处方药
		if i.ProductIsPrescribedDrug == 0 {
			//if i.IsDrugs == 1 && warehouseCategory == 3 {
			//	return errors.New("不允许上架非处方药药品")
			//}
			return
		}
		if i.PrescribeComplete == 0 {
			return errors.New("缺少处方药相关属性")
		}
		if i.CanPrescribe == 0 {
			return errors.New("商品库是处方药，互联网医院不是处方药或者不可开处方")
		}
	}
	return
}

// QueryAwenProductUpInfoByIds 查询阿闻渠道上架商品信息
func QueryAwenProductUpInfoByIds(productIds []int, warehouseCategory int) (upInfos map[int]*AwenProductUpInfo, err error) {
	var infos []*AwenProductUpInfo

	var ids strings.Builder
	for _, id := range productIds {
		if ids.Len() == 0 {
			ids.WriteString(cast.ToString(id))
		} else {
			ids.WriteString(",")
			ids.WriteString(cast.ToString(id))
		}
	}
	idStr := ids.String()

	erpId := 2
	if warehouseCategory == 3 {
		erpId = 4
	}

	if err = NewDbConn().SQL(fmt.Sprintf(`select t.sku_id,t.product_id,t.parent_sku_id,st.third_sku_id,p.product_type,p.group_type,p.is_prescribed_drug as product_is_prescribed_drug,
       if(p.disease is null or p.drug_dosage is null or p.dosing_days =0,0,1) as prescribe_complete,p.is_drugs
       from (
    select id as sku_id,product_id,0 as parent_sku_id from dc_product.sku where product_id in (%s)
union all
select group_sku_id as sku_id,group_product_id as product_id,sku_id as parent_sku_id from dc_product.sku_group where product_id in(%s)
              ) t
join dc_product.product p on t.product_id = p.id
left join dc_product.sku_third st on st.sku_id = t.sku_id and st.erp_id = ?;`, idStr, idStr), erpId).Find(&infos); err != nil {
		return
	} else if len(infos) == 0 {
		return
	}

	var skuNos []string                                         // 子龙货号
	var prescribeSkuIds []int                                   // 处方药sku
	skuInfoMaps := make(map[int]*AwenProductUpInfo, len(infos)) // sku关联
	thirdInfoMaps := make(map[string]*AwenProductUpInfo)        // 货号关联
	groupMap := make(map[int][]*AwenProductUpInfo)              // 组合商品关联

	upInfos = make(map[int]*AwenProductUpInfo)

	for _, info := range infos {
		skuInfoMaps[info.SkuId] = info
		if info.ProductType == 1 {
			if info.ThirdSkuId != "" {
				thirdInfoMaps[info.ThirdSkuId] = info
				skuNos = append(skuNos, info.ThirdSkuId)
			}
			if info.ProductIsPrescribedDrug == 1 {
				prescribeSkuIds = append(prescribeSkuIds, info.SkuId)
			}
		}
		if info.ParentSkuId != 0 {
			groupMap[info.ParentSkuId] = append(groupMap[info.ParentSkuId], info)
		} else {
			upInfos[info.SkuId] = info // 组合商品子商品不返回
		}
	}

	if len(skuNos) > 0 && warehouseCategory == 3 {
		exClient := et.GetExternalClient()
		if out, err := exClient.ZiLong.ProductList(exClient.Ctx, &et.ZiLongProductListReq{
			ProductCode: skuNos,
			Number:      1,
			Size:        int32(len(skuNos)) * 2,
		}); err != nil {
			return nil, err
		} else if out.Code != 200 {
			return nil, errors.New(out.Message)
		} else {
			for _, list := range out.Data {
				if info, has := thirdInfoMaps[list.ProductCode]; has {
					info.CanSell = list.CanSell // 子龙是否可售
				}
			}
		}
	}

	if len(prescribeSkuIds) > 0 {
		var pmSkuIds []int
		if err = GetMedicalDBConn().Table("pm_medicine").Where("is_prescription_drug = 1 and status = 1").In("sku_id", prescribeSkuIds).
			Select("sku_id").Find(&pmSkuIds); err != nil {
			return
		}
		for _, id := range pmSkuIds {
			skuInfoMaps[id].CanPrescribe = 1 // 是否可开处方
		}
	}

	// 返回组合子商品
	for _, info := range upInfos {
		if info.ProductType != 3 {
			continue
		}
		if group, has := groupMap[info.SkuId]; has {
			info.ChildRen = group
		}
	}

	return
}

// QueryProductList 查询阿闻渠道上架商品信息
func QueryProductList(productIds []int) (upInfos map[int]int, err error) {
	var infos = []*models.Product{}

	var ids strings.Builder
	for _, id := range productIds {
		if ids.Len() == 0 {
			ids.WriteString(cast.ToString(id))
		} else {
			ids.WriteString(",")
			ids.WriteString(cast.ToString(id))
		}
	}
	idStr := ids.String()

	if err = NewDbConn().SQL(fmt.Sprintf(`select id,is_drugs from product where id in (%s);`, idStr)).Find(&infos); err != nil {
		return
	} else if len(infos) == 0 {
		return
	}
	upInfos = make(map[int]int)
	for _, v := range infos {
		if v.IsDrugs == 1 {
			upInfos[v.Id] = v.IsDrugs
		}
	}
	return
}

// QueryAwenProductUpInfoByIds 查询阿闻渠道上架商品信息 v6.27.2
func QueryStoredrugInfo(finance_codes []string, channel_id int) (upInfos map[string]string) {
	db := NewDbConn()

	var data []*models.Store
	upInfos = make(map[string]string, 0)
	err := db.Select("finance_code ,sell_drugs, drugs_channel_ids").Where("sell_drugs= ? ", 1).And("FIND_IN_SET(?, drugs_channel_ids)", channel_id).
		In("finance_code", finance_codes).Find(&data)
	if err != nil {
		glog.Error("查询门店是否可以售药品出错", err)
	}
	for _, x := range data {
		upInfos[x.FinanceCode] = "1"
	}
	return upInfos
}

// 开始执行上架
func (this *ChannelProductUpDown) UpPorudct() {
	logPrefix := fmt.Sprintf("单门店单商品-商品上架====,渠道：%d,门店：%s,商品：%s", this.ChannelId, kit.JsonEncode(this.FinanceCodes), kit.JsonEncode(this.ProductIds))
	glog.Info(logPrefix, "入参：", kit.JsonEncode(this))
	defer func() {
		// 出现异常则记录
		if err := recover(); err != nil {
			this.UnknownError = errors.New("出现了未处理的错误")
			glog.Error("执行上架 panic", err)
		}
	}()
	// 初始化参数
	this.init()
	storeLen := len(this.StoreProducts)

	p := new(Product)
	agencyConfig, err := p.GetAgencyConfig(this.Ctx, &empty.Empty{})
	if err != nil {
		glog.Error("执行上架,获取代运营配置异常：", err)
		return
	}
	// 初始化门店和appChannel的关系
	this.GetFinanAppChannel(this.FinanceCodes)

	var upInfos map[int]*AwenProductUpInfo
	//var productInfos map[int]int

	productIds := make([]int, len(this.ProductIds))
	for i, id := range this.ProductIds {
		productIds[i] = cast.ToInt(id)
	}
	if this.ChannelId != ChannelDigitalHealth {
		// 这里无法确定仓类型，先指定为门店仓拉出更全的校验信息
		// 具体校验时传对应仓
		if upInfos, err = QueryAwenProductUpInfoByIds(productIds, 3); err != nil {
			glog.Errorf("执行上架,ChannelProductUpDown productIds:%s, error:%s", kit.JsonEncode(productIds), err.Error())
			return
		}
	}

	//查询这批门店是否当前渠道是否可以上架药品的数据
	StoredrugMap := QueryStoredrugInfo(this.FinanceCodes, this.ChannelId)

	for ix, storeProduct := range this.StoreProducts {
		if this.TaskId > 0 {
			glog.Info("执行上架,打印定时任务当前执行进度任务id:", this.TaskId, "当前:", ix, "/", storeLen, "门店：", storeProduct.StoreFinanceCode)
		}

		data := new(ic.GetStockInfoResponse)
		// 上架执行结果
		var upDownResult = &ChannelProductUp_Result{StoreProduct: storeProduct}
		this.UpResult = append(this.UpResult, upDownResult)

		if (this.ChannelId == ChannelMtId || this.ChannelId == ChannelElmId) && !CanCallThirdApi(cast.ToInt(storeProduct.ProductId), this.ChannelId, storeProduct.StoreFinanceCode) {
			desc := "上架失败:商品未在第三方创建，请先编辑后再进行操作"
			upDownResult.Message = desc
			glog.Error("执行上架,商品无法上架:没有第三方商品id====10", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
			go this.updateChannelProduct(storeProduct)
			continue
		}

		// v6.5.8判断是否是马氏，雀巢等门店，需要排除商品
		//获取appchannel
		financeCode := storeProduct.StoreFinanceCode
		var appChannel int32

		if info, ok := this.FinanceCodesAppChannel[financeCode]; ok {
			appChannel = int32(info)
		} else {
			upDownResult.Message = fmt.Sprintf("执行上架,未查询到门店的appchannel信息financeCode:%sproduct:%s", storeProduct.StoreFinanceCode, storeProduct.ProductId)
			glog.Error("执行上架,未查询到门店的appchannel信息financeCode失败:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
			go this.updateChannelProduct(storeProduct)
			continue
		}

		var flag bool
		for i := range agencyConfig.ConfigData {
			if appChannel == agencyConfig.ConfigData[i] {
				flag = true
			}
		}

		// 只控制实物商品
		if !flag {
			skuMap, err := p.IsUpProduct(this.Ctx, appChannel, 0)
			if err != nil {
				upDownResult.Message = fmt.Sprintf("执行上架,查询上架商品信息异常financeCode:%sproduct:%s", storeProduct.StoreFinanceCode, storeProduct.ProductId)
				continue
			}
			if CheckIsCombinationProduct(cast.ToInt(storeProduct.ProductId)) { // 其他代运营的商品限制组合商品上架
				upDownResult.Message = fmt.Sprintf("执行上架,该代运营类型门店限制组合商品上架，如需上架请联系总部运营financeCode:%sproduct:%s", storeProduct.StoreFinanceCode, storeProduct.ProductId)
				continue
			}
			if _, ok := skuMap[cast.ToInt(storeProduct.ProductSkuId)]; !ok {
				upDownResult.Message = fmt.Sprintf("执行上架,该商品禁止上架，如需上架请联系总部运营financeCode:%sproduct:%s", storeProduct.StoreFinanceCode, storeProduct.ProductId)
				continue
			}
		}

		if CheckIsVirtualProduct(cast.ToInt(storeProduct.ProductId)) {
			upDownResult.Message = fmt.Sprintf("执行上架,虚拟商品无法上架:%s", storeProduct.ProductId)
			glog.Error("执行上架,虚拟商品无法上架:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
			go this.updateChannelProduct(storeProduct)
			continue
		}

		// 阿闻渠道门店仓
		if this.ChannelId != ChannelDigitalHealth {
			if upInfo, has := upInfos[cast.ToInt(storeProduct.ProductSkuId)]; has {

				isCanSell := false

				//是否允许卖药品
				if _, hasDrugs := StoredrugMap[storeProduct.StoreFinanceCode]; hasDrugs {
					isCanSell = true
				}

				//如果是药品，并且不允许卖药，就提示 v6.27.2
				if !isCanSell && upInfo.IsDrugs == 1 {
					upDownResult.Message = storeProduct.ProductId + " 药品不允许上架"
					glog.Error("执行上架,药品不允许上架:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
					go this.updateChannelProduct(storeProduct)
					continue
				}

				if storeProduct.StoreWarehouseCategory == 3 || storeProduct.StoreWarehouseCategory == 4 {
					if err := upInfo.Check(storeProduct.StoreWarehouseCategory, storeProduct.StoreFinanceCode, this.ChannelId, ""); err != nil {
						upDownResult.Message = storeProduct.ProductId + " " + err.Error()
						glog.Error("执行上架,阿闻渠道Check失败:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
						go this.updateChannelProduct(storeProduct)
						continue
					}
				}

			}
		}

		// 基础校验
		err = checkIsSnapOrUp(storeProduct, this.ChannelId)
		if err != nil {
			upDownResult.Message = fmt.Sprintf("%s: %s", err.Error(), storeProduct.ProductId)
			glog.Error("执行上架,基础校验失败:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
			go this.updateChannelProduct(storeProduct)
			continue
		}
		// 同步价格的时候提到这里来，价格校验也在这里处理
		// v6.0 上架前需要同步价格到各个渠道 放到这里来进行处理
		// 同步价格优化性能
		var channelProductPriceSync ChannelProductPriceSync
		if this.IsSyncPrice {
			channelProductPriceSync.ChannelId = this.ChannelId
			channelProductPriceSync.FinanceCode = storeProduct.StoreFinanceCode
			channelProductPriceSync.ChannelFinanaceCode = storeProduct.StoreChannelFinanceCodes
			channelProductPriceSync.WarehouseId = storeProduct.StoreWarehouseId
			channelProductPriceSync.WarehouseCategory = storeProduct.StoreWarehouseCategory
			channelProductPriceSync.ProductId = storeProduct.ProductId
			channelProductPriceSync.ProductSkuId = storeProduct.ProductSkuId
			// 前置仓 or 电商仓
			if storeProduct.StoreWarehouseCategory == 1 ||
				storeProduct.StoreWarehouseCategory == 4 || storeProduct.StoreWarehouseCategory == 5 {
				channelProductPriceSync.ProductThirdSkuId = storeProduct.ProductA8SkuId
			}
			if storeProduct.StoreWarehouseCategory == 3 { // 门店仓
				channelProductPriceSync.ProductThirdSkuId = storeProduct.ProductZilongId
			}

			// 同步价格到第三方渠道
			glog.Infof("执行上架,同步价格参数;  门店id: %s, 渠道id: %d,商品id: %s,仓库类型：%v", channelProductPriceSync.FinanceCode, channelProductPriceSync.ChannelId, channelProductPriceSync.ProductId, storeProduct.StoreWarehouseCategory)
			// 检验参数
			err := channelProductPriceSync.checkParams()
			if err != nil {
				upDownResult.Message = fmt.Sprintf("%s: %s", err.Error(), storeProduct.ProductId)
				glog.Error("执行上架,检验参数失败:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
				go this.updateChannelProduct(storeProduct)
				continue
			}
			// 构造单价
			err = channelProductPriceSync.BuildPrice()
			if err != nil {
				upDownResult.Message = fmt.Sprintf("%s: %s", err.Error(), storeProduct.ProductId)
				glog.Error("执行上架,构造单价失败:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
				go this.updateChannelProduct(storeProduct)
				continue
			}
		}
		//A.上架前校验
		err = this.beforeUp(storeProduct, data)
		if err != nil {
			upDownResult.Message = fmt.Sprintf("上架前校验失败:%s", err.Error())
			glog.Error("执行上架,上架前校验失败:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId, "仓库类型：", storeProduct.StoreWarehouseCategory)
			go this.updateChannelProduct(storeProduct)
			continue
		}

		// B.开始上架
		switch this.ChannelId {
		case ChannelAwenId:
			err := this.upProductToAwen(storeProduct)
			if err != nil {
				upDownResult.Message = fmt.Sprintf("上架失败:%s", err.Error())
			} else {
				upDownResult.IsSuccess = true
			}
		case ChannelMtId: // 美团
			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()
			err := this.upProductToMt(storeProduct, grpcClient)
			if err != nil {
				upDownResult.Message = fmt.Sprintf("上架失败:%s", err.Error())
			} else {
				upDownResult.IsSuccess = true
			}
		case ChannelElmId: // 饿了么
			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()
			err := this.upProductToElm(storeProduct, grpcClient)
			if err != nil {
				upDownResult.Message = fmt.Sprintf("上架失败:%s", err.Error())
			} else {
				upDownResult.IsSuccess = true
			}
		case ChannelJddjId: // 京东
			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()
			err := this.upProductToJd(storeProduct, grpcClient)
			if err != nil {
				upDownResult.Message = fmt.Sprintf("上架失败:%s", err.Error())
			} else {
				upDownResult.IsSuccess = true
			}
		case ChannelDigitalHealth: //互联网医院
			err := this.upProductToDigitalHealth(storeProduct)
			if err != nil {
				upDownResult.Message = fmt.Sprintf("上架失败:%s", err.Error())
			} else {
				upDownResult.IsSuccess = true
			}
		default:
			upDownResult.Message = fmt.Sprintf("不支持的渠道Id-%d", this.ChannelId)
		}

		// C.上架后回调
		// 修改上架表hasStock的状态
		if upDownResult.IsSuccess {
			//todo 同步库存到mq
			this.SynchronizeInventoryToThird(storeProduct)
			//记录上架日志
			go new(Product).SaveChannelProductLogDetail(this.ChannelId, cast.ToInt(storeProduct.ProductId), this.UpType,
				storeProduct.StoreFinanceCode, this.UserNo, this.UserName)

			err := this.afterUp(storeProduct, channelProductPriceSync, data, int32(this.ChannelId))
			// 添加之前快照表的id和上架表对应不上的bug_fix修复
			repairSnapToChannelStoreSnapIdRelation(storeProduct, int32(this.ChannelId))
			if err != nil {
				upDownResult.Message = fmt.Sprintf("上架成功，回调出错:%s", err.Error())
			}

		} else {
			go this.updateChannelProduct(storeProduct)
			glog.Error("执行上架,上架失败:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
		}
	}
}

func (this *ChannelProductUpDown) SynchronizeInventoryToThird(storeProduct *ChannelProductUp_StoreProduct) {
	if this.ChannelId != ChannelAwenId {
		if storeProduct.ProductType == 3 {
			// 校验商品库存信息
			stock, _ := GetStockInfoBySkuCodeAndShopId(int32(this.ChannelId), cast.ToInt32(storeProduct.ProductSkuId), storeProduct.StoreFinanceCode, 1)
			retailSkuStock := models.StoreStock{
				App_poi_code: "",
				Food_data:    []models.FoodDataStore{},
				Sender:       UpdateStockSender,
			}
			sku := models.SkusStore{
				Sku_id: storeProduct.ProductSkuId,
				Stock:  strconv.Itoa(int(stock)),
			}
			foodData := models.FoodDataStore{
				App_food_code: storeProduct.ProductId,
				Skus:          []models.SkusStore{},
			}
			foodData.Skus = append(foodData.Skus, sku)
			retailSkuStock.Food_data = append(retailSkuStock.Food_data, foodData)

			var mqInfos []models.MqInfo

			retailSkuStock.App_poi_code = fmt.Sprintf("#%d", storeProduct.StoreWarehouseId)
			//把消息转成json并丢到mq里面
			mqString, _ := json.Marshal(retailSkuStock)
			mqInfos = append(mqInfos, models.MqInfo{
				Exchange: "datacenter",
				Quene:    "dc_sz_stock_mq_has_stock",
				Content:  string(mqString),
			})

			db := NewOrderCenterDbConn()
			var channelIds []int32
			if err := db.Table("dc_dispatch.warehouse_relation_shop").
				Where("warehouse_id = ? and shop_id = ?", storeProduct.StoreWarehouseId, storeProduct.StoreFinanceCode).
				In("channel_id", []int{2, 3, 4}). // 2美团,3饿了么,4京东到家
				Select("channel_id").Find(&channelIds); err != nil {
				glog.Info("SynchronizeInventoryToThird 查询仓库门店关系出错：" + err.Error())
				return
			}
			if len(channelIds) > 0 {
				retailSkuStock.App_poi_code = storeProduct.StoreFinanceCode
				content, _ := json.Marshal(retailSkuStock)
				mqInfo := models.MqInfo{
					Exchange: "datacenter",
					Content:  string(content),
				}

				for _, channelId := range channelIds {
					switch channelId {
					case 2: // 美团
						mqInfo.Quene = "dc_sz_stock_mq"
					case 3: // 饿了么
						mqInfo.Quene = "dc_sz_stock_mq_elm"
					case 4: // 京东到家
						mqInfo.Quene = "dc_sz_stock_mq_jddj"
					default:
						continue
					}
					mqInfos = append(mqInfos, mqInfo)
				}
			}

			_, err := db.Insert(mqInfos)
			if err != nil {
				glog.Error("SynchronizeInventoryToThird 同步第三方库存，写入mq异常", err.Error())
			}
		}
	}
}

func checkIsSnapOrUp(storeProduct *ChannelProductUp_StoreProduct, channel_id int) error {
	// 先校验快照存不存在，因为第三方可能没有快照，后面在更新价格的时候无法更新快照
	if storeProduct.ProductSnapshotId == 0 && channel_id != ChannelAwenId {
		return errors.New(fmt.Sprintf("快照不存在，请先编辑商品 %s", storeProduct.ProductId))
	}
	// 校验商品已经上架
	var channelStoreProduct models.ChannelStoreProduct
	engine.Where("channel_id=?", channel_id).Where("product_id=?", storeProduct.ProductId).
		Where("finance_code=?", storeProduct.StoreFinanceCode).Get(&channelStoreProduct)
	if channelStoreProduct.Id > 0 && channelStoreProduct.UpDownState == 1 {
		return errors.New(fmt.Sprintf("商品已经上架 %s", storeProduct.ProductId))
	}

	// 平台和渠道的货号对应不上的
	var SkuData struct {
		ProductId         string `json:"product_id"`
		SkuId             string `json:"sku_id"`
		ThirdSkuId        string `json:"third_sku_id"`
		ChannelThirdSkuId string `json:"channel_third_sku_id"`
	}

	// 电商仓 or 前置仓
	if storeProduct.StoreWarehouseCategory == 1 || storeProduct.StoreWarehouseCategory == 4 ||
		storeProduct.StoreWarehouseCategory == 5 {
		engine.SQL("select a.product_id, a.sku_id, a.third_sku_id, b.third_sku_id as channel_third_sku_id from channel_sku_third a join sku_third b on a.product_id = b.product_id and a.sku_id = b.sku_id "+
			"and a.erp_id = b.erp_id where a.third_sku_id != b.third_sku_id and a.erp_id = ? and a.product_id = ? and a.sku_id = ? and a.channel_id = ?", 2, storeProduct.ProductId, storeProduct.ProductSkuId, channel_id).Get(&SkuData)
	}
	if storeProduct.StoreWarehouseCategory == 3 { // 门店仓
		engine.SQL("select a.product_id, a.sku_id, a.third_sku_id, b.third_sku_id as channel_third_sku_id from channel_sku_third a join sku_third b on a.product_id = b.product_id and a.sku_id = b.sku_id "+
			"and a.erp_id = b.erp_id where a.third_sku_id != b.third_sku_id and a.erp_id = ? and a.product_id = ? and a.sku_id = ? and a.channel_id = ? ", 4, storeProduct.ProductId, storeProduct.ProductSkuId, channel_id).Get(&SkuData)
	}

	if len(SkuData.ProductId) > 0 {
		return errors.New(fmt.Sprintf("平台的第三方货号和渠道的货号不一致 %s : %s", SkuData.ProductId, SkuData.SkuId))
	}

	return nil
}

func GetSnapInfo(channelId int, productIds []string, financeCode []string) ([]*models.ChannelProductSnapshot, map[string]*models.ChannelProductSnapshot, error) {
	var channelProductSnapMap = make(map[string]*models.ChannelProductSnapshot)
	var channelProductSnaps []*models.ChannelProductSnapshot
	err := engine.Where("channel_id=?", channelId).
		In("product_id", productIds).
		In("finance_code", financeCode).Find(&channelProductSnaps)
	if err != nil {
		glog.Error(err)
		return channelProductSnaps, channelProductSnapMap, nil
	}
	for _, channelProductSnap := range channelProductSnaps {
		channelProductSnapMap[fmt.Sprintf("%s%d", channelProductSnap.FinanceCode, channelProductSnap.ProductId)] = channelProductSnap
	}
	return channelProductSnaps, channelProductSnapMap, nil
}

// 获取商品与店铺关联关系列表
func (this *ChannelProductUpDown) buildStoreProducts(channelId int, productIds []string, financeCodes []string) []*ChannelProductUp_StoreProduct {
	glog.Info("buildStoreProducts run  productIds : ", productIds, " channelId ", channelId, "  financeCode ", financeCodes)
	var channelStoreProduct []*ChannelProductUp_StoreProduct

	// 获取第三方平台配置的门店id
	var StoreMap = make(map[string]*dac.StoreInfo) // 门店与渠道平台对应关系
	datacenterGrpc := GetDataCenterClient()
	defer datacenterGrpc.Close()
	res, err := datacenterGrpc.RPC.QueryStoreInfo(datacenterGrpc.Ctx, &dac.StoreInfoRequest{ChannelId: int32(channelId), FinanceCode: financeCodes})
	if err != nil {
		glog.Error(err)
	} else {
		for _, detail := range res.Details {
			StoreMap[detail.FinanceCode] = detail
		}
	}
	// 保存门店的appChannel信息,方便后续使用
	//this.FinanceCodesAppChannel = StoreMap

	// 获取仓库
	//var WarehouseMap = make(map[string]*dc.WarehouseList) // 门店与仓库对应关系
	client := GetDispatchClient()
	defer client.Close()

	// 获取渠道关联关系，修改下面的仓库类型，因为以前是更具绑定关系来判断的现在需要按照渠道关系去获取货号和价格等信息
	product := new(Product)
	var WarehouseData = make(map[string]models.ChannelWarehouse)
	resp, err := product.GetChannelWarehouses(financeCodes, int32(channelId), nil, 1)
	if err != nil {
		glog.Error("GetChannelWarehouses 获取渠道关联关系异常：", err.Error())
	}
	for _, v := range resp {
		// 前置虚拟仓当前置仓处理
		WarehouseData[v.ShopId] = v
	}

	// 查询商品快照
	snapshots, channelProductSnapMap, _ := GetSnapInfo(channelId, productIds, financeCodes)

	// 阿闻渠道没有快照的时候增加快照信息, 其他的渠道没有快照上架的时候会报错，在这里不用写快照处理
	if channelId == ChannelAwenId {
		for _, v_finance := range financeCodes {
			for _, v_product_id := range productIds {
				//虚拟商品限制快照生成
				var product_type int
				engine.SQL("select product_type from dc_product.product p where id = ?", v_product_id).Get(&product_type)
				if product_type == 2 {
					continue
				}
				if _, ok := channelProductSnapMap[fmt.Sprintf("%s%s", v_finance, v_product_id)]; !ok {
					//  写入阿闻渠道的快照信息
					productModel := Product{}

					int32Product := make([]int32, 0)
					stringProduct := make([]string, 0)
					int32Product = append(int32Product, cast.ToInt32(v_product_id))
					stringProduct = append(stringProduct, v_product_id)

					response, err := productModel.QueryChannelProductSnapshot(client.Ctx, &pc.ChannelProductSnapshotRequest{
						ChannelId:   int32(channelId),
						UserNo:      this.UserNo,
						ProductId:   int32Product,
						FinanceCode: v_finance,
					})
					if err != nil {
						msg := "查询阿闻渠道的快照信息失败"
						glog.Error(msg, err.Error())
					}

					//info := loadLoginUserInfo(this.Ctx)
					//glog.Info("阿闻渠道没有快照的时候增加快照信息", kit.JsonEncode(info))
					_, err = productModel.NewChannelProductSnapshot(this.Ctx, response.Details[0])
					if err != nil {
						msg := "写入阿闻渠道的快照信息失败"
						glog.Error(msg, err.Error())
					}

					// 查询赋值
					stringFinacode := make([]string, 0)
					stringFinacode = append(stringFinacode, v_finance)
					_, mapData, _ := GetSnapInfo(channelId, stringProduct, stringFinacode)
					if len(mapData) > 0 {
						channelProductSnapMap[fmt.Sprintf("%s%s", v_finance, v_product_id)] = mapData[fmt.Sprintf("%s%s", v_finance, v_product_id)]
						snapshots = append(snapshots, mapData[fmt.Sprintf("%s%s", v_finance, v_product_id)])
					}

				}

			}
		}
	}

	// 商品的第三方货号
	var channelSkuThrids []*models.ChannelSkuThird
	var channelSkuThirdMap = make(map[string][]*models.ChannelSkuThird) // 商品第三方sku列表
	engine.Where("channel_id=?", channelId).In("product_id", productIds).Find(&channelSkuThrids)

	for _, channelSkuThrid := range channelSkuThrids {
		var key = cast.ToString(channelSkuThrid.ProductId)
		channelSkuThirdMap[key] = append(channelSkuThirdMap[key], channelSkuThrid)
	}

	// 查询渠道商品
	var productMap = make(map[string]*models.ChannelProduct)
	var channelProduct []*models.ChannelProduct
	err = engine.In("id", productIds).Where("channel_id=?", this.ChannelId).Find(&channelProduct)
	if err != nil {
		glog.Error(err)
	}
	for _, product := range channelProduct {
		productMap[strconv.Itoa(int(product.Id))] = product
	}

	// 处理商品
	for _, financeCode := range financeCodes {
		channelStore, isChannelStore := StoreMap[financeCode] // 第三方店铺Id
		warehouse, isWarehouse := WarehouseData[financeCode]  // 仓库类型

		for _, productId := range productIds {
			channelProductRequest, isChannelProduct := channelProductSnapMap[fmt.Sprintf("%s%s", financeCode, productId)]
			product, isProduct := productMap[productId]
			// 商品与门店财务代码的关联关系
			var storeProduct = ChannelProductUp_StoreProduct{}
			storeProduct.ProductId = productId
			// 提取第三方门店Id
			if isChannelStore {
				storeProduct.StoreChannelFinanceCodes = channelStore.ChannelStoreId
			}
			// 提取sku信息
			//兼容多规格处理数据
			if len(snapshots) > 0 && isChannelProduct {
				for _, v1 := range snapshots {
					if v1.FinanceCode == financeCode {
						var jsonData pc.ChannelProductRequest
						err := json.Unmarshal([]byte(v1.JsonData), &jsonData)
						if err != nil {
							msg := "快照为空无法上架，请先编辑商品信息"
							glog.Error(msg, err.Error())
							continue
						}
						// 之前的sku和第三方全部是获取的第一个
						for _, skuVData := range jsonData.SkuInfo {
							st_data := storeProduct
							if st_data.ProductId != cast.ToString(skuVData.ProductId) {
								continue
							}
							st_data.ProductSkuId = cast.ToString(skuVData.SkuId)
							st_data.ProductSkuStorePrice = skuVData.StorePrice
							st_data.ProductSkuPreposePrice = skuVData.PreposePrice
							st_data.ProductWeight = skuVData.WeightForUnit
							st_data.ProductSnapshotId = channelProductRequest.Id
							st_data.ProductChannelCategoryId = int(jsonData.Product.ChannelCategoryId)
							st_data.ProductChannelTagId = int(jsonData.Product.ChannelTagId)
							// 提取组合商品信息
							if isProduct {
								st_data.ProductType = int(product.ProductType)
								var useRange = strings.Split(product.UseRange, ",")
								st_data.ProductGroupUseRang = make(map[int]bool)
								for _, u := range useRange {
									st_data.ProductGroupUseRang[cast.ToInt(u)] = true
								}
								// 虚拟商品设置过期日期
								if product.ProductType == 2 && product.TermType == 1 {
									st_data.ProductGroupTermEndDate = time.Unix(int64(product.TermValue), 0)
								} else {
									st_data.ProductGroupTermEndDate = time.Now().AddDate(1, 0, 0)
								}
							}
							// 提取仓库类型
							st_data.StoreFinanceCode = financeCode
							if isWarehouse {
								st_data.StoreWarehouseCategory = warehouse.Category
								st_data.StoreWarehouseId = warehouse.WarehouseId
							}

							for _, SkuThirdData := range skuVData.SkuThird {
								if SkuThirdData.ErpId == 4 && st_data.ProductSkuId == cast.ToString(SkuThirdData.SkuId) { // 前置仓
									st_data.ProductZilongId = SkuThirdData.ThirdSkuId
								}
								if SkuThirdData.ErpId == 2 && st_data.ProductSkuId == cast.ToString(SkuThirdData.SkuId) { // 门店仓
									st_data.ProductA8SkuId = SkuThirdData.ThirdSkuId
								}
							}
							channelStoreProduct = append(channelStoreProduct, &st_data)
						}
					}
				}
			} else {
				// 以前的处理逻辑，现在保留主要为了兼容第三方平台的上架
				skuThirds, isSkuThird := channelSkuThirdMap[productId]
				// 渠道商品
				var channelProduct models.ChannelProduct
				engine.Where("channel_id=?", channelId).Where("id=?", productId).Get(&channelProduct)
				if channelProduct.Id > 0 {
					storeProduct.ProductChannelCategoryId = int(channelProduct.CategoryId)
				}
				storeProduct.ProductChannelTagId = int(channelProduct.ChannelTagId)
				// 渠道sku
				channelSkuDatas := make([]models.ChannelSku, 0)
				engine.Where("channel_id=?", channelId).Where("product_id=?", channelProduct.Id).Find(&channelSkuDatas)
				for _, channelSku := range channelSkuDatas {
					bytes, _ := json.Marshal(storeProduct)
					newStoreProduct := ChannelProductUp_StoreProduct{}
					json.Unmarshal(bytes, &newStoreProduct)
					newStoreProduct.ProductSkuId = cast.ToString(channelSku.Id)
					newStoreProduct.ProductWeight = channelSku.WeightForUnit
					newStoreProduct.ProductSkuStorePrice = channelSku.StorePrice
					newStoreProduct.ProductSkuPreposePrice = channelSku.PreposePrice

					// 提取组合商品信息
					if isProduct {
						newStoreProduct.ProductType = int(product.ProductType)
						var useRange = strings.Split(product.UseRange, ",")
						newStoreProduct.ProductGroupUseRang = make(map[int]bool)
						for _, u := range useRange {
							newStoreProduct.ProductGroupUseRang[cast.ToInt(u)] = true
						}
						// 虚拟商品设置过期日期
						if product.ProductType == 2 && product.TermType == 1 {
							newStoreProduct.ProductGroupTermEndDate = time.Unix(int64(product.TermValue), 0)
						} else {
							newStoreProduct.ProductGroupTermEndDate = time.Now().AddDate(1, 0, 0)
						}
					}
					// 提取第三方sku信息
					if isSkuThird {
						for _, skuThird := range skuThirds {
							if skuThird.ErpId == 2 && cast.ToString(skuThird.SkuId) == newStoreProduct.ProductSkuId {
								newStoreProduct.ProductA8SkuId = skuThird.ThirdSkuId
							}
							if skuThird.ErpId == 4 && cast.ToString(skuThird.SkuId) == newStoreProduct.ProductSkuId {
								newStoreProduct.ProductZilongId = skuThird.ThirdSkuId
							}
						}
					}
					// 提取仓库类型
					newStoreProduct.StoreFinanceCode = financeCode
					if isWarehouse {
						newStoreProduct.StoreWarehouseCategory = warehouse.Category
						newStoreProduct.StoreWarehouseId = warehouse.WarehouseId
					}
					// 保存数据
					channelStoreProduct = append(channelStoreProduct, &newStoreProduct)
				}
			}
		}
	}

	glog.Info("buildStoreProducts exit  productIds : ", productIds, " channelId ", channelId, "  financeCode ", financeCodes)
	return channelStoreProduct
}

// 上架到阿闻
func (this *ChannelProductUpDown) upProductToAwen(storeProduct *ChannelProductUp_StoreProduct) error {
	var tran = engine.NewSession()
	defer tran.Close()
	// 开始事务
	err := tran.Begin()
	if err != nil {
		return err
	}

	// 查询快照
	var productSnapshot models.ChannelProductSnapshot
	_, err = engine.Where("channel_id=?", this.ChannelId).Where("finance_code=?", storeProduct.StoreFinanceCode).
		Where("product_id=?", storeProduct.ProductId).Get(&productSnapshot)
	if err != nil {
		return errors.New("获取商品快照失败")
	}
	if productSnapshot.Id == 0 {
		// 保存快好
		productSnapshot.ChannelId = this.ChannelId
		productSnapshot.FinanceCode = storeProduct.StoreFinanceCode
		productSnapshot.ProductId = cast.ToInt32(storeProduct.ProductId)
		productSnapshot.CreateDate = time.Now()
		productSnapshot.UserNo = this.UserNo

		in := &pc.ChannelProductSnapshotRequest{
			FinanceCode: storeProduct.StoreFinanceCode,
			ChannelId:   int32(this.ChannelId),
			ProductId:   []int32{cast.ToInt32(storeProduct.ProductId)},
		}
		var product Product
		result, err := product.QueryChannelProductSnapshot(context.Background(), in)
		if err != nil {
			return errors.New("获取新的商品快照失败")
		} else {
			if len(result.Details) > 0 {
				productSnapshot.JsonData = result.Details[0].JsonData
			}
		}

		// 保存
		_, err = tran.Insert(&productSnapshot)
		if err != nil {
			return errors.New(fmt.Sprintf("保存新的商品快照失败,%s", err.Error()))
		}
	}
	var jsonData pc.ChannelProductRequest
	err = json.Unmarshal([]byte(productSnapshot.JsonData), &jsonData)
	if err != nil {
		return err
	}
	//查询上架信息
	var model models.ChannelStoreProduct
	_, err = engine.Where("channel_id=?", this.ChannelId).And("finance_code=?", storeProduct.StoreFinanceCode).And("product_id=?", storeProduct.ProductId).Get(&model)
	if err != nil {
		return err
	}
	if model.Id > 0 {
		model.UpDownState = 1
	} else {
		model.UpDownState = 1
		model.ChannelId = this.ChannelId
		model.FinanceCode = storeProduct.StoreFinanceCode
		model.ProductId = cast.ToInt(storeProduct.ProductId)
		model.ChannelCategoryId = int(jsonData.Product.ChannelCategoryId)
		model.ChannelCategoryName = jsonData.Product.ChannelCategoryName
		model.CreateDate = time.Now()
		model.UpdateDate = time.Now()
		model.SnapshotId = productSnapshot.Id
		model.IsRecommend = int(jsonData.Product.IsRecommend)
		model.Name = jsonData.Product.Name
		model.SkuId = int(jsonData.SkuInfo[0].SkuId)
		// 电商仓取门店仓价格
		if storeProduct.StoreWarehouseCategory == 3 || storeProduct.StoreWarehouseCategory == 1 {
			model.MarketPrice = int(storeProduct.ProductSkuStorePrice)
		}
		if storeProduct.StoreWarehouseCategory == 4 {
			model.MarketPrice = int(storeProduct.ProductSkuPreposePrice)
		}
		_, err := tran.Insert(model)
		if err != nil {
			return err
		}
	}
	// 提交事物
	err = tran.Commit()
	if err != nil {
		return err
	}
	return nil
}

// 上架到美团
func (this *ChannelProductUpDown) upProductToMt(storeProduct *ChannelProductUp_StoreProduct, client *et.Client) error {
	// 请求参数
	mtReq := &et.RetailSellStatusRequest{
		AppPoiCode: storeProduct.StoreChannelFinanceCodes,
		FoodData:   []*et.AppFoodCode{},
		SellStatus: 0,
	}
	mtReq.FoodData = append(mtReq.FoodData, &et.AppFoodCode{AppFoodCode: storeProduct.ProductId})

	storeMasterId, err := GetAppChannelByFinanceCode(storeProduct.StoreFinanceCode)
	if err != nil {
		glog.Error("upProductToMt", "GetAppChannelByFinanceCode failed,", kit.JsonEncode(storeProduct))
		return errors.New("GetAppChannelByFinanceCode failed")
	}
	mtReq.StoreMasterId = storeMasterId
	if !CanCallThirdApi(cast.ToInt(storeProduct.ProductId), ChannelMtId, storeProduct.StoreFinanceCode) {
		glog.Error("没有第三方商品id====11,商品id:", cast.ToInt(storeProduct.ProductId), "财务编码：", storeProduct.StoreFinanceCode)
		return errors.New("商品未在第三方创建，请先编辑后再进行操作")
	}
	// 调用远程更新状态
	res, err := client.RPC.RetailSellStatus(client.Ctx, mtReq)
	glog.Info("上架-4444上下架到美团返回数据", kit.JsonEncode(res), ",入参：", kit.JsonEncode(mtReq), "错误err：", kit.JsonEncode(err))

	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	}
	errMsg := ""
	if res.Code != 200 && res.Error != nil {
		errMsg = res.Error.Msg
	}
	UpdateProductThirdSyncErr(cast.ToInt(storeProduct.ProductId), ChannelMtId, storeProduct.StoreFinanceCode, errMsg)
	if res.Code != 200 {
		return errors.New(res.Error.Msg)
	}

	return nil
}

// 上架到京东
func (this *ChannelProductUpDown) upProductToJd(storeProduct *ChannelProductUp_StoreProduct, client *et.Client) error {

	storeMasterId, err := GetAppChannelByFinanceCode(storeProduct.StoreFinanceCode)
	if err != nil {
		glog.Error("upProductToJd,", "GetAppChannelByFinanceCode,", storeProduct.StoreFinanceCode, err)
		return err
	}

	syncData := &et.BatchUpdateVendibilityRequest{
		StationNo:     storeProduct.StoreChannelFinanceCodes,
		UserPin:       "xrp",
		StoreMasterId: storeMasterId,
	}
	syncData.StockVendibilityList = append(syncData.StockVendibilityList, &et.JddjStockVendibility{DoSale: true, OutSkuId: storeProduct.ProductSkuId})

	res, err := client.JddjProduct.BatchUpdateVendibility(client.Ctx, syncData)
	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else if res.RetCode != "0" {
		return errors.New(res.RetMsg)
	} else {
		// 同步京东库存
		if storeProduct.ProductStock > 0 {
			//构造请求
			updateStockReq := &et.UpdateStockRequest{
				StationNo: storeProduct.StoreChannelFinanceCodes,
				UserPin:   "xrp",
				SkuStockList: []*et.SkuStockList{
					{
						OutSkuId: storeProduct.ProductSkuId,
						StockQty: int32(storeProduct.ProductStock),
					},
				},
				StoreMasterId: storeMasterId,
			}

			stockRes, err := client.JddjProduct.UpdateStock(client.Ctx, updateStockReq)
			if err != nil || strings.ContainsAny(stockRes.RetMsg, "失败") {
				return errors.New("上架失败，同步京东库存失败，")
			}
		}

	}
	return nil
}

// 上架到互联网医院
func (this *ChannelProductUpDown) upProductToDigitalHealth(storeProduct *ChannelProductUp_StoreProduct) error {
	var tran = engine.NewSession()
	defer tran.Close()
	// 开始事务
	err := tran.Begin()
	if err != nil {
		return err
	}
	// 查询快照
	var productSnapshot models.ChannelProductSnapshot
	_, err = engine.Where("channel_id=?", this.ChannelId).Where("finance_code=?", storeProduct.StoreFinanceCode).Where("product_id=?", storeProduct.ProductId).Get(&productSnapshot)
	if err != nil {
		return errors.New("获取商品快照失败")
	}
	if productSnapshot.Id == 0 {
		productSnapshot.ChannelId = this.ChannelId
		productSnapshot.FinanceCode = storeProduct.StoreFinanceCode
		productSnapshot.ProductId = cast.ToInt32(storeProduct.ProductId)
		productSnapshot.CreateDate = time.Now()
		productSnapshot.UserNo = this.UserNo

		in := &pc.ChannelProductSnapshotRequest{
			FinanceCode: storeProduct.StoreFinanceCode,
			ChannelId:   int32(this.ChannelId),
			ProductId:   []int32{cast.ToInt32(storeProduct.ProductId)},
		}
		var product Product
		result, err := product.QueryChannelProductSnapshot(context.Background(), in)
		if err != nil {
			return errors.New("获取新的商品快照失败")
		} else {
			if len(result.Details) > 0 {
				productSnapshot.JsonData = result.Details[0].JsonData
			}
		}
		_, err = tran.Insert(&productSnapshot)
		if err != nil {
			return errors.New(fmt.Sprintf("保存新的商品快照失败,%s", err.Error()))
		}
	}
	var jsonData pc.ChannelProductRequest
	err = json.Unmarshal([]byte(productSnapshot.JsonData), &jsonData)
	if err != nil {
		return err
	}
	//查询上架信息
	var model models.ChannelStoreProduct
	_, err = engine.Where("channel_id=?", this.ChannelId).And("finance_code=?", storeProduct.StoreFinanceCode).And("product_id=?", storeProduct.ProductId).Get(&model)
	if err != nil {
		return err
	}
	if model.Id > 0 {
		model.UpDownState = 1
	} else {
		model.UpDownState = 1
		model.ChannelId = this.ChannelId
		model.FinanceCode = storeProduct.StoreFinanceCode
		model.ProductId = cast.ToInt(storeProduct.ProductId)
		model.ChannelCategoryId = int(jsonData.Product.ChannelCategoryId)
		model.ChannelCategoryName = jsonData.Product.ChannelCategoryName
		model.CreateDate = time.Now()
		model.UpdateDate = time.Now()
		model.SnapshotId = productSnapshot.Id
		model.IsRecommend = int(jsonData.Product.IsRecommend)
		model.Name = jsonData.Product.Name
		model.SkuId = int(jsonData.SkuInfo[0].SkuId)
		if storeProduct.StoreWarehouseCategory == 3 {
			model.MarketPrice = int(storeProduct.ProductSkuStorePrice)
		}
		if storeProduct.StoreWarehouseCategory == 4 {
			model.MarketPrice = int(storeProduct.ProductSkuPreposePrice)
		}
		_, err := tran.Insert(model)
		if err != nil {
			return err
		}
	}
	err = tran.Commit()
	if err != nil {
		return err
	}
	return nil
}

// 上架到饿了么
func (this *ChannelProductUpDown) upProductToElm(storeProduct *ChannelProductUp_StoreProduct, client *et.Client) error {
	// 调用远程接口

	//todo 确认storeProduct.StoreFinanceCode 是否只是一个门店的财务编码还是多个
	appChannel, err := GetAppChannelByFinanceCode(storeProduct.StoreFinanceCode)
	if err != nil {
		return err
	}

	if appChannel == 0 {
		err = errors.New("饿了么上架获取appChannel值错误")
		return err
	}
	elmIn := &et.UpdateElmShopSkuPriceRequest{
		ShopId:      storeProduct.StoreChannelFinanceCodes,
		CustomSkuId: storeProduct.ProductSkuId,
		AppChannel:  appChannel,
	}
	elmOnlineResponse, err := client.ELMPRODUCT.OnlineElmShopSkuOne(context.Background(), elmIn)
	glog.Info("上下架商品到饿了么返回数据：：", kit.JsonEncode(elmOnlineResponse), "请求数据为：", kit.JsonEncode(elmIn), ",err为：", kit.JsonEncode(err))

	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	}
	// 更新调用第三方返回错误信息
	UpdateProductThirdSyncErr(cast.ToInt(storeProduct.ProductId), ChannelElmId, storeProduct.StoreFinanceCode, elmOnlineResponse.Error)

	if elmOnlineResponse.Code != 200 {
		return errors.New(elmOnlineResponse.Error)
	}
	return nil
}

func (this *ChannelProductUpDown) updateChannelProduct(storeProduct *ChannelProductUp_StoreProduct) {
	// 更新上架状态
	var channelStoreProduct models.ChannelStoreProduct
	if _, err := engine.Where("channel_id=?", this.ChannelId).Where("product_id=?", storeProduct.ProductId).
		Where("finance_code=?", storeProduct.StoreFinanceCode).Get(&channelStoreProduct); err != nil {
		glog.Error("执行上下架,查询渠道商品失败 productIds : ", storeProduct.ProductId)
	}

	if channelStoreProduct.DownType == 7 {
		channelStoreProduct.DownType = -3
	} else if channelStoreProduct.DownType == 5 {
		channelStoreProduct.DownType = -2
	} else {
		return
	}
	if _, err := engine.ID(channelStoreProduct.Id).Cols("down_type").Update(&channelStoreProduct); err != nil {
		glog.Error("执行上下架,更新渠道商品状态失败 run  productIds : ", storeProduct.ProductId)
	}
}

// 上架后的通用操作
func (this *ChannelProductUpDown) afterUp(storeProduct *ChannelProductUp_StoreProduct, channelProductPriceSync ChannelProductPriceSync, in *ic.GetStockInfoResponse, channelId int32) error {
	logPrefix := fmt.Sprintf("上架后的通用操作：product_id为%s,finance_code为%s,channel_id为%d", storeProduct.ProductId, storeProduct.StoreFinanceCode, this.ChannelId)
	// 查询快照
	var channelProductSnapshot models.ChannelProductSnapshot
	_, err := engine.Where("channel_id=?", this.ChannelId).Where("product_id=?", storeProduct.ProductId).Where("finance_code=?", storeProduct.StoreFinanceCode).Get(&channelProductSnapshot)
	if err != nil {
		return err
	}

	if channelProductSnapshot.Id > 0 {
		// 查询渠道商品上架表
		var channelStoreProduct models.ChannelStoreProduct
		_, err := engine.Where("channel_id=?", this.ChannelId).Where("product_id=?", storeProduct.ProductId).Where("finance_code=?", storeProduct.StoreFinanceCode).Get(&channelStoreProduct)
		if err != nil {
			return err
		}
		if channelStoreProduct.Id == 0 { // 不存在上架记录则新增
			glog.Info(logPrefix, "， 入参为：", kit.JsonEncode(storeProduct), "渠道门店商品信息不存在")
			// 解析sku信息
			var channelProductRequest pc.ChannelProductRequest
			err = json.Unmarshal([]byte(channelProductSnapshot.JsonData), &channelProductRequest)
			// 新增
			channelStoreProduct.ChannelId = this.ChannelId
			channelStoreProduct.FinanceCode = storeProduct.StoreFinanceCode
			channelStoreProduct.ProductId = cast.ToInt(storeProduct.ProductId)
			channelStoreProduct.ChannelCategoryId = int(channelProductRequest.Product.ChannelCategoryId)
			channelStoreProduct.ChannelCategoryName = channelProductRequest.Product.ChannelCategoryName
			channelStoreProduct.IsRecommend = 0
			channelStoreProduct.UpDownState = 1
			channelStoreProduct.HasStock = 1
			channelStoreProduct.SnapshotId = channelProductSnapshot.Id
			channelStoreProduct.Name = channelProductRequest.Product.Name
			channelStoreProduct.SkuId = int(channelProductRequest.SkuInfo[0].SkuId)

			_, err := engine.Insert(&channelStoreProduct)
			if err != nil {
				glog.Error(logPrefix, "插入渠道门店商品数据失败：err", err.Error())
				return err
			}
		} else { // 已经存在上架记录则更新上架状态
			channelStoreProduct.UpDownState = 1
			channelStoreProduct.HasStock = 1
			// 更新上架状态
			_, err := engine.ID(channelStoreProduct.Id).Cols("up_down_state,has_stock").Update(&channelStoreProduct)
			if err != nil {
				glog.Error(logPrefix, "更新渠道门店商品上架失败：err", err.Error())
				return err
			}
		}

		//更新product_has_stock表
		err = InsertOrUpdateProductHasStock(channelStoreProduct, in)
		if err != nil {
			return err
		}

	}

	// v6.0 上架前需要同步价格到各个渠道 放到前面去处理
	if this.IsSyncPrice {

		////// 同步价格
		//if channelProductPriceSync.ProductSyncPrice == 0 {
		//	glog.Info("无价格重新初始化一遍：", kit.JsonEncode(channelProductPriceSync))
		//	var channelProductPriceSync ChannelProductPriceSync
		//	channelProductPriceSync.ChannelId = this.ChannelId
		//	channelProductPriceSync.FinanceCode = storeProduct.StoreFinanceCode
		//	channelProductPriceSync.ChannelFinanaceCode = storeProduct.StoreChannelFinanceCodes
		//	channelProductPriceSync.WarehouseId = storeProduct.StoreWarehouseId
		//	channelProductPriceSync.WarehouseCategory = storeProduct.StoreWarehouseCategory
		//	channelProductPriceSync.ProductId = storeProduct.ProductId
		//	channelProductPriceSync.ProductSkuId = storeProduct.ProductSkuId
		//	if storeProduct.StoreWarehouseCategory == 4 { // 前置仓
		//		channelProductPriceSync.ProductThirdSkuId = storeProduct.ProductA8SkuId
		//	}
		//	if storeProduct.StoreWarehouseCategory == 3 { // 门店仓
		//		channelProductPriceSync.ProductThirdSkuId = storeProduct.ProductZilongId
		//	}
		//	//同步价格到第三方渠道
		//	err = channelProductPriceSync.SyncPrice()
		//}
		glog.Info("同步价格到第三方渠道：", kit.JsonEncode(channelProductPriceSync))
		if channelProductPriceSync.ChannelId == ChannelAwenId { // 阿闻
			return channelProductPriceSync.syncPriceToAwen()
		} else {
			// A . 先更新快照或上架表的价格
			err = channelProductPriceSync.syncPriceToChannelProductSnap()
			if err != nil {
				return errors.New(fmt.Sprintf("更新快照价格失败:%s", err.Error()))
			}
			// B. 更新第三发价格信息
			if channelProductPriceSync.ChannelId == ChannelMtId { // 美团
				return channelProductPriceSync.syncPriceToMt()
			}
			if channelProductPriceSync.ChannelId == ChannelElmId { // 饿了么
				return channelProductPriceSync.syncPriceToElm()
			}
			if channelProductPriceSync.ChannelId == ChannelJddjId { // 京东
				return channelProductPriceSync.syncPriceToJd()
			}
		}
		if err != nil {
			return err
		}
	}

	return nil
}

// 添加之前快照表的id和上架表对应不上的bug_fix修复
func repairSnapToChannelStoreSnapIdRelation(storeProduct *ChannelProductUp_StoreProduct, channelId int32) {
	product := models.ChannelStoreProduct{}
	engine.SQL("select * from channel_store_product where finance_code=? and product_id=? and channel_id=? ",
		storeProduct.StoreFinanceCode, storeProduct.ProductId, channelId).Get(&product)
	snapshot := models.ChannelProductSnapshot{}
	engine.SQL("select * from channel_product_snapshot where finance_code=? and product_id= ? and channel_id = ?",
		storeProduct.StoreFinanceCode, storeProduct.ProductId, channelId).Get(&snapshot)
	if product.Id > 0 && snapshot.Id > 0 && product.SnapshotId != snapshot.Id {
		engine.Exec("update channel_store_product set snapshot_id= ?, update_date= now() where id=?;", snapshot.Id, product.Id)
	}

}

//////////////////////////////////////////////////////////////////  下架
// 下架商品

// 下架之前进行的操作
func (this *ChannelProductUpDown) beforeDown(storeProduct *ChannelProductUp_StoreProduct) error {
	var channelStoreProduct models.ChannelStoreProduct
	engine.Where("channel_id=?", this.ChannelId).Where("product_id=?", storeProduct.ProductId).Where("finance_code=?", storeProduct.StoreFinanceCode).Get(&channelStoreProduct)
	if channelStoreProduct.Id > 0 && channelStoreProduct.UpDownState == 0 {
		return errors.New("商品已经下架")
	}
	return nil
}

func (this *ChannelProductUpDown) DownPorudct() {
	defer func() {
		// 出现异常则记录
		if err := recover(); err != nil {
			this.UnknownError = errors.New("出现了未处理的错误")
			glog.Error("执行下架,panic:", err)
		}
	}()
	// 初始化参数
	this.init()
	for _, storeProduct := range this.StoreProducts {
		// 下架执行结果
		var upDownResult = &ChannelProductUp_Result{StoreProduct: storeProduct}
		this.UpResult = append(this.UpResult, upDownResult)

		if (this.ChannelId == ChannelMtId || this.ChannelId == ChannelElmId) && !CanCallThirdApi(cast.ToInt(storeProduct.ProductId), this.ChannelId, storeProduct.StoreFinanceCode) {
			desc := "下架失败:商品未在第三方创建，请先编辑后再进行操作"
			upDownResult.Message = desc
			glog.Error("执行下架,商品无法上架:没有第三方商品id====12", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
			go this.updateChannelProduct(storeProduct)
			continue
		}
		// A.下架前校验
		err := this.beforeDown(storeProduct)
		if err != nil {
			upDownResult.Message = err.Error()
			glog.Error("执行下架,下架前校验失败:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
			go this.updateChannelProduct(storeProduct)
			continue
		}
		// B.开始下架
		switch this.ChannelId {
		case ChannelAwenId:
			err := this.downProductToAwen(storeProduct)
			if err != nil {
				upDownResult.Message = err.Error()
			} else {
				upDownResult.IsSuccess = true
			}
		case ChannelMtId: // 美团
			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()
			err := this.downProductToMt(storeProduct, grpcClient)
			if err != nil {
				upDownResult.Message = err.Error()
			} else {
				upDownResult.IsSuccess = true
			}
		case ChannelElmId: // 饿了么

			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()
			err := this.downProductToElm(storeProduct, grpcClient)
			if err != nil {
				upDownResult.Message = err.Error()
			} else {
				upDownResult.IsSuccess = true
			}
		case ChannelJddjId: // 京东
			// 获取grpc链接
			var grpcClient = et.GetExternalClient()
			defer grpcClient.Close()
			err := this.downProductToJd(storeProduct, grpcClient)
			if err != nil {
				upDownResult.Message = err.Error()
			} else {
				upDownResult.IsSuccess = true
			}
		case ChannelDigitalHealth: // 互联网医疗下架
			err := this.DownDigitalHealth(storeProduct)
			if err != nil {
				upDownResult.Message = err.Error()
			} else {
				upDownResult.IsSuccess = true
			}
		default:
			upDownResult.Message = fmt.Sprintf("不支持的渠道Id-%d", this.ChannelId)
		}
		// C.上架后回调
		if upDownResult.IsSuccess {
			//记录上架日志
			if this.DownType == enum.DownRecordType7DayNoStock || this.DownType == enum.DownRecordTypeNoSell { // 七天无库存自动下架的
				go func() {
					new(Product).SaveChannelProductLogDetail(this.ChannelId, cast.ToInt(storeProduct.ProductId), enum.RecordTypeAutoDown,
						storeProduct.StoreFinanceCode, this.UserNo, this.UserName)
				}()
			} else if this.DownType == enum.DownRecordTypeBatch { // 批量下架
				go func() {
					new(Product).SaveChannelProductLogDetail(this.ChannelId, cast.ToInt(storeProduct.ProductId), enum.RecordTypeDownBatch,
						storeProduct.StoreFinanceCode, this.UserNo, this.UserName)
				}()
			} else if this.DownType == enum.DownRecordTypeExpire { // 过期商品自动下架
				go func() {
					new(Product).SaveChannelProductLogDetail(this.ChannelId, cast.ToInt(storeProduct.ProductId), enum.RecordTypeAutoDown,
						storeProduct.StoreFinanceCode, this.UserNo, this.UserName)
				}()
			} else { // 单个下架
				go func() {
					new(Product).SaveChannelProductLogDetail(this.ChannelId, cast.ToInt(storeProduct.ProductId), enum.RecordTypeDownOne,
						storeProduct.StoreFinanceCode, this.UserNo, this.UserName)
				}()
			}

			err := this.afterDown(storeProduct, this.DownType)
			if err != nil {
				glog.Error(err)
			}

			//下架更新channel_store_product_has_stock表中的has_stock_up字段通过
			err = UpdateHasStockUp(storeProduct, this.ChannelId)
		} else {
			storeProduct.SyncError = upDownResult.Message
			go this.updateChannelProduct(storeProduct)
			glog.Error("执行下架,下架失败:", upDownResult.Message, "门店:", storeProduct.StoreFinanceCode, "渠道channel_id:", this.ChannelId, "productId:", storeProduct.ProductId)
		}
	}
}

// 下架到阿闻
func (this *ChannelProductUpDown) DownDigitalHealth(storeProduct *ChannelProductUp_StoreProduct) error {
	// 查询上架记录
	var channelStoreProdct models.ChannelStoreProduct
	_, err := engine.Where("channel_id=?", this.ChannelId).And("finance_code=?", storeProduct.StoreFinanceCode).And("product_id=?", storeProduct.ProductId).Get(&channelStoreProdct)
	if err != nil {
		return err
	} else {
		if channelStoreProdct.Id > 0 {
			channelStoreProdct.UpDownState = 0
			channelStoreProdct.UpdateDate = time.Now()

			_, err := engine.ID(channelStoreProdct.Id).Cols("up_down_state,update_date").Update(&channelStoreProdct)
			if err != nil {
				return err
			}
		} else {
			return errors.New("未找到上架记录")
		}
	}
	return nil
}

// 下架到阿闻
func (this *ChannelProductUpDown) downProductToAwen(storeProduct *ChannelProductUp_StoreProduct) error {
	// 查询上架记录
	var channelStoreProdct models.ChannelStoreProduct
	_, err := engine.Where("channel_id=?", this.ChannelId).And("finance_code=?", storeProduct.StoreFinanceCode).And("product_id=?", storeProduct.ProductId).Get(&channelStoreProdct)
	if err != nil {
		return err
	} else {
		if channelStoreProdct.Id > 0 {
			channelStoreProdct.UpDownState = 0
			channelStoreProdct.UpdateDate = time.Now()
			_, err := engine.ID(channelStoreProdct.Id).Cols("up_down_state,update_date").Update(&channelStoreProdct)
			if err != nil {
				return err
			}
		} else {
			return errors.New("未找到上架记录")
		}
	}
	return nil
}

// 下架到美团
func (this *ChannelProductUpDown) downProductToMt(storeProduct *ChannelProductUp_StoreProduct, client *et.Client) error {
	// 请求参数
	mtReq := &et.RetailSellStatusRequest{
		AppPoiCode: storeProduct.StoreChannelFinanceCodes,
		FoodData:   []*et.AppFoodCode{},
		SellStatus: 1,
	}
	mtReq.FoodData = append(mtReq.FoodData, &et.AppFoodCode{AppFoodCode: storeProduct.ProductId})
	// 调用远程更新状态

	storeMasterId, err := GetAppChannelByFinanceCode(storeProduct.StoreFinanceCode)
	if err != nil {
		glog.Error("upProductToMt", "GetAppChannelByFinanceCode failed,", kit.JsonEncode(storeProduct))
		return errors.New("GetAppChannelByFinanceCode failed")
	}
	mtReq.StoreMasterId = storeMasterId
	if !CanCallThirdApi(cast.ToInt(storeProduct.ProductId), ChannelMtId, storeProduct.StoreFinanceCode) {
		glog.Error("没有第三方商品id====13,商品id:", cast.ToInt(storeProduct.ProductId), "财务编码：", storeProduct.StoreFinanceCode)
		return errors.New("商品未在第三方创建，请先编辑后再进行操作")
	}
	res, err := client.RPC.RetailSellStatus(client.Ctx, mtReq)
	glog.Info("下架-5555上下架到美团返回数据", kit.JsonEncode(res), ",入参：", kit.JsonEncode(mtReq), "错误err：", kit.JsonEncode(err))

	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	}
	errMsg := ""
	if res.Code != 200 && res.Error != nil {
		errMsg = res.Error.Msg
	}
	UpdateProductThirdSyncErr(cast.ToInt(storeProduct.ProductId), ChannelMtId, storeProduct.StoreFinanceCode, errMsg)

	if res.Code != 200 {
		return errors.New(res.Message)
	}
	return nil
}

// 下架到京东
func (this *ChannelProductUpDown) downProductToJd(storeProduct *ChannelProductUp_StoreProduct, client *et.Client) error {
	storeMasterId, err := GetAppChannelByFinanceCode(storeProduct.StoreFinanceCode)
	if err != nil {
		glog.Error("downProductToJd,", "GetAppChannelByFinanceCode,", storeProduct.StoreFinanceCode, err)
		return err
	}

	syncData := &et.BatchUpdateVendibilityRequest{
		StationNo:     storeProduct.StoreChannelFinanceCodes,
		UserPin:       "xrp",
		StoreMasterId: storeMasterId,
	}
	syncData.StockVendibilityList = append(syncData.StockVendibilityList, &et.JddjStockVendibility{DoSale: false, OutSkuId: storeProduct.ProductSkuId})

	res, err := client.JddjProduct.BatchUpdateVendibility(client.Ctx, syncData)
	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	} else if res.RetCode != "0" {
		return errors.New(res.RetMsg)
	}
	return nil
}

// 下架到饿了么
func (this *ChannelProductUpDown) downProductToElm(storeProduct *ChannelProductUp_StoreProduct, client *et.Client) error {
	logPrefix := fmt.Sprintf("下架饿了么单个商品====入参：%s", kit.JsonEncode(storeProduct))
	// 调用远程接口

	appChannel, err := GetAppChannelByFinanceCode(storeProduct.StoreFinanceCode)
	if err != nil {
		return err
	}

	if appChannel == 0 {
		err = errors.New("获取appChannel值错误")
		return err
	}
	elmIn := &et.UpdateElmShopSkuPriceRequest{
		ShopId:      storeProduct.StoreChannelFinanceCodes,
		CustomSkuId: storeProduct.ProductSkuId,
		AppChannel:  appChannel,
	}
	if !CanCallThirdApi(cast.ToInt(storeProduct.ProductId), ChannelElmId, storeProduct.StoreFinanceCode) {
		glog.Error(logPrefix, "没有第三方商品id====14,商品id:", cast.ToInt(storeProduct.ProductId), "财务编码：", storeProduct.StoreFinanceCode)
		return errors.New("商品未在第三方创建，请先编辑后再进行操作")
	}
	if len(storeProduct.StoreChannelFinanceCodes) == 0 {
		glog.Error(logPrefix, "饿了么门店id为空1")
		return errors.New("饿了么门店id为空")
	}
	elmOnlineResponse, err := client.ELMPRODUCT.OfflineElmShopSkuOne(client.Ctx, elmIn)
	glog.Info(logPrefix, "上下架商品到饿了么返回数据(单个商品饿了么下架)：", kit.JsonEncode(elmOnlineResponse), "请求数据为：", kit.JsonEncode(elmIn), ",err为：", kit.JsonEncode(err))

	if err != nil {
		return errors.New(fmt.Sprintf("external通信失败,%s", err.Error()))
	}
	// 更新调用第三方返回错误信息
	UpdateProductThirdSyncErr(cast.ToInt(storeProduct.ProductId), ChannelElmId, storeProduct.StoreFinanceCode, elmOnlineResponse.Message)
	if elmOnlineResponse.Code != 200 {
		return errors.New(elmOnlineResponse.Message)
	}
	return nil
}

// C.下架之前进行的操作
func (this *ChannelProductUpDown) afterDown(storeProduct *ChannelProductUp_StoreProduct, downType int) error {
	var channelStoreProduct models.ChannelStoreProduct
	_, err := engine.Where("channel_id=?", this.ChannelId).Where("product_id=?", storeProduct.ProductId).Where("finance_code=?", storeProduct.StoreFinanceCode).Get(&channelStoreProduct)
	if err != nil {
		return err
	} else {
		if channelStoreProduct.Id > 0 {
			channelStoreProduct.UpDownState = 0
			channelStoreProduct.UpdateDate = time.Now()
			channelStoreProduct.DownType = downType
			engine.ID(channelStoreProduct.Id).Cols("up_down_state,update_date, down_type").Update(&channelStoreProduct)
		}
	}
	return nil
}

// ////////////////////////////////////////////////////////////////  公用方法
// 导出上架结果 url为导出文档地址,如果地址出错则返回json编码的错误信息
// fileUrl 文档第三方地址
// upResult 上架结果说明
func (this *ChannelProductUpDown) Export() (fileUrl, upResult string, successNum, failNum int32) {
	defer func() {
		// 出现异常则记录
		if err := recover(); err != nil {
			this.UnknownError = errors.New("出现了未处理的错误")
			glog.Error(err)
		}
	}()
	//var successCount int
	// 数据行
	var excelRow [][]string
	excelRow = append(excelRow, []string{"财务编码", "平台商品ID", "失败原因"})

	productMap := map[string][][]string{}

	for _, k := range this.UpResult {
		productKey := k.StoreProduct.StoreFinanceCode + "-" + k.StoreProduct.ProductSkuId

		if k.IsSuccess == false && len(k.Message) > 0 {
			if _, ok := productMap[productKey]; ok {
				continue
			} else {
				failNum++
				productMap[productKey] = append(productMap[productKey], []string{k.StoreProduct.StoreFinanceCode, k.StoreProduct.ProductSkuId, k.Message})
			}
		} else {
			successNum++
		}
	}

	for _, v := range productMap {
		excelRow = append(excelRow, v...)
	}

	if len(this.UpResult) == 0 {
		return
	}

	if failNum == 0 {
		upResult = "成功"
	} else {
		url, err := ExportProductErr(excelRow)
		if err != nil || len(url) == 0 {
			// 		// 如果保存到网络出错则写入日志
			glog.Error(err)
			glog.Warning("上架错误列表 " + utils.ObjectToJsonString(excelRow))
		} else {
			fileUrl = url
		}
		if successNum == 0 {
			upResult = "失败"
		} else {
			upResult = fmt.Sprintf("部分成功(共:%d,成功:%d,失败:%d)", len(this.UpResult), successNum, failNum)
		}
	}
	return
}

// 同步更新下架的has_stock表的状态
func UpdateHasStockUp(productHasStock *ChannelProductUp_StoreProduct, channelId int) error {
	ints := make([]int, 0)
	err := engine.Table("channel_store_product_has_stock").Cols("id").Where("channel_id=? and finance_code=?  and product_id=? ",
		channelId, productHasStock.StoreFinanceCode, productHasStock.ProductId).Find(&ints)
	if err != nil {
		glog.Error("UpdateHasStockUp查询hasStock失败error: ", err.Error())
		return err
	}
	glog.Info("UpdateHasStockUp查询到的stock信息：", kit.JsonEncode(ints))

	if len(ints) > 0 {
		_, err := engine.In("id", ints).Cols("has_stock_up").Update(&models.ChannelStoreProductHasStock{HasStockUp: 0})
		if err != nil {
			glog.Error("UpdateHasStockUp字段hasStockUp失败：", err.Error())
			return err
		}
	}

	return nil
}

/*
*
同步更新product_has_stock表
*/
func InsertOrUpdateProductHasStock(channelStoreProduct models.ChannelStoreProduct, in *ic.GetStockInfoResponse) error {
	glog.Info("InsertOrUpdateProductHasStock参数: ", kit.JsonEncode(channelStoreProduct), " in :", kit.JsonEncode(in))
	if channelStoreProduct.ChannelId == ChannelAwenId {
		product := models.ChannelProduct{Id: int32(channelStoreProduct.ProductId), ChannelId: int32(channelStoreProduct.ChannelId)}
		b, e := engine.Where("id=?", product.Id).Where("channel_id= ?", product.ChannelId).Get(&product)
		if e != nil {
			glog.Error("商品信息异常", product.Id, "channel_id:", product.ChannelId)
			return errors.New("商品信息异常")
		}
		if !b {
			glog.Error("查询不到商品信息", product.Id, "channel_id:", product.ChannelId)
			return errors.New("查询不到商品信息")
		}
		// 判断如果是否是虚拟商品
		if product.ProductType == 3 {
			infos := []*ic.SkuCodeInfo{}
			info := ic.SkuCodeInfo{}
			info.FinanceCode = channelStoreProduct.FinanceCode
			info.Sku = cast.ToString(channelStoreProduct.SkuId)
			codeInfos := append(infos, &info)

			data := new(ic.GetStockInfoResponse)
			int32s, e := GetStockInfoBySkuCode(0, codeInfos, product.ChannelId, 1, data)
			if e != nil {
				glog.Error("虚拟商品查询异常:", int32s, "data: ", data)
				return errors.New("虚拟商品查询异常")
			}
			in = data // 将组合商品的查询结果返回
		}

		if in != nil && len(in.GoodsInWarehouse) > 0 {
			for _, v := range in.GoodsInWarehouse {
				var productHasStock = models.ChannelStoreProductHasStock{}
				productHasStock.ChannelStoreProductId = channelStoreProduct.Id
				productHasStock.ChannelId = channelStoreProduct.ChannelId
				productHasStock.FinanceCode = channelStoreProduct.FinanceCode
				productHasStock.ProductId = channelStoreProduct.ProductId

				productHasStock.SkuId = int(channelStoreProduct.SkuId)
				productHasStock.WarehouseId = int(v.WarehouseId)

				if v.Stock > 0 {
					productHasStock.HasStock = 1
					productHasStock.HasStockUp = 1 // 有库存并上架
				} else {
					productHasStock.HasStock = 0
					productHasStock.HasStockUp = 0
				}
				//先查询
				glog.Info("productHasStock参数信息：", kit.JsonEncode(productHasStock))
				stocks := make([]models.ChannelStoreProductHasStock, 0)
				err := engine.Where("channel_id=? and finance_code=? and warehouse_id=?  and product_id=? ",
					productHasStock.ChannelId, productHasStock.FinanceCode, productHasStock.WarehouseId, productHasStock.ProductId).OrderBy("update_time desc").Find(&stocks)
				if err != nil {
					glog.Error("查询hasStock失败error: ", err.Error())
					return err
				}
				var stock models.ChannelStoreProductHasStock
				if len(stocks) >= 1 {
					//删除重复的数据bug
					stock = stocks[0]
					hasStocks := stocks[1:]
					glog.Info("删除的has_stock的重复信息：", kit.JsonEncode(hasStocks))
					for _, v := range hasStocks {
						engine.Exec("delete from channel_store_product_has_stock where id = ?", v.Id)
					}

				}
				glog.Info("查询到的stock信息：", kit.JsonEncode(stock))
				if stock.Id <= 0 {
					// insert
					productHasStock.CreateTime = time.Now()
					_, err := engine.Insert(&productHasStock)
					if err != nil {
						glog.Error("查询productHasStock表失败error: ", err.Error())
						return err
					}
				} else {
					// update
					productHasStock.Id = stock.Id
					productHasStock.UpdateTime = time.Now()
					_, err = engine.Id(productHasStock.Id).Cols("has_stock", "update_time", "channel_store_product_id", "has_stock_up", "warehouse_category").Update(&productHasStock)
					if err != nil {
						glog.Error("更新productHasStock表失败error：", err.Error())
					}
				}
			}
		}
	}
	return nil

}

// 渠道商品上架，支持单门店多门店
func (c *Product) AddUpOrDownTask(ctx context.Context, in *pc.CreateBatchTaskRequest) (*pc.BaseResponse, error) {

	logPreFix := "多商品批量上架参数"
	glog.Info(logPreFix, kit.JsonEncode(in))
	var response = &pc.BaseResponse{Code: 400}

	var upOrDownDto = models.AddUpOrDownTaskDTO{}

	var productIds []string
	err := json.Unmarshal([]byte(in.OperationFileUrl), &upOrDownDto)
	if err != nil {
		glog.Error(logPreFix, "解析in参数异常", err.Error())
		return response, err
	}

	for i := range upOrDownDto.ProductIds {
		productIds = append(productIds, cast.ToString(upOrDownDto.ProductIds[i]))
	}

	// 上下架类封装
	var channelProductUpDown ChannelProductUpDown
	channelProductUpDown.ProductIds = productIds
	channelProductUpDown.ChannelId = int(in.ChannelId)
	channelProductUpDown.FinanceCodes = []string{upOrDownDto.FinanceCode}
	channelProductUpDown.UserNo = in.CreateId
	channelProductUpDown.UserName = in.CreateName
	channelProductUpDown.Ctx = ctx
	// 全部渠道需要同步价格 商品上架需要同步价格
	channelProductUpDown.IsSyncPrice = true

	var getTaskVo = pc.GetUserUnFinishedTaskRequest{
		UserNo:        in.CreateId,
		ChannelId:     in.ChannelId,
		OperationData: utils.ObjectToJsonString(channelProductUpDown),
	}
	if in.UpOrDown == 1 {
		getTaskVo.TaskContent = 4
	} else {
		getTaskVo.TaskContent = 18
	}
	userTask, err := c.GetUserUnFinishedTask(ctx, &getTaskVo) // 重复任务判断

	if err != nil {
		response.Message = "生成异步任务失败,请稍后重试"
		return response, nil
	}
	if userTask.Id > 0 {
		response.Message = "已经存在相同的任务"
		return response, nil
	}
	//保存到异步任务
	task := models.TaskList{
		ChannelId: in.ChannelId,
		//TaskContent:      0,
		OperationFileUrl: utils.ObjectToJsonString(channelProductUpDown),
		ModifyId:         in.CreateId,
		CreateId:         in.CreateId,
		CreateName:       in.CreateName,
		CreateMobile:     in.CreateMobile,
		CreateIp:         in.CreateIp,
		IpLocation:       in.IpLocation,
	}

	if in.UpOrDown == 1 { //上架
		task.TaskContent = 4
		task.ExtendedData = enum.ChannelMap[int(in.ChannelId)] + enum.TaskContentMapText[4]
	} else {
		task.TaskContent = 18 // 下架
		task.ExtendedData = enum.ChannelMap[int(in.ChannelId)] + enum.TaskContentMapText[18]
	}
	_, err = InsertTaskList(task)
	if err != nil {
		glog.Error(err)
		response.Message = "生成异步任务失败,请稍后重试"
		return response, nil
	}

	response.Code = 200
	return response, nil
}

// 查询门店仓库信息
func GetWarehouseShop() (channelShopKey map[string]string) {
	session := NewDbConn().NewSession()
	defer session.Close()
	var warehouseShop []models.WarehouseShop
	if err := session.SQL("select wrs.shop_id,wrs.channel_id from dc_dispatch.warehouse_relation_shop wrs " +
		"join dc_dispatch.warehouse w on wrs.warehouse_id = w.id where w.category = 3;").
		Find(&warehouseShop); err != nil {
		glog.Error("GetWarehouseShop,查询渠道商品失败,error:", err.Error())
	}
	channelShopKey = make(map[string]string, 0)
	for _, v := range warehouseShop {
		key := fmt.Sprintf("%s%d", v.ShopId, v.ChannelId)
		channelShopKey[key] = v.ShopId
	}
	return
}

// 查询是否有组合
func CheckGroupProduct(productId string, channelId int) string {
	session := NewDbConn().NewSession()
	defer session.Close()
	var pid []string
	if err := session.SQL("select distinct group_product_id from dc_product.channel_sku_group where product_type=1 and product_id =? and channel_id = ?;", productId, channelId).
		Find(&pid); err != nil {
		glog.Error("checkGroupProduct,查询渠道商品失败,商品id:", productId, ",error:", err.Error())
	}
	if len(pid) > 0 {
		productId = strings.Join(pid, ",")
	}
	return productId
}
