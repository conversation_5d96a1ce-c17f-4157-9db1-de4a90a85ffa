package services

import (
	"_/models"
	"_/proto/pc"
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"testing"
	"time"

	"github.com/golang/protobuf/ptypes/empty"
	kit "github.com/tricobbler/rp-kit"

	"github.com/go-xorm/xorm"
	_struct "github.com/golang/protobuf/ptypes/struct"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
)

func TestUuid(t *testing.T) {
	for {
		func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println(err)
				}
			}()
			println(config.GetString("grpc.datacenter"))
		}()
		time.Sleep(time.Second * 1)
	}
}

// func Test_handleSkuInfo(t *testing.T) {
// 	type args struct {
// 		info []*pc.SkuInfo
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 	}{
// 		{
// 			name: "",
// 			args: args{info: createArr()},
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			sku := make(map[string][]*pc.SkuInfo)
// 			handleSkuInfo(tt.args.info, uuid.New().String(), sku)

// 			for _, v := range sku {
// 				for _, v2 := range v {
// 					if v2 != nil {
// 						fmt.Println(v2.SpecId, v2.SpecValueId,v2.MarketPrice, "")
// 					} else {
// 						println()
// 					}
// 				}
// 				//println(v.SpecId, v.SpecValueId, v.MarketPrice)
// 			}
// 			t.Fail()
// 		})
// 	}
// }

// func TestProduct_QueryCategory(t *testing.T) {
// 	type args struct {
// 		ctx context.Context
// 		in  *pc.Category
// 	}
// 	tests := []struct {
// 		name    string
// 		p       *Product
// 		args    args
// 		want    *pc.CategoryResponse
// 		wantErr bool
// 	}{
// 		{
// 			name: "",
// 			p:    &Product{},
// 			args: args{
// 				ctx: nil,
// 				in:  &pc.Category{},
// 			},
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, err := tt.p.QueryCategory(tt.args.ctx, tt.args.in)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("Product.QueryCategory() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("Product.QueryCategory() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }
// func Test_GetStockInfoBySkuCode(t *testing.T) {
// 	type args struct {
// 		ctx         context.Context
// 		in          []*pc.SkuInfo
// 		financeCode []string
// 	}
// 	tests := []struct {
// 		name string
// 		p    *InventoryCenterClient
// 		args args
// 		want *pc.BaseResponse
// 	}{
// 		{
// 			name: "t1",
// 			p:    GetInventoryCenterClient(),
// 			args: args{
// 				ctx: nil,
// 				in: []*pc.SkuInfo{
// 					&pc.SkuInfo{
// 						SkuId: 1000147001,
// 					},
// 				},
// 				financeCode: []string{"CX0010"},
// 			},
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, err := tt.p.GetStockInfoBySkuCode(tt.args.in, tt.args.financeCode)
// 			if err != nil {
// 				glog.Error(err)
// 				return
// 			}
// 			t.Log(got)
// 		})
// 	}
// }

func TestProduct_EditChannelProduct(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.ChannelProductRequest
	}
	tests := []struct {
		name    string
		p       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "编辑渠道商品t1",
			p:    &Product{},
			want: &pc.BaseResponse{
				Code: 200,
			},
			args: args{
				ctx: nil,
				in: &pc.ChannelProductRequest{
					Product: &pc.ChannelProduct{
						CategoryId: 1140,
						BrandId:    0,
						Name:       "测试商品",
						// Code:          getUuid(),
						// BarCode:       getUuid(),
						IsDel:         0,
						IsGroup:       0,
						Pic:           "",
						SellingPoint:  "商品卖点",
						Video:         "",
						ContentPc:     "电脑端富文本内容",
						ContentMobile: "手机端富文本内容",
						IsDiscount:    0,
						ProductType:   1,
					},
					SkuInfo: []*pc.SkuInfo{
						&pc.SkuInfo{
							SkuThird: []*pc.SkuThird{
								&pc.SkuThird{
									ThirdSpuId: "001",
									ThirdSkuId: "123",
									ErpId:      4,
								},
							},
							Skuv: []*pc.SkuValue{
								&pc.SkuValue{
									SpecId:      1,
									SpecValueId: 1,
									Pic:         "xxx.jpg",
								},
							},
							RetailPrice: 150,
							MarketPrice: 199,
						},
						&pc.SkuInfo{
							SkuThird: []*pc.SkuThird{
								&pc.SkuThird{
									ThirdSpuId: "123",
									ThirdSkuId: "123",
									ErpId:      4,
								},
							},
							Skuv: []*pc.SkuValue{
								&pc.SkuValue{
									SpecId:      1,
									SpecValueId: 2,
									Pic:         "xxx.jpg",
								},
							},
							RetailPrice: 150,
							MarketPrice: 199,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.EditChannelProduct(tt.args.ctx, tt.args.in)
			glog.Info(got.Code, got.Message, got.Error)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.EditChannelProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.EditChannelProduct() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_NewProduct(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.ProductRequest
	}
	tests := []struct {
		name    string
		p       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "新增商品",
			p:    &Product{},
			args: args{
				ctx: nil,
				in: &pc.ProductRequest{
					Product: &pc.Product{
						CategoryId: 1140,
						BrandId:    0,
						Name:       "测试商品",
						// Code:          getUuid(),
						// BarCode:       getUuid(),
						IsDel:         0,
						IsGroup:       0,
						Pic:           "http://file.vetscloud.com/1b5da7366666394e056367dc10226ddd,http://file.vetscloud.com/1390de573bb5e762988abb9ffdabcf02",
						SellingPoint:  "商品卖点",
						Video:         "",
						ContentPc:     "电脑端富文本内容",
						ContentMobile: "手机端富文本内容",
						IsDiscount:    0,
						ProductType:   1,
						Disease: []*pc.ProductDiagnoseDicList{
							{Code: "xxx", Name: "名称"},
						},
						DrugDosage: &pc.ProductDrugDosage{
							DosingUnitName:   "2323",
							DosingWayName:    "2323",
							UseFrequencyName: "2323",
							RecommendDosage: []*pc.ProductDrugDosage_RecommendDosage{
								{
									Value: 0,
									Name:  "",
								},
							},
						},
					},
					SkuInfo: []*pc.SkuInfo{
						&pc.SkuInfo{
							SkuThird: []*pc.SkuThird{
								&pc.SkuThird{
									ThirdSpuId: getUuid(),
									ThirdSkuId: getUuid(),
									ErpId:      4,
								},
							},
							Skuv: []*pc.SkuValue{
								&pc.SkuValue{
									SpecId:      1,
									SpecValueId: 1,
									Pic:         "xxx.jpg",
								},
							},
							RetailPrice: 150,
							MarketPrice: 199,
							BarCode:     getUuid(),
						},
					},
					/* ProductAttr: []*pc.ProductAttr{
						&pc.ProductAttr{
							AttrId:      getUuid(),
							AttrValueId: getUuid(),
						},
						&pc.ProductAttr{
							AttrId:      getUuid(),
							AttrValueId: getUuid(),
						},
						&pc.ProductAttr{
							AttrId:      getUuid(),
							AttrValueId: getUuid(),
						},
						&pc.ProductAttr{
							AttrId:      getUuid(),
							AttrValueId: getUuid(),
						},
					}, */
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.NewProduct(tt.args.ctx, tt.args.in)
			glog.Info(got.Code, got.Message, got.Error)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.NewProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.NewProduct() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_DelProduct(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.ArrayIntValue
	}
	tests := []struct {
		name    string
		p       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "根据删除商品",
			p:    &Product{},
			args: args{
				ctx: nil,
				in:  &pc.ArrayIntValue{Value: []int32{1000002}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.DelProduct(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.DelProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.DelProduct() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_QueryProduct(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.QueryProductRequest
	}
	tests := []struct {
		name    string
		p       *Product
		args    args
		want    *pc.ProductResponse
		wantErr bool
	}{
		{
			name: "商品库商品列表",
			p:    &Product{},
			args: args{
				in: &pc.QueryProductRequest{
					PageIndex: 1,
					PageSize:  10,
					WhereType: "",
					Where:     "",
					//IsExclude: 1,
					IsPrescribedDrug: 0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.QueryProduct(tt.args.ctx, tt.args.in)

			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QueryProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QueryProduct() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_NewErp(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.Erp
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "插入ERP",
			c:    &Product{},
			args: args{
				ctx: nil,
				in: &pc.Erp{
					Id:    0,
					Name:  "测试",
					IsUse: 0,
				},
			},
			want:    &pc.BaseResponse{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.NewErp(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.NewErp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.NewErp() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_QueryAttr(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.IdRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.AttrResponse
		wantErr bool
	}{
		{
			name:    "查询商品属性",
			c:       &Product{},
			args:    args{ctx: context.Background(), in: &pc.IdRequest{Id: "6824f0d72a974a3eb747341dfb9f593f"}},
			want:    &pc.AttrResponse{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryAttr(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QueryAttr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QueryAttr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_QueryAttrValue(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.IdRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.AttrValueResponse
		wantErr bool
	}{
		{
			name:    "查询商品属性值",
			c:       &Product{},
			args:    args{ctx: context.Background(), in: &pc.IdRequest{Id: "0bebab44320449429ad38863134448a2,0f37e5b12af04335907a0bb9e0e75392"}},
			want:    &pc.AttrValueResponse{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryAttrValue(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QueryAttrValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QueryAttrValue() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_QueryProductAttr(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.IdRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.ProductAttrResponse
		wantErr bool
	}{
		{
			name:    "查询商品的属性",
			c:       &Product{},
			args:    args{ctx: context.Background(), in: &pc.IdRequest{Id: "0675dd5ef5ee4875b899ae0e96fe52f1"}},
			want:    &pc.ProductAttrResponse{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryProductAttr(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QueryProductAttr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QueryProductAttr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMediaClassList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.MediaClassListRequest
	}
	tests := []struct {
		name    string
		p       *Product
		args    args
		want    *pc.MediaClassListResponse
		wantErr bool
	}{
		{
			name: "图片视频相册列表",
			p:    &Product{},
			args: args{
				in: &pc.MediaClassListRequest{
					KeywordType: 1,
					Keyword:     "211",
					ApicType:    1,
					PageIndex:   1,
					PageSize:    10,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.MediaClassList(tt.args.ctx, tt.args.in)
			fmt.Println(got)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.MediaClassList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.MediaClassList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMediaItemList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.MediaItemListRequest
	}
	tests := []struct {
		name    string
		p       *Product
		args    args
		want    *pc.MediaItemListResponse
		wantErr bool
	}{
		{
			name: "图片视频列表",
			p:    &Product{},
			args: args{
				in: &pc.MediaItemListRequest{
					MediaType: 1,
					AclassId:  "2",
					PageIndex: 1,
					PageSize:  5,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.MediaItemList(tt.args.ctx, tt.args.in)
			fmt.Println(got)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.MediaItemList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.MediaItemList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMediaUpload(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.MediaUploadRequest
	}
	tests := []struct {
		name    string
		p       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "图片视频上传",
			p:    &Product{},
			args: args{
				in: &pc.MediaUploadRequest{
					AclassId:  1,
					ApicType:  1,
					ApicName:  "asdas.jpg",
					StoreId:   30,
					StoreName: "商家名称",
					ApicSize:  10000,
					ApicPath:  "http://fsfsfs.faf.cn/sdfgasdgsasdfasf",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.MediaUpload(tt.args.ctx, tt.args.in)
			fmt.Println(got)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.MediaItemList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.MediaItemList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestQuery(t *testing.T) {
	Engine := NewDbConn()
	mp, _ := Engine.Table("spec").Select("id as k,name as v").QueryString()
	for k, v := range mp {
		fmt.Println(k, v, v["k"], v["v"])
	}
	//fmt.Println(mp,err)
}

/* func TestProduct_QuerySku(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.IdRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.SkuResponse
		wantErr bool
	}{
		{
			name: "",
			c:    &Product{},
			args: args{ctx: context.Background(), in: &pc.IdRequest{Id: "05ae481424c6401d832dbda7057f215e"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QuerySku(tt.args.ctx, tt.args.in)
			bt, _ := json.Marshal(got.Details)
			fmt.Println(string(bt))
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QuerySku() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QuerySku() = %v, want %v", got, tt.want)
			}
		})
	}
} */

func TestProduct_QueryCategoryRecursion(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.IdRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.CategoryResponse
		wantErr bool
	}{
		{
			name: "",
			c:    &Product{},
			args: args{ctx: context.Background(), in: &pc.IdRequest{Id: "6720e4110b564a4aa187f3368c351eeb"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryCategoryRecursion(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QueryCategoryRecursion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QueryCategoryRecursion() = %v, want %v", got, tt.want)
			}
		})
	}
}

// func TestProduct_QuerySkuThird(t *testing.T) {
// 	type args struct {
// 		ctx context.Context
// 		in *pc.SkuThirdRequest
// 	}
// 	tests := []struct {
// 		name    string
// 		c       *Product
// 		args    args
// 		want    *pc.SkuThirdResponse
// 		wantErr bool
// 	}{
// 		{
// 			name: "",
// 			c:    &Product{},
// 			args: args{ctx: context.Background(), in: &pc.SkuThirdRequest{ProductId: "05ae481424c6401d832dbda7057f215e"}},
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, err := tt.c.QuerySkuThird(tt.args.ctx, tt.args.in)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("Product.QuerySkuThird() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("Product.QuerySkuThird() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

func Test_getCategoryMap(t *testing.T) {
	c := &Product{}
	res, _ := c.QueryCategoryRecursion(nil, &pc.IdRequest{Id: "51b25500bf9b48b4833d46a62a75f81f"})

	type args struct {
		parentId int32
		arr      []*pc.Category
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "",
			args: args{parentId: 0, arr: res.Details},
			want: " ",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := c.getCategoryName(tt.args.parentId, tt.args.arr); got != tt.want {
				t.Errorf("getCategoryMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

/* func TestProduct_QueryProductSingle(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.ProductSkuIdRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.ProductResponse
		wantErr bool
	}{
		{
			name: "",
			c:    &Product{},
			args: args{ctx: nil, in: &pc.ProductSkuIdRequest{
				//Id:&pc.ProductSkuIdRequest_ProductId{ProductId: "6ceb156c4c734d4db6866f3627cd65f6"},
				Id: &pc.ProductSkuIdRequest_SkuId{SkuId: "c233fbb5d7ed49d5a4e4b93486e503cb"},
			},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryProductSingle(tt.args.ctx, tt.args.in)
			println(got.Details[0].Name)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QueryProductSingle() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QueryProductSingle() = %v, want %v", got, tt.want)
			}
		})
	}
} */

func TestProduct_NewCategory(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.Category
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "",
			c:    &Product{},
			args: args{ctx: context.Background(), in: &pc.Category{
				Name:     "测试",
				ParentId: 1041,
			}},
		},
	}
	for _, tt := range tests {
		for {
			t.Run(tt.name, func(t *testing.T) {
				got, err := tt.c.NewCategory(tt.args.ctx, tt.args.in)
				if (err != nil) != tt.wantErr {
					t.Errorf("Product.NewCategory() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Product.NewCategory() = %v, want %v", got, tt.want)
				}
			})
			time.Sleep(time.Second * 1)
		}
	}
}

func TestProduct_QuerySkuThirdList(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.SkuThirdListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.SkuThirdListResponse
		wantErr bool
	}{
		{
			name: "",
			args: args{ctx: context.Background(), in: &pc.SkuThirdListRequest{
				Systemid:      "2",
				Startmodified: "",
				Endmodified:   "",
				Productname:   "",
				PageIndex:     1,
				PageSize:      5,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.QuerySkuThirdList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("QuerySkuThirdList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("QuerySkuThirdList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

/* func TestProduct_EditProductIsUse(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *wrappers.Int32Value
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "编辑商品已使用状态",
			c:    &Product{},
			args: args{ctx: nil, in: &wrappers.Int32Value{Value: 1000127}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.EditProductIsUse(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.EditProductIsUse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.EditProductIsUse() = %v, want %v", got, tt.want)
			}
		})
	}
} */

func TestProduct_GetThirdSpuSkuId(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.ArrayIntValue
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.ThirdSpuSkuIdResponse
		wantErr bool
	}{
		{
			name: "",
			c:    &Product{},
			args: args{
				ctx: nil,
				in:  &pc.ArrayIntValue{Value: []int32{2}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetThirdSpuSkuId(tt.args.ctx, tt.args.in)
			for k, v := range got.Details.Fields {
				fmt.Println(k, v)
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.GetThirdSpuSkuId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.GetThirdSpuSkuId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_EditProduct(t *testing.T) {
	str := `{"product":{"id":1018189,"category_id":1140,"brand_id":256,"name":"测试商品22000","code":"","bar_code":"","create_date":"2020-08-20 14:28:21","update_date":"2020-08-20 14:28:23","is_del":0,"is_group":0,"pic":"http://file.vetscloud.com/1390de573bb5e762988abb9ffdabcf02,http://file.vetscloud.com/1390de573bb5e762988abb9ffdabcf02,,,","selling_point":"商品卖点000","video":"http://file.vetscloud.com/a9ca48e1809f1dbf6d6fbe8895c4f2cf.mp4","content_pc":"<p>电脑端富文本内容3331111111111111111</p>","content_mobile":"<p>手机端富文本内容</p>","is_discount":0,"product_type":1,"is_use":0,"category_name":"猫站>猫咪专区>猫咪玩具>猫抓板","sku":null,"attr":null,"channel_id":""},"product_attr":[],"sku_info":[{"sku_third":[{"id":22774,"sku_id":1018189001,"third_sku_id":"2bdaf5c2983b432ca78ca2ea7f151e34","erp_id":4,"product_id":1018189,"erp_name":"子龙货号","third_spu_id":"1f6411c1eaf049f9a70872d96ba19ec33333","channel_id":0,"is_use":0}],"skuv":[{"id":19800,"spec_id":1,"spec_value_id":1,"sku_id":1018189001,"product_id":1018189,"pic":"xxx222.jpg","spec_name":"种类","spec_value_value":"小型幼犬1.5KG","details":null,"channel_id":0}],"retail_price":150,"sku_id":1018189001,"product_id":1018189,"market_price":199,"sku_group":null,"bar_code":"95842620ac7e4ad9b4b82e6ba90080f799","channel_id":0,"is_use":0,"weight_for_unit":111,"weight_unit":"千克(kg)","min_order_count":0,"price_unit":"","prepose_price":111,"store_price":111}],"sku_group":[]}`
	var req pc.ProductRequest
	if err := json.Unmarshal([]byte(str), &req); err != nil {
		t.Error(err)
	}

	type args struct {
		ctx context.Context
		in  *pc.ProductRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "编辑商品",
			c:    &Product{},
			args: args{ctx: nil, in: &req},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.EditProduct(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.EditProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.EditProduct() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_QueryProductSnapshot(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.ChannelProductSnapshotRequest
	}
	_userinfo := models.LoginUserInfo{UserNo: "test"}

	ctx := context.WithValue(context.TODO(), "user_info", &_userinfo)
	tests := []struct {
		name string
		c    *Product
		args args
	}{
		{
			name: "快照查询",
			c:    &Product{},
			args: args{
				ctx: ctx,
				in: &pc.ChannelProductSnapshotRequest{
					ProductId:   []int32{1031452},
					ChannelId:   1,
					FinanceCode: "CX0004",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got, err := tt.c.QueryChannelProductSnapshot(tt.args.ctx, tt.args.in); err != nil {
				t.Error(err)
			} else {
				if len(got.Details) == 0 {
					t.Error("len 0")
				}
			}
		})
	}
}

func TestProduct_QueryChannelProductOnly(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.OneofIdRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.ProductResponse
		wantErr bool
	}{
		{
			name: "渠道商品主表查询 ",
			c:    &Product{},
			args: args{
				ctx: context.Background(),
				in: &pc.OneofIdRequest{
					Id:        &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{1000145}}},
					ChannelId: 2,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryChannelProductOnly(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QueryChannelProductOnly() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Printf("%v", got.Details)
			if len(got.Details) == 0 {
				t.Errorf("Product.QueryChannelProductOnly() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_QueryChannelProductAttr(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.OneofIdRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.ProductAttrResponse
		wantErr bool
	}{
		{
			name: "渠道商品属性查询 ",
			c:    &Product{},
			args: args{
				ctx: context.Background(),
				in: &pc.OneofIdRequest{
					Id:        &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{100010}}},
					ChannelId: 1,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryChannelProductAttr(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QueryChannelProductAttr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(got.Details) == 0 {
				t.Errorf("Product.QueryChannelProductAttr() = %v, want %v", got, tt.want)
			}
		})
	}
}

//func TestProduct_ChannelProductUp(t *testing.T) {
//	type args struct {
//		ctx context.Context
//		in  *pc.ChannelProductUpRequest
//	}
//	tests := []struct {
//		name    string
//		c       *Product
//		args    args
//		want    *pc.ChannelProductSnapshotResponse
//		wantErr bool
//	}{
//		{
//			name: "快照查询",
//			c:    &Product{},
//			args: args{
//				ctx: context.Background(),
//				in: &pc.ChannelProductUpRequest{
//					ProductId:    "1000122",
//					ChannelId:    1,
//					FinanceCode:  "CX0004",
//					AppPoiCode:   "4889_2701013",
//					IsAllFinance: 0,
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if got, err := tt.c.ChannelProductUp(tt.args.ctx, tt.args.in); err != nil {
//				t.Error(err)
//			} else if got.Code != 200 {
//				t.Errorf(got.Message)
//			}
//		})
//	}
//}

func TestProduct_QuerySpecSingle(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.IdRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.SpecResponse
		wantErr bool
	}{
		{
			name:    "",
			c:       &Product{},
			args:    args{ctx: context.Background(), in: &pc.IdRequest{Id: "1,2"}},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QuerySpecSingle(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QuerySpecSingle() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QuerySpecSingle() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPic(t *testing.T) {
	pic := `<p><img src="http://file.vetscloud.com/1948d14fd05071070f321ba27d9a1bcf"><img src="http://file.vetscloud.com/1948d14fd05071070f321ba27d9a1bcf"><img src="http://file.vetscloud.com/1948d14fd05071070f321ba27d9a1bcf">http://123.jpg https://456.gif</p>`
	pattern := `(https?|ftp|file)://[-\w+&@#/%=~|?!:,.;]+[-\w+&@#/%=~|]`
	pattern = `(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]`

	for _, v := range regexp.MustCompile(pattern).FindAllString(pic, -1) {
		println(v)
	}
}

func BenchmarkRegexUrl(b *testing.B) {
	pic := `<p><img src="http://file.vetscloud.com/1948d14fd05071070f321ba27d9a1bcf"><img src="http://file.vetscloud.com/1948d14fd05071070f321ba27d9a1bcf"><img src="http://file.vetscloud.com/1948d14fd05071070f321ba27d9a1bcf">http://123.jpg https://456.gif</p>`
	// pattern := `(https?)://[-\w+&@#/%=~|?!:,.;]+[-\w+&@#/%=~|]`
	// pattern = `(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]`
	rex := regexp.MustCompile(`(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]`)
	for i := 0; i < b.N; i++ {
		rex.FindAllString(pic, -1)
	}
}

func TestProduct_ImportProduct(t *testing.T) {
	type args struct {
		ctx context.Context
		url string
	}
	tests := []struct {
		name    string
		c       Product
		args    args
		want    *pc.ImportExcelProductResponse
		wantErr bool
	}{
		{
			name:    "",
			args:    args{ctx: context.Background(), url: "http://file.vetscloud.com/861dcfa9107a59eaf8393fa07a007a42.xlsx"},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.ImportProduct(tt.args.url, 3)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.ImportProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.ImportProduct() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_BatchOnTheShelfToAW(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.BatchOnTheShelfToAWRequest
	}
	var params = pc.BatchOnTheShelfToAWRequest{
		UserNo:      "U_IMS6YU3",
		ChannelId:   1,
		FinanceCode: "RP0231",
		IsAll:       0,
		ProductId:   "1000075",
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BatchOnTheShelfToAWResponse
		wantErr bool
	}{

		{name: "BatchOnTheShelfToAW", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.BatchOnTheShelfToAW(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchOnTheShelfToAW() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchOnTheShelfToAW() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetTaskList(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.GetTaskListRequest
	}
	var params = pc.GetTaskListRequest{
		Sort:        "createTimeAsc",
		TaskStatus:  1,
		TaskContent: 31, //代码限制，当前版本只做批量新建
		ChannelId:   -1,
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *pc.BatchOnTheShelfToAWResponse
	}{

		{name: "BatchOnTheShelfToAW", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.GetTaskList(tt.args.ctx, tt.args.in)
			t.Logf("%#v", got)
			if err != nil {
				t.Errorf("BatchOnTheShelfToAW() error = %v", err)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchOnTheShelfToAW() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_BatchOnTheShelf(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		finance_code string
		product_id   int32
		channel_id   int32
		UpDownState  int32
		session      *xorm.Session
		ctx          context.Context
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		wantBool    bool
		wantSnap_id int32
		wantErr     bool
	}{

		{name: "TestProduct_BatchOnTheShelf", args: args{
			finance_code: "xnCX0141",
			product_id:   1051018,
			channel_id:   1,
			UpDownState:  1,
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			gotBool, _, _, err := c.BatchOnTheShelf(tt.args.finance_code, tt.args.product_id, tt.args.channel_id, tt.args.UpDownState, tt.args.session, tt.args.ctx)
			t.Log(kit.JsonEncode(gotBool))
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchOnTheShelf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_BatchCreateToAW(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.BatchCreateToAWRequest
	}

	var params = pc.BatchCreateToAWRequest{
		FinanceCode: "CX0013",
		ChannelId:   1,
		UserNo:      "U_28XFC85",
		Category:    4,
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BatchBaseResponse
		wantErr bool
	}{

		{name: "BatchCreateToAW", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.BatchCreateToAW(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchCreateToAW() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchCreateToAW() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_insertExcelProductUpdate(t *testing.T) {
	type args struct {
		row        []string
		updateType int
	}

	rows := []string{"100017", "945651", "gODY007", "S0208XX1099", "商品名称", "", "1", "", "", "1", "", "", "", "", "", "", "", "", ""}

	tests := []struct {
		name string
		args args
		want string
	}{
		{name: "insertExcelProductUpdate", args: args{row: rows, updateType: 3}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := insertExcelProductUpdate(tt.args.row, tt.args.updateType); got != tt.want {
				t.Errorf("insertExcelProductUpdate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_QuerySkuThirdzlCK(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.NewSkuThirdzlRequest
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.SkuThirdResponse
		wantErr bool
	}{

		{name: "子龙获取符合要求的商品"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			app := pc.NewSkuThirdzlRequest{}

			app.ErpId = 4
			app.Sku_Third_List = append(app.Sku_Third_List, "S0207XX213")
			app.Sku_Third_List = append(app.Sku_Third_List, "S000HNB8NM")
			app.Sku_Third_List = append(app.Sku_Third_List, "S0301XX1104")

			got, err := c.QuerySkuThirdzlCK(tt.args.ctx, &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QuerySkuThirdzlCK() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QuerySkuThirdzlCK() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_NewESData(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.NewESDataRequest
	}

	var params pc.NewESDataRequest
	params.Id = 1108605
	params.SkuId = 1000003001
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{

		{name: "BatchCreateToAW", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.NewESData(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewESData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewESData() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_UPetNewESData(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.NewESDataRequest
	}
	var params pc.NewESDataRequest
	params.Id = 0
	params.SkuId = 100507
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{

		{name: "UPetNewESData", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.UPetNewESData(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("UPetNewESData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UPetNewESData() got = %v, want %v", got, tt.want)
			}
		})
	}
}

//func TestProduct_QueryChainGoodsId(t *testing.T) {
//	type args struct {
//		ctx context.Context
//		in  *wrappers.Int32Value
//	}
//	tests := []struct {
//		name    string
//		c       *Product
//		args    args
//		want    *_struct.Struct
//		wantErr bool
//	}{
//		{
//			name: "根据财务编码查电商同一城市的商品",
//			c:    &Product{},
//			args: args{
//				ctx: context.Background(),
//				in:  &wrappers.Int32Value{Value: 1},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := tt.c.QueryChainGoodsId(tt.args.ctx, tt.args.in)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Product.QueryChainGoodsId() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Product.QueryChainGoodsId() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestProduct_QueryAllChannelProduct(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.QueryAllChannelRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *_struct.Struct
		wantErr bool
	}{
		{
			name: "查询全渠道商品",
			c:    &Product{},
			args: args{
				ctx: context.Background(),
				in: &pc.QueryAllChannelRequest{
					ErpId:     2,
					PageIndex: 0,
					PageSize:  10,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryAllChannelProduct(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.QueryChainGoodsId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.QueryChainGoodsId() = %v, want %v", got, tt.want)
			}
		})
	}
}
func TestProduct_GetProductPriceByBJ(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.GetProductPriceByBJRequest
	}

	var params pc.GetProductPriceByBJRequest
	params.ProductCode = append(params.ProductCode, "C0024LXWUM")
	//params.ProductCode = append(params.ProductCode, "C010101019")
	params.StructCode = append(params.StructCode, "CX0004")
	//params.StructCode = append(params.StructCode, "CX0011")
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.GetProductPriceByBJResponse
		wantErr bool
	}{

		{name: "GetProductPriceByBJ", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.GetProductPriceByBJ(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetProductPriceByBJ() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetProductPriceByBJ() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetProductPriceByLocalBJ(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.GetProductPriceByBJRequest
	}
	var params pc.GetProductPriceByBJRequest
	params.ProductCode = append(params.ProductCode, "S10XXXX149")
	params.StructCode = append(params.StructCode, "CX0010")
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.GetProductPriceByBJResponse
		wantErr bool
	}{

		{name: "GetProductPriceByLocalBJ", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.GetProductPriceByLocalBJ(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetProductPriceByLocalBJ() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetProductPriceByLocalBJ() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_UpdatePriceToChannel(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.UpdatePriceToChannelRequest
	}

	var params pc.UpdatePriceToChannelRequest
	params.ProductId = 1020640
	params.SkuId = 1020640001
	params.FinanceCode = "CX0010"
	params.ChannelId = 1
	params.ProductCode = "C0102XX010"
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{

		{name: "UpdatePriceToChannel", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.UpdatePriceToChannel(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdatePriceToChannel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdatePriceToChannel() got = %v, want %v", got, tt.want)
			}
		})
	}
}

//func TestProduct_getChannelProductSnapshot(t *testing.T) {
//	type fields struct {
//		categoryNames []string
//	}
//	type args struct {
//		productID   []int32
//		channelID   int
//		financeCode string
//	}
//	tests := []struct {
//		name   string
//		fields fields
//		args   args
//		want   []pc.ChannelProductOneResponse
//	}{
//
//		{name: "UpdatePriceToChannel", args: args{
//			productID:   []int32{1019876, 1019928},
//			channelID:   1,
//			financeCode: "CX0010",
//		}},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Product{
//				categoryNames: tt.fields.categoryNames,
//			}
//			if got := c.getChannelProductSnapshot(tt.args.productID, tt.args.channelID, tt.args.financeCode); !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("getChannelProductSnapshot() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestProduct_DrugRecoverUp(t *testing.T) {
	tests := []struct {
		name    string
		c       *Product
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "药品恢复上架",
			c:    &Product{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.DrugRecoverUp()
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.DrugRecoverUp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.DrugRecoverUp() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_AllDrugDown(t *testing.T) {
	tests := []struct {
		name    string
		c       *Product
		want    *pc.BaseResponse
		wantErr bool
	}{
		{
			name: "药品全部下架",
			c:    &Product{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.AllDrugDown()
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.AllDrugDown() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.AllDrugDown() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_UpdateGroupProductSkuPrice(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *pc.GroupProductUpdatePriceRequest
	}
	tests := []struct {
		name    string
		c       *Product
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		// {
		// 	name: "更新平台组合商品价格",
		// 	c:    &Product{},
		// 	args: args{
		// 		ctx: context.Background(),
		// 		in: &pc.GroupProductUpdatePriceRequest{
		// 			UpdateDate:  "2020-03-08 15:59:11",
		// 			TablePrefix: "",
		// 		},
		// 	},
		// 	want: &pc.BaseResponse{
		// 		Code: 200,
		// 	},
		// 	wantErr: false,
		// },
		// {
		// 	name: "更新管家组合商品价格",
		// 	c:    &Product{},
		// 	args: args{
		// 		ctx: context.Background(),
		// 		in: &pc.GroupProductUpdatePriceRequest{
		// 			UpdateDate:  "2020-03-08 15:59:11",
		// 			TablePrefix: "gj_",
		// 		},
		// 	},
		// 	want: &pc.BaseResponse{
		// 		Code: 200,
		// 	},
		// 	wantErr: false,
		// },
		{
			name: "更新渠道组合商品价格",
			c:    &Product{},
			args: args{
				ctx: context.Background(),
				in: &pc.GroupProductUpdatePriceRequest{
					UpdateDate:  "2020-03-08 15:59:11",
					TablePrefix: "channel_",
				},
			},
			want: &pc.BaseResponse{
				Code: 200,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.UpdateGroupProductSkuPrice(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.UpdateGroupProductSkuPrice() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.UpdateGroupProductSkuPrice() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMap(t *testing.T) {
	mp := make(map[int]models.Sku)
	engine.Table("sku").Select("id,market_price").Limit(10).Find(&mp)
	for k, v := range mp {
		fmt.Println(k, v.MarketPrice)
	}
}

func TestProduct_GetStockInfoBySkuCodeApi(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.GetStockInfoApiRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.GetStockInfoBySkuCodeResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "查询库存"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			par := pc.GetStockInfoApiRequest{}
			par.FinanceCode = "RP0158"
			par1 := pc.ProductsInfoApi{}
			par1.SkuId = 1519532409
			par1.ProductType = 1

			par2 := pc.ProductsInfoApi{}
			par2.SkuId = 1531310507
			par2.ProductType = 3

			roup1 := pc.SkuGroup{}
			roup1.ProductType = 1
			roup1.GroupSkuId = 10109467
			roup1.Count = 1

			par2.Details = append(par2.Details, &roup1)

			roup2 := pc.SkuGroup{}
			roup2.ProductType = 1
			roup2.GroupSkuId = 1519533409
			roup2.Count = 1
			par2.Details = append(par2.Details, &roup2)

			roup3 := pc.SkuGroup{}
			roup3.ProductType = 2
			roup3.GroupSkuId = 10121244
			roup3.Count = 1
			par2.Details = append(par2.Details, &roup3)

			par.ProductsInfo = append(par.ProductsInfo, &par1)
			par.ProductsInfo = append(par.ProductsInfo, &par2)

			got, err := c.GetStockInfoBySkuCodeApi(context.Background(), &par)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.GetStockInfoBySkuCodeApi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.GetStockInfoBySkuCodeApi() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_SyncProductToEs(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.IdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "测试删除和添加ES"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.SyncProductToEs(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SyncProductToEs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SyncProductToEs() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_QueryChildProducts(t *testing.T) {
	type fields struct {
		categoryNames []string
	}
	type args struct {
		ctx context.Context
		in  *pc.QueryChildProductsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.ChildProductsResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				in: &pc.QueryChildProductsRequest{
					ParentSkuId:     "1031520099",
					ChannelId:       1,
					Erp:             2,
					FinanceCode:     "CX0004",
					ParentProductId: 1031520,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := Product{
				categoryNames: tt.fields.categoryNames,
			}
			got, err := c.QueryChildProducts(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
			/*if (err != nil) != tt.wantErr {
				t.Errorf("QueryChildProducts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("QueryChildProducts() got = %v, want %v", got, tt.want)
			}*/
		})
	}
}

func TestProduct_ExportWarehouseWhite(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		warehouseKey string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantUrl string
		wantNum int32
		wantErr bool
	}{
		{
			name: "TestProduct_ExportWarehouseWhite",
		}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotUrl, gotNum, err := c.ExportWarehouseWhite(tt.args.warehouseKey)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExportWarehouseWhite() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotUrl != tt.wantUrl {
				t.Errorf("ExportWarehouseWhite() gotUrl = %v, want %v", gotUrl, tt.wantUrl)
			}
			if gotNum != tt.wantNum {
				t.Errorf("ExportWarehouseWhite() gotNum = %v, want %v", gotNum, tt.wantNum)
			}
		})
	}
}

func TestProduct_GetDsProductSimpleInfo(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		financeCode string
		channelId   int
		warehouseId int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []models.PriceSync
		wantErr bool
	}{
		{
			name: "",
			args: args{
				financeCode: "CX0013",
				channelId:   1,
				warehouseId: 1294,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := c.GetDsProductSimpleInfo(tt.args.financeCode, tt.args.channelId, tt.args.warehouseId)
			t.Log(got, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDsProductSimpleInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_CheckAddCart(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.CheckAddCartReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.CheckAddCartRes
		wantErr bool
	}{
		{
			name: "",
			args: args{in: &pc.CheckAddCartReq{
				FinanceCode: "CX0013",
				ChannelId:   1,
				BarCode:     "145224",
				SkuId:       0,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.CheckAddCart(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAddCart() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_ChannelCategoryWithCount(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.ChannelCategoryWithCountReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ChannelCategoryWithCountRes
		wantErr bool
	}{
		{
			name: "",
			args: args{in: &pc.ChannelCategoryWithCountReq{
				ChannelId:   1,
				FinanceCode: "CX0010",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.ChannelCategoryWithCount(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChannelCategoryWithCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestGetQzProductSimpleInfo(t *testing.T) {
	type args struct {
		channelId   int
		warehouseId int
		financeCode string
	}
	tests := []struct {
		name     string
		args     args
		wantList []models.PriceSync
		wantErr  bool
	}{
		{
			name: "测试自动上架",
			args: args{
				channelId:   2,
				warehouseId: 233,
				financeCode: "CX0013",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotList, err := GetQzProductSimpleInfo(tt.args.channelId, tt.args.warehouseId, tt.args.financeCode)
			t.Log(kit.JsonEncode(gotList))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetQzProductSimpleInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestGetProductSimpleInfo(t *testing.T) {
	type args struct {
		channelId   int
		warehouseId int
		financeCode string
	}
	tests := []struct {
		name     string
		args     args
		wantList []models.PriceSync
		wantErr  bool
	}{
		{
			args: args{
				channelId:   4,
				warehouseId: 1007922,
				financeCode: "CX0013",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotList, err := GetProductSimpleInfo(tt.args.channelId, tt.args.warehouseId, tt.args.financeCode, tt.args.financeCode)
			t.Log(gotList, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetProductSimpleInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_ChannelProductExport(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		in *pc.ChannelProductListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantUrl string
		wantNum int32
		wantErr bool
	}{
		{
			name: "",
			args: args{in: &pc.ChannelProductListReq{
				PageIndex:   0,
				PageSize:    0,
				Where:       "",
				WhereType:   "",
				CategoryId:  0,
				BrandId:     0,
				ProductType: 0,
				ChannelId:   1,
				UpDownState: 1,
				FinanceCode: "CX0011",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotUrl, gotNum, err := c.ChannelProductExport(tt.args.in)

			t.Log(gotUrl, gotNum, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChannelProductExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_GetProductCategory(t *testing.T) {
	var p Product
	var ctx context.Context
	p.GetProductCategory(ctx, &pc.GetProductCategoryRequest{
		ProductIds: []string{""},
		SkuIds:     []string{"1531803409"},
	})
}

func TestProduct_DelProductYJ(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.DelProductParameter
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "删除商品数据"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			app := pc.DelProductParameter{}
			//app.ProductId=append(app.ProductId, 1047208)
			//app.ProductId=append(app.ProductId, 1047197)
			//app.ProductId=append(app.ProductId, 1047201)
			//app.ProductId=append(app.ProductId, 1047204)
			app.BeginTime = "2022-05-27 15:41:12"
			app.EndTime = "2022-05-27 15:41:24"
			got, err := c.DelProductYJ(context.Background(), &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("DelProductYJ() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DelProductYJ() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_R1SyncPrice(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.R1PriceSyncReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ProductBaseResponse
		wantErr bool
	}{
		{
			name: "",
			args: args{in: &pc.R1PriceSyncReq{
				Prices: []*pc.R1PriceSyncReq_Price{
					{
						SkuNo:                    "R1002",
						CentralizedPurchasePrice: 5,
						RetailPrice:              6.3,
						TradePrice:               6.0,
					}, {
						SkuNo:                    "R1003",
						CentralizedPurchasePrice: 5,
						RetailPrice:              6.3,
						TradePrice:               6.0,
					},
				},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.R1PriceSync(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("R1SyncPrice() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_R1PriceSyncSku(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.R1PriceSyncSkuReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ProductBaseResponse
		wantErr bool
	}{
		{
			name: "sku",
			args: args{in: &pc.R1PriceSyncSkuReq{
				Search: "1047198001,1023419099",
				Type:   1,
			}},
		},
		{
			name: "货号",
			args: args{in: &pc.R1PriceSyncSkuReq{
				Search: "2146081001,YGL-lksdj",
				Type:   2,
			}},
		},
		{
			name: "组合",
			args: args{in: &pc.R1PriceSyncSkuReq{
				Search: "1047205099,1023419099",
				Type:   3,
			}},
		},
		{
			name: "全量",
			args: args{in: &pc.R1PriceSyncSkuReq{
				Type: 9,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.R1PriceSyncSku(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("R1PriceSyncSku() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_DiagnoseDic(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.ProductDiagnoseDicReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ProductDiagnoseDicRes
		wantErr bool
	}{
		{
			name: "",
			args: args{in: &pc.ProductDiagnoseDicReq{
				PageIndex: 0,
				PageSize:  0,
				Keyword:   "厌氧",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.DiagnoseDic(tt.args.ctx, tt.args.in)
			t.Log(gotOut, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("DiagnoseDic() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_QueryProductOnly(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.OneofIdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.ProductResponse
		wantErr bool
	}{
		{
			args: args{in: &pc.OneofIdRequest{
				Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(1047284)}}},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := c.QueryProductOnly(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryProductOnly() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("QueryProductOnly() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_QueryDisease(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.ProductQueryDiseaseReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ProductQueryDiseaseRes
		wantErr bool
	}{
		{
			args: args{in: &pc.ProductQueryDiseaseReq{
				Skus: []*pc.ProductQueryDiseaseReq_SkuNum{
					{
						SkuId: 1047284001,
						Num:   1,
					}, {
						SkuId: 1047281001,
						Num:   1,
					}, {
						SkuId: 1047205099,
						Num:   2,
					}, {
						SkuId: 1047203099,
						Num:   2,
					}, {
						SkuId: 1047198001,
						Num:   2,
					},
				},
				FinanceCode: "CX0004",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.QueryDisease(tt.args.ctx, tt.args.in)
			t.Log(gotOut, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryDisease() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_DrugInfo(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.ProductDrugInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ProductDrugInfoRes
		wantErr bool
	}{
		{
			args: args{in: &pc.ProductDrugInfoReq{
				Ids: []int32{1047198001, 1047284},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.DrugInfo(tt.args.ctx, tt.args.in)
			t.Log(gotOut, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("DrugInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_PetType(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *empty.Empty
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ProductPetTypeRes
		wantErr bool
	}{
		{
			args: args{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.PetType(tt.args.ctx, tt.args.in)
			t.Log(gotOut, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("PetType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_ZiLongDrugSync(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.ZiLongDrugSyncReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pc.ProductBaseResponse
		wantErr bool
	}{
		{
			args: args{in: &pc.ZiLongDrugSyncReq{
				Data: []*pc.ZiLongDrugSyncReq_Data{
					{
						ProductCode:      "C010101009",
						IsPrescribedDrug: 0,
						CanSell:          0,
					}, {
						ProductCode:      "C0408XX057",
						IsPrescribedDrug: 0,
						CanSell:          0,
					},
				},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			gotOut, err := c.ZiLongDrugSync(tt.args.ctx, tt.args.in)
			t.Log(gotOut, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("ZiLongDrugSync() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProduct_ExportAllProduct(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		ctx context.Context
		in  *pc.QueryProductRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pc.ExportProductResponse
		wantErr bool
	}{
		{
			args: args{in: &pc.QueryProductRequest{
				PageIndex:        0,
				PageSize:         0,
				Where:            "",
				WhereType:        "",
				CategoryId:       0,
				BrandId:          0,
				ProductType:      1,
				IsGroup:          0,
				IsDel:            0,
				ChannelId:        0,
				UpDownState:      0,
				IsRecommend:      0,
				FinanceCode:      "",
				IsGj:             0,
				IsDrugs:          0,
				UseRange:         0,
				IsExclude:        0,
				SelectType:       0,
				SourceType:       0,
				StoreCategoryId:  0,
				IsPrescribedDrug: 1,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			got, err := c.ExportAllProduct(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExportAllProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ExportAllProduct() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetUpProductsWarehouseIds1(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		skuId int32
		eType int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name: "TestProduct_GetUpProductsWarehouseIds1",
			args: args{
				//skuId: 1047202001,//248
				skuId: 1032747001, //sit
				eType: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			c.GetUpProductsWarehouseIds(tt.args.skuId, tt.args.eType)
		})
	}
}

func TestProduct_GetProductSimpleInfo(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		finance_code  string
		channel_id    int
		warehouseId   int
		warehouseCode string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []models.PriceSync
	}{
		{
			args: args{
				finance_code:  "CX0013",
				channel_id:    3,
				warehouseId:   233,
				warehouseCode: "CX0004",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			if got := c.GetProductSimpleInfo(tt.args.finance_code, tt.args.channel_id, tt.args.warehouseId, tt.args.warehouseCode); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetProductSimpleInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetProductSimpleInfo1(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		finance_code  string
		channel_id    int
		warehouseId   int
		warehouseCode string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []models.PriceSync
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				channel_id:    1,
				finance_code:  "CX0013",
				warehouseId:   28,
				warehouseCode: "CX0004",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			if got := c.GetProductSimpleInfo(tt.args.finance_code, tt.args.channel_id, tt.args.warehouseId, tt.args.warehouseCode); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetProductSimpleInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetProductQzCodeInfo(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		finance_code string
		channel_id   int
		warehouseId  int
		a8list       []models.QzcPriceSync
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantList []models.PriceSync
	}{

		// TODO: Add test cases.
		{name: "前置仓自动上架",
			args: args{channel_id: 1, finance_code: "CX0011", warehouseId: 28},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			var idForA8 = []models.QzcPriceSync{}
			if gotList := c.GetProductQzCodeInfo(tt.args.finance_code, tt.args.channel_id, tt.args.warehouseId, idForA8); !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("GetProductQzCodeInfo() = %v, want %v", gotList, tt.wantList)
			}
		})
	}
}

func TestProduct_GetProductSimpleInfo2(t *testing.T) {
	type fields struct {
		categoryNames []string
		oldSnap       string
	}
	type args struct {
		finance_code  string
		channel_id    int
		warehouseId   int
		warehouseCode string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []models.PriceSync
	}{
		// TODO: Add test cases.
		{
			name: "测试",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Product{
				categoryNames: tt.fields.categoryNames,
				oldSnap:       tt.fields.oldSnap,
			}
			if got := c.GetProductSimpleInfo(tt.args.finance_code, tt.args.channel_id, tt.args.warehouseId, tt.args.warehouseCode); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetProductSimpleInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}
