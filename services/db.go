package services

import (
	"errors"
	"os"
	"runtime"
	"time"

	kit "github.com/tricobbler/rp-kit"

	"github.com/go-redis/redis"
	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
)

var (
	engine          *xorm.Engine
	redisHandle     *redis.Client
	redisKeepHandle *redis.Client
	upetengine      *xorm.Engine
	dcEngine        *xorm.Engine
	orderEngine     *xorm.Engine
	// 互联网医疗
	medicalEngine *kit.DBEngine

	// 子龙数据库 hospital_db
	zlEngine *kit.DBEngine
)

func SetupDB() {
	redisHandle = GetRedisConn()
	redisKeepHandle = KeepAliveRedisConn()
	engine = NewDbConn()
	upetengine = UpetNewDbConn()
	dcEngine = NewDatacenterDbConn()
	orderEngine = NewOrderCenterDbConn()

	medicalEngine = kit.NewDBEngine(config.GetString("mysql.pet-medical"))
	// 数据库定时探活
	go medicalEngine.DBEngineCheck(medicalEngine.NewXormEngineInterface, 3, 10)

	zlEngine = kit.NewDBEngine(config.GetString("mysql.zl_hospital_db"))
	// 数据库定时探活
	go zlEngine.DBEngineCheck(zlEngine.NewXormEngineInterface, 3, 10)
}

func CloseDB() {
	engine.Close()
	upetengine.Close()
	dcEngine.Close()
	redisHandle.Close()
	redisKeepHandle.Close()
}

//获取redis集群客户端
func GetRedisConn() *redis.Client {

	var db = cast.ToInt(config.GetString("redis.DB"))
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")
	//根据环境变量启动定时任务
	if !kit.EnvCanCron() {
		if redisHandle != nil {
			//_, err := redisHandle.Ping().Result()
			//换一种探活的模式
			_, err := redisHandle.Del("").Result()
			//glog.Info("redis connections: ", redisHandle.PoolStats().TotalConns)
			if err == nil {
				return redisHandle
			} else {
				redisHandle = redis.NewClient(&redis.Options{
					Addr:         addr,
					Password:     pwd,
					DB:           db,
					MinIdleConns: 28,
					IdleTimeout:  30,
					PoolSize:     512,
					MaxConnAge:   30 * time.Second,
				})
				return redisHandle
			}

		} else {
			redisHandle = redis.NewClient(&redis.Options{
				Addr:         addr,
				Password:     pwd,
				DB:           db,
				MinIdleConns: 28,
				IdleTimeout:  30,
				PoolSize:     512,
				MaxConnAge:   30 * time.Second,
			})
			return redisHandle
		}
	}

	//glog.Info("redis connections:" + addr + ",paw:" + pwd)

	redisHandle1 := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: pwd,
		DB:       db,
	})
	return redisHandle1
}

// 获取redis长连接
func KeepAliveRedisConn() *redis.Client {
	var db = cast.ToInt(config.GetString("redis.DB"))
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")
	//根据环境变量启动定时任务
	if redisKeepHandle != nil {
		//_, err := redisHandle.Ping().Result()
		//换一种探活的模式
		_, err := redisKeepHandle.Del("").Result()
		if err == nil {
			return redisKeepHandle
		} else {
			redisKeepHandle = redis.NewClient(&redis.Options{
				Addr:         addr,
				Password:     pwd,
				DB:           db,
				MinIdleConns: 10,                   // 最小空闲连接数
				IdleTimeout:  10,                   // 空闲连接的超时时间
				PoolSize:     4 * runtime.NumCPU(), // 连接池大小，连接池中的连接的最大数量
				MaxConnAge:   0,                    // 从连接池获取连接的超时时间
			})
			return redisKeepHandle
		}
	} else {
		redisKeepHandle = redis.NewClient(&redis.Options{
			Addr:         addr,
			Password:     pwd,
			DB:           db,
			MinIdleConns: 10,
			IdleTimeout:  10,
			PoolSize:     4 * runtime.NumCPU(),
			MaxConnAge:   0,
		})
		return redisKeepHandle
	}

	redisHandle1 := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: pwd,
		DB:       db,
	})
	return redisHandle1
}

// NewDbConn 连接池，请勿关闭
func NewDbConn(dataSourceName ...string) *xorm.Engine {
	var err error

	if engine != nil {
		if err = engine.DB().Ping(); err == nil {
			return engine
		}
		return engine
	}

	var mySqlStr string
	if len(dataSourceName) == 1 {
		mySqlStr = dataSourceName[0]
	} else {
		mySqlStr = config.GetString("mysql.dc_product")
	}

	//mySqlStr = "s2b2c:9iIJth3tJzhmSk5w@(124.221.96.140:23306)/dc_product?charset=utf8mb4" //腾讯云测试
	//mySqlStr ="root:XjIrQepuHn7u^E8D@(39.106.30.60:13306)/dc_product?charset=utf8mb4"
	//mySqlStr = "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/dc_product?charset=utf8mb4"
	//mySqlStr = "readonly:fdSDF3er(34@(10.1.1.242:5532)/dc_product?charset=utf8"
	//mySqlStr = "root:Rp000000@(10.1.1.245:3306)/dc_product?charset=utf8"
	//mySqlStr = "root:Rp000000@(192.168.254.11:3306)/dc_product?charset=utf8"
	// mySqlStr = "dbuser:ZQA!2sxQQ@(10.1.1.242:5532)/dc_product?charset=utf8mb4"
	//mySqlStr = "data:Rp000000@(10.12.12.78:3306)/dc_product?charset=utf8mb4" // 压测

	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err = xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(GetDBError(err))
	}
	engine.ShowSQL(true)
	if kit.IsDebug {
		// engine.ShowSQL()
		engine.ShowExecTime()
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(120 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(1000)

	engine.SetTZLocation(time.Local)

	return engine
}

func NewDatacenterDbConn() *xorm.Engine {
	var err error

	if dcEngine != nil {
		if err := dcEngine.DB().Ping(); err == nil {
			return dcEngine
		}
	}

	mySqlStr := config.GetString("mysql.datacenter")
	//mySqlStr = "readonly:fdSDF3er(34@(10.1.1.242:5532)/datacenter?charset=utf8"
	//mySqlStr = "root:Rp000000@(10.1.1.245:3306)/datacenter?charset=utf8"
	//mySqlStr = "s2b2c:9iIJth3tJzhmSk5w@(172.30.2.14:23306)/datacenter?charset=utf8mb4" //sit1
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	dcEngine, err = xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(GetDBError(err))
	}

	if kit.IsDebug {
		// dcEngine.ShowSQL()
		dcEngine.ShowExecTime()
	}

	//空闲关闭时间
	dcEngine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	dcEngine.SetMaxIdleConns(5)
	//最大连接数
	dcEngine.SetMaxOpenConns(500)

	dcEngine.SetTZLocation(time.Local)

	return dcEngine
}

func UpetNewDbConn() *xorm.Engine {
	if upetengine != nil {
		if err := upetengine.DB().Ping(); err == nil {
			return upetengine
		}
	}

	upetengine = upetnewDbConn()
	return upetengine
}

func upetnewDbConn() *xorm.Engine {
	if upetengine != nil {
		if err := upetengine.DB().Ping(); err == nil {
			return upetengine
		}
	}

	mySqlStr := config.GetString("mysql.upetmart")
	//mySqlStr = "readonly:fdSDF3er(34@(10.1.1.242:5532)/dc_product?charset=utf8"
	//mySqlStr = "root:Rp000000@(10.1.1.245:3306)/dc_product?charset=utf8"
	// mySqlStr = "root:rp@dbadmin@(192.168.254.37:3306)/testupetmart?charset=utf8"
	//mySqlStr = "s2b2c:9iIJth3tJzhmSk5w@(172.30.2.14:23306)/testupetmart?charset=utf8mb4" //sit1
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	upetengine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(GetDBError(err))
	}
	upetengine.ShowSQL()
	if kit.IsDebug {
		// upetengine.ShowSQL()
		upetengine.ShowExecTime()
	}

	//空闲关闭时间
	upetengine.SetConnMaxLifetime(120 * time.Second)
	//最大空闲连接
	upetengine.SetMaxIdleConns(10)
	//最大连接数
	upetengine.SetMaxOpenConns(3000)

	location, _ := time.LoadLocation("Asia/Shanghai")
	upetengine.SetTZLocation(location)

	// upetengine.ShowSQL()
	return upetengine
}

func GetDBError(err error) error {
	return errors.New("数据库操作失败：" + err.Error())
}

func NewOrderCenterDbConn() *xorm.Engine {
	var err error

	if orderEngine != nil {
		if err := orderEngine.DB().Ping(); err == nil {
			return orderEngine
		}
	}

	mySqlStr := config.GetString("mysql.dc_order")
	//mySqlStr = "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/dc_product?charset=utf8mb4"

	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	orderEngine, err = xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(GetDBError(err))
	}

	if kit.IsDebug {
		// dcEngine.ShowSQL()
		orderEngine.ShowExecTime()
	}

	//空闲关闭时间
	orderEngine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	orderEngine.SetMaxIdleConns(5)
	//最大连接数
	orderEngine.SetMaxOpenConns(500)

	orderEngine.SetTZLocation(time.Local)

	return orderEngine
}

func GetMedicalDBConn() *xorm.Engine {
	if medicalEngine == nil || medicalEngine.Engine == nil {
		medicalEngine = kit.NewDBEngine(config.GetString("mysql.pet-medical"))
	}

	xe := medicalEngine.Engine.(*xorm.Engine)
	// 开发环境显示sql日志
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		xe.ShowSQL(true)
	}
	return xe
}

func GetZlDBConn() *xorm.Engine {
	if zlEngine == nil || zlEngine.Engine == nil {
		zlEngine = kit.NewDBEngine(config.GetString("mysql.zl_hospital_db"))
	}

	xe := zlEngine.Engine.(*xorm.Engine)
	// 开发环境显示sql日志
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		xe.ShowSQL(true)
	}
	return xe
}
