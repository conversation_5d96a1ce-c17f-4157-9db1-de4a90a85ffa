package services

import (
	"_/models"
	"_/proto/dac"
	"_/proto/pc"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-errors/errors"
	"github.com/maybgit/glog"
	"github.com/ppkg/distributed-worker/dto"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"strings"
	"time"
)

type SyncCategoryContext struct {
	// 任务ID
	TaskId int32 `json:"task_id"`
	// task_input
	InputJson string
	// 渠道，1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;
	ChannelId int32 `json:"channel_id"`
	Size      int   `json:"size"`
	// 有多少个任务被调度
	ScheduleSize int `json:"schedule_size"`
	// 调度器返回的ID
	ScheduleId int `json:"schedule_id"`
	// 处理失败门店
	FailList []*ReqDto `json:"fail_list"`
	// 处理成功门店
	SuccessList []*ReqDto `json:"success_list"`
	// 系统错误
	SystemError string `json:"system_error"`
	// 用户登录信息
	UserInfo *models.LoginUserInfo `json:"user_info"`

	// taskDetail 详情
	TaskDetail string
}

type ReqDto struct {
	// 同步类型-必须  1 新增 2 修改 3 删除
	SyncType int
	// 渠道-必须
	ChannelId int
	// 分类Id-必须
	CategoryId int
	// 分类名称-必须
	CategoryName string
	// 上级分类id-必须
	ParentId int
	// 排序-必须
	Sort int
	// 财务代码
	FinanceCode string
	// shopName 门店名称
	ShopName string
	// 是否同步成功
	IsSuccess bool
	// 执行结果
	Message string
	// 第三方门店的id
	ChannelStoreId string
	// appChannel
	AppChannel int32
}

// 门店切仓任务
func DealAsyncTaskCategory(taskContent int32) func() bool {
	return func() bool {
		//查询新任务的ID
		params := &pc.GetTaskListRequest{
			Sort:        "createTimeAsc",
			TaskStatus:  1,
			ChannelId:   -1,          //-1 查询全部的
			TaskContent: taskContent, // 查询task_content in (22)
			Status:      1,
			Page:        1,
			PageSize:    1,
		}

		//获取需要执行的任务，只取一条数据
		pd := new(Product)
		result, err := pd.GetTaskList(context.Background(), params)
		if err != nil {
			glog.Errorf("DealAsyncTaskSwitchWarehouse 定时查询新任务异常,%+v", err)
			return false
		}
		//无任务的时候，直接返回
		if len(result.TaskList) == 0 {
			return false
		}

		for _, task := range result.TaskList {
			myCtx := SyncCategoryContext{
				TaskId:    task.Id,
				ChannelId: task.ChannelId,
				InputJson: task.OperationFileUrl,
			}
			// 提交任务
			err := submitCategoryScheduleJob(&myCtx)
			glog.Info("submitCategoryScheduleJob: ", kit.JsonEncode(myCtx))
			if err != nil {
				glog.Errorf("DealAsyncTaskSwitchWarehouse 门店异步切仓处理异常 %v", err)
				myCtx.SystemError = err.Error()
			}

			updateModel := models.TaskList{
				TaskStatus:     3,
				TaskDetail:     myCtx.TaskDetail,
				ResulteFileUrl: "",
				ModifyTime:     time.Now(),
			}
			// 如果有调度任务则设置为进行中
			if myCtx.ScheduleSize > 0 {
				updateModel.TaskStatus = 2
				updateModel.ContextData = kit.JsonEncode(myCtx)
			}

			// 当任务完成时导出excel文档 没有任何任务直接完成退出或者有系统异常直接退出
			if updateModel.TaskStatus == 3 && len(myCtx.SystemError) > 0 {
				sync := new(CategorySync)
				for i := range myCtx.FailList {
					reqDto := myCtx.FailList[i]
					params_result := SyncCategoryToThirdParams_Result{
						categoryId:     reqDto.CategoryId,
						categoryName:   reqDto.CategoryName,
						financeCode:    reqDto.FinanceCode,
						ShopName:       reqDto.ShopName,
						IsSuccess:      reqDto.IsSuccess,
						Message:        myCtx.SystemError,
						ChannelId:      reqDto.ChannelId,
						SyncType:       reqDto.SyncType,
						ChannelStoreId: reqDto.ChannelStoreId,
						AppChannel:     reqDto.AppChannel,
					}

					sync.Result = append(sync.Result, &params_result)
				}

				fileUrl, desc := sync.ExportToExcel()
				glog.Info("taskdetail : ", desc)

				updateModel.ResulteFileUrl = fileUrl
			}

			_, err = engine.Where("id=?", task.Id).Update(updateModel)
			if err != nil {
				glog.Errorf("DealAsyncTaskSwitchWarehouse 更新任务状态异常,%+v", err)
			}
		}
		return true
	}
}

// 提交调度job
func submitCategoryScheduleJob(ctx *SyncCategoryContext) error {
	glog.Infof("submitCategoryScheduleJob 调度器信息 %s,连接信息:%+v", kit.JsonEncode(workerCtx.GetNodeId()), workerCtx.GetLeaderConn())
	name := fmt.Sprintf("同步分类 %s ", time.Now().Format("200601021504"))
	req := dto.AsyncJobRequest{
		Name:  name,
		Label: "同步分类",
		Meta: map[string]string{
			"taskId": cast.ToString(ctx.TaskId),
		},
		Type:     "syncCategory",
		IsNotify: true,
		PluginSet: []string{
			"SyncCategoryToThird",
		},

		// 门店分片
		TaskInputList:          []string{},
		TaskExceptionOperation: 1,
	}

	//获取门店数据 阿闻渠道默认是成功的
	var details = []*models.TaskDetail{&models.TaskDetail{
		ChannelId: ChannelAwenId,
		Error:     "操作成功1个, 操作失败0个",
		AllNum:    1,
		FailNum:   0,
	}}

	var data []CategorySync
	err := json.Unmarshal([]byte(ctx.InputJson), &data)
	if err != nil {
		glog.Info("json解析错误", err.Error())
		msg := "失败-序列化参数失败" + err.Error()
		for _, v := range IntsChannel {
			details = append(details, &models.TaskDetail{
				ChannelId: v,
				Error:     msg,
			})
		}
		return err
	}

	syncs := make(map[int]CategorySync, 0)
	for i := range data {
		sync := data[i]
		syncs[sync.ChannelId] = sync
	}

	var taskList []string

	for _, channel_id := range IntsChannel {
		if syncCategory, ok := syncs[channel_id]; ok {

			glog.Info("syncCategory ： ", kit.JsonEncode(syncCategory))
			detail := models.TaskDetail{
				ChannelId: channel_id,
			}
			var StoreMasterResult []*dac.StoreInfo
			if channel_id == ChannelElmId || channel_id == ChannelMtId {
				StoreMasterResult, err = GetThirdStoreMaster(int32(channel_id), "")
			} else if channel_id == ChannelJddjId {
				client := dac.GetDataCenterClient()
				req := dac.GetStoreMasterListRequest{
					InfoLevel: 99,
				}
				resp, err := client.RPC.GetStoreMasterList(context.Background(), &req)
				if err != nil {
					glog.Error("GetThirdStoreMaster err: ", err.Error())
					detail.Error = "获取绑定的渠道门店数量异常" + err.Error()
					details = append(details, &detail)
					continue
				}
				for i := range resp.Data {
					info := resp.Data[i]
					if info.JddjAppId == "" || info.JddjAppSecret == "" || info.JddjAppMerchantId == "" {
						continue
					}
					StoreMasterResult = append(StoreMasterResult, &dac.StoreInfo{AppChannel: info.Id})
				}

			}
			glog.Info("获取绑定的渠道门店数量：", len(StoreMasterResult), " 渠道名称：", channel_id, " err: ", err)
			detail.AllNum = len(StoreMasterResult)
			// 任务详情
			if err != nil {
				glog.Error("GetThirdStoreMaster err: ", err.Error())
				detail.Error = "获取绑定的渠道门店数量异常" + err.Error()
				details = append(details, &detail)
				continue
			}

			syncAppChannelMap := make(map[int32]struct{}, 0)
			for i := range StoreMasterResult {
				info := StoreMasterResult[i]

				if channel_id == ChannelJddjId { // jddj按照appid来同步
					info := StoreMasterResult[i]
					syncAppChannelMap[info.AppChannel] = struct{}{}
				} else {
					var result ReqDto
					result.ChannelId = channel_id
					result.CategoryId = syncCategory.CategoryId
					result.CategoryName = syncCategory.CategoryName
					result.FinanceCode = info.FinanceCode
					result.ShopName = info.Name
					result.SyncType = syncCategory.SyncType

					result.ChannelStoreId = info.ChannelStoreId
					result.AppChannel = info.AppChannel
					result.Sort = syncCategory.Sort
					result.ParentId = syncCategory.ParentId

					bytes, _ := json.Marshal(result)
					taskList = append(taskList, string(bytes))

					ctx.FailList = append(ctx.FailList, &result)
				}
			}

			if channel_id == ChannelJddjId {
				detail.AllNum = len(syncAppChannelMap)
			}
			glog.Info("syncAppChannelMap", kit.JsonEncode(syncAppChannelMap))
			for k, v := range syncAppChannelMap {
				glog.Info("jddj: appchannel", k, " v: ", v)
				var result ReqDto
				result.ChannelId = channel_id
				result.CategoryId = syncCategory.CategoryId
				result.CategoryName = syncCategory.CategoryName
				//result.financeCode = info.FinanceCode
				//result.ShopName = info.Name
				result.SyncType = syncCategory.SyncType
				//result.ChannelStoreId = info.ChannelStoreId
				result.Sort = syncCategory.Sort
				result.ParentId = syncCategory.ParentId
				result.AppChannel = k
				bytes, _ := json.Marshal(result)
				taskList = append(taskList, string(bytes))
				ctx.FailList = append(ctx.FailList, &result)
			}
			details = append(details, &detail)
		}
	}

	ctx.TaskDetail = kit.JsonEncode(details)
	// 分片数为0直接退出
	req.TaskInputList = taskList
	ctx.ScheduleSize = len(req.TaskInputList)
	// 如果没有符合条件数据则直接return
	if ctx.ScheduleSize == 0 {
		return nil
	}
	jobId, err := workerCtx.AsyncSubmitJob(req)
	if err != nil {
		glog.Errorf("DealAsyncTaskSwitchWarehouse 请求分布式调度异常,taskId:%d,请求参数:%s,%+v", ctx.TaskId, kit.JsonEncode(req), err)
		return fmt.Errorf("请求分布式调度异常,%+v", err)
	}

	ctx.ScheduleId = int(jobId)

	glog.Info("提交任务的TaskId：", ctx.TaskId, " jobId ", jobId)
	return nil
}

// 重试直接插入一条新的任务
func (c *Product) RollBackTask(ctx context.Context, in *pc.DeleteTaskRequest) (*pc.BaseResponse, error) {

	glog.Info("重试任务id：", kit.JsonEncode(in))

	response := pc.BaseResponse{}
	response.Code = 400

	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		return &response, errors.New("用户解析失败")
	}
	glog.Info("userInfo:", kit.JsonEncode(userInfo))
	if in.TaskId > 0 {

		if in.TaskContent == int32(MoveCategoryProduct) {
			return DealMoveProduct(in, userInfo)
		} else {
			return DealCategory(in, userInfo)
		}

	} else {
		response.Code = 400
		return &response, errors.New("无效的任务id")
	}
}

/**
移动商品分类
*/
func DealMoveProduct(in *pc.DeleteTaskRequest, userInfo *models.LoginUserInfo) (*pc.BaseResponse, error) {
	response := pc.BaseResponse{}
	response.Code = 400
	list := models.TaskList{}
	engine.SQL("select * from dc_product.task_list tl where task_content =? and id = ?;",
		MoveCategoryProduct, in.TaskId).Get(&list)

	if list.Id <= 0 {
		return &response, errors.New("未查询到任务id")
	}
	if list.TaskStatus == 1 || list.TaskStatus == 2 {
		return &response, errors.New("任务已经在执行中或者正在等待执行,请稍后再试")
	}
	var taskId int
	engine.Table(&models.TaskList{}).Select("id").Where("task_content = ? ", MoveCategoryProduct).In("task_status", []int{1, 2}).Get(&taskId)
	if taskId > 0 {
		return &response, errors.New("已经有移动商品的异步任务在进行中，请稍后再试" + cast.ToString(taskId))
	}

	list.TaskDetail = ""
	list.Id = 0
	list.ResulteFileUrl = ""
	list.TaskStatus = 1 // 清空数据重跑
	list.CreateTime = time.Now()
	list.CreateName = userInfo.UserName
	list.CreateId = userInfo.UserNo

	_, err := engine.Insert(list)
	if err != nil {
		glog.Error("插入新任务异常:", err.Error())
		return &response, errors.New("插入新任务异常,请稍后再试")
	}
	response.Code = 200
	return &response, nil
}

func DealCategory(in *pc.DeleteTaskRequest, userInfo *models.LoginUserInfo) (*pc.BaseResponse, error) {

	response := pc.BaseResponse{}
	response.Code = 400
	list := models.TaskList{}
	engine.SQL("select * from dc_product.task_list tl where task_content =? and id = ?;",
		SyncCategoryTaskContent, in.TaskId).Get(&list)

	if list.Id <= 0 {
		return &response, errors.New("未查询到任务id")
	}
	if list.TaskStatus == 1 || list.TaskStatus == 2 {
		return &response, errors.New("任务已经在执行中或者正在等待执行,请稍后再试")
	}

	// 判断是编辑和新增是否删除，删除的不让重试了，因为落不了阿闻了
	if cast.ToInt(list.RequestHeader) == 1 || cast.ToInt(list.RequestHeader) == 2 {
		var hasNum int
		engine.SQL("select count(1) from dc_product.channel_category cc where id = ? ", list.Category).Get(&hasNum)
		if hasNum <= 0 {
			return &response, errors.New("分类在阿闻已经被删除，无法重试，请新建分类")
		}
	}

	// 判断一下是否有任务在执行
	var num int32
	// 如果是二级分类父类在执行也不能删除
	var parent_id string
	engine.SQL("select parent_id from dc_product.channel_category cc where id = ?;", list.Category).Get(&parent_id)
	var category_id = []string{cast.ToString(list.Category)}
	if len(parent_id) > 0 {
		category_id = append(category_id, parent_id)
	}
	sql := "select count(1) from dc_product.task_list tl where task_content =? and task_status in (1,2) and  category in ('" + strings.Join(category_id, "','") + "')"

	engine.SQL(sql, SyncCategoryTaskContent).Get(&num)
	if num > 0 {
		glog.Error("插入新任务失败，有正在执行的二级分类或者父分类")
		return &response, errors.New("有正在执行的二级分类或者父分类,请稍后再试")
	}

	list.TaskDetail = ""
	list.Id = 0
	list.ResulteFileUrl = ""
	list.TaskStatus = 1 // 清空数据重跑
	list.CreateTime = time.Now()
	list.CreateName = userInfo.UserName
	list.CreateId = userInfo.UserNo

	_, err := engine.Insert(list)
	if err != nil {
		glog.Error("插入新任务异常:", err.Error())
		return &response, errors.New("插入新任务异常,请稍后再试")
	}
	response.Code = 200
	return &response, nil
}
