package services

import (
	"_/models"
	"_/proto/et"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/maybgit/glog"
	"github.com/ppkg/distributed-worker/dto"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"net/http"
	"strings"
	"sync"
	"time"
)

type MtCateGorySync struct {
}

// 取消异步任务
func (c *Product) CancelSyncChannelCategoryTask(ctx context.Context, req *pc.CancelTaskRequest) (*pc.CancelTaskResponse, error) {
	if req.Id == 0 {
		return nil, errors.New("参数ID不能为空")
	}
	resp := &pc.CancelTaskResponse{
		Code: http.StatusBadRequest,
	}
	taskResp, err := c.GetTaskList(ctx, &pc.GetTaskListRequest{
		Id:          int32(req.Id),
		TaskContent: 31,
		ChannelId:   -1,
	})
	if err != nil {
		glog.Errorf("Product/CancelTask 查询任务异常,请求参数:%s,%+v", kit.JsonEncode(req), err)
		return nil, err
	}

	if len(taskResp.TaskList) == 0 {
		resp.Message = "未获取任务信息"
		return resp, nil
	}
	// 任务处于未开始或进行中则可以进行取消，否则不可以
	// 1:未开始;2:进行中;
	task := taskResp.TaskList[0]
	if task.TaskStatus == 3 {
		resp.Message = "当前任务已完成，无法取消"
		return resp, nil
	}
	if task.TaskStatus == 4 {
		resp.Message = "当前任务已取消，不用重复提交"
		return resp, nil
	}
	if task.ContextData != "" {
		var contextData SyncChannelCategoryContext
		err = json.Unmarshal([]byte(task.ContextData), &contextData)
		if err != nil {
			glog.Errorf("Product/CancelTask 反序列化异常,参数:%s,%+v", task.ContextData, err)
			resp.Message = "反序列化上下文异常"
			resp.Error = err.Error()
			return resp, nil
		}
		if contextData.ScheduleId != 0 {
			err = workerCtx.ManualCancelJob(dto.ManualCancelRequest{
				Id:     int64(contextData.ScheduleId),
				Reason: "用户手动取消任务",
			})
			if err != nil {
				glog.Errorf("Product/CancelTask 取消任务失败,参数:%d,%+v", contextData.ScheduleId, err)
				resp.Message = "取消任务失败"
				resp.Error = err.Error()
				return resp, nil
			}
		}
	}

	updateModel := models.TaskList{
		TaskStatus: 4,
		TaskDetail: "该任务已取消",
		ModifyTime: time.Now(),
	}
	_, err = engine.Where("id=?", task.Id).Update(updateModel)
	if err != nil {
		resp.Message = "更新任务失败"
		resp.Error = err.Error()
		return resp, nil
	}
	resp.Code = http.StatusOK
	resp.Message = "取消成功"
	return resp, nil
}

//导入任务
func (this *Product) ImportSyncCategoryShop(ctx context.Context, in *pc.ImportSyncCategoryShopRequest) (*pc.BaseResponse, error) {
	glog.Info("ImportSyncCategoryShop导入需要同步分类的门店参数: ", in)
	out := &pc.BaseResponse{Code: 400}
	//用户校验
	userInfo := loadLoginUserInfo(ctx)
	if userInfo == nil {
		out.Message = "用户不存在"
		out.Error = "用户不存在"
		return out, nil
	}

	// 下载excel
	req, err := http.NewRequest("POST", in.QiniuUrl, nil)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}

	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows := f.GetRows(sheetName)

	// excel为空
	if len(rows) <= 1 {
		out.Message = "未读取到表格数据，请导入需要同步分类的门店信息"
		return out, nil
	}
	type ExtendData struct {
		TodoList     []string `json:"todo_list"`
		ConflictList []string `json:"conflict_list"`
	}

	db := NewDbConn()
	var taskListNotComplete []models.TaskList
	err = db.Table("task_list").Select("id,extended_data").Where("task_content=31 AND task_status IN(1,2)").Find(&taskListNotComplete)
	if err != nil {
		out.Message = "查询未完成任务失败"
		return out, nil
	}

	existShopTaskMap := make(map[string]int32)
	taskListNotCompleteNum := len(taskListNotComplete)
	if taskListNotCompleteNum > 0 {
		for _, v := range taskListNotComplete {
			if v.ExtendedData == "" {
				continue
			}
			itemExtendData := new(ExtendData)
			_ = json.Unmarshal([]byte(v.ExtendedData), itemExtendData)
			for _, financeCode := range itemExtendData.TodoList {
				existShopTaskMap[financeCode] = v.Id
			}
		}
	}

	var shopNum int32
	shopMap := make(map[string]struct{})
	var financeCodes []string
	var conflictFinanceCodes []string
	for i, v := range rows {
		if i == 0 {
			continue
		}
		financeCode := strings.TrimSpace(v[0])
		if _, ok := shopMap[financeCode]; !ok { //重复的过滤掉
			shopMap[financeCode] = struct{}{}
			shopNum++
			if taskListNotCompleteNum > 0 {
				//未完成任务里
				if _, nok := existShopTaskMap[financeCode]; nok {
					conflictFinanceCodes = append(conflictFinanceCodes, financeCode)
				} else { //不再未完成的门店里
					financeCodes = append(financeCodes, financeCode)
				}
			} else {
				financeCodes = append(financeCodes, financeCode)
			}
		}
	}

	taskExtendData := ExtendData{
		TodoList:     financeCodes,
		ConflictList: conflictFinanceCodes,
	}
	strTaskExtendData, _ := json.Marshal(taskExtendData)
	//查询是否有重复的任务

	channels := strings.Split(in.ChannelId, ",")
	if len(channels) == 0 {
		out.Message = "未指定渠道参数"
		return out, nil
	}
	userInfoJson, err := json.Marshal(userInfo)
	var taskList []*models.TaskList
	for _, channelId := range channels {
		if channelId != "2" {
			out.Message = "当前只支持美团的门店的分类同步"
			return out, nil
		}
		task := new(models.TaskList)
		task.CreateName = userInfo.UserName
		task.CreateIp = in.UserIp
		task.ChannelId = cast.ToInt32(channelId)
		task.CreateId = userInfo.UserNo
		task.ModifyId = userInfo.UserNo
		task.ModifyTime = time.Now()
		task.CreateTime = time.Now()
		task.TaskContent = 31
		task.ShopNum = shopNum
		task.TaskStatus = 1
		task.Status = 1
		task.OperationFileUrl = in.QiniuUrl
		task.RequestHeader = string(userInfoJson)
		task.ExtendedData = string(strTaskExtendData)
		taskList = append(taskList, task)
	}
	// 保存任务信息
	_, err = NewDbConn().Insert(&taskList)
	if err != nil {
		glog.Error(err)
		out.Message = "导入同步分类的门店信息失败"
		return out, err
	}

	out.Message = "批量导入同步分类的门店信息任务进行中..."
	out.Code = 200
	return out, err
}

//v6.4.0
//step 1:
//获取美团某个门店下的所有的分类，把美团该分类全部删除，以前创建的分类可能只有名称就按名称删除，
//有CODE的按CODE删除，并且删除本地 channel_category_thirdid 关系表的数据
//按照门店的维度
//输入门店
//坑：美团那边的实际删除是异步的  我们这边得到的结果是成功，接着立马进行第二步调用RenewCategory同步分类时，会部分提示失败：分类已存在，实际上
//并不是真的存在，而是美团的异步删除还没有完成而已，所以需要我们在第二步同步分类失败时 进行重试
func (this *Product) GetAndDeleteCategory(ctx context.Context, in *pc.SyncCategoryRequest) (*pc.GetAndDeleteCategoryResponse, error) {
	// 请求参数
	res := new(pc.GetAndDeleteCategoryResponse)
	res.Code = http.StatusOK
	var (
		failList []*pc.GetAndDeleteFailList
		err      error
	)

	switch in.ChannelId {
	case 2:
		failList, err = new(MtCateGorySync).GetAndDeleteCategory(in)
	default:
		res.Code = http.StatusBadRequest
		res.Message = "渠道暂时不能同步分类"
		res.Error = "渠道暂时不能同步分类"
	}
	if err != nil {
		res.Code = http.StatusBadRequest
		res.Message = err.Error()
		res.Error = err.Error()
	}
	res.FailList = failList
	return res, nil
}

//v6.4.0
//step 2:重建分类
//把管家商品表的数据select * from channel_category where channel_id=1同步一份数据到美团，新增成功后，把对应关系存入
//channel_category_thirdid 表，注意顺序问题 channel_category 表里面有排序字段的
//输入
func (this *Product) RenewCategory(ctx context.Context, in *pc.SyncCategoryRequest) (*pc.RenewCategoryResponse, error) {
	// 请求参数
	res := new(pc.RenewCategoryResponse)
	res.Code = http.StatusOK
	var (
		failList []*pc.RenewCategoryFailList
		err      error
	)
	switch in.ChannelId {
	case 2:
		failList, err = new(MtCateGorySync).RenewCategory(in)
	default:
		res.Code = http.StatusBadRequest
		res.Message = "渠道暂未实现"
		res.Error = "渠道暂未实现"
	}
	if err != nil {
		res.Code = http.StatusBadRequest
		res.Message = "上传分类失败"
		res.Error = err.Error()
	}
	res.FailList = failList
	return res, nil
}

//v6.4.0
//step 3:将商品关联到分类上
//根据门店编码查询美团上的所有商品（东华做的6.3.8里面会有这个接口），然后再本地数据库查询快照表 channel_product_snapshot ，
//对应的商品分类，把商品在美团移动到该分类下，如果美团上的商品在本地找不到快照数据，则不处理，说明是美团上创建的商品，我们这边没有分类，无法处理
//输入 门店
func (this *Product) RelateCategoryAndProduct(ctx context.Context, in *pc.SyncCategoryRequest) (*pc.RelateCategoryAndProductResponse, error) {
	// 请求参数
	res := new(pc.RelateCategoryAndProductResponse)
	res.Code = http.StatusOK
	var (
		failList []*pc.RelateCategoryAndProductFailList
		err      error
	)
	switch in.ChannelId {
	case 2:
		failList, err = new(MtCateGorySync).RelateCategoryAndProduct(in)
	default:
		res.Code = http.StatusBadRequest
		res.Message = "渠道暂未实现"
		res.Error = "渠道暂未实现"
	}
	if err != nil {
		res.Code = http.StatusBadRequest
		res.Message = "更新商品分类失败"
		res.Error = err.Error()
	}
	res.FailList = failList
	return res, nil
}

//美团GetAndDeleteCategory
func (this *MtCateGorySync) GetAndDeleteCategory(in *pc.SyncCategoryRequest) ([]*pc.GetAndDeleteFailList, error) {
	// 请求参数
	var (
		res     []*pc.GetAndDeleteFailList
		request et.MtRetailCatDeleteRequest
		err     error
		mu      sync.Mutex
	)

	externalGrpc := et.GetExternalClient()
	request.AppPoiCode = in.AppPoiCode
	request.StoreMasterId = in.StoreMasterId

	getRequest := new(et.AppPoiCodeRequest)
	getRequest.StoreMasterId = in.StoreMasterId
	getRequest.AppPoiCode = in.AppPoiCode
	response, err := externalGrpc.RPC.RetailCatList(externalGrpc.Ctx, getRequest)
	glog.Info("GetAndDeleteCategory-查询到的分类", "参数：", kit.JsonEncode(getRequest), "结果：", kit.JsonEncode(response), ",", kit.JsonEncode(in))
	if err != nil {
		//直接返回错误
		return res, errors.New("获取第三方门店分类出错,错误信息：" + err.Error())
	}
	if response.Code != 200 {
		return res, errors.New("获取第三方门店分类失败,失败原因：" + response.Message)
	}
	if len(response.Data) == 0 {
		return res, errors.New("未查询美团门店的分类数据")
	}

	type retryFail struct {
		name string
		code string
		err  string
	}
	var deleteFail []retryFail

	//后面的错误都当做同步失败房费faillist里返回 并不返回err信息
	doDelete := func(name, code string) error {
		deleteCategoryRequest := et.MtRetailCatDeleteRequest{
			AppPoiCode:          in.AppPoiCode,
			StoreMasterId:       in.StoreMasterId,
			MoveProductToUncate: 1,
		}
		//首先按照code删除  没有code的时候用分类删除
		if code != "" {
			deleteCategoryRequest.CategoryCode = code
		}
		if deleteCategoryRequest.CategoryCode == "" {
			deleteCategoryRequest.CategoryName = name
		}
		glog.Info("GetAndDeleteCategory-请求删除分类 参数：", kit.JsonEncode(deleteCategoryRequest), ",", kit.JsonEncode(in))
		deadLineCtx, _ := context.WithTimeout(context.Background(), 30*time.Second)
		deleteRes, errRpc := externalGrpc.RPC.MtRetailCatDelete(deadLineCtx, &deleteCategoryRequest)
		glog.Info("GetAndDeleteCategory-请求删除分类 结果：", "", kit.JsonEncode(deleteCategoryRequest), "", kit.JsonEncode(deleteRes), ",", errRpc, ",", kit.JsonEncode(in))
		mu.Lock()
		defer mu.Unlock()
		if errRpc != nil {
			//切片不是并发安全的 所以要加锁
			deleteFail = append(deleteFail, retryFail{name: name, code: code, err: errRpc.Error()})
			//fmt.Println("删除失败：err:" + errRpc.Error())
			return errRpc
		}
		if deleteRes != nil && deleteRes.Code != 200 {
			if deleteRes.Message != "商品分类不存在" {
				deleteFail = append(deleteFail, retryFail{name: name, code: code, err: deleteRes.Message})
			}
			//fmt.Println("删除失败:" + deleteRes.Message)
			return errors.New(deleteRes.Message)
		}
		return nil
	}

	//并发请求
	deleteCateGory := response.Data
	//分成60个一组 并发请求 如果太多了 可能导致external redis并发读报错
	sliceLen := len(deleteCateGory)
	for {
		if len(deleteCateGory) < sliceLen {
			sliceLen = len(deleteCateGory)
		}
		postSlice := deleteCateGory[:sliceLen]

		wg := new(sync.WaitGroup)
		for _, category := range postSlice {
			wg.Add(1)
			go func(name, code string) {
				defer func() {
					wg.Done()
				}()
				_ = doDelete(name, code)
			}(category.Name, category.Code)
		}
		//等待执行完成
		wg.Wait()

		deleteCateGory = deleteCateGory[sliceLen:]
		if len(deleteCateGory) == 0 {
			break
		}
	}

	glog.Info("GetAndDeleteCategory-重试之前删除失败的分类", kit.JsonEncode(res), ",", kit.JsonEncode(in))
	var retryCount int
	if len(deleteFail) > 0 {
		for i := 0; ; i++ {
			//最多循环2次
			retryCount++
			if i == 20 {
				for _, fail := range deleteFail {
					res = append(res, &pc.GetAndDeleteFailList{
						CategoryName: fail.name,
						Error:        fail.err,
					})
				}
				break
			}
			//拷贝一份 并清空原始变量
			copyDeleteFail := deleteFail
			deleteFail = []retryFail{}
			//重试
			for _, fail := range copyDeleteFail {
				_ = doDelete(fail.name, fail.code)

			}
			if len(deleteFail) == 0 {
				break
			}
		}
	}
	//fmt.Println("---------------------------------------------------------++++++++++++++++++++")
	//如果有删除失败的要重试 保证全部删除成功
	glog.Info("GetAndDeleteCategory-分类重试次数：", retryCount, ",", kit.JsonEncode(in))
	glog.Info("GetAndDeleteCategory-删除失败的分类", kit.JsonEncode(res), ",", kit.JsonEncode(in))

	//删除 本地数据库的数据
	db := NewDbConn()
	_, err = db.Exec("DELETE FROM channel_category_thirdid WHERE channel_id = ? AND channel_store_id = ?", in.ChannelId, in.AppPoiCode)
	if err != nil {
		return res, errors.New("删除分类成功，但清除本地分类数据出错:" + err.Error())
	}
	return res, nil
}

//美团GRenewCategory
////把管家商品表的数据select * from channel_category where channel_id=1同步一份数据到美团，新增成功后，把对应关系存入
////channel_category_thirdid 表，注意顺序问题 channel_category 表里面有排序字段的
func (this *MtCateGorySync) RenewCategory(in *pc.SyncCategoryRequest) ([]*pc.RenewCategoryFailList, error) {
	// 请求参数
	var (
		err              error
		res              []*pc.RenewCategoryFailList
		awenCategoryList []*models.ChannelCategory
		thirdCategory    []*models.ChannelCategoryThirdid
		mu               sync.Mutex
	)
	start := time.Now()
	db := NewDbConn()
	err = db.SQL("SELECT * FROM channel_category WHERE channel_id = 1 ORDER BY parent_id ASC").Find(&awenCategoryList)
	if err != nil {
		return res, errors.New("查询本地阿闻分类数据出错:" + err.Error())
	}
	type myCategory struct {
		Id        int32         `json:"id"`
		Name      string        `json:"name"`
		Sort      int32         `json:"sort"`
		ChannelId int32         `json:"channel_id"`
		ParentId  int32         `json:"parent_id"`
		Children  []*myCategory `json:"children"`
	}
	CategoryMap := make(map[int32]*myCategory)
	//将二级分类放在以及分类下 先同步一级分类 再同步二级分类
	for _, category := range awenCategoryList {
		//一级
		if category.ParentId == 0 {
			CategoryMap[category.Id] = &myCategory{
				Id:        category.Id,
				Name:      category.Name,
				Sort:      category.Sort,
				ChannelId: category.ChannelId,
			}
		} else { //二级
			if _, ok := CategoryMap[category.ParentId]; !ok {
				CategoryMap[category.ParentId] = new(myCategory)
			}
			child := &myCategory{
				Id:        category.Id,
				Name:      category.Name,
				Sort:      category.Sort,
				ChannelId: category.ChannelId,
				ParentId:  category.ParentId,
			}
			CategoryMap[category.ParentId].Children = append(CategoryMap[category.ParentId].Children, child)
		}
	}

	var postCategory []*myCategory
	for key, category := range CategoryMap {
		//有的子分类 没有父分类数据 那么父分类的id = 0 这种数据不用同步
		if category.Id == 0 {
			delete(CategoryMap, key)
		}

	}
	for _, category := range CategoryMap {
		postCategory = append(postCategory, category)
	}
	//将分类上传至美团
	externalGrpc := et.GetExternalClient()
	//后面的错误都当做同步失败房费faillist里返回 并不返回err信息
	type retryFail struct {
		Parent   *myCategory `json:"parent"`
		Children *myCategory `json:"children"`
		Err      string      `json:"err"`
	}

	var renewFail []retryFail
	doSync := func(parent, children *myCategory) error {
		//重置时间 让每个请求的过期时间都为30秒
		var request et.RetailCatUpdateRequest
		request.AppPoiCode = in.AppPoiCode
		request.StoreMasterId = in.StoreMasterId
		postCategoryId := parent.Id
		if children == nil { //一级分类
			request.CategoryName = parent.Name
			request.CategoryCode = cast.ToString(parent.Id)
			request.Sequence = parent.Sort
		} else { // 二级分类
			//request.CategoryNameOrigin = parent.Name
			request.CategoryCodeOrigin = cast.ToString(parent.Id)
			request.CategoryName = parent.Name
			request.CategoryCode = cast.ToString(parent.Id)
			request.SecondaryCategoryName = children.Name
			request.SecondaryCategoryCode = cast.ToString(children.Id)
			request.Sequence = children.Sort
			postCategoryId = children.Id
		}

		glog.Info("RenewCategory新建分类请求参数:", kit.JsonEncode(request), ",", kit.JsonEncode(in))
		deadLineCtx, _ := context.WithTimeout(context.Background(), 300*time.Second)
		syncRes, errRpc := externalGrpc.RPC.RetailCatUpdate(deadLineCtx, &request)

		mu.Lock()
		defer mu.Unlock()
		if errRpc != nil {
			renewFail = append(renewFail, retryFail{
				Parent:   parent,
				Children: children,
				Err:      errRpc.Error(),
			})
			if children == nil {
				glog.Info("RenewCategory新建分类请求美团出错一级分类 返回:", ",", "|", kit.JsonEncode(request), ",", kit.JsonEncode(syncRes), ",", errRpc, "|", kit.JsonEncode(in))
			} else {
				glog.Info("RenewCategory新建分类请求美团出错二级分类 返回:", ",", "|", kit.JsonEncode(request), ",", kit.JsonEncode(syncRes), ",", errRpc, "|", kit.JsonEncode(in))
			}
			return errRpc
		}
		if syncRes != nil && syncRes.Code != 200 {
			renewFail = append(renewFail, retryFail{
				Parent:   parent,
				Children: children,
				Err:      syncRes.Message,
			})
			if children == nil {
				glog.Info("RenewCategory新建分类请求美团失败一级分类 返回:", ",", "|", kit.JsonEncode(request), ",", kit.JsonEncode(syncRes), ",", errRpc, "|", kit.JsonEncode(in))
			} else {
				glog.Info("RenewCategory新建分类请求美团失败二级分类 返回:", ",", "|", kit.JsonEncode(request), ",", kit.JsonEncode(syncRes), ",", errRpc, "|", kit.JsonEncode(in))
			}
			return errors.New(syncRes.Message)
		}
		thirdCategory = append(thirdCategory, &models.ChannelCategoryThirdid{
			ChannelId:      in.ChannelId,
			ChannelStoreId: in.AppPoiCode,
			CategoryId:     cast.ToString(postCategoryId), //第三方分类id 美团没有
			Id:             postCategoryId,
		})

		return nil
	}
	glog.Info("RenewCategory-同步分类需要同步的数据", kit.JsonEncode(postCategory), ",", kit.JsonEncode(in))

	//更新
	sliceLen := len(postCategory)
	for {
		if len(postCategory) < sliceLen {
			sliceLen = len(postCategory)
		}
		postSlice := postCategory[:sliceLen]

		wg := new(sync.WaitGroup)
		for _, category := range postSlice {
			wg.Add(1)
			go func(category *myCategory) {
				defer func() {
					wg.Done()
				}()
				//fmt.Println("开始同步分类------", category.Name)
				errSync := doSync(category, nil)
				//没有子分类 直接返回
				if len(category.Children) == 0 {
					return
				}
				//有二级分类 且一级分类处理失败 则不再处理二级分类
				if errSync != nil {
					mu.Lock()
					defer mu.Unlock()
					for _, childCategory := range category.Children {
						renewFail = append(renewFail, retryFail{
							Parent:   category,
							Children: childCategory,
							Err:      "父分类同步失败,父分类：" + category.Name,
						})
					}
					return
				}
				//处理二级分类
				for _, childCategory := range category.Children {
					//fmt.Println("开始同步子分类------", childCategory.Name)
					_ = doSync(category, childCategory)
				}
			}(category)
		}
		//等待执行完成
		wg.Wait()

		postCategory = postCategory[sliceLen:]
		if len(postCategory) == 0 {
			break
		}
	}

	//fmt.Println("---------------------------------------------------------++++++++++++++++++++")
	//如果有删除失败的要重试 保证全部删除成功
	glog.Info("RenewCategory-重试之前同步失败的分类", kit.JsonEncode(renewFail), ",", kit.JsonEncode(in))
	glog.Info("RenewCategory时间统计 执行完第一步花费时间", time.Since(start), ",参数：", kit.JsonEncode(in))
	var retryCount int
	//坑：在第一步删除门店分类时，美团那边的实际删除是异步的  我们这边得到的删除结果是成功，接着立马进行同步分类时，会部分提示失败：分类已存在，实际上
	//并不是真的存在，而是美团的异步删除还没有完成而已，所以需要我们在同步分类失败时 进行重试
	notDeleteTryMap := make(map[string]int)

	if len(renewFail) > 0 {
		tryAgainFlag := false
		for i := 0; ; i++ {
			time.Sleep(4 * time.Second)
			//拷贝一份 并清空原始变量
			copyDeleteFail := renewFail
			//置空
			renewFail = []retryFail{}
			//重组失败的//子分类放在父分类下
			failParentMap := make(map[int32]*myCategory)
			retryCount++
			for _, fail := range copyDeleteFail {
				fmt.Println("重试请求参数", kit.JsonEncode(fail.Parent), ",", kit.JsonEncode(fail.Children))
				//子分类 看看父分类是否失败 如果失败了 则自动放入到失败之中 不再请求
				if fail.Children != nil {
					if val, ok := failParentMap[fail.Children.ParentId]; ok {
						renewFail = append(renewFail, retryFail{
							Parent:   fail.Parent,
							Children: fail.Children,
							Err:      "父分类同步失败,父分类：" + val.Name,
						})
						continue
					}
				}
				var errSync error
				//没有必要的错误直接不再请求 直接放入renew
				if strings.Contains(fail.Err, "包含敏感词") { //不再跑了
					renewFail = append(renewFail, fail)
					errSync = errors.New(fail.Err)
				} else {
					tryAgainFlag = true //实际上再次发生了请求 需要再次重试
					if strings.Contains(fail.Err, "对应的不是同一个分类") {
						if fail.Children == nil {
							notDeleteTryMap[fail.Parent.Name] = i
						} else {
							notDeleteTryMap[fail.Children.Name] = i
						}
					}
					errSync = doSync(fail.Parent, fail.Children)

				}
				if errSync != nil && fail.Children == nil {
					failParentMap[fail.Parent.Id] = fail.Parent
				}
			}
			//没有错误 或者没有可以跑的错误 或者 满了5次
			if len(renewFail) == 0 || tryAgainFlag == false || i == 60 {
				break
			}
		}
	}
	//统计一下次数 看看会花多久 以及多少次
	glog.Info("RenewCategory-分类重试次数：", retryCount, ",", kit.JsonEncode(in))
	glog.Info("RenewCategory-分类不对应重试次数统计：", kit.JsonEncode(notDeleteTryMap), ",", kit.JsonEncode(in))

	if len(renewFail) > 0 {
		for _, fail := range renewFail {
			if fail.Children == nil {
				res = append(res, &pc.RenewCategoryFailList{
					CategoryId:   int64(fail.Parent.Id),
					CategoryName: fail.Parent.Name,
					Error:        fail.Err,
				})
			} else {
				res = append(res, &pc.RenewCategoryFailList{
					CategoryId:   int64(fail.Children.Id),
					CategoryName: fail.Children.Name,
					Error:        fail.Err,
				})
			}
		}
	}
	glog.Info("RenewCategory时间统计 执行完第二步花费时间", time.Since(start), ",参数：", kit.JsonEncode(in))
	//fmt.Println("---------------------------------------------------------++++++++++++++++++++")
	//如果有删除失败的要重试 保证全部删除成功
	glog.Info("RenewCategory-重试后依然同步失败的分类", kit.JsonEncode(res), ",", kit.JsonEncode(in))

	//写入第三方表
	_, err = NewDbConn().Insert(thirdCategory)
	if err != nil {
		fmt.Println(err.Error())
		return res, errors.New("更新本地第三方分类数据出错:" + err.Error())
	}
	return res, nil
}

//美团RelateCategoryAndProduct
//根据门店编码查询美团上的所有商品（东华做的6.3.8里面会有这个接口），然后再本地数据库查询快照表 channel_product_snapshot ，
//对应的商品分类，把商品在美团移动到该分类下，如果美团上的商品在本地找不到快照数据，则不处理，说明是美团上创建的商品，我们这边没有分类，无法处理
func (this *MtCateGorySync) RelateCategoryAndProduct(in *pc.SyncCategoryRequest) ([]*pc.RelateCategoryAndProductFailList, error) {
	// 请求参数
	start := time.Now()
	var (
		err         error
		mu          = new(sync.Mutex)
		res         []*pc.RelateCategoryAndProductFailList
		productList []*et.StoreRetailInfo
	)

	externalGrpc := et.GetExternalClient()

	getRequest := new(et.RetailListRequest)
	getRequest.StoreMasterId = in.StoreMasterId
	getRequest.AppPoiCode = in.AppPoiCode
	getRequest.FinanceCode = in.FinanceCode
	getRequest.PageIndex = 1
	getRequest.PageSize = 200 //todo 写成配置

	response, err := externalGrpc.RPC.RetailList(externalGrpc.Ctx, getRequest)
	if err != nil {
		return res, errors.New("查询门店商品数据出错")
	}
	if response == nil || len(response.Data) == 0 {
		return res, errors.New("未查询到查询门店商品数据")
	}
	productList = append(productList, response.Data...)
	//总数
	totalPage := int(response.ExtraInfo.TotalCount/getRequest.PageSize + 1)
	//一个循环将所有的数据查询完
	wg := new(sync.WaitGroup)
	var queryErrors []string
	for i := 2; i <= totalPage; i++ {
		wg.Add(1)
		//没有数据或者报错了则结束循环
		go func(i int) {
			defer func() {
				wg.Done()
			}()
			syncRequest := new(et.RetailListRequest)
			syncRequest.StoreMasterId = in.StoreMasterId
			syncRequest.AppPoiCode = in.AppPoiCode
			syncRequest.FinanceCode = in.FinanceCode
			syncRequest.PageIndex = int32(i)
			syncRequest.PageSize = 200

			//fmt.Println("查询++++++", syncRequest.PageIndex)
			deadLineCtx, _ := context.WithTimeout(context.Background(), 12*time.Hour)
			responseLeft, errRpc := externalGrpc.RPC.RetailList(deadLineCtx, syncRequest)
			if errRpc != nil {
				queryErrors = append(queryErrors, errRpc.Error())
				return
			}
			if responseLeft == nil {
				queryErrors = append(queryErrors, "未查询到数据")
				return
			}
			if len(responseLeft.Data) > 0 {
				mu.Lock()
				productList = append(productList, responseLeft.Data...)
				mu.Unlock()
			} else if len(responseLeft.Data) == 0 {
				queryErrors = append(queryErrors, "查询到的数据为空")
			}
		}(i)
	}
	wg.Wait()
	glog.Info("RelateCategoryAndProduct时间统计 执行完第一步花费时间", time.Since(start), ",参数：", kit.JsonEncode(in))
	//有查询错误
	if len(queryErrors) > 0 {
		glog.Info("syncChannelCategory获取到美团商品条数分类查询失败原因", queryErrors, "，参数", kit.JsonEncode(in))
		return res, errors.New("获取美团数据失败")
	}

	//此处查询商品的快照信息
	type MtSku struct {
		SkuId string `json:"sku_id"`
	}
	//循环商品 并通过快照表查询商品的分类id
	var productIds []string
	//美团的商品id 与skuid映射
	mtProducts := make(map[string]string)
	var mt []*et.StoreRetailInfo
	for _, product := range productList {
		var skuInfo []*MtSku
		productIds = append(productIds, product.AppFoodCode)
		mt = append(mt, product)
		_ = json.Unmarshal([]byte(product.Skus), &skuInfo)
		if len(skuInfo) > 0 {
			mtProducts[product.AppFoodCode] = skuInfo[0].SkuId
		} else {
			mtProducts[product.AppFoodCode] = ""
		}
	}

	db := NewDbConn()
	var jsonDatas []string

	err = db.SQL("SELECT json_data FROM channel_product_snapshot WHERE channel_id = ? AND finance_code = ? AND "+
		"product_id IN('"+strings.Join(productIds, "','")+"')", in.ChannelId, in.FinanceCode).Find(&jsonDatas)
	if err != nil {
		return res, errors.New("查询商品快照信息出错" + err.Error())
	}
	glog.Info("RelateCategoryAndProduct时间统计 执行完第二步花费时间", time.Since(start), ",参数：", kit.JsonEncode(in))
	if len(jsonDatas) == 0 {
		return res, errors.New("未查询到商品快照信息")
	}

	type ProductSnapData struct {
		ProductId         int64 `json:"id"`
		ChannelCategoryId int64 `json:"channel_category_id"`
		SkuId             int64 `json:"sku_id"`
	}
	type StrProductSnapData struct {
		ProductId         string `json:"id"`
		ChannelCategoryId string `json:"channel_category_id"`
		SkuId             string `json:"sku_id"`
	}
	type Sku struct {
		SkuId int64 `json:"sku_id"`
	}
	type ProductSnap struct {
		Product *ProductSnapData `json:"product"`
		SkuInfo []*Sku           `json:"sku_info"`
	}

	productMap := make(map[string]struct{})
	//需要更新分类的商品
	afterFilterNum := len(jsonDatas)

	var relateProductCategory []*StrProductSnapData
	for _, v := range jsonDatas {
		if v == "" {
			afterFilterNum--
			continue
		}
		snapDetail := new(ProductSnap)
		_ = json.Unmarshal([]byte(v), snapDetail)
		productId := cast.ToString(snapDetail.Product.ProductId)
		//快照里可能存在重复的数据
		if _, ok := productMap[productId]; ok {
			afterFilterNum--
			continue
			//fmt.Println("有重复啊啊啊啊")
		} else {
			productMap[cast.ToString(snapDetail.Product.ProductId)] = struct{}{}
			relateProductCategory = append(relateProductCategory, &StrProductSnapData{
				ProductId:         cast.ToString(snapDetail.Product.ProductId),
				ChannelCategoryId: cast.ToString(snapDetail.Product.ChannelCategoryId),
				SkuId:             cast.ToString(snapDetail.SkuInfo[0].SkuId),
			})
		}

	}

	//找出在在美团存在的商品 而 在快照中没有查询出来的商品
	for productId, skuId := range mtProducts {
		if _, ok := productMap[productId]; !ok {
			res = append(res, &pc.RelateCategoryAndProductFailList{
				ProductId: productId,
				SkuId:     skuId,
				Error:     "快照里无该商品或者快照数据不正确,appFoodCode：" + productId,
			})
		}
	}

	//查询已经同步成功的分类
	type awenCategoryInfo struct {
		AwenCategoryId string
		CategoryName   string
	}
	var awenCategory []*awenCategoryInfo
	err = db.SQL("SELECT a.id AS awen_category_id,b.name as category_name FROM channel_category_thirdid a JOIN channel_category b ON "+
		"a.id = b.id WHERE a.channel_id = ? AND a.channel_store_id = ?", in.ChannelId, in.AppPoiCode).
		Find(&awenCategory)
	if err != nil {
		return res, errors.New("未查第三方分类id出错" + err.Error())
	}
	if len(awenCategory) == 0 {
		return res, errors.New("未查第三方分类id")
	}

	awenCategoryMap := make(map[string]string)
	for _, category := range awenCategory {
		awenCategoryMap[category.AwenCategoryId] = category.CategoryName
	}

	//成功关联的商品
	var successRelate []string
	var failRelate []string
	var retryFail []*pc.RelateCategoryAndProductFailList
	//后面的错误都当做同步失败房费faillist里返回 并不返回err信息
	doRelate := func(productId, skuId, categoryName, categoryId string) error {
		//重置时间 让每个请求的过期时间都为30秒
		relateRequest := &et.RetailInitDataCategoryRequest{
			OperateType:   2,
			AppPoiCode:    in.AppPoiCode,
			StoreMasterId: in.StoreMasterId,
			AppFoodCode:   cast.ToString(productId), //商品id todo 这个使用查询下来的东西
			CategoryCode:  cast.ToString(categoryId),
		}
		deadLineCtx, _ := context.WithTimeout(context.Background(), 24*time.Hour)
		deleteRes, errRpc := externalGrpc.RPC.RetailInitDataCategory(deadLineCtx, relateRequest)

		mu.Lock()
		defer mu.Unlock()
		if errRpc != nil {
			retryFail = append(retryFail, &pc.RelateCategoryAndProductFailList{
				ProductId:    productId,
				SkuId:        skuId,
				CategoryId:   categoryId,
				CategoryName: categoryName,
				Error:        errRpc.Error(),
			})
			//failRelate = append(failRelate, skuId)
			glog.Error("关联商品分类出错------", errRpc.Error(), ",", kit.JsonEncode(relateRequest), ",", kit.JsonEncode(in))
			return errRpc
		}
		if deleteRes != nil && deleteRes.Code != 200 {
			retryFail = append(retryFail, &pc.RelateCategoryAndProductFailList{
				ProductId:    productId,
				SkuId:        skuId,
				CategoryId:   categoryId,
				CategoryName: categoryName,
				Error:        deleteRes.Message,
			})
			//failRelate = append(failRelate, skuId)
			glog.Error("关联商品分类失败", deleteRes.Message, ",", kit.JsonEncode(relateRequest), ",", kit.JsonEncode(in))
			return errors.New(deleteRes.Message)
		}
		if deleteRes == nil {
			retryFail = append(retryFail, &pc.RelateCategoryAndProductFailList{
				ProductId:    productId,
				SkuId:        skuId,
				CategoryId:   categoryId,
				CategoryName: categoryName,
				Error:        "未读取到返回数据",
			})
			//failRelate = append(failRelate, skuId)
			glog.Error("关联商品分类失败，未读取到返回数据", ",", kit.JsonEncode(relateRequest), ",", kit.JsonEncode(in))
			return errors.New("未读取到返回数据")
		}
		successRelate = append(successRelate, skuId)
		return nil
	}

	//通过商品的快照查询商品的分类
	//todo 对接后方可确定
	//	i, j := 0, 0

	var findCategorySuccess []string
	//每100个1组
	sliceLen := len(relateProductCategory)
	categoryWrongNum := 0
	for {
		if len(relateProductCategory) < sliceLen {
			sliceLen = len(relateProductCategory)
		}
		postSlice := relateProductCategory[:sliceLen]

		wgUpdate := new(sync.WaitGroup)
		//j++
		//fmt.Println("关联--组-------------------------", j)
		for _, productInfo := range postSlice {
			//	i++
			//	fmt.Println("关联---------", productInfo.ProductId, "------", i)
			//如果没有分类 则不在上传
			categoryId := productInfo.ChannelCategoryId
			skuId := productInfo.SkuId
			categoryName := awenCategoryMap[categoryId]
			_, ok := awenCategoryMap[categoryId]
			if categoryId == "" || !ok {
				res = append(res, &pc.RelateCategoryAndProductFailList{
					ProductId:    productInfo.ProductId,
					SkuId:        skuId,
					CategoryId:   categoryId,
					CategoryName: categoryName,
					Error:        "未找到分类(快照里没有分类id或者分类id在不在第三方门店的分类中)",
				})
				categoryWrongNum++
				continue
			}
			wgUpdate.Add(1)
			findCategorySuccess = append(findCategorySuccess, skuId)
			go func(productId, skuId, categoryName, categoryId string) {
				defer func() {
					wgUpdate.Done()
				}()
				_ = doRelate(productId, skuId, categoryName, categoryId)
			}(productInfo.ProductId, skuId, categoryName, categoryId)
		}
		//等待执行完成
		wgUpdate.Wait()
		relateProductCategory = relateProductCategory[sliceLen:]
		if len(relateProductCategory) == 0 {
			break
		}
	}

	glog.Info("RelateCategoryAndProduct-重试之前同步失败的分类", kit.JsonEncode(retryFail), ",", kit.JsonEncode(in))
	var retryCount int
	if len(retryFail) > 0 {
		for i := 0; ; i++ {
			//最多循环2次
			retryCount++
			if i == 50 {
				for _, fail := range retryFail {
					res = append(res, &pc.RelateCategoryAndProductFailList{
						ProductId:    fail.ProductId,
						SkuId:        fail.SkuId,
						CategoryId:   fail.CategoryId,
						CategoryName: fail.CategoryName,
						Error:        fail.Error,
					})
					failRelate = append(successRelate, fail.SkuId)
				}
				break
			}
			//拷贝一份 并清空原始变量
			copyDeleteFail := retryFail
			retryFail = []*pc.RelateCategoryAndProductFailList{}
			//重试
			for _, fail := range copyDeleteFail {
				_ = doRelate(fail.ProductId, fail.SkuId, fail.CategoryName, fail.CategoryId)

			}
			if len(retryFail) == 0 {
				break
			}
		}
	}
	//fmt.Println("---------------------------------------------------------++++++++++++++++++++")
	//如果有删除失败的要重试 保证全部删除成功
	glog.Info("RelateCategoryAndProduct-分类重试次数：", retryCount, ",", kit.JsonEncode(in))
	glog.Info("RelateCategoryAndProduct重试之后依然失败的分类", kit.JsonEncode(res), ",", kit.JsonEncode(in))

	glog.Info("syncChannelCategory-所有的商品数量：", len(productList),
		"，快照过滤后的条数：", afterFilterNum,
		"，分类错误的数量：", categoryWrongNum,
		"请求更新分类的商品条数：", len(findCategorySuccess),
		",请求更新分类成功的商品条数:", len(successRelate),
		",请求更新分类失败的商品条数:", len(failRelate), failRelate,
		",所有失败的数量:", len(res),
		"请求入参", kit.JsonEncode(in),
	)
	//fmt.Println("关联分类结束")
	glog.Info("RelateCategoryAndProduct时间统计 执行完第三步花费时间", time.Since(start), ",参数：", kit.JsonEncode(in))
	//glog.Info("syncChannelCategory 关联失败的分类商品", kit.JsonEncode(res))
	return res, nil
}

// 分布式调度器回调通知
func (c *Product) SyncChannelCategoryScheduleCallback(ctx context.Context, req *pc.SyncChannelCategoryScheduleCallbackRequest) (*empty.Empty, error) {
	glog.Infof("syncChannelCategoryScheduleCallback 收到分类同步回调通知,入参:%s", kit.JsonEncode(req))
	if req.TaskId == 0 {
		glog.Errorf("syncChannelCategoryScheduleCallback 参数taskId不能为空")
		return &empty.Empty{}, nil
	}

	taskResp, err := c.GetTaskList(ctx, &pc.GetTaskListRequest{
		Id:          int32(req.TaskId),
		TaskContent: 31,
		ChannelId:   -1,
	})
	if err != nil {
		glog.Errorf("syncChannelCategoryScheduleCallback 查询任务详情失败,taskId:%d,%+v", req.TaskId, err)
		return nil, fmt.Errorf("查询任务详情失败,%+v", err)
	}
	if len(taskResp.TaskList) == 0 {
		glog.Errorf("syncChannelCategoryScheduleCallback 查询不到任务信息,taskId:%d", req.TaskId)
		return &empty.Empty{}, nil
	}

	task := taskResp.TaskList[0]
	countMap := make(map[string]struct{})
	var successNum, failNum int32
	for _, v := range req.Data {
		if _, ok := countMap[v.FinanceCode]; !ok {
			if v.IsSuccess {
				successNum++
			} else {
				failNum++
			}
		}
		countMap[v.FinanceCode] = struct{}{}
	}

	// 更新任务信息
	updateModel := models.TaskList{
		TaskDetail:     fmt.Sprintf("同步成功%d个门店，同步失败%d个门店", successNum, failNum),
		ResulteFileUrl: "",
		ModifyTime:     time.Now(),
		SuccessNum:     successNum,
		FailNum:        failNum,
	}
	//已取消状态 不更改为完成
	if task.TaskStatus != 4 {
		updateModel.TaskStatus = 3
	}
	updateModel.ResulteFileUrl = exportSyncChannelCategoryError(int32(req.TaskId), req.Data)

	_, err = engine.Where("id=?", task.Id).Update(updateModel)
	if err != nil {
		glog.Errorf("syncChannelCategoryScheduleCallback 更新任务状态异常,taskId:%d,%+v", req.TaskId, err)
		return nil, fmt.Errorf("更新任务状态异常,%+v", err)
	}
	return &empty.Empty{}, nil
}

func (c *Product) CancelSyncChannelCategoryThirdTask(ctx context.Context, req *pc.CancelTaskRequest) (*pc.CancelTaskResponse, error) {
	if req.Id == 0 {
		return nil, errors.New("参数ID不能为空")
	}
	resp := &pc.CancelTaskResponse{
		Code: http.StatusBadRequest,
	}

	if req.TaskContent <= 0 {
		req.TaskContent = int64(SyncCategoryTaskContent)
	}
	taskResp, err := c.GetTaskList(ctx, &pc.GetTaskListRequest{
		Id:          int32(req.Id),
		TaskContent: int32(req.TaskContent),
		ChannelId:   -1,
	})
	if err != nil {
		glog.Errorf("Product/CancelSyncChannelCategoryThirdTask 查询任务异常,请求参数:%s,%+v", kit.JsonEncode(req), err)
		return resp, err
	}

	if len(taskResp.TaskList) == 0 {
		resp.Message = "未获取任务信息"
		return resp, nil
	}
	// 任务处于未开始或进行中则可以进行取消，否则不可以
	// 1:未开始;2:进行中;
	task := taskResp.TaskList[0]
	if task.TaskStatus == 3 {
		resp.Message = "当前任务已完成，无法取消"
		return resp, nil
	}
	if task.TaskStatus == 4 {
		resp.Message = "当前任务已取消，不用重复提交"
		return resp, nil
	}

	if req.TaskContent == int64(MoveCategoryProduct) {
		err := c.DealCancelMoveProductTask(ctx, task)
		if err != nil {
			resp.Message = "取消移动商品任务异常：" + err.Error()
			glog.Error(resp.Message)
			return resp, err
		}

	} else {
		err := c.DealCancelCategoryThirdTask(ctx, task)
		if err != nil {
			resp.Message = "取消分类任务异常" + err.Error()
			glog.Error(resp.Message)
			return resp, err
		}
	}

	updateModel := models.TaskList{
		TaskStatus: 4,
		TaskDetail: "该任务已取消",
		ModifyTime: time.Now(),
	}
	_, err = engine.Where("id=?", task.Id).Update(updateModel)
	if err != nil {
		resp.Message = "更新任务异常" + err.Error()
		return resp, err
	}

	resp.Code = http.StatusOK
	resp.Message = "取消成功"
	glog.Info("resp:", kit.JsonEncode(resp))
	return resp, nil
}

func (c *Product) DealCancelCategoryThirdTask(ctx context.Context, task *pc.TaskList) error {

	if task.ContextData != "" {
		var contextData SyncChannelCategoryContext
		err := json.Unmarshal([]byte(task.ContextData), &contextData)
		if err != nil {
			glog.Errorf("Product/CancelSyncChannelCategoryThirdTask 反序列化异常,参数:%s,%+v", task.ContextData, err)
			return err
		}
		if contextData.ScheduleId != 0 {
			err = workerCtx.ManualCancelJob(dto.ManualCancelRequest{
				Id:     int64(contextData.ScheduleId),
				Reason: "用户手动取消任务",
			})
			if err != nil {
				glog.Errorf("Product/CancelSyncChannelCategoryThirdTask 取消任务失败,参数:%d,%+v", contextData.ScheduleId, err)
				return err
			}
		}
	}

	redisHandle.Del("task:productcenter:synccategory:new")

	return nil
}

func (c *Product) DealCancelMoveProductTask(ctx context.Context, task *pc.TaskList) error {

	redisHandle.Del("productcenter:task:moveCategoryProduct")

	return nil
}
